---
alwaysApply: true
---

Trigger-Based Animations (trigger):
Description: Specifies the element that triggers the animation when it enters or leaves the viewport.
Example: Animations start when the top of a trigger element (e.g., .box) reaches a specific point in the viewport, such as start: "top center".marmelab.comthecodecreative.com
Common Use: Triggering animations like fade-ins, slide-ins, or rotations when an element becomes visible.
Scrub Animations (scrub):
Description: Links the animation’s progress directly to the scrollbar position, allowing the animation to play forward or backward as the user scrolls. Setting scrub: true provides a direct 1:1 mapping, while a number (e.g., scrub: 0.5) adds a smoothing delay in seconds.gsap.commarmelab.comwebbae.net
Example: gsap.to(".box", { x: 500, scrollTrigger: { trigger: ".box", scrub: true } }); moves an element horizontally as the user scrolls.
Common Use: Parallax effects, continuous scaling, or opacity changes tied to scroll position.
Pin Animations (pin):
Description: Fixes an element in place during scrolling, keeping it visible until the ScrollTrigger’s end point is reached. Can be combined with pinSpacing to control spacing behavior.gsap.comgsap.com
Example: gsap.to("#pin-windmill", { scrollTrigger: { pin: true, trigger: "#pin-windmill", start: "50% 50%", end: "bottom 50%" } }); pins an element in place while scrolling through a section.
Common Use: Sticky headers, pinned sections, or elements that remain visible during a scroll sequence.
Toggle Actions (toggleActions):
Description: Controls animation behavior at four toggle points: onEnter, onLeave, onEnterBack, and onLeaveBack. Options include play, pause, resume, reset, restart, complete, reverse, or none.nobledesktop.com
Example: toggleActions: "play pause resume reset" plays the animation when entering, pauses when leaving, resumes when re-entering backward, and resets when scrolling past the start.
Common Use: Controlling animations like fade-ins or slide-ins that restart or reverse based on scroll direction.
Snap Animations (snap):
Description: Automatically snaps the scroll position to specific points, such as predefined values, array intervals, or labels in a timeline. Can include easing and delay for smooth snapping.gsap.comgithub.com
Example: snap: { snapTo: [0, 0.5, 1], duration: 0.5, ease: "power2.out" } snaps the scroll to 0%, 50%, or 100% of the animation’s progress.
Common Use: Section-based scrolling, carousel snapping, or aligning content to specific scroll points.
Toggle Class (toggleClass):
Description: Adds or removes a CSS class based on scroll position, allowing CSS-driven animations to be triggered.github.com
Example: toggleClass: { className: "active", targets: ".box" } adds the active class when the trigger is active.
Common Use: Toggling visibility, styles, or CSS animations on scroll.
Horizontal Scrolling (horizontal):
Description: Enables animations for horizontal scrolling sections, often controlled by vertical scrolling.gsap.comdev.to
Example: gsap.fromTo(".wrapper", { x: "100%" }, { x: 0, scrollTrigger: { trigger: section, scrub: 0.5 } }); creates a horizontal slide effect.
Common Use: Horizontal carousels, galleries, or storytelling sections.
Container Animation (containerAnimation):
Description: Links ScrollTrigger to a GSAP timeline or tween controlling a container’s animation, often used for complex, orchestrated sequences.github.com
Example: Used in horizontal scrolling setups where a container’s animation drives child element animations.
Common Use: Complex multi-element animations within a scrolling container.
Anticipate Pin (anticipatePin):
Description: Adjusts pinning to occur slightly early to avoid visual glitches due to browser rendering delays.gsap.com
Example: anticipatePin: 1 ensures smooth pinning for large sections.
Common Use: Enhancing pinned section transitions.
Markers (markers):
Description: Visual debugging aids that show start and end points of the ScrollTrigger in the viewport. Can be customized with colors, font sizes, etc.thecodecreative.comnobledesktop.com
Example: markers: { startColor: "purple", endColor: "fuchsia", fontSize: "4rem" } displays custom markers.
Common Use: Debugging animation timing and positioning.
Common Animation Effects with ScrollTrigger
While ScrollTrigger itself doesn’t categorize animations by “types,” developers use it to create specific effects by combining GSAP tweens with ScrollTrigger properties. Below are common animation effects, often referred to in tutorials and demos, with their typical implementations:

Fade In/Out:
Description: Changes an element’s opacity as it enters or leaves the viewport.
Example: gsap.from(".box", { opacity: 0, scrollTrigger: { trigger: ".box", start: "top 80%" } });thecodecreative.com
Use Case: Introducing elements like text or images as they scroll into view.
Slide In/Out:
Description: Moves an element along the x or y axis, often from off-screen to on-screen.
Example: gsap.from(".box", { x: 500, scrollTrigger: { trigger: ".box", start: "top 80%" } });marmelab.com
Use Case: Animating cards, images, or sections sliding into place.
Parallax:
Description: Creates depth by moving elements at different speeds relative to the scroll position.
Example: gsap.to(".background", { y: 100, scrollTrigger: { trigger: ".section", scrub: true } });marmelab.commedium.com
Use Case: Background images or layers moving slower than foreground content.
Scale:
Description: Changes an element’s size, often tied to scroll progress.
Example: gsap.from(".element", { scale: 0.5, scrollTrigger: { trigger: ".element", scrub: true } });gsap.com
Use Case: Zooming in images or icons as they enter the viewport.
Rotation:
Description: Rotates an element, often for dynamic visual effects.
Example: gsap.to("#pin-windmill-svg", { rotateZ: 900, scrollTrigger: { trigger: "#pin-windmill", scrub: true } });gsap.com
Use Case: Spinning logos or objects during scroll.
Background Position/Glitch:
Description: Animates CSS properties like backgroundPosition for effects like glitches or shifts.
Example: gsap.to(".glitch-slide", { keyframes: { "0%": { backgroundPosition: "0 0" }, "50%": { backgroundPosition: "-50px 0" } }, scrollTrigger: { trigger: ".glitch-slide", scrub: true } });stackoverflow.com
Use Case: Creating textured or glitchy visual effects.
Staggered Animations:
Description: Animates multiple elements with a delay between each, often used with gsap.utils.toArray.
Example: gsap.from(".word", { opacity: 0, yPercent: 100, stagger: 0.5, scrollTrigger: { trigger: "#home-about-section", start: "top 60%" } });reddit.com
Use Case: Animating text words or grid items sequentially.
Horizontal Scroll Sections:
Description: Animates elements to move horizontally as the user scrolls vertically.
Example: gsap.fromTo(".wrapper", { x: "100%" }, { x: 0, scrollTrigger: { trigger: section, scrub: 0.5 } });dev.to
Use Case: Horizontal galleries or storytelling sections.
Infinite Loop Animations:
Description: Resets animations to create a seamless loop when scrolling reaches the end.
Example: Animating elements to reset positions on loop, though this can be complex and may cause jumps if not configured properly.stackoverflow.com
Use Case: Continuous card animations or looping carousels.