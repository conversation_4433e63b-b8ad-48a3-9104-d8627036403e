{"mcpServers": {"serena": {"command": "/opt/homebrew/bin/uvx", "args": ["--from", "git+https://github.com/oraios/serena", "serena-mcp-server"], "env": {"FASTMCP_LOG_LEVEL": "ERROR"}, "disabled": false, "autoApprove": ["initial_instructions", "check_onboarding_performed", "activate_project", "find_file", "read_file"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "disabled": false, "autoApprove": []}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest"], "disabled": false, "autoApprove": []}, "desktop-commander": {"command": "npx", "args": ["-y", "@wonderwhy-er/desktop-commander"], "disabled": false, "autoApprove": ["list_directory", "get_config", "search_files"]}}}