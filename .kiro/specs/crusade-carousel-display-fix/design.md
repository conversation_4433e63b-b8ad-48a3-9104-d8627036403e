# Design Document

## Overview

This design addresses the crusade carousel display issues in index.php where card content is being cropped across different screen sizes. The solution involves modifying the CSS layout system, adjusting container heights, and ensuring proper responsive behavior for the seamless loop carousel implementation.

## Architecture

The crusade carousel system consists of several interconnected components:
1. **Container Structure** - Main carousel wrapper and positioning
2. **Card Layout System** - Individual crusade card styling and dimensions
3. **GSAP Animation System** - Seamless loop carousel animations
4. **Responsive Design** - Breakpoint-specific adjustments

### Current Issues Analysis

#### Root Cause Identification
- Fixed height containers (`min-h-screen`, `h-96`) are constraining content
- Absolute positioning in GSAP seamless loop is causing overflow issues
- Responsive breakpoints not properly adjusting for content height variations
- CSS overflow settings are hiding content instead of expanding containers

## Components and Interfaces

### 1. Container System Redesign

#### Main Carousel Container
```css
/* Current problematic approach */
#crusadesCarousel {
    overflow: hidden;
    position: relative;
    height: 24rem; /* Fixed height causing cropping */
}

/* New flexible approach */
#crusadesCarousel {
    overflow: visible; /* Allow content to expand */
    position: relative;
    min-height: 24rem; /* Minimum height, can expand */
    height: auto; /* Dynamic height based on content */
}
```

#### Slider Container Adjustments
```css
/* Current positioning system */
#crusadesSlider {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    max-width: 4xl;
}

/* Enhanced positioning system */
#crusadesSlider {
    position: relative; /* Change from absolute to relative */
    width: 100%;
    max-width: 4xl;
    margin: 0 auto; /* Center horizontally */
    min-height: fit-content; /* Expand to content */
}
```

### 2. Crusade Card Layout Improvements

#### Card Container Structure
```css
/* Enhanced card styling */
.crusade-card {
    position: absolute; /* Maintained for GSAP animation */
    width: 100%;
    height: auto; /* Remove fixed height */
    min-height: fit-content; /* Expand to content */
    padding: 0.75rem 1rem; /* Consistent padding */
}

/* Card content container */
.crusade-card .bg-white {
    height: auto; /* Remove fixed height constraints */
    min-height: fit-content;
    display: flex;
    flex-direction: column;
}
```

#### Responsive Card Adjustments
```css
/* Mobile optimizations */
@media (max-width: 640px) {
    .crusade-card {
        padding: 0.5rem;
    }
    
    .crusade-card .bg-white {
        border-radius: 0.5rem; /* Maintain mobile rounded corners */
    }
}

/* Tablet adjustments */
@media (min-width: 641px) and (max-width: 1024px) {
    .crusade-card {
        padding: 0.75rem;
    }
}

/* Desktop optimizations */
@media (min-width: 1025px) {
    .crusade-card {
        padding: 1rem 1.5rem;
    }
    
    .crusade-card .bg-white {
        border-radius: 0; /* Sharp corners for desktop */
    }
}
```

### 3. Image and Content Layout

#### Image Container Fixes
```css
/* Flexible image containers */
.crusade-card .aspect-\[16\/9\] {
    aspect-ratio: 16/9;
    overflow: hidden;
    height: auto; /* Remove fixed height */
}

/* Responsive image adjustments */
@media (max-width: 768px) {
    .crusade-card .lg\:aspect-auto {
        aspect-ratio: 16/9; /* Maintain aspect ratio on mobile */
        height: auto;
    }
}

@media (min-width: 1024px) {
    .crusade-card .lg\:h-96 {
        height: auto; /* Remove fixed height */
        min-height: 24rem; /* Minimum height for consistency */
        max-height: none; /* Allow expansion */
    }
}
```

#### Content Section Layout
```css
/* Flexible content sections */
.crusade-card .lg\:w-1\/2 {
    display: flex;
    flex-direction: column;
    justify-content: flex-start; /* Align to top instead of center */
    padding: 1rem 1.5rem;
    min-height: fit-content;
}

/* Text content containers */
.crusade-card .space-y-4,
.crusade-card .space-y-6,
.crusade-card .space-y-8 {
    height: auto;
    min-height: fit-content;
}
```

### 4. GSAP Animation System Adjustments

#### Seamless Loop Modifications
```javascript
// Enhanced animation function to handle dynamic heights
const animateFunc = element => {
    const tl = gsap.timeline();
    
    // Get dynamic height of element
    const elementHeight = element.scrollHeight;
    
    tl.fromTo(element, 
        {scale: 0, opacity: 0, height: 0}, 
        {
            scale: 1, 
            opacity: 1, 
            height: 'auto', // Use auto height instead of fixed
            zIndex: 100, 
            duration: 0.5, 
            yoyo: true, 
            repeat: 1, 
            ease: "power1.in", 
            immediateRender: false
        }
    )
    .fromTo(element, 
        {xPercent: 400}, 
        {xPercent: -400, duration: 1, ease: "none", immediateRender: false}, 0
    );
    return tl;
};
```

#### Container Height Management
```javascript
// Function to update carousel container height based on active card
function updateCarouselHeight() {
    const activeCard = document.querySelector('.crusade-card[data-index="' + currentCrusadeIndex + '"]');
    const carousel = document.getElementById('crusadesCarousel');
    
    if (activeCard && carousel) {
        const cardHeight = activeCard.scrollHeight;
        const minHeight = window.innerWidth <= 768 ? 400 : 500;
        const targetHeight = Math.max(cardHeight + 40, minHeight); // Add padding
        
        gsap.to(carousel, {
            height: targetHeight,
            duration: 0.3,
            ease: "power2.out"
        });
    }
}
```

## Data Models

### Responsive Breakpoint System

#### Breakpoint Configuration
```javascript
const responsiveConfig = {
    mobile: {
        maxWidth: 640,
        cardPadding: '0.5rem',
        minHeight: 350,
        imageAspectRatio: '16/9'
    },
    tablet: {
        minWidth: 641,
        maxWidth: 1024,
        cardPadding: '0.75rem',
        minHeight: 400,
        imageAspectRatio: '4/3'
    },
    desktop: {
        minWidth: 1025,
        cardPadding: '1rem 1.5rem',
        minHeight: 500,
        imageAspectRatio: 'auto'
    }
};
```

### Card Content Structure
```javascript
// Enhanced card data model
const cardContentModel = {
    image: {
        src: 'string',
        alt: 'string',
        aspectRatio: 'responsive' // Adapts to screen size
    },
    content: {
        title: 'string',
        description: 'string', // Can be long text
        details: {
            date: 'string',
            venue: 'string',
            address: 'string'
        },
        actions: {
            registerLink: 'string'
        }
    },
    layout: {
        height: 'auto', // Dynamic based on content
        minHeight: 'responsive' // Based on screen size
    }
};
```

## Error Handling

### Content Overflow Prevention
- Implement content length detection and automatic height adjustment
- Add fallback minimum heights for edge cases
- Handle image loading errors gracefully without breaking layout

### Animation Error Handling
```javascript
// Enhanced error handling for GSAP animations
function safeAnimateCard(element) {
    try {
        // Check if element exists and has content
        if (!element || element.scrollHeight === 0) {
            console.warn('Invalid card element for animation');
            return gsap.timeline();
        }
        
        return animateFunc(element);
    } catch (error) {
        console.error('Card animation error:', error);
        // Return basic timeline as fallback
        return gsap.timeline().set(element, {opacity: 1, scale: 1});
    }
}
```

## Testing Strategy

### Visual Regression Testing
1. **Cross-Device Testing**: Test on various screen sizes (320px to 2560px width)
2. **Content Variation Testing**: Test with short and long content
3. **Animation Testing**: Verify smooth transitions without cropping
4. **Performance Testing**: Ensure dynamic height changes don't impact performance

### Test Cases

#### 1. Content Display Tests
```javascript
describe('Crusade Card Display', () => {
    test('should display full content on mobile', () => {
        // Test mobile viewport (375px width)
        expect(getCardOverflow()).toBe(0);
    });
    
    test('should display full content on tablet', () => {
        // Test tablet viewport (768px width)
        expect(getCardOverflow()).toBe(0);
    });
    
    test('should display full content on desktop', () => {
        // Test desktop viewport (1200px width)
        expect(getCardOverflow()).toBe(0);
    });
});
```

#### 2. Responsive Behavior Tests
```javascript
describe('Responsive Layout', () => {
    test('should adjust height based on content', () => {
        const longContentCard = createCardWithLongContent();
        const shortContentCard = createCardWithShortContent();
        
        expect(longContentCard.height).toBeGreaterThan(shortContentCard.height);
    });
    
    test('should maintain minimum height requirements', () => {
        const emptyCard = createEmptyCard();
        expect(emptyCard.height).toBeGreaterThanOrEqual(getMinHeight());
    });
});
```

## Implementation Considerations

### Performance Optimization
- Use `transform3d` for hardware acceleration
- Implement lazy loading for card images
- Optimize GSAP animations for smooth performance
- Use `will-change` CSS property judiciously

### Browser Compatibility
- Ensure CSS Grid and Flexbox fallbacks
- Test on Safari, Chrome, Firefox, and Edge
- Verify mobile browser compatibility (iOS Safari, Chrome Mobile)

### Accessibility Improvements
- Maintain keyboard navigation functionality
- Ensure screen reader compatibility with dynamic content
- Preserve focus management during carousel transitions

## Security Considerations

### Content Security
- Sanitize dynamic content to prevent XSS
- Validate image URLs and sources
- Ensure safe handling of user-generated content in cards

### Performance Security
- Prevent excessive DOM manipulation
- Limit animation complexity to avoid performance attacks
- Implement reasonable content length limits