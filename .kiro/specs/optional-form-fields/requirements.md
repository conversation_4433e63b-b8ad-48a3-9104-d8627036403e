# Requirements Document

## Introduction

This specification addresses the need to make certain form fields optional in the crusade registration forms. Currently, the zone, church, and group fields are marked as required, but they should be optional to accommodate users who may not have this information or for whom these fields are not applicable.

## Requirements

### Requirement 1

**User Story:** As a user filling out the individual crusade registration form, I want the zone, church, and group fields to be optional, so that I can complete the form even if I don't have this information or it doesn't apply to me.

#### Acceptance Criteria

1. WHEN a user accesses the individual crusade registration form (host-crusade.php) THEN the zone field SHALL NOT be marked as required
2. WHEN a user accesses the individual crusade registration form (host-crusade.php) THEN the church field SHALL NOT be marked as required  
3. WHEN a user accesses the individual crusade registration form (host-crusade.php) THEN the group field SHALL NOT be marked as required
4. WHEN a user submits the individual crusade registration form with empty zone, church, or group fields THEN the form SHALL submit successfully without validation errors

### Requirement 2

**User Story:** As a user filling out the church crusade registration form, I want the zone, church, and group fields to be optional, so that I can complete the form even if I don't have this information or it doesn't apply to me.

#### Acceptance Criteria

1. WHEN a user accesses the church crusade registration form (host-crusade-church.php) THEN the zone field SHALL NOT be marked as required (if present)
2. WHEN a user accesses the church crusade registration form (host-crusade-church.php) THEN the church field SHALL NOT be marked as required
3. WHEN a user accesses the church crusade registration form (host-crusade-church.php) THEN the group field SHALL NOT be marked as required (if present)
4. WHEN a user submits the church crusade registration form with empty zone, church, or group fields THEN the form SHALL submit successfully without validation errors

### Requirement 3

**User Story:** As a mobile user filling out the church crusade registration form, I want the zone, church, and group fields to be optional, so that I can complete the form even if I don't have this information or it doesn't apply to me.

#### Acceptance Criteria

1. WHEN a user accesses the mobile church crusade registration form (host-crusade-church-mobile.php) THEN the zone field SHALL NOT be marked as required (if present)
2. WHEN a user accesses the mobile church crusade registration form (host-crusade-church-mobile.php) THEN the church field SHALL NOT be marked as required
3. WHEN a user accesses the mobile church crusade registration form (host-crusade-church-mobile.php) THEN the group field SHALL NOT be marked as required (if present)
4. WHEN a user submits the mobile church crusade registration form with empty zone, church, or group fields THEN the form SHALL submit successfully without validation errors

### Requirement 4

**User Story:** As a user, I want the form labels to clearly indicate which fields are optional, so that I understand which information I can skip if I don't have it.

#### Acceptance Criteria

1. WHEN a user views any registration form THEN optional fields SHALL NOT display an asterisk (*) indicating they are required
2. WHEN a user views any registration form THEN optional fields MAY display "(Optional)" in the label to clearly indicate they are not required
3. WHEN a user views any registration form THEN the visual distinction between required and optional fields SHALL be clear and consistent

### Requirement 5

**User Story:** As a developer, I want the form validation logic to be updated consistently across all forms, so that the user experience is uniform and the backend can handle optional fields properly.

#### Acceptance Criteria

1. WHEN the form validation runs on any registration form THEN it SHALL NOT require zone, church, or group fields to have values
2. WHEN the form is submitted to the backend THEN the backend SHALL accept empty values for zone, church, and group fields
3. WHEN the form validation JavaScript runs THEN it SHALL skip validation for zone, church, and group fields
4. WHEN a user moves between form steps THEN the validation SHALL NOT prevent progression due to empty zone, church, or group fields