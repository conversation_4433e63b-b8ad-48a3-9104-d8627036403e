# Design Document

## Overview

The sponsor page will be a standalone PHP page that replicates the give section functionality from the main index page. It will be located at `/sponsor/index.php` and provide a focused donation experience with full header and footer integration.

## Architecture

### File Structure
```
/sponsor/
  └── index.php (main sponsor page)
```

### Dependencies
- `includes/header.php` - Site header with navigation
- `includes/footer.php` - Site footer with contact information
- `includes/config.php` - Configuration settings
- `includes/languages.php` - Language support
- `includes/TranslationService.php` - Translation functionality
- `assets/js/donation.js` - Donation JavaScript functionality
- `process-donation.php` - Backend payment processing

## Components and Interfaces

### Page Structure
The sponsor page will follow this structure:
1. **Header Include** - Complete site navigation and language selector
2. **Main Content** - Give section with donation form
3. **Footer Include** - Contact information and additional links

### Give Section Components
1. **Title and Description** - Translated heading and subtitle
2. **Left Content Panel** - Description text and benefit list
3. **Right Payment Form** - Donation form with payment options
4. **Decorative Elements** - Floating boxes for visual appeal

### Payment Form Elements
1. **Payment Method Selection** - Card payment and ESPEES options
2. **Amount Selection** - Preset amounts ($100, $500, $1000)
3. **Custom Amount Input** - User-defined donation amount
4. **Donor Information** - Name and email fields
5. **Payment Processing** - Stripe integration for card payments
6. **ESPEES Display** - Payment code for ESPEES option

## Data Models

### Donation Data Structure
```php
$donationData = [
    'amount' => float,           // Donation amount
    'donor_name' => string,      // Donor's full name
    'donor_email' => string,     // Donor's email address
    'payment_method' => string   // 'card_payment' or 'espees'
];
```

### Translation Keys
The page will use existing translation keys from the main site:
- `give_title` - Main heading
- `give_subtitle` - Subtitle text
- `give_join_us` - Left panel heading
- `give_description` - Description text
- `give_sponsor_crusades` - Benefit list item
- `give_fund_outreach` - Benefit list item
- `give_enable_distribution` - Benefit list item
- `give_select_amount` - Form heading
- `give_custom_amount` - Custom amount label
- `give_enter_amount` - Amount placeholder
- `give_full_name` - Name field label
- `give_enter_full_name` - Name placeholder
- `give_email_address` - Email field label
- `give_enter_email` - Email placeholder
- `give_donate_now` - Submit button text
- `give_secure_payment` - Security notice

## Error Handling

### Client-Side Validation
1. **Amount Validation** - Ensure minimum $1 donation
2. **Name Validation** - Require non-empty donor name
3. **Email Validation** - Validate email format
4. **Payment Method** - Ensure payment method is selected

### Server-Side Processing
1. **Payment Processing Errors** - Handle Stripe API errors
2. **Network Errors** - Handle connection failures
3. **Validation Errors** - Server-side input validation
4. **ESPEES Handling** - Display payment code without processing

### Error Display
- Use SweetAlert for user-friendly error messages
- Display inline validation messages for form fields
- Provide clear instructions for error resolution

## Testing Strategy

### Unit Testing
1. **Form Validation** - Test all validation rules
2. **Amount Selection** - Test preset and custom amounts
3. **Payment Method Toggle** - Test switching between payment methods
4. **Translation Loading** - Test language switching

### Integration Testing
1. **Stripe Integration** - Test payment processing flow
2. **Header/Footer Integration** - Test include functionality
3. **Language Integration** - Test translation service
4. **Mobile Responsiveness** - Test across device sizes

### User Acceptance Testing
1. **Donation Flow** - Complete donation process testing
2. **Multi-language Support** - Test in different languages
3. **Cross-browser Compatibility** - Test in major browsers
4. **Accessibility** - Test with screen readers and keyboard navigation

## Visual Design

### Layout
- Two-column layout on desktop (content left, form right)
- Single-column layout on mobile (stacked vertically)
- Consistent spacing and typography with main site

### Decorative Elements
- Floating boxes with various colors and rotations
- Gradient dividers and borders
- Consistent color scheme (primary blue, accent yellow, gold)

### Responsive Breakpoints
- Mobile: < 768px (single column)
- Tablet: 768px - 1024px (adjusted spacing)
- Desktop: > 1024px (full two-column layout)

### Accessibility
- Proper heading hierarchy (h1, h2, h3)
- Alt text for decorative elements
- Focus indicators for interactive elements
- Screen reader friendly form labels