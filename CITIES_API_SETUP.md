# Cities API Setup Guide

The cities autocomplete feature uses the **GeoNames API** to provide real-time city suggestions with country identification. This guide will help you set it up.

## Quick Setup (5 minutes)

### Step 1: Get a Free GeoNames Account
1. Go to [GeoNames Registration](https://www.geonames.org/login)
2. Click **"create a new user account"**
3. Fill in the registration form with your details
4. **Important**: Verify your email address (check spam folder)
5. Log in to your new account

### Step 2: Enable Web Services
1. Go to [Manage Account](https://www.geonames.org/manageaccount)
2. Click on **"Click here to enable"** next to "Free Web Services"
3. Your account is now ready for API calls!

### Step 3: Configure Your Website
1. Open the file: `config/geonames.php`
2. Replace `'demo'` with your GeoNames username:
   ```php
   'username' => 'your_actual_username_here',
   ```
3. Save the file

### Step 4: Test the Setup
1. Go to your registration form
2. Start typing a city name in the "Target Cities" field
3. You should see real city suggestions appear!

---

## Features

✅ **Real-time city search** - Type any city name  
✅ **Global coverage** - Cities from every country  
✅ **Smart suggestions** - Sorted by population relevance  
✅ **Country auto-detection** - Cities automatically identify their countries  
✅ **Comma-separated input** - Type multiple cities like "Lagos, Accra, Nairobi"  
✅ **Keyboard navigation** - Use arrow keys and Enter to select  
✅ **Caching** - Fast responses with 24-hour cache  
✅ **Fallback data** - Works even if API is temporarily down  

---

## Alternative Configuration Methods

### Method 1: Environment Variable
Add to your server environment or `.htaccess`:
```bash
GEONAMES_USERNAME=your_username_here
```

### Method 2: Direct Code Edit
Edit `api/cities-search.php` and change:
```php
$geonamesUsername = 'your_username_here';
```

---

## Troubleshooting

### "Using limited fallback data" message
- **Cause**: API not configured or GeoNames service unavailable
- **Solution**: Follow steps above to set up your GeoNames username

### Cities not appearing
- **Check**: Is your GeoNames account email verified?
- **Check**: Are web services enabled in your GeoNames account?
- **Check**: Is your username correct in the config file?

### API errors in browser console
- **Check**: Network connectivity
- **Check**: GeoNames service status at [geonames.org](https://www.geonames.org)
- **View logs**: Check your server error logs for detailed messages

---

## API Response Format

The API returns structured data for each city:
```json
{
  "cities": [
    {
      "name": "Lagos",
      "country": "Nigeria", 
      "countryCode": "NG",
      "admin1": "Lagos State",
      "population": ********,
      "latitude": 6.45407,
      "longitude": 3.39467,
      "timezone": "Africa/Lagos"
    }
  ],
  "source": "geonames",
  "total_results": 8
}
```

---

## Cost & Limits

- **GeoNames Free Account**: 1,000 requests per hour
- **GeoNames Premium**: Higher limits available
- **Our Caching**: Reduces API calls significantly
- **Cost**: Completely free for normal usage

---

## Privacy & Data

- No personal data is sent to GeoNames
- Only city names are searched
- Results are cached locally for 24 hours
- No tracking or analytics on city searches

---

Need help? Check the [GeoNames Documentation](http://www.geonames.org/export/web-services.html) or contact your developer. 