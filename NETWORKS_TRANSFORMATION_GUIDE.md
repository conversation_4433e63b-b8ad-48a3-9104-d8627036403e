# Networks Data Transformation Guide

This guide explains how to transform your networks data to match the NOTC data.json structure.

## Files Created

1. **transform_networks.php** - Main transformation script
2. **merge_networks.php** - Script to merge networks data with existing NOTC data
3. **sample_networks.tsv** - Sample TSV file with correct format
4. **networks_data.json** - Output file with transformed networks data
5. **merged_data.json** - Final merged data file

## Step-by-Step Process

### Step 1: Prepare Your Networks Data

Your networks data should be in TSV (Tab-Separated Values) format with these columns:

```
Registration Type	Name	Organization/Network Name	Designation	Email	Phone	Zone	Group	Church	Church Type	Ministry Name	Crusade Title	Country	City	Crusade Type	Number of Crusades	Venue	Expected Attendance	Comments	Additional Comments
```

### Step 2: Transform Networks Data

Run the transformation script:

```bash
php transform_networks.php your_networks_file.tsv networks_data.json
```

This will:
- Parse the TSV file
- Convert each network registration to NOTC format
- Generate unique IDs
- Map crusade types to standard format
- Create proper country/city data structures
- Add all required metadata fields

### Step 3: Merge with Existing NOTC Data

```bash
php merge_networks.php notc/data.json networks_data.json final_data.json
```

This will:
- Combine church and network registrations
- Update total counts
- Maintain the same data structure as NOTC

### Step 4: Replace Your NOTC Data

```bash
cp final_data.json notc/data.json
```

## Data Structure Mapping

The transformation maps your networks data as follows:

| Source Field | Target Field | Notes |
|--------------|--------------|-------|
| Name | first_name, last_name, full_name | Split by space |
| Organization/Network Name | network_name | Used as primary identifier |
| Email | email | Direct mapping |
| Phone | phone, full_phone | Direct mapping |
| Country | selected_countries | Converted to lowercase with dashes |
| City | selected_cities_data | Creates proper city object |
| Crusade Type | crusade_types | Mapped to standard types |
| Number of Crusades | number_of_crusades | Direct mapping |
| Expected Attendance | expected_attendance | Direct mapping |
| Comments | additional_comments | Combined with additional comments |

## Crusade Type Mapping

- `OPEN` → `street`
- `MEGA` → `mega`
- `ONLINE` → `online`
- `CITY CRUSADE` → `mega`
- Others → `other`

## Network Type Detection

The script automatically detects network types based on organization name:
- Contains "RIM" → `RIM`
- Contains "REON" → `REON`
- Contains "TNI" → `TNI`
- Contains "ministry" → `ministry`
- Contains "church" → `church`
- Default → `other`

## Generated Fields

The script automatically generates:
- Unique IDs (format: `net_[uniqid]`)
- Registration timestamps
- Formatted dates
- Display names
- Country codes
- Phone display formats
- Server info
- Status (set to "approved")

## Example Usage

```bash
# Transform your networks data
php transform_networks.php my_networks.tsv networks_output.json

# Merge with existing NOTC data
php merge_networks.php notc/data.json networks_output.json final_output.json

# Replace the original NOTC data
cp final_output.json notc/data.json
```

## Troubleshooting

1. **Empty output**: Check that your file is in TSV format with tab separators
2. **Missing data**: Ensure all required columns are present
3. **Encoding issues**: Make sure your file is UTF-8 encoded
4. **Permission errors**: Check file permissions for read/write access

## Data Validation

After transformation, verify:
- Total registration counts are correct
- All network entries have proper structure
- Country codes are valid
- Crusade types are mapped correctly
- No duplicate IDs exist

The transformed data will be fully compatible with your existing NOTC system and dashboard.