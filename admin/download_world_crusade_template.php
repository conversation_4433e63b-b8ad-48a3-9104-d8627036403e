<?php
// World Crusade Request CSV Template Download
session_start();

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit();
}

// Set headers for CSV download
header('Content-Type: text/csv');
header('Content-Disposition: attachment; filename="World_Crusade_Request_Template_' . date('Y-m-d') . '.csv"');

$output = fopen('php://output', 'w');

// Add BOM for UTF-8 encoding (helps with Excel compatibility)
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// Header Section
fputcsv($output, ['WORLD CRUSADE REQUEST TEMPLATE']);
fputcsv($output, ['Generated on: ' . date('F j, Y')]);
fputcsv($output, ['']);

// Instructions Section
fputcsv($output, ['=== INSTRUCTIONS ===']);
fputcsv($output, ['1. Fill out this template completely and accurately']);
fputcsv($output, ['2. Choose ONE registration type below']);
fputcsv($output, ['3. Fill only the sections relevant to your registration type']);
fputcsv($output, ['4. Do NOT modify column headers or system fields']);
fputcsv($output, ['5. Save as CSV format when complete']);
fputcsv($output, ['6. Submit the completed file to the administration']);
fputcsv($output, ['']);

// Registration Type Selection
fputcsv($output, ['=== REGISTRATION TYPE SELECTION ===']);
fputcsv($output, ['Choose ONE by entering "X" in the appropriate cell:']);
fputcsv($output, ['Individual Registration', 'Church/Group/Zone Registration', 'Organisation/Network Registration']);
fputcsv($output, ['[Enter X here]', '[Enter X here]', '[Enter X here]']);
fputcsv($output, ['']);

// Field Requirements by Type
fputcsv($output, ['=== FIELD REQUIREMENTS BY REGISTRATION TYPE ===']);
fputcsv($output, ['']);
fputcsv($output, ['INDIVIDUAL REGISTRATION:']);
fputcsv($output, ['Required: Personal Info + Crusade Details']);
fputcsv($output, ['Optional: Church/Group Information']);
fputcsv($output, ['']);
fputcsv($output, ['CHURCH/GROUP/ZONE REGISTRATION:']);
fputcsv($output, ['Required: Personal Info + Church Info + Contact Info + Crusade Details']);
fputcsv($output, ['']);
fputcsv($output, ['ORGANISATION/NETWORK REGISTRATION:']);
fputcsv($output, ['Required: Personal Info + Organisation Info + Contact Info + Crusade Details']);
fputcsv($output, ['']);

// Valid Values Section
fputcsv($output, ['=== VALID VALUES FOR DROPDOWN FIELDS ===']);
fputcsv($output, ['Designation: Pastor, Reverend, Bishop, Evangelist, Minister, Brother, Sister, Dr, Prof, Mr, Mrs, Miss']);
fputcsv($output, ['Crusade Type: evangelistic, healing, youth, conference, other']);
fputcsv($output, ['Church Type: zone, group, church, other']);
fputcsv($output, ['']);

// Data Entry Section Header
fputcsv($output, ['=== DATA ENTRY SECTION ===']);
fputcsv($output, ['Fill the row below with your information. Delete example rows after reviewing.']);
fputcsv($output, ['']);

// Column Headers
fputcsv($output, [
    // Section 1: Personal Information
    'First Name*',
    'Last Name*',
    'Designation',
    'Email*',
    'Phone*',
    'Address',
    
    // Section 2: Church/Group Information (for Church registration)
    'Zone',
    'Group Name',
    'Church Name',
    'Church Type',
    'Ministry Name',
    
    // Section 3: Organisation Information (for Organisation registration)
    'Organisation Name',
    'Organisation Email',
    'Organisation Phone',
    'Organisation Address',
    
    // Section 4: Contact Information (for Church/Organisation)
    'Contact Email',
    'Contact Phone',
    'KingsChat Username',
    'Contact Address',
    
    // Section 5: Crusade Details (Required for all)
    'Crusade Title*',
    'Crusade Type*',
    'Country*',
    'City/Location*',
    'Venue*',
    'Expected Attendance*',
    
    // Section 6: Additional Information
    'Comments',
    'Additional Comments',
    
    // Section 7: System Fields (DO NOT EDIT)
    'Registration Type',
    'Status',
    'Admin Notes',
    'Created Date',
    'ID'
]);

// Example Data Section
fputcsv($output, ['']);
fputcsv($output, ['=== EXAMPLE DATA (DELETE BEFORE SUBMITTING) ===']);

// Individual Example
fputcsv($output, [
    'John', 'Doe', 'Pastor', '<EMAIL>', '******-567-8900', '123 Main Street, Anytown, USA',
    'Zone 1', 'Group Alpha', 'Grace Community Church', '', '',
    '', '', '', '',
    '', '', '@johndoe', '',
    'City-Wide Healing Crusade', 'healing', 'United States', 'New York', 'Madison Square Garden', '10000',
    'Expecting great miracles', 'Need wheelchair accessibility',
    'individual', 'pending', '', '', ''
]);

// Church Example
fputcsv($output, [
    'Jane', 'Smith', 'Reverend', '<EMAIL>', '+1-987-654-3210', '456 Church Avenue, Faith City, USA',
    'Zone 2', 'Group Beta', 'Faith Community Church', 'church', '',
    '', '', '', '',
    '<EMAIL>', '+1-555-123-4567', '@janesmith', '456 Church Avenue, Faith City, USA',
    'Youth Revival Conference', 'youth', 'Canada', 'Toronto', 'Metro Convention Center', '5000',
    'Annual youth gathering', 'Require audio/visual equipment',
    'church', 'pending', '', '', ''
]);

// Organisation Example
fputcsv($output, [
    'Michael', 'Johnson', 'Director', '<EMAIL>', '+44-20-1234-5678', '789 Ministry Boulevard, London, UK',
    '', '', '', '', '',
    'Global Ministry Network', '<EMAIL>', '+44-20-1234-5678', '789 Ministry Boulevard, London, UK',
    '<EMAIL>', '+44-20-1234-5678', '@globalministry', '789 Ministry Boulevard, London, UK',
    'International Evangelistic Conference', 'evangelistic', 'United Kingdom', 'London', 'ExCeL London', '15000',
    'Multi-day international event', 'Translation services required',
    'organisation', 'pending', '', '', ''
]);

// Data Entry Row
fputcsv($output, ['']);
fputcsv($output, ['=== YOUR DATA ENTRY ROW ===']);
fputcsv($output, ['Fill the row below with your information:']);

// Empty row for user data
fputcsv($output, [
    '', '', '', '', '', '',  // Personal Info
    '', '', '', '', '',      // Church Info
    '', '', '', '',          // Organisation Info
    '', '', '', '',          // Contact Info
    '', '', '', '', '', '',  // Crusade Details
    '', '',                  // Additional Info
    '', 'pending', '', '', '' // System Fields
]);

// Footer Instructions
fputcsv($output, ['']);
fputcsv($output, ['=== SUBMISSION CHECKLIST ===']);
fputcsv($output, ['□ Registration type selected']);
fputcsv($output, ['□ All required fields completed']);
fputcsv($output, ['□ Example rows deleted']);
fputcsv($output, ['□ File saved as CSV format']);
fputcsv($output, ['□ Ready for submission']);
fputcsv($output, ['']);
fputcsv($output, ['For questions or support, contact the administration team.']);

fclose($output);
exit();
?>