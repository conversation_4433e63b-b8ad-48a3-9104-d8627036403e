<?php
// Start session for admin authentication
session_start();

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit();
}

// Get crusade ID from URL
$crusadeId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Define the path to the JSON file
$jsonFile = __DIR__ . '/../data/crusades.json';

// Function to read crusades from JSON file
function getCrusades($file) {
    if (!file_exists($file)) {
        error_log("Crusades file not found: " . $file);
        return ['crusades' => []];
    }
    $json = file_get_contents($file);
    if ($json === false) {
        error_log("Failed to read crusades file: " . $file);
        return ['crusades' => []];
    }
    $data = json_decode($json, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("JSON decode error: " . json_last_error_msg());
        return ['crusades' => []];
    }
    return $data;
}

// Function to save crusades to JSON file
function saveCrusades($file, $data) {
    $json = json_encode($data, JSON_PRETTY_PRINT);
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("JSON encode error: " . json_last_error_msg());
        return false;
    }
    if (file_put_contents($file, $json) === false) {
        error_log("Failed to write to crusades file: " . $file);
        return false;
    }
    return true;
}

// Get current crusades
$crusades = getCrusades($jsonFile);

// Find the crusade to edit
$crusadeToEdit = null;
foreach ($crusades['crusades'] as $crusade) {
    if ($crusade['id'] === $crusadeId) {
        $crusadeToEdit = $crusade;
        break;
    }
}

// If crusade not found, redirect to crusades page
if (!$crusadeToEdit) {
    header('Location: crusades.php');
    exit();
}

// Function to generate a slug from title
function generateSlug($title) {
    // Convert to lowercase
    $slug = strtolower($title);
    // Replace spaces and special characters with hyphens
    $slug = preg_replace('/[^a-z0-9]+/', '-', $slug);
    // Remove leading/trailing hyphens
    $slug = trim($slug, '-');
    // Limit length to 50 characters
    $slug = substr($slug, 0, 50);
    // Remove trailing hyphen if created by substr
    $slug = rtrim($slug, '-');

    return $slug;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Handle file upload if a new image is provided
    $imagePath = $crusadeToEdit['image']; // Keep existing image by default

    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
        $uploadDir = __DIR__ . '/../assets/images/';
        $fileName = uniqid() . '_' . basename($_FILES['image']['name']);
        $targetPath = $uploadDir . $fileName;

        if (move_uploaded_file($_FILES['image']['tmp_name'], $targetPath)) {
            $imagePath = 'assets/images/' . $fileName;
        }
    }

    // Handle register link generation
    $registerLink = '';
    if (!empty($_POST['register_link'])) {
        // Use custom register link provided by user
        $registerLink = $_POST['register_link'];
    } else {
        // Generate register link from title slug
        $slug = generateSlug($_POST['title']);
        if (!empty($slug)) {
            $registerLink = 'register.php?event=' . urlencode($slug);
        }
    }

    // Update crusade data
    $updatedCrusade = [
        'id' => $crusadeId,
        'title' => $_POST['title'],
        'date' => $_POST['date'],
        'time' => $_POST['time'],
        'venue' => $_POST['venue'],
        'address' => $_POST['address'],
        'image' => $imagePath,
        'description' => $_POST['description'],
        'register_link' => $registerLink
    ];

    // Find and update the crusade in the array
    foreach ($crusades['crusades'] as $key => $crusade) {
        if ($crusade['id'] === $crusadeId) {
            $crusades['crusades'][$key] = $updatedCrusade;
            break;
        }
    }

    // Save updated data
    if (saveCrusades($jsonFile, $crusades)) {
        header('Location: crusades.php?message=Crusade+updated+successfully&type=success');
        exit();
    }
}

require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/languages.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Crusade - Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#dc2626',
                        'primary-dark': '#b91c1c',
                    },
                },
            },
        }
    </script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8 flex justify-between items-center">
                <h1 class="text-2xl font-bold text-gray-900">Edit Crusade</h1>
                <a href="crusades.php" class="text-primary hover:text-primary-dark">Back to Crusades</a>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <form action="" method="POST" enctype="multipart/form-data" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="title" class="block text-sm font-medium text-gray-700">Title</label>
                            <input type="text" id="title" name="title" required 
                                   value="<?php echo htmlspecialchars($crusadeToEdit['title']); ?>"
                                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                        </div>

                        <div>
                            <label for="date" class="block text-sm font-medium text-gray-700">Date</label>
                            <input type="date" id="date" name="date" required 
                                   value="<?php echo htmlspecialchars($crusadeToEdit['date']); ?>"
                                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                        </div>

                        <div>
                            <label for="time" class="block text-sm font-medium text-gray-700">Time</label>
                            <input type="time" id="time" name="time" required 
                                   value="<?php echo htmlspecialchars($crusadeToEdit['time']); ?>"
                                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                        </div>

                        <div>
                            <label for="venue" class="block text-sm font-medium text-gray-700">Venue</label>
                            <input type="text" id="venue" name="venue" required 
                                   value="<?php echo htmlspecialchars($crusadeToEdit['venue']); ?>"
                                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                        </div>

                        <div class="md:col-span-2">
                            <label for="address" class="block text-sm font-medium text-gray-700">Address</label>
                            <input type="text" id="address" name="address" required 
                                   value="<?php echo htmlspecialchars($crusadeToEdit['address']); ?>"
                                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                        </div>

                        <div class="md:col-span-2">
                            <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                            <textarea id="description" name="description" rows="3" required
                                      class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary"><?php echo htmlspecialchars($crusadeToEdit['description']); ?></textarea>
                        </div>

                        <div>
                            <label for="register_link" class="block text-sm font-medium text-gray-700">Register Link</label>
                            <input type="text" id="register_link" name="register_link"
                                   value="<?php echo htmlspecialchars($crusadeToEdit['register_link'] ?? ''); ?>"
                                   class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary"
                                   placeholder="Leave blank to auto-generate from title">
                            <p class="mt-1 text-xs text-gray-500">Custom registration link (optional). If left blank, will auto-generate: register.php?event=title-slug</p>
                        </div>

                        <div class="md:col-span-2">
                            <label for="image" class="block text-sm font-medium text-gray-700">Event Image</label>
                            <input type="file" id="image" name="image" accept="image/*"
                                   class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-white hover:file:bg-primary-dark">
                            <?php if ($crusadeToEdit['image']): ?>
                                <div class="mt-2">
                                    <p class="text-sm text-gray-500">Current image:</p>
                                    <img src="../<?php echo htmlspecialchars($crusadeToEdit['image']); ?>" alt="Current event image" class="mt-1 h-32 w-auto">
                                </div>
                            <?php endif; ?>
                            <input type="hidden" name="current_image" value="<?php echo htmlspecialchars($crusadeToEdit['image']); ?>">
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3">
                        <a href="crusades.php" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                            Cancel
                        </a>
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                            Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </main>
    </div>
</body>
</html>
