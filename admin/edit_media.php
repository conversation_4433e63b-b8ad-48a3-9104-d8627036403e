<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session for admin authentication
session_start();

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit();
}

// Get media ID from URL
$mediaId = $_GET['id'] ?? '';
if (empty($mediaId)) {
    header('Location: crusades.php?tab=media&message=Media not found&type=error');
    exit();
}

// Define file paths
$mediaJsonFile = __DIR__ . '/../data/media.json';
$categoriesJsonFile = __DIR__ . '/../data/categories.json';

// Function to read media from JSON file
function getMedia($file) {
    if (!file_exists($file)) {
        return ['media' => []];
    }
    $json = file_get_contents($file);
    return json_decode($json, true) ?: ['media' => []];
}

// Function to read categories from JSON file
function getCategories($file) {
    if (!file_exists($file)) {
        return ['categories' => []];
    }
    $json = file_get_contents($file);
    return json_decode($json, true) ?: ['categories' => []];
}

// Function to save media to JSON file
function saveMedia($file, $data) {
    $json = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
    return file_put_contents($file, $json) !== false;
}

// Load data
$mediaData = getMedia($mediaJsonFile);
$categoriesData = getCategories($categoriesJsonFile);

// Find the media item to edit
$mediaItem = null;
$mediaIndex = null;
foreach ($mediaData['media'] as $index => $media) {
    if ($media['id'] == $mediaId) {
        $mediaItem = $media;
        $mediaIndex = $index;
        break;
    }
}

if (!$mediaItem) {
    header('Location: crusades.php?tab=media&message=Media not found&type=error');
    exit();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Validate required fields
        $title = trim($_POST['title'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $video_url = trim($_POST['video_url'] ?? '');
        $thumbnail = trim($_POST['thumbnail'] ?? '');
        $date_added = $_POST['date_added'] ?? '';
        $category = $_POST['category'] ?? '';
        $featured = isset($_POST['featured']);

        if (empty($title) || empty($description) || empty($video_url) || empty($date_added) || empty($category)) {
            throw new Exception('Please fill in all required fields.');
        }

        // Update media item
        $mediaData['media'][$mediaIndex] = [
            'id' => $mediaId,
            'title' => $title,
            'description' => $description,
            'video_url' => $video_url,
            'thumbnail' => $thumbnail,
            'date_added' => $date_added,
            'category' => $category,
            'featured' => $featured
        ];

        // Save updated data
        if (saveMedia($mediaJsonFile, $mediaData)) {
            header('Location: crusades.php?tab=media&message=Media updated successfully&type=success');
            exit();
        } else {
            throw new Exception('Failed to save media. Please check file permissions.');
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Media - Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#dc2626', // Red-600
                        'primary-dark': '#b91c1c', // Red-700
                    },
                },
            },
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-white shadow">
            <div class="max-w-4xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center">
                    <h1 class="text-2xl font-bold text-gray-900">Edit Media</h1>
                    <a href="crusades.php?tab=media" class="text-primary hover:text-primary-dark">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Media
                    </a>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-4xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
            <?php if (isset($error)): ?>
                <div class="mb-6 p-4 bg-red-100 text-red-800">
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- Edit Media Form -->
            <div class="bg-white shadow p-6">
                <h2 class="text-xl font-semibold mb-6">Edit Media Details</h2>
                
                <form method="POST" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="title" class="block text-sm font-medium text-gray-700">Title *</label>
                            <input type="text" id="title" name="title" required 
                                   value="<?php echo htmlspecialchars($mediaItem['title']); ?>"
                                   class="mt-1 block w-full border border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                        </div>
                        
                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700">Category *</label>
                            <select id="category" name="category" required 
                                    class="mt-1 block w-full border border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                                <option value="">Select Category</option>
                                <?php foreach ($categoriesData['categories'] as $category): ?>
                                    <option value="<?php echo htmlspecialchars($category['name']); ?>" 
                                            <?php echo $mediaItem['category'] === $category['name'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($category['display_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="md:col-span-2">
                            <label for="description" class="block text-sm font-medium text-gray-700">Description *</label>
                            <textarea id="description" name="description" rows="3" required
                                     class="mt-1 block w-full border border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary"><?php echo htmlspecialchars($mediaItem['description']); ?></textarea>
                        </div>
                        
                        <div>
                            <label for="video_url" class="block text-sm font-medium text-gray-700">Video URL *</label>
                            <input type="url" id="video_url" name="video_url" required 
                                   value="<?php echo htmlspecialchars($mediaItem['video_url']); ?>"
                                   class="mt-1 block w-full border border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary"
                                   placeholder="https://www.youtube.com/embed/...">
                            <p class="mt-1 text-xs text-gray-500">Use YouTube embed URL format</p>
                        </div>
                        
                        <div>
                            <label for="thumbnail" class="block text-sm font-medium text-gray-700">Thumbnail URL</label>
                            <input type="url" id="thumbnail" name="thumbnail" 
                                   value="<?php echo htmlspecialchars($mediaItem['thumbnail'] ?? ''); ?>"
                                   class="mt-1 block w-full border border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary"
                                   placeholder="https://img.youtube.com/vi/VIDEO_ID/maxresdefault.jpg">
                            <p class="mt-1 text-xs text-gray-500">YouTube thumbnail URL (optional)</p>
                        </div>
                        
                        <div>
                            <label for="date_added" class="block text-sm font-medium text-gray-700">Date Added *</label>
                            <input type="date" id="date_added" name="date_added" required 
                                   value="<?php echo htmlspecialchars($mediaItem['date_added']); ?>"
                                   class="mt-1 block w-full border border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" id="featured" name="featured" value="1" 
                                   <?php echo !empty($mediaItem['featured']) ? 'checked' : ''; ?>
                                   class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="featured" class="ml-2 block text-sm text-gray-900">
                                Featured Video
                            </label>
                        </div>
                    </div>
                    
                    <!-- Video Preview -->
                    <?php if (!empty($mediaItem['video_url'])): ?>
                    <div class="border-t pt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Current Video Preview</h3>
                        <div class="aspect-w-16 aspect-h-9 max-w-2xl">
                            <iframe src="<?php echo htmlspecialchars($mediaItem['video_url']); ?>" 
                                    frameborder="0" 
                                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                                    allowfullscreen
                                    class="w-full h-64 border border-gray-300"></iframe>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <div class="flex justify-between pt-6 border-t">
                        <a href="crusades.php?tab=media" 
                           class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                            <i class="fas fa-times mr-2"></i>
                            Cancel
                        </a>
                        
                        <button type="submit" 
                                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                            <i class="fas fa-save mr-2"></i>
                            Update Media
                        </button>
                    </div>
                </form>
            </div>
        </main>
    </div>
</body>
</html>