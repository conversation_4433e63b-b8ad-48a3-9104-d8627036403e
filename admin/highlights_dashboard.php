<?php
// Define the path to the highlights JSON file
$highlightsJsonFile = __DIR__ . '/../data/highlights.json';

// Function to read highlights from JSON file
function getHighlights($file) {
    if (!file_exists($file)) {
        return ['highlights' => []];
    }
    $json = file_get_contents($file);
    return json_decode($json, true);
}

// Get current highlights
$highlights = getHighlights($highlightsJsonFile);

// Check if we're editing a highlight
$editingHighlight = null;
if (isset($_GET['edit']) && !empty($_GET['edit'])) {
    $editId = (int)$_GET['edit'];
    foreach ($highlights['highlights'] as $highlight) {
        if ((int)$highlight['id'] === $editId) {
            $editingHighlight = $highlight;
            break;
        }
    }
}
?>

<div class="space-y-8">
    <!-- Add New Highlight Form -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4">
            <?php echo $editingHighlight ? 'Edit R.E.C.C.O.R.D.s Highlight' : 'Add New R.E.C.C.O.R.D.s Highlight'; ?>
        </h2>
        
        <?php if ($editingHighlight): ?>
            <div class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                <p class="text-blue-800 text-sm">
                    You are editing: <strong><?php echo htmlspecialchars($editingHighlight['title']); ?></strong>
                    <a href="crusades.php?tab=highlights" class="ml-2 text-blue-600 hover:text-blue-700 underline">Cancel Edit</a>
                </p>
            </div>
        <?php endif; ?>
        
        <form action="crusades.php?tab=highlights" method="POST" class="space-y-4">
            <input type="hidden" name="action" value="<?php echo $editingHighlight ? 'update_highlight' : 'add_highlight'; ?>">
            <?php if ($editingHighlight): ?>
                <input type="hidden" name="highlight_id" value="<?php echo $editingHighlight['id']; ?>">
            <?php endif; ?>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700">Title (English)</label>
                    <input type="text" id="title" name="title" required 
                           value="<?php echo htmlspecialchars($editingHighlight['title'] ?? ''); ?>"
                           class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-red-500 focus:border-red-500">
                </div>
                
                <div>
                    <label for="category" class="block text-sm font-medium text-gray-700">Category</label>
                    <input type="text" id="category" name="category" required 
                           value="<?php echo htmlspecialchars($editingHighlight['category'] ?? ''); ?>"
                           class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-red-500 focus:border-red-500"
                           placeholder="e.g., Event Report, Youth Conference, Distribution">
                </div>
                
                <div>
                    <label for="badge" class="block text-sm font-medium text-gray-700">Badge Text</label>
                    <select id="badge" name="badge" required class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-red-500 focus:border-red-500">
                        <option value="BREAKING" <?php echo isset($editingHighlight['badge']) && $editingHighlight['badge'] === 'BREAKING' ? 'selected' : ''; ?>>BREAKING</option>
                        <option value="LIVE" <?php echo isset($editingHighlight['badge']) && $editingHighlight['badge'] === 'LIVE' ? 'selected' : ''; ?>>LIVE</option>
                        <option value="FEATURED" <?php echo isset($editingHighlight['badge']) && $editingHighlight['badge'] === 'FEATURED' ? 'selected' : ''; ?>>FEATURED</option>
                        <option value="URGENT" <?php echo isset($editingHighlight['badge']) && $editingHighlight['badge'] === 'URGENT' ? 'selected' : ''; ?>>URGENT</option>
                        <option value="NEW" <?php echo isset($editingHighlight['badge']) && $editingHighlight['badge'] === 'NEW' ? 'selected' : ''; ?>>NEW</option>
                    </select>
                </div>
                
                <div>
                    <label for="badge_color" class="block text-sm font-medium text-gray-700">Badge Color</label>
                    <select id="badge_color" name="badge_color" required class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-red-500 focus:border-red-500">
                        <option value="red" <?php echo isset($editingHighlight['badge_color']) && $editingHighlight['badge_color'] === 'red' ? 'selected' : ''; ?>>Red</option>
                        <option value="green" <?php echo isset($editingHighlight['badge_color']) && $editingHighlight['badge_color'] === 'green' ? 'selected' : ''; ?>>Green</option>
                        <option value="blue" <?php echo isset($editingHighlight['badge_color']) && $editingHighlight['badge_color'] === 'blue' ? 'selected' : ''; ?>>Blue</option>
                        <option value="yellow" <?php echo isset($editingHighlight['badge_color']) && $editingHighlight['badge_color'] === 'yellow' ? 'selected' : ''; ?>>Yellow</option>
                        <option value="purple" <?php echo isset($editingHighlight['badge_color']) && $editingHighlight['badge_color'] === 'purple' ? 'selected' : ''; ?>>Purple</option>
                    </select>
                </div>
                
                <div>
                    <label for="date" class="block text-sm font-medium text-gray-700">Date</label>
                    <input type="date" id="date" name="date" required 
                           value="<?php echo htmlspecialchars($editingHighlight['date'] ?? ''); ?>"
                           class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-red-500 focus:border-red-500">
                </div>
                
                
                <div>
                    <label for="location" class="block text-sm font-medium text-gray-700">Location</label>
                    <input type="text" id="location" name="location" required 
                           value="<?php echo htmlspecialchars($editingHighlight['location'] ?? ''); ?>"
                           class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-red-500 focus:border-red-500"
                           placeholder="e.g., Lagos, Nigeria">
                </div>
                
                <div>
                    <label for="images" class="block text-sm font-medium text-gray-700">Image URLs/Paths (one per line)</label>
                    <textarea id="images" name="images" rows="3" required 
                              class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-red-500 focus:border-red-500"
                              placeholder="https://example.com/image1.jpg&#10;assets/images/highlight2.jpg&#10;https://cdn.example.com/image3.png"><?php 
                              if (isset($editingHighlight['images']) && is_array($editingHighlight['images'])) {
                                  echo htmlspecialchars(implode("\n", $editingHighlight['images']));
                              }
                              ?></textarea>
                    <p class="mt-1 text-sm text-gray-500">Enter public URLs (https://...) or local file paths (assets/images/...), one per line</p>
                </div>
                
                <div>
                    <label for="videos" class="block text-sm font-medium text-gray-700">Video URLs/Paths (one per line, optional)</label>
                    <textarea id="videos" name="videos" rows="2" 
                              class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-red-500 focus:border-red-500"
                              placeholder="https://example.com/video1.mp4&#10;assets/videos/highlight2.mp4"><?php 
                              if (isset($editingHighlight['videos']) && is_array($editingHighlight['videos'])) {
                                  echo htmlspecialchars(implode("\n", $editingHighlight['videos']));
                              }
                              ?></textarea>
                    <p class="mt-1 text-sm text-gray-500">Enter public URLs (https://...) or local file paths (assets/videos/...), one per line</p>
                </div>
                
            </div>
            
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700">Description (English)</label>
                <textarea id="description" name="description" rows="3" required 
                          class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-red-500 focus:border-red-500"><?php echo htmlspecialchars($editingHighlight['description'] ?? ''); ?></textarea>
            </div>
            
            <!-- Auto Translation Notice -->
            <div class="border-t pt-4">
                <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-blue-800">Automatic Translation</h3>
                            <div class="mt-2 text-sm text-blue-700">
                                <p>Translations will be automatically generated using API translation services for all supported languages when this highlight is displayed on the frontend.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="pt-4">
                <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
                    <?php echo $editingHighlight ? 'Update Highlight' : 'Add Highlight'; ?>
                </button>
                <?php if ($editingHighlight): ?>
                    <a href="crusades.php?tab=highlights" 
                       class="ml-3 bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 inline-block">
                        Cancel
                    </a>
                <?php endif; ?>
            </div>
        </form>
    </div>

    <!-- Existing Highlights -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4">Existing R.E.C.C.O.R.D.s Highlights</h2>
        
        <?php if (!empty($highlights['highlights'])): ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Badge</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Media</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($highlights['highlights'] as $highlight): ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($highlight['title']); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo htmlspecialchars(substr($highlight['description'], 0, 100)) . '...'; ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                        <?php echo htmlspecialchars($highlight['category']); ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                        <?php 
                                        switch($highlight['badge_color']) {
                                            case 'red': echo 'bg-red-100 text-red-800'; break;
                                            case 'green': echo 'bg-green-100 text-green-800'; break;
                                            case 'blue': echo 'bg-blue-100 text-blue-800'; break;
                                            case 'yellow': echo 'bg-yellow-100 text-yellow-800'; break;
                                            case 'purple': echo 'bg-purple-100 text-purple-800'; break;
                                            default: echo 'bg-gray-100 text-gray-800';
                                        } 
                                        ?>">
                                        <?php echo htmlspecialchars($highlight['badge']); ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo date('M j, Y', strtotime($highlight['date'])); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <div class="text-xs">
                                        <?php if (!empty($highlight['images'])): ?>
                                            <div>📷 <?php echo count($highlight['images']); ?> image(s)</div>
                                        <?php endif; ?>
                                        <?php if (!empty($highlight['videos'])): ?>
                                            <div>🎥 <?php echo count($highlight['videos']); ?> video(s)</div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <a href="crusades.php?tab=highlights&edit=<?php echo $highlight['id']; ?>" 
                                           class="text-blue-600 hover:text-blue-900">Edit</a>
                                        <form action="crusades.php?tab=highlights" method="POST" class="inline" 
                                              onsubmit="return confirm('Are you sure you want to delete this highlight?')">
                                            <input type="hidden" name="action" value="delete_highlight">
                                            <input type="hidden" name="highlight_id" value="<?php echo $highlight['id']; ?>">
                                            <button type="submit" class="text-red-600 hover:text-red-900">Delete</button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <p class="text-gray-500">No highlights found. Add your first R.E.C.C.O.R.D.s highlight above.</p>
        <?php endif; ?>
    </div>
</div>