<?php
// Function to read mission trip enrollments from JSON file
function getMissionTripEnrollments($file) {
    if (!file_exists($file)) {
        return [];
    }
    $json = file_get_contents($file);
    return json_decode($json, true) ?: [];
}

// Function to get crusades for filtering (mission trip enrollments)
function getCrusadesForMissionFilter($file) {
    if (!file_exists($file)) {
        return ['crusades' => []];
    }
    $json = file_get_contents($file);
    return json_decode($json, true) ?: ['crusades' => []];
}

// Define file paths
$missionEnrollmentsFile = __DIR__ . '/../data/mission_trip_enrollments.json';
$crusadesFile = __DIR__ . '/../data/crusades.json';

// Get mission trip enrollments and crusades
$missionEnrollments = getMissionTripEnrollments($missionEnrollmentsFile);
$crusadesData = getCrusadesForMissionFilter($crusadesFile);

// Handle filters
$crusadeFilter = $_GET['crusade_filter'] ?? '';
$statusFilter = $_GET['status_filter'] ?? '';
$dateFrom = $_GET['date_from'] ?? '';
$dateTo = $_GET['date_to'] ?? '';

// Apply filters
$filteredEnrollments = $missionEnrollments;

if (!empty($crusadeFilter)) {
    $filteredEnrollments = array_filter($filteredEnrollments, function($enrollment) use ($crusadeFilter) {
        return ($enrollment['crusade_id'] ?? '') === $crusadeFilter;
    });
}

if (!empty($statusFilter)) {
    $filteredEnrollments = array_filter($filteredEnrollments, function($enrollment) use ($statusFilter) {
        return ($enrollment['status'] ?? 'pending') === $statusFilter;
    });
}

if (!empty($dateFrom)) {
    $filteredEnrollments = array_filter($filteredEnrollments, function($enrollment) use ($dateFrom) {
        return date('Y-m-d', strtotime($enrollment['created_at'])) >= $dateFrom;
    });
}

if (!empty($dateTo)) {
    $filteredEnrollments = array_filter($filteredEnrollments, function($enrollment) use ($dateTo) {
        return date('Y-m-d', strtotime($enrollment['created_at'])) <= $dateTo;
    });
}

// Sort by creation date (newest first)
usort($filteredEnrollments, function($a, $b) {
    return strtotime($b['created_at']) - strtotime($a['created_at']);
});

// Handle CSV export
if (isset($_GET['export']) && $_GET['export'] === 'csv') {
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="mission_trip_enrollments_' . date('Y-m-d') . '.csv"');

    $output = fopen('php://output', 'w');

    // CSV headers
    fputcsv($output, [
        'ID', 'Created Date', 'Designation', 'First Name', 'Last Name', 'Email', 'Phone', 
        'KingsChat Username', 'Zone', 'Group', 'Church', 'Mission Trip Interest', 
        'Destination Country', 'Passport Ready', 'Financial Readiness', 'Health Clearance',
        'Crusade Interest', 'Sponsor Interest', 'Sponsor Type', 
        'Sponsor Amount', 'Additional Comments', 'Status'
    ]);

    // CSV data
    foreach ($filteredEnrollments as $enrollment) {
        fputcsv($output, [
            $enrollment['id'], 
            $enrollment['created_at'], 
            $enrollment['designation'] ?? '',
            $enrollment['first_name'], 
            $enrollment['last_name'],
            $enrollment['email'], 
            $enrollment['phone'], 
            $enrollment['kingschat_username'] ?? '',
            $enrollment['zone'] ?? '', 
            $enrollment['group'] ?? '', 
            $enrollment['church'] ?? '', 
            ($enrollment['mission_trip_interest'] ?? '') === 'yes' ? 'Yes' : 'No',
            $enrollment['destination_country'] ?? '',
            ($enrollment['passport_ready'] ?? '') === 'yes' ? 'Yes' : 'No',
            ($enrollment['financial_readiness'] ?? '') === 'yes' ? 'Yes' : 'No',
            ($enrollment['health_clearance'] ?? '') === 'yes' ? 'Yes' : 'No',
            $enrollment['crusade_title'] ?? 'General Interest',
            ($enrollment['sponsor_interest'] ?? false) ? 'Yes' : 'No',
            $enrollment['sponsor_type'] ?? '',
            $enrollment['sponsor_amount'] ?? '',
            $enrollment['additional_comments'] ?? '', 
            $enrollment['status'] ?? 'pending'
        ]);
    }

    fclose($output);
    exit();
}

// Statistics
$totalEnrollments = count($missionEnrollments);
$confirmedInterest = count(array_filter($missionEnrollments, function($e) { return ($e['mission_trip_interest'] ?? '') === 'yes'; }));
$withSponsorship = count(array_filter($missionEnrollments, function($e) { return !empty($e['sponsor_interest']); }));
$pendingApplications = count(array_filter($missionEnrollments, function($e) { return ($e['status'] ?? 'pending') === 'pending'; }));
?>

<div class="space-y-6">
    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-globe text-2xl text-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Total Applications</p>
                    <p class="text-2xl font-semibold text-gray-900"><?php echo $totalEnrollments; ?></p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle text-2xl text-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Confirmed Interest</p>
                    <p class="text-2xl font-semibold text-gray-900"><?php echo $confirmedInterest; ?></p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-clock text-2xl text-yellow-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Pending Applications</p>
                    <p class="text-2xl font-semibold text-gray-900"><?php echo $pendingApplications; ?></p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-hand-holding-usd text-2xl text-red-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">With Sponsorship</p>
                    <p class="text-2xl font-semibold text-gray-900"><?php echo $withSponsorship; ?></p>
                </div>
            </div>
        </div>
    </div>


    <!-- Filters and Export -->
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex flex-wrap items-center justify-between gap-4 mb-6">
            <h2 class="text-xl font-semibold text-gray-900">Mission Trip Applications</h2>
            <div class="flex items-center space-x-2">
                <a href="?tab=missions&export=csv<?php 
                    echo !empty($crusadeFilter) ? '&crusade_filter=' . urlencode($crusadeFilter) : '';
                    echo !empty($statusFilter) ? '&status_filter=' . urlencode($statusFilter) : '';
                    echo !empty($dateFrom) ? '&date_from=' . urlencode($dateFrom) : '';
                    echo !empty($dateTo) ? '&date_to=' . urlencode($dateTo) : '';
                ?>" 
                   class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    <i class="fas fa-download mr-2"></i>
                    Export CSV
                </a>
            </div>
        </div>

        <!-- Filter Form -->
        <form method="GET" class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
            <input type="hidden" name="tab" value="missions">
            
            <div>
                <label for="crusade_filter" class="block text-sm font-medium text-gray-700 mb-1">Crusade</label>
                <select name="crusade_filter" id="crusade_filter" class="w-full border border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary text-sm">
                    <option value="">All Crusades</option>
                    <?php foreach ($crusadesData['crusades'] as $crusade): ?>
                        <option value="<?php echo $crusade['id']; ?>" <?php echo $crusadeFilter == $crusade['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($crusade['title']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div>
                <label for="status_filter" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select name="status_filter" id="status_filter" class="w-full border border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary text-sm">
                    <option value="">All Statuses</option>
                    <option value="pending" <?php echo $statusFilter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                    <option value="approved" <?php echo $statusFilter === 'approved' ? 'selected' : ''; ?>>Approved</option>
                    <option value="contacted" <?php echo $statusFilter === 'contacted' ? 'selected' : ''; ?>>Contacted</option>
                </select>
            </div>

            <div>
                <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">From Date</label>
                <input type="date" name="date_from" id="date_from" value="<?php echo htmlspecialchars($dateFrom); ?>" 
                       class="w-full border border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary text-sm">
            </div>

            <div>
                <label for="date_to" class="block text-sm font-medium text-gray-700 mb-1">To Date</label>
                <input type="date" name="date_to" id="date_to" value="<?php echo htmlspecialchars($dateTo); ?>" 
                       class="w-full border border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary text-sm">
            </div>

            <div class="flex flex-col space-y-2">
                <button type="submit" class="inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    <i class="fas fa-filter mr-2"></i>
                    Filter
                </button>
                <a href="?tab=missions" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    <i class="fas fa-times mr-2"></i>
                    Clear
                </a>
            </div>
        </form>

        <!-- Results Info -->
        <div class="flex justify-between items-center mb-4">
            <p class="text-sm text-gray-700">
                Showing <?php echo count($filteredEnrollments); ?> of <?php echo $totalEnrollments; ?> applications
            </p>
        </div>

        <!-- Enrollments Table -->
        <?php if (empty($filteredEnrollments)): ?>
            <div class="text-center py-12">
                <i class="fas fa-globe text-4xl text-gray-400 mb-4"></i>
                <p class="text-gray-500 text-lg">No mission trip applications found</p>
                <?php if (!empty($statusFilter) || !empty($dateFrom) || !empty($dateTo) || !empty($crusadeFilter)): ?>
                    <p class="text-gray-400 text-sm mt-2">Try adjusting your filters</p>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Applicant Info</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Church Info</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mission Interest</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Destination</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Travel Readiness</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Crusade Interest</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sponsorship</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($filteredEnrollments as $enrollment): ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                <i class="fas fa-user text-gray-600"></i>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">
                                                <?php 
                                                $designation = !empty($enrollment['designation']) ? ucfirst($enrollment['designation']) . ' ' : '';
                                                echo htmlspecialchars($designation . $enrollment['first_name'] . ' ' . $enrollment['last_name']); 
                                                ?>
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                <?php echo htmlspecialchars($enrollment['kingschat_username'] ?? 'N/A'); ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php 
                                    $churchInfo = [];
                                    if (!empty($enrollment['zone'])) $churchInfo[] = $enrollment['zone'];
                                    if (!empty($enrollment['group'])) $churchInfo[] = $enrollment['group'];
                                    if (!empty($enrollment['church'])) $churchInfo[] = $enrollment['church'];
                                    echo htmlspecialchars(implode(' | ', $churchInfo) ?: 'N/A');
                                    ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php if (($enrollment['mission_trip_interest'] ?? '') === 'yes'): ?>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold bg-green-100 text-green-800">
                                            Confirmed
                                        </span>
                                    <?php else: ?>
                                        <span class="text-gray-400">Not confirmed</span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo htmlspecialchars($enrollment['destination_country'] ?? 'Not specified'); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="space-y-1">
                                        <?php 
                                        $passportReady = ($enrollment['passport_ready'] ?? '') === 'yes';
                                        $financialReady = ($enrollment['financial_readiness'] ?? '') === 'yes';
                                        $healthReady = ($enrollment['health_clearance'] ?? '') === 'yes';
                                        $allReady = $passportReady && $financialReady && $healthReady;
                                        ?>
                                        <?php if ($allReady): ?>
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold bg-green-100 text-green-800">
                                                All Confirmed
                                            </span>
                                        <?php else: ?>
                                            <div class="space-y-1">
                                                <?php if ($passportReady): ?>
                                                    <span class="inline-flex px-1 py-0.5 text-xs bg-green-100 text-green-800">✓ Passport</span>
                                                <?php else: ?>
                                                    <span class="inline-flex px-1 py-0.5 text-xs bg-red-100 text-red-800">✗ Passport</span>
                                                <?php endif; ?>
                                                
                                                <?php if ($financialReady): ?>
                                                    <span class="inline-flex px-1 py-0.5 text-xs bg-green-100 text-green-800">✓ Financial</span>
                                                <?php else: ?>
                                                    <span class="inline-flex px-1 py-0.5 text-xs bg-red-100 text-red-800">✗ Financial</span>
                                                <?php endif; ?>
                                                
                                                <?php if ($healthReady): ?>
                                                    <span class="inline-flex px-1 py-0.5 text-xs bg-green-100 text-green-800">✓ Health</span>
                                                <?php else: ?>
                                                    <span class="inline-flex px-1 py-0.5 text-xs bg-red-100 text-red-800">✗ Health</span>
                                                <?php endif; ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php if (!empty($enrollment['crusade_title'])): ?>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                                            <?php echo htmlspecialchars($enrollment['crusade_title']); ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="text-gray-400">General Interest</span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php if (!empty($enrollment['sponsor_interest'])): ?>
                                        <div class="space-y-1">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                                Sponsor
                                            </span>
                                            <?php if (!empty($enrollment['sponsor_type'])): ?>
                                                <div class="text-xs text-gray-600">
                                                    <?php echo ucfirst(str_replace('_', ' ', $enrollment['sponsor_type'])); ?>
                                                    <?php if (!empty($enrollment['sponsor_amount'])): ?>
                                                        - $<?php echo number_format($enrollment['sponsor_amount'], 2); ?>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    <?php else: ?>
                                        <span class="text-gray-400">No sponsorship</span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?php echo htmlspecialchars($enrollment['email']); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo htmlspecialchars($enrollment['phone']); ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo date('M j, Y', strtotime($enrollment['created_at'])); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                        <?php 
                                        switch($enrollment['status'] ?? 'pending') {
                                            case 'approved': echo 'bg-green-100 text-green-800'; break;
                                            case 'contacted': echo 'bg-blue-100 text-blue-800'; break;
                                            case 'pending': 
                                            default: echo 'bg-yellow-100 text-yellow-800';
                                        }
                                        ?>">
                                        <?php echo ucfirst($enrollment['status'] ?? 'pending'); ?>
                                    </span>
                                </td>
                            </tr>
                            <?php if (!empty($enrollment['additional_comments'])): ?>
                            <tr class="bg-gray-50">
                                <td colspan="10" class="px-6 py-3">
                                    <div class="text-sm">
                                        <span class="font-medium text-gray-700">Additional Comments:</span>
                                        <span class="text-gray-600 ml-2"><?php echo htmlspecialchars($enrollment['additional_comments']); ?></span>
                                    </div>
                                </td>
                            </tr>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>