<?php
// Pastor <PERSON> Requests Dashboard Component
// This file is included in the admin interface as a tab

// Function to read crusade requests from JSON file
function getPastorCrusadeRequests($file) {
    if (!file_exists($file)) {
        return ['requests' => []];
    }
    $json = file_get_contents($file);
    return json_decode($json, true) ?: ['requests' => []];
}

// Function to update request status
function updatePastorRequestStatus($requestId, $status, $notes = '') {
    $file = __DIR__ . '/../data/pastor_crusade_requests.json';
    $data = getPastorCrusadeRequests($file);
    
    foreach ($data['requests'] as &$request) {
        if ($request['id'] == $requestId) {
            $request['status'] = $status;
            $request['admin_notes'] = $notes;
            $request['updated_at'] = date('Y-m-d H:i:s');
            break;
        }
    }
    
    file_put_contents($file, json_encode($data, JSON_PRETTY_PRINT));
    return true;
}

// Handle status updates
if ($_POST && isset($_POST['action']) && $_POST['action'] === 'update_pastor_request_status') {
    $requestId = $_POST['request_id'] ?? '';
    $status = $_POST['status'] ?? '';
    $notes = $_POST['admin_notes'] ?? '';
    
    if ($requestId && $status) {
        updatePastorRequestStatus($requestId, $status, $notes);
        $_SESSION['message'] = "Pastor crusade request status updated successfully!";
        $_SESSION['messageType'] = 'success';
        header('Location: ' . $_SERVER['PHP_SELF'] . '?tab=crusade-requests');
        exit();
    }
}

// Get requests data
$requestsFile = __DIR__ . '/../data/pastor_crusade_requests.json';
$requestsData = getPastorCrusadeRequests($requestsFile);

// Apply filters
$filterStatus = $_GET['filter_status'] ?? '';
$filterCountry = $_GET['filter_country'] ?? '';
$searchTerm = $_GET['search'] ?? '';

$filteredRequests = $requestsData['requests'];

// Filter by status
if (!empty($filterStatus)) {
    $filteredRequests = array_filter($filteredRequests, function($req) use ($filterStatus) {
        return ($req['status'] ?? 'pending') === $filterStatus;
    });
}

// Filter by country
if (!empty($filterCountry)) {
    $filteredRequests = array_filter($filteredRequests, function($req) use ($filterCountry) {
        return stripos($req['country'], $filterCountry) !== false;
    });
}

// Search filter
if (!empty($searchTerm)) {
    $filteredRequests = array_filter($filteredRequests, function($req) use ($searchTerm) {
        return stripos($req['first_name'], $searchTerm) !== false ||
               stripos($req['last_name'], $searchTerm) !== false ||
               stripos($req['email'], $searchTerm) !== false ||
               stripos($req['crusade_title'], $searchTerm) !== false ||
               stripos($req['church'], $searchTerm) !== false;
    });
}

// Sort by submission date (newest first)
usort($filteredRequests, function($a, $b) {
    return strtotime($b['timestamp']) - strtotime($a['timestamp']);
});

// Get unique countries for filter
$countries = [];
foreach ($requestsData['requests'] as $request) {
    if (!empty($request['country']) && !in_array($request['country'], $countries)) {
        $countries[] = $request['country'];
    }
}
sort($countries);

// Get statistics
$totalRequests = count($requestsData['requests']);
$pendingRequests = count(array_filter($requestsData['requests'], function($req) {
    return ($req['status'] ?? 'pending') === 'pending';
}));
$approvedRequests = count(array_filter($requestsData['requests'], function($req) {
    return ($req['status'] ?? 'pending') === 'approved';
}));
$rejectedRequests = count(array_filter($requestsData['requests'], function($req) {
    return ($req['status'] ?? 'pending') === 'rejected';
}));

?>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="bg-white p-6 border border-gray-200">
        <div class="flex items-center">
            <div class="p-3 bg-blue-100 text-blue-600 rounded-full">
                <i class="fas fa-list text-xl"></i>
            </div>
            <div class="ml-4">
                <h3 class="text-lg font-semibold text-gray-900"><?php echo $totalRequests; ?></h3>
                <p class="text-gray-600">Total Requests</p>
            </div>
        </div>
    </div>

    <div class="bg-white p-6 border border-gray-200">
        <div class="flex items-center">
            <div class="p-3 bg-yellow-100 text-yellow-600 rounded-full">
                <i class="fas fa-clock text-xl"></i>
            </div>
            <div class="ml-4">
                <h3 class="text-lg font-semibold text-gray-900"><?php echo $pendingRequests; ?></h3>
                <p class="text-gray-600">Pending</p>
            </div>
        </div>
    </div>

    <div class="bg-white p-6 border border-gray-200">
        <div class="flex items-center">
            <div class="p-3 bg-green-100 text-green-600 rounded-full">
                <i class="fas fa-check text-xl"></i>
            </div>
            <div class="ml-4">
                <h3 class="text-lg font-semibold text-gray-900"><?php echo $approvedRequests; ?></h3>
                <p class="text-gray-600">Approved</p>
            </div>
        </div>
    </div>

    <div class="bg-white p-6 border border-gray-200">
        <div class="flex items-center">
            <div class="p-3 bg-red-100 text-red-600 rounded-full">
                <i class="fas fa-times text-xl"></i>
            </div>
            <div class="ml-4">
                <h3 class="text-lg font-semibold text-gray-900"><?php echo $rejectedRequests; ?></h3>
                <p class="text-gray-600">Rejected</p>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="bg-white p-6 border border-gray-200 mb-8">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">Filter Pastor Crusade Requests</h3>
    <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <input type="hidden" name="tab" value="crusade-requests">
        
        <div>
            <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search</label>
            <input type="text" id="search" name="search" value="<?php echo htmlspecialchars($searchTerm); ?>"
                   placeholder="Name, email, crusade title..."
                   class="w-full px-3 py-2 border border-gray-300 focus:border-primary focus:outline-none">
        </div>

        <div>
            <label for="filter_status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
            <select id="filter_status" name="filter_status" 
                    class="w-full px-3 py-2 border border-gray-300 focus:border-primary focus:outline-none">
                <option value="">All Statuses</option>
                <option value="pending" <?php echo $filterStatus === 'pending' ? 'selected' : ''; ?>>Pending</option>
                <option value="approved" <?php echo $filterStatus === 'approved' ? 'selected' : ''; ?>>Approved</option>
                <option value="rejected" <?php echo $filterStatus === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                <option value="in_review" <?php echo $filterStatus === 'in_review' ? 'selected' : ''; ?>>In Review</option>
            </select>
        </div>

        <div>
            <label for="filter_country" class="block text-sm font-medium text-gray-700 mb-2">Country</label>
            <select id="filter_country" name="filter_country" 
                    class="w-full px-3 py-2 border border-gray-300 focus:border-primary focus:outline-none">
                <option value="">All Countries</option>
                <?php foreach ($countries as $country): ?>
                <option value="<?php echo htmlspecialchars($country); ?>" 
                        <?php echo $filterCountry === $country ? 'selected' : ''; ?>>
                    <?php echo htmlspecialchars($country); ?>
                </option>
                <?php endforeach; ?>
            </select>
        </div>

        <div class="flex items-end space-x-2">
            <button type="submit" class="bg-primary text-white px-4 py-2 hover:bg-red-700 transition-colors">
                <i class="fas fa-search mr-2"></i>Filter
            </button>
            <a href="?tab=crusade-requests" class="bg-gray-500 text-white px-4 py-2 hover:bg-gray-600 transition-colors">
                <i class="fas fa-times mr-2"></i>Clear
            </a>
        </div>
    </form>
</div>

<!-- Requests Table -->
<div class="bg-white border border-gray-200 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Pastor Crusade Requests (<?php echo count($filteredRequests); ?>)</h3>
    </div>

    <?php if (empty($filteredRequests)): ?>
    <div class="text-center py-12">
        <i class="fas fa-inbox text-4xl text-gray-400 mb-4"></i>
        <p class="text-gray-600">No pastor crusade requests found.</p>
    </div>
    <?php else: ?>
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pastor</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Crusade Details</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Submitted</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <?php foreach ($filteredRequests as $request): 
                    $status = $request['status'] ?? 'pending';
                    $statusColors = [
                        'pending' => 'bg-yellow-100 text-yellow-800',
                        'approved' => 'bg-green-100 text-green-800',
                        'rejected' => 'bg-red-100 text-red-800',
                        'in_review' => 'bg-blue-100 text-blue-800'
                    ];
                    $statusColor = $statusColors[$status] ?? 'bg-gray-100 text-gray-800';
                ?>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div>
                            <div class="text-sm font-medium text-gray-900">
                                <?php echo htmlspecialchars(($request['designation'] ?? '') . ' ' . $request['first_name'] . ' ' . $request['last_name']); ?>
                            </div>
                            <div class="text-sm text-gray-500"><?php echo htmlspecialchars($request['email']); ?></div>
                            <div class="text-sm text-gray-500"><?php echo htmlspecialchars($request['phone']); ?></div>
                        </div>
                    </td>
                    <td class="px-6 py-4">
                        <div>
                            <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($request['crusade_title']); ?></div>
                            <div class="text-sm text-gray-500">Venue: <?php echo htmlspecialchars($request['venue']); ?></div>
                            <div class="text-sm text-gray-500">
                                Expected: <?php echo htmlspecialchars($request['expected_attendance']); ?>
                            </div>
                            <div class="text-sm text-gray-500">
                                Date: <?php echo date('M j, Y', strtotime($request['preferred_date'])); ?> at 
                                <?php echo date('g:i A', strtotime($request['preferred_time'])); ?>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900"><?php echo htmlspecialchars($request['city'] . ', ' . $request['country']); ?></div>
                        <div class="text-sm text-gray-500">Zone: <?php echo htmlspecialchars($request['zone']); ?></div>
                        <div class="text-sm text-gray-500">Church: <?php echo htmlspecialchars($request['church']); ?></div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo $statusColor; ?>">
                            <?php echo ucfirst($status); ?>
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <?php echo date('M j, Y g:i A', strtotime($request['timestamp'])); ?>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        <button onclick="viewPastorRequest(<?php echo htmlspecialchars(json_encode($request)); ?>)" 
                                class="text-blue-600 hover:text-blue-900">View</button>
                        <button onclick="updatePastorStatus('<?php echo $request['id']; ?>', '<?php echo $status; ?>')" 
                                class="text-green-600 hover:text-green-900">Update</button>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    <?php endif; ?>
</div>

<!-- View Request Modal -->
<div id="viewPastorModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-4xl w-full max-h-screen overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Pastor Crusade Request Details</h3>
                <button onclick="closePastorModal('viewPastorModal')" class="float-right text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="viewPastorModalContent" class="p-6">
                <!-- Content will be populated by JavaScript -->
            </div>
        </div>
    </div>
</div>

<!-- Update Status Modal -->
<div id="statusPastorModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-md w-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Update Pastor Request Status</h3>
                <button onclick="closePastorModal('statusPastorModal')" class="float-right text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form method="POST" class="p-6">
                <input type="hidden" name="action" value="update_pastor_request_status">
                <input type="hidden" name="request_id" id="statusPastorRequestId">
                
                <div class="mb-4">
                    <label for="pastor_status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select name="status" id="pastor_status" required
                            class="w-full px-3 py-2 border border-gray-300 focus:border-primary focus:outline-none">
                        <option value="pending">Pending</option>
                        <option value="in_review">In Review</option>
                        <option value="approved">Approved</option>
                        <option value="rejected">Rejected</option>
                    </select>
                </div>

                <div class="mb-6">
                    <label for="pastor_admin_notes" class="block text-sm font-medium text-gray-700 mb-2">Admin Notes</label>
                    <textarea name="admin_notes" id="pastor_admin_notes" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 focus:border-primary focus:outline-none resize-none"
                              placeholder="Add notes about this status change..."></textarea>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closePastorModal('statusPastorModal')" 
                            class="px-4 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50 transition-colors">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 bg-primary text-white hover:bg-red-700 transition-colors">
                        Update Status
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function viewPastorRequest(request) {
    const modal = document.getElementById('viewPastorModal');
    const content = document.getElementById('viewPastorModalContent');
    
    content.innerHTML = `
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Personal Information</h4>
                <div class="space-y-3">
                    <div><strong>Name:</strong> ${request.designation || ''} ${request.first_name} ${request.last_name}</div>
                    <div><strong>Email:</strong> ${request.email}</div>
                    <div><strong>Phone:</strong> ${request.phone}</div>
                    <div><strong>KingsChat:</strong> ${request.kingschat_username}</div>
                </div>
            </div>
            
            <div>
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Church Information</h4>
                <div class="space-y-3">
                    <div><strong>Zone:</strong> ${request.zone}</div>
                    <div><strong>Group:</strong> ${request.group}</div>
                    <div><strong>Church:</strong> ${request.church}</div>
                </div>
            </div>
            
            <div class="md:col-span-2">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Crusade Details</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div><strong>Title:</strong> ${request.crusade_title}</div>
                    <div><strong>Location:</strong> ${request.city}, ${request.country}</div>
                    <div><strong>Venue:</strong> ${request.venue}</div>
                    <div><strong>Expected Attendance:</strong> ${request.expected_attendance}</div>
                    <div><strong>Preferred Date:</strong> ${new Date(request.preferred_date).toLocaleDateString()}</div>
                    <div><strong>Preferred Time:</strong> ${request.preferred_time}</div>
                </div>
            </div>
            
            ${request.additional_comments ? `
            <div class="md:col-span-2">
                <h4 class="text-lg font-semibold text-gray-900 mb-2">Additional Comments</h4>
                <p class="text-gray-700">${request.additional_comments}</p>
            </div>
            ` : ''}
            
            ${request.admin_notes ? `
            <div class="md:col-span-2">
                <h4 class="text-lg font-semibold text-gray-900 mb-2">Admin Notes</h4>
                <p class="text-gray-700 bg-gray-50 p-3 rounded">${request.admin_notes}</p>
            </div>
            ` : ''}
            
            <div class="md:col-span-2 border-t pt-4">
                <div class="flex justify-between items-center">
                    <div>
                        <strong>Status:</strong> 
                        <span class="ml-2 px-2 py-1 text-xs font-semibold rounded-full ${getPastorStatusClass(request.status || 'pending')}">
                            ${(request.status || 'pending').charAt(0).toUpperCase() + (request.status || 'pending').slice(1)}
                        </span>
                    </div>
                    <div class="text-sm text-gray-500">
                        Submitted: ${new Date(request.timestamp).toLocaleDateString()} ${new Date(request.timestamp).toLocaleTimeString()}
                    </div>
                </div>
            </div>
        </div>
    `;
    
    modal.classList.remove('hidden');
}

function updatePastorStatus(requestId, currentStatus) {
    document.getElementById('statusPastorRequestId').value = requestId;
    document.getElementById('pastor_status').value = currentStatus;
    document.getElementById('statusPastorModal').classList.remove('hidden');
}

function closePastorModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

function getPastorStatusClass(status) {
    const classes = {
        'pending': 'bg-yellow-100 text-yellow-800',
        'approved': 'bg-green-100 text-green-800', 
        'rejected': 'bg-red-100 text-red-800',
        'in_review': 'bg-blue-100 text-blue-800'
    };
    return classes[status] || 'bg-gray-100 text-gray-800';
}

// Close modals when clicking outside
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('bg-gray-600')) {
        closePastorModal('viewPastorModal');
        closePastorModal('statusPastorModal');
    }
});
</script> 