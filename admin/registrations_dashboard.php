<?php
// Registration Dashboard Component
// This file is included in the admin interface as a tab

// Function to read registrations from JSON file
function getRegistrations($file) {
    if (!file_exists($file)) {
        return ['registrations' => []];
    }
    $json = file_get_contents($file);
    return json_decode($json, true) ?: ['registrations' => []];
}

// Function to read crusades from JSON file
function getCrusadesForFilter($file) {
    if (!file_exists($file)) {
        return ['crusades' => []];
    }
    $json = file_get_contents($file);
    return json_decode($json, true) ?: ['crusades' => []];
}

// Get registrations and crusades data
$registrationsFile = __DIR__ . '/../data/registrations.json';
$crusadesFile = __DIR__ . '/../data/crusades.json';
$registrations = getRegistrations($registrationsFile);
$crusades = getCrusadesForFilter($crusadesFile);

// Apply filters
$filterCrusade = $_GET['filter_crusade'] ?? '';
$filterAttending = $_GET['filter_attending'] ?? '';
$filterDateFrom = $_GET['filter_date_from'] ?? '';
$filterDateTo = $_GET['filter_date_to'] ?? '';
$searchTerm = $_GET['search'] ?? '';

$filteredRegistrations = $registrations['registrations'];

// Filter by crusade
if (!empty($filterCrusade)) {
    $filteredRegistrations = array_filter($filteredRegistrations, function($reg) use ($filterCrusade) {
        return $reg['crusade_id'] == $filterCrusade;
    });
}

// Filter by attending status
if (!empty($filterAttending)) {
    $filteredRegistrations = array_filter($filteredRegistrations, function($reg) use ($filterAttending) {
        return $reg['attending'] === $filterAttending;
    });
}

// Filter by date range
if (!empty($filterDateFrom)) {
    $filteredRegistrations = array_filter($filteredRegistrations, function($reg) use ($filterDateFrom) {
        return $reg['timestamp'] >= $filterDateFrom . ' 00:00:00';
    });
}

if (!empty($filterDateTo)) {
    $filteredRegistrations = array_filter($filteredRegistrations, function($reg) use ($filterDateTo) {
        return $reg['timestamp'] <= $filterDateTo . ' 23:59:59';
    });
}

// Search filter
if (!empty($searchTerm)) {
    $filteredRegistrations = array_filter($filteredRegistrations, function($reg) use ($searchTerm) {
        $searchFields = [
            $reg['first_name'], $reg['last_name'], $reg['email'], 
            $reg['phone'], $reg['crusade_title']
        ];
        $searchText = strtolower(implode(' ', $searchFields));
        return strpos($searchText, strtolower($searchTerm)) !== false;
    });
}

// Handle registration detail view
$viewRegistration = null;
if (isset($_GET['view'])) {
    $viewId = $_GET['view'];
    foreach ($registrations['registrations'] as $reg) {
        if ($reg['id'] === $viewId) {
            $viewRegistration = $reg;
            break;
        }
    }
}

// Calculate statistics
$totalRegistrations = count($registrations['registrations']);
$filteredCount = count($filteredRegistrations);
$attendingYes = count(array_filter($registrations['registrations'], function($reg) {
    return $reg['attending'] === 'Yes';
}));
$attendingNo = count(array_filter($registrations['registrations'], function($reg) {
    return $reg['attending'] === 'No';
}));

// Group by crusade for statistics
$crusadeStats = [];
foreach ($registrations['registrations'] as $reg) {
    $crusadeId = $reg['crusade_id'];
    if (!isset($crusadeStats[$crusadeId])) {
        $crusadeStats[$crusadeId] = [
            'title' => $reg['crusade_title'],
            'total' => 0,
            'attending' => 0,
            'not_attending' => 0
        ];
    }
    $crusadeStats[$crusadeId]['total']++;
    if ($reg['attending'] === 'Yes') {
        $crusadeStats[$crusadeId]['attending']++;
    } elseif ($reg['attending'] === 'No') {
        $crusadeStats[$crusadeId]['not_attending']++;
    }
}
?>

<!-- Registration Dashboard Content -->
<div class="space-y-6">
    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-users text-white text-sm"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Total Registrations</p>
                    <p class="text-2xl font-semibold text-gray-900"><?php echo $totalRegistrations; ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-check text-white text-sm"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Attending</p>
                    <p class="text-2xl font-semibold text-gray-900"><?php echo $attendingYes; ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-times text-white text-sm"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Not Attending</p>
                    <p class="text-2xl font-semibold text-gray-900"><?php echo $attendingNo; ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-filter text-white text-sm"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Filtered Results</p>
                    <p class="text-2xl font-semibold text-gray-900"><?php echo $filteredCount; ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Filters</h3>
        <form method="GET" class="grid grid-cols-1 md:grid-cols-6 gap-4" onsubmit="return applyFilters(event)">
            <input type="hidden" name="tab" value="registrations">
            
            <div>
                <label for="filter_crusade" class="block text-sm font-medium text-gray-700 mb-1">Crusade</label>
                <select id="filter_crusade" name="filter_crusade" class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary text-sm">
                    <option value="">All Crusades</option>
                    <?php foreach ($crusades['crusades'] as $crusade): ?>
                        <option value="<?php echo $crusade['id']; ?>" <?php echo $filterCrusade == $crusade['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($crusade['title']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div>
                <label for="filter_attending" class="block text-sm font-medium text-gray-700 mb-1">Attending</label>
                <select id="filter_attending" name="filter_attending" class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary text-sm">
                    <option value="">All</option>
                    <option value="Yes" <?php echo $filterAttending === 'Yes' ? 'selected' : ''; ?>>Yes</option>
                    <option value="No" <?php echo $filterAttending === 'No' ? 'selected' : ''; ?>>No</option>
                </select>
            </div>

            <div>
                <label for="filter_date_from" class="block text-sm font-medium text-gray-700 mb-1">From Date</label>
                <input type="date" id="filter_date_from" name="filter_date_from" value="<?php echo htmlspecialchars($filterDateFrom); ?>" class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary text-sm">
            </div>

            <div>
                <label for="filter_date_to" class="block text-sm font-medium text-gray-700 mb-1">To Date</label>
                <input type="date" id="filter_date_to" name="filter_date_to" value="<?php echo htmlspecialchars($filterDateTo); ?>" class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary text-sm">
            </div>

            <div>
                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <input type="text" id="search" name="search" value="<?php echo htmlspecialchars($searchTerm); ?>" placeholder="Name, email, phone..." class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary text-sm">
            </div>

            <div class="flex items-end">
                <button type="submit" class="w-full bg-primary text-white py-2 px-4 rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary text-sm">
                    Apply Filters
                </button>
            </div>
        </form>

        <div class="mt-4 flex justify-between items-center">
            <?php
            // Build export URL with current filters
            $exportParams = $_GET;
            $exportParams['export'] = 'csv';
            $exportParams['tab'] = 'registrations';
            ?>
            <a href="?<?php echo http_build_query($exportParams); ?>"
               class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                <i class="fas fa-download mr-2"></i>
                Export CSV
            </a>

            <?php if (!empty($searchTerm) || !empty($filterCrusade) || !empty($filterAttending) || !empty($filterDateFrom) || !empty($filterDateTo)): ?>
                <button onclick="clearFilters()" class="text-sm text-gray-500 hover:text-gray-700 cursor-pointer">
                    Clear all filters
                </button>
            <?php endif; ?>
        </div>
    </div>

    <!-- Registrations Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">
                Registrations
                <span class="text-sm font-normal text-gray-500">(<?php echo $filteredCount; ?> of <?php echo $totalRegistrations; ?>)</span>
            </h3>
        </div>

        <?php if (empty($filteredRegistrations)): ?>
            <div class="px-6 py-12 text-center">
                <i class="fas fa-inbox text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-500">No registrations found.</p>
                <?php if (!empty($searchTerm) || !empty($filterCrusade) || !empty($filterAttending)): ?>
                    <p class="text-sm text-gray-400 mt-2">Try adjusting your filters.</p>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Crusade</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Attending</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Registration Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($filteredRegistrations as $registration): ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">
                                        <?php echo htmlspecialchars($registration['title'] . ' ' . $registration['first_name'] . ' ' . $registration['last_name']); ?>
                                    </div>
                                    <?php if (!empty($registration['church'])): ?>
                                        <div class="text-sm text-gray-500"><?php echo htmlspecialchars($registration['church']); ?></div>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?php echo htmlspecialchars($registration['email']); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo htmlspecialchars($registration['phone']); ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?php echo htmlspecialchars($registration['crusade_title']); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo date('M j, Y', strtotime($registration['crusade_date'])); ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php if ($registration['attending'] === 'Yes'): ?>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                            <i class="fas fa-check mr-1"></i> Yes
                                        </span>
                                    <?php elseif ($registration['attending'] === 'No'): ?>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                            <i class="fas fa-times mr-1"></i> No
                                        </span>
                                    <?php else: ?>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                            Unknown
                                        </span>
                                    <?php endif; ?>

                                    <?php if ($registration['coming_with'] > 0): ?>
                                        <div class="text-xs text-gray-500 mt-1">+<?php echo $registration['coming_with']; ?> guests</div>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo date('M j, Y g:i A', strtotime($registration['timestamp'])); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <?php
                                    $viewParams = $_GET;
                                    $viewParams['view'] = $registration['id'];
                                    $viewParams['tab'] = 'registrations';
                                    ?>
                                    <a href="?<?php echo http_build_query($viewParams); ?>"
                                       class="text-primary hover:text-primary-dark">
                                        View Details
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Registration Detail Modal -->
<?php if ($viewRegistration): ?>
<div id="registration-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex justify-between items-center pb-4 border-b">
                <h3 class="text-lg font-medium text-gray-900">Registration Details</h3>
                <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Modal Content -->
            <div class="mt-4 space-y-6">
                <!-- Personal Information -->
                <div>
                    <h4 class="text-md font-semibold text-gray-800 mb-3">Personal Information</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div><strong>Name:</strong> <?php echo htmlspecialchars($viewRegistration['title'] . ' ' . $viewRegistration['first_name'] . ' ' . $viewRegistration['last_name']); ?></div>
                        <div><strong>Email:</strong> <?php echo htmlspecialchars($viewRegistration['email']); ?></div>
                        <div><strong>Phone:</strong> <?php echo htmlspecialchars($viewRegistration['phone']); ?></div>
                        <div><strong>Church:</strong> <?php echo htmlspecialchars($viewRegistration['church'] ?: 'Not specified'); ?></div>
                        <div class="md:col-span-2"><strong>Address:</strong> <?php echo htmlspecialchars($viewRegistration['address'] ?: 'Not provided'); ?></div>
                    </div>
                </div>

                <!-- Event Information -->
                <div>
                    <h4 class="text-md font-semibold text-gray-800 mb-3">Event Information</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div><strong>Crusade:</strong> <?php echo htmlspecialchars($viewRegistration['crusade_title']); ?></div>
                        <div><strong>Date:</strong> <?php echo date('F j, Y', strtotime($viewRegistration['crusade_date'])); ?></div>
                        <div><strong>Venue:</strong> <?php echo htmlspecialchars($viewRegistration['crusade_venue']); ?></div>
                        <div><strong>Registration Date:</strong> <?php echo date('M j, Y g:i A', strtotime($viewRegistration['timestamp'])); ?></div>
                    </div>
                </div>

                <!-- Attendance Information -->
                <div>
                    <h4 class="text-md font-semibold text-gray-800 mb-3">Attendance Information</h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div><strong>Attending:</strong>
                            <span class="<?php echo $viewRegistration['attending'] === 'Yes' ? 'text-green-600' : 'text-red-600'; ?>">
                                <?php echo $viewRegistration['attending']; ?>
                            </span>
                        </div>
                        <div><strong>Coming with:</strong> <?php echo $viewRegistration['coming_with']; ?> people</div>
                        <div><strong>Inviting:</strong> <?php echo $viewRegistration['inviting']; ?> people</div>
                        <div><strong>Source:</strong> <?php echo htmlspecialchars($viewRegistration['source']); ?></div>
                        <div><strong>Attended before:</strong> <?php echo $viewRegistration['attended_before']; ?></div>
                        <div><strong>Wants updates:</strong> <?php echo $viewRegistration['updates']; ?></div>
                    </div>
                </div>

                <!-- Prayer & Healing -->
                <div>
                    <h4 class="text-md font-semibold text-gray-800 mb-3">Prayer & Healing</h4>
                    <div class="space-y-2 text-sm">
                        <div><strong>Needs healing:</strong> <?php echo $viewRegistration['healing']; ?></div>
                        <div><strong>Wants prayer:</strong> <?php echo $viewRegistration['prayer']; ?></div>
                        <?php if (!empty($viewRegistration['prayer_type'])): ?>
                            <div><strong>Prayer types:</strong> <?php echo implode(', ', $viewRegistration['prayer_type']); ?></div>
                        <?php endif; ?>
                        <?php if (!empty($viewRegistration['prayer_details'])): ?>
                            <div><strong>Prayer details:</strong> <?php echo htmlspecialchars($viewRegistration['prayer_details']); ?></div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Rhapsody of Realities -->
                <div>
                    <h4 class="text-md font-semibold text-gray-800 mb-3">Rhapsody of Realities</h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div><strong>Interested:</strong> <?php echo $viewRegistration['rhapsody_interested']; ?></div>
                        <div><strong>Wants to buy:</strong> <?php echo $viewRegistration['buying']; ?></div>
                        <div><strong>Copies needed:</strong> <?php echo $viewRegistration['copies']; ?></div>
                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="flex justify-end pt-4 border-t mt-6">
                <button onclick="closeModal()" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function closeModal() {
    // Remove the view parameter and reload
    const url = new URL(window.location);
    url.searchParams.delete('view');
    window.location.href = url.toString();
}

// Close modal when clicking outside
document.getElementById('registration-modal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});
</script>
<?php endif; ?>
