<?php
// World Crusade Requests Dashboard - included in crusades.php

// Function to read world crusade requests from JSON file
function getWorldCrusadeRequests($file) {
    if (!file_exists($file)) {
        return ['requests' => []];
    }
    $json = file_get_contents($file);
    $data = json_decode($json, true);
    return $data ?: ['requests' => []];
}

// Function to save world crusade requests to JSON file
function saveWorldCrusadeRequests($file, $data) {
    $json = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
    return file_put_contents($file, $json) !== false;
}

// Function to update request status
function updateWorldRequestStatus($requestId, $status, $notes = '') {
    global $worldRequestsFile;
    $data = getWorldCrusadeRequests($worldRequestsFile);
    
    foreach ($data['requests'] as &$request) {
        if ((int)$request['id'] === (int)$requestId) {
            $request['status'] = $status;
            $request['admin_notes'] = $notes;
            $request['updated_at'] = date('Y-m-d H:i:s');
            break;
        }
    }
    
    return saveWorldCrusadeRequests($worldRequestsFile, $data);
}

// Define file path
$worldRequestsFile = __DIR__ . '/../data/world_crusade_requests.json';

// Handle status updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update_world_status') {
    $requestId = (int)$_POST['request_id'];
    $status = $_POST['status'];
    $notes = $_POST['notes'] ?? '';
    
    if (updateWorldRequestStatus($requestId, $status, $notes)) {
        $_SESSION['message'] = 'Request status updated successfully!';
        $_SESSION['messageType'] = 'success';
    } else {
        $_SESSION['message'] = 'Error updating request status.';
        $_SESSION['messageType'] = 'error';
    }
    
    header('Location: ' . $_SERVER['PHP_SELF'] . '?tab=world-requests');
    exit();
}


// Get world crusade requests
$worldRequests = getWorldCrusadeRequests($worldRequestsFile);

// Handle filters
$statusFilter = $_GET['status_filter'] ?? '';
$countryFilter = $_GET['country_filter'] ?? '';
$typeFilter = $_GET['type_filter'] ?? '';
$registrationTypeFilter = $_GET['registration_type_filter'] ?? '';
$dateFrom = $_GET['date_from'] ?? '';
$dateTo = $_GET['date_to'] ?? '';

// Apply filters
$filteredRequests = $worldRequests['requests'];

if (!empty($statusFilter)) {
    $filteredRequests = array_filter($filteredRequests, function($request) use ($statusFilter) {
        return ($request['status'] ?? 'pending') === $statusFilter;
    });
}

if (!empty($countryFilter)) {
    $filteredRequests = array_filter($filteredRequests, function($request) use ($countryFilter) {
        return stripos($request['country'] ?? '', $countryFilter) !== false;
    });
}

if (!empty($typeFilter)) {
    $filteredRequests = array_filter($filteredRequests, function($request) use ($typeFilter) {
        return ($request['crusade_type'] ?? '') === $typeFilter;
    });
}

if (!empty($registrationTypeFilter)) {
    $filteredRequests = array_filter($filteredRequests, function($request) use ($registrationTypeFilter) {
        return ($request['registration_type'] ?? '') === $registrationTypeFilter;
    });
}

if (!empty($dateFrom)) {
    $filteredRequests = array_filter($filteredRequests, function($request) use ($dateFrom) {
        return date('Y-m-d', strtotime($request['created_at'])) >= $dateFrom;
    });
}

if (!empty($dateTo)) {
    $filteredRequests = array_filter($filteredRequests, function($request) use ($dateTo) {
        return date('Y-m-d', strtotime($request['created_at'])) <= $dateTo;
    });
}

// Sort by creation date (newest first)
usort($filteredRequests, function($a, $b) {
    return strtotime($b['created_at']) - strtotime($a['created_at']);
});

// CSV export is now handled in crusades.php before any HTML output

// Statistics
$totalRequests = count($worldRequests['requests']);
$pendingRequests = count(array_filter($worldRequests['requests'], function($r) { return ($r['status'] ?? 'pending') === 'pending'; }));
$approvedRequests = count(array_filter($worldRequests['requests'], function($r) { return ($r['status'] ?? 'pending') === 'approved'; }));
$contactedRequests = count(array_filter($worldRequests['requests'], function($r) { return ($r['status'] ?? 'pending') === 'contacted'; }));
$rejectedRequests = count(array_filter($worldRequests['requests'], function($r) { return ($r['status'] ?? 'pending') === 'rejected'; }));

// Get unique countries for filter
$countries = array_unique(array_filter(array_column($worldRequests['requests'], 'country')));
sort($countries);

// Get unique crusade types for filter
$crusadeTypes = array_unique(array_filter(array_column($worldRequests['requests'], 'crusade_type')));
sort($crusadeTypes);

// Get unique registration types for filter
$registrationTypes = array_unique(array_filter(array_column($worldRequests['requests'], 'registration_type')));
sort($registrationTypes);
?>

<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.tailwindcss.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css">

<!-- Custom DataTables Styling -->
<style>
.dataTables_wrapper .dataTables_length select,
.dataTables_wrapper .dataTables_filter input {
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

.dataTables_wrapper .dataTables_filter input:focus {
    outline: none;
    border-color: #dc2626;
    box-shadow: 0 0 0 1px #dc2626;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: #dc2626 !important;
    border-color: #dc2626 !important;
    color: white !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: #f3f4f6 !important;
    border-color: #d1d5db !important;
    color: #374151 !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    background: #b91c1c !important;
    border-color: #b91c1c !important;
    color: white !important;
}

.dt-button {
    background: #dc2626 !important;
    border: 1px solid #dc2626 !important;
    color: white !important;
    padding: 0.5rem 1rem !important;
    border-radius: 0.375rem !important;
    font-size: 0.875rem !important;
    margin-right: 0.5rem !important;
}

.dt-button:hover {
    background: #b91c1c !important;
    border-color: #b91c1c !important;
}

.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate {
    color: #374151;
}

.dataTables_wrapper .dataTables_length label,
.dataTables_wrapper .dataTables_filter label {
    font-weight: 500;
    color: #374151;
}

.dataTables_wrapper .dataTables_processing {
    color: #dc2626;
    font-weight: 500;
}

/* Fix for responsive design */
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before {
    background-color: #dc2626;
}

table.dataTable.dtr-inline.collapsed > tbody > tr.parent > td.dtr-control:before {
    background-color: #b91c1c;
}
</style>

<div class="space-y-6">
    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-globe text-2xl text-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Total Requests</p>
                    <p class="text-2xl font-semibold text-gray-900"><?php echo $totalRequests; ?></p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-clock text-2xl text-yellow-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Pending</p>
                    <p class="text-2xl font-semibold text-gray-900"><?php echo $pendingRequests; ?></p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle text-2xl text-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Approved</p>
                    <p class="text-2xl font-semibold text-gray-900"><?php echo $approvedRequests; ?></p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-phone text-2xl text-purple-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Contacted</p>
                    <p class="text-2xl font-semibold text-gray-900"><?php echo $contactedRequests; ?></p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-times-circle text-2xl text-red-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Rejected</p>
                    <p class="text-2xl font-semibold text-gray-900"><?php echo $rejectedRequests; ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Table Container -->
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex flex-wrap items-center justify-between gap-4 mb-6">
            <h2 class="text-xl font-semibold text-gray-900">World Crusade Requests</h2>
            <div class="flex items-center space-x-2">
                <a href="../directors-dashboard.php" 
                   target="_blank"
                   class="inline-flex items-center px-4 py-2 border-0 shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200">
                    <i class="fas fa-external-link-alt mr-2"></i>
                    Director's Dashboard
                </a>
                                <a href="download_world_crusade_template.php" 
                   class="inline-flex items-center px-3 py-2 border border-red-600 shadow-sm text-sm leading-4 font-medium rounded-md text-red-600 bg-white hover:bg-red-600 hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-600 transition-colors duration-200">
                    <i class="fas fa-file-csv mr-2"></i>
                    Download Template
                </a>
                <a href="?tab=world-requests&amp;export=csv" 
                   class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-600">
                    <i class="fas fa-download mr-2"></i>
                    Export CSV
                </a>
            </div>
        </div>

        <!-- DataTable -->
        <?php if (empty($worldRequests['requests'])): ?>
            <div class="text-center py-12">
                <i class="fas fa-globe text-4xl text-gray-400 mb-4"></i>
                <p class="text-gray-500 text-lg">No world crusade requests found</p>
            </div>
        <?php else: ?>
            <div class="overflow-x-auto">
                <table id="worldCrusadeRequestsTable" class="min-w-full divide-y divide-gray-200 display responsive nowrap" style="width:100%">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Requester</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Organization</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Crusade Title</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Crusade Type</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Country</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Venue</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Attendance</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">KingsChat</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Zone/Group/Church</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Number of Crusades</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($worldRequests['requests'] as $request): ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo $request['id']; ?>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-8 w-8">
                                            <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                                                <i class="fas fa-user text-gray-600 text-xs"></i>
                                            </div>
                                        </div>
                                        <div class="ml-3">
                                            <div class="text-sm font-medium text-gray-900">
                                                <?php 
                                                $registrationType = $request['registration_type'] ?? 'individual';
                                                
                                                if ($registrationType === 'organisation' || $registrationType === 'network') {
                                                    echo htmlspecialchars($request['organization_name'] ?? 'Organisation', ENT_QUOTES, 'UTF-8');
                                                } elseif ($registrationType === 'church') {
                                                    if (!empty($request['first_name']) && !empty($request['last_name'])) {
                                                        $designation = !empty($request['designation']) ? ucfirst($request['designation']) . ' ' : '';
                                                        echo htmlspecialchars($designation . $request['first_name'] . ' ' . $request['last_name'], ENT_QUOTES, 'UTF-8');
                                                    } else {
                                                        $churchName = $request['church_name'] ?? $request['ministry_name'] ?? $request['group_name'] ?? 'Church Contact';
                                                        echo htmlspecialchars($churchName, ENT_QUOTES, 'UTF-8');
                                                    }
                                                } else {
                                                    $designation = !empty($request['designation']) ? ucfirst($request['designation']) . ' ' : '';
                                                    $firstName = $request['first_name'] ?? '';
                                                    $lastName = $request['last_name'] ?? '';
                                                    echo htmlspecialchars($designation . $firstName . ' ' . $lastName, ENT_QUOTES, 'UTF-8');
                                                }
                                                ?>
                                            </div>
                                            <?php if (!empty($request['designation'])): ?>
                                                <div class="text-xs text-gray-500"><?php echo htmlspecialchars($request['designation'], ENT_QUOTES, 'UTF-8'); ?></div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                        <?php 
                                        switch($request['registration_type'] ?? 'individual') {
                                            case 'church': echo 'bg-purple-100 text-purple-800'; break;
                                            case 'organisation': 
                                            case 'network': echo 'bg-green-100 text-green-800'; break;
                                            case 'individual': 
                                            default: echo 'bg-blue-100 text-blue-800';
                                        }
                                        ?>">
                                        <?php echo ucfirst($request['registration_type'] ?? 'Individual'); ?>
                                    </span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo htmlspecialchars($request['organization_name'] ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?>
                                </td>
                                <td class="px-4 py-4 text-sm text-gray-900">
                                    <?php echo htmlspecialchars($request['crusade_title'] ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?>
                                </td>
                                <td class="px-4 py-4 text-sm">
                                    <?php 
                                    $crusadeType = $request['crusade_type'] ?? 'Open';
                                    $crusadeTypes = $request['crusade_types'] ?? [];
                                    
                                    // If it's an array (church registration), display multiple badges
                                    if (is_array($crusadeTypes) && !empty($crusadeTypes)) {
                                        foreach ($crusadeTypes as $type) {
                                            $badgeClass = 'bg-gray-100 text-gray-800';
                                            switch(strtolower($type)) {
                                                case 'mega': $badgeClass = 'bg-red-100 text-red-800'; break;
                                                case 'tap2read': $badgeClass = 'bg-purple-100 text-purple-800'; break;
                                                case 'youths-aglow': $badgeClass = 'bg-blue-100 text-blue-800'; break;
                                                case 'teevolution': $badgeClass = 'bg-indigo-100 text-indigo-800'; break;
                                                case 'say-yes-to-kids': $badgeClass = 'bg-yellow-100 text-yellow-800'; break;
                                                case 'nolb': $badgeClass = 'bg-green-100 text-green-800'; break;
                                                case 'leading-ladies': $badgeClass = 'bg-pink-100 text-pink-800'; break;
                                                case 'mighty-men': $badgeClass = 'bg-gray-100 text-gray-800'; break;
                                                case 'professionals': $badgeClass = 'bg-teal-100 text-teal-800'; break;
                                                case 'tv': $badgeClass = 'bg-purple-100 text-purple-800'; break;
                                                case 'social-media': $badgeClass = 'bg-blue-100 text-blue-800'; break;
                                                case 'online': $badgeClass = 'bg-blue-100 text-blue-800'; break;
                                                case 'mystreamspace': $badgeClass = 'bg-indigo-100 text-indigo-800'; break;
                                                case 'mall': $badgeClass = 'bg-orange-100 text-orange-800'; break;
                                                case 'school': $badgeClass = 'bg-green-100 text-green-800'; break;
                                                case 'hospital': $badgeClass = 'bg-red-100 text-red-800'; break;
                                                case 'street': $badgeClass = 'bg-orange-100 text-orange-800'; break;
                                                case 'village': $badgeClass = 'bg-green-100 text-green-800'; break;
                                                case 'prison': $badgeClass = 'bg-gray-100 text-gray-800'; break;
                                                case 'football': $badgeClass = 'bg-green-100 text-green-800'; break;
                                                case 'community': $badgeClass = 'bg-blue-100 text-blue-800'; break;
                                                case 'transport-station': $badgeClass = 'bg-yellow-100 text-yellow-800'; break;
                                            }
                                            echo '<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full mr-1 mb-1 ' . $badgeClass . '">';
                                            echo ucfirst(htmlspecialchars($type, ENT_QUOTES, 'UTF-8'));
                                            echo '</span>';
                                        }
                                    } else {
                                        // Single crusade type (individual/organisation registration)
                                        $badgeClass = 'bg-gray-100 text-gray-800';
                                        switch(strtolower($crusadeType)) {
                                            case 'online': $badgeClass = 'bg-blue-100 text-blue-800'; break;
                                            case 'open': $badgeClass = 'bg-green-100 text-green-800'; break;
                                            case 'tap2read': $badgeClass = 'bg-purple-100 text-purple-800'; break;
                                            case 'mega': $badgeClass = 'bg-red-100 text-red-800'; break;
                                            case 'street': $badgeClass = 'bg-orange-100 text-orange-800'; break;
                                        }
                                        echo '<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ' . $badgeClass . '">';
                                        echo ucfirst(htmlspecialchars($crusadeType, ENT_QUOTES, 'UTF-8'));
                                        echo '</span>';
                                    }
                                    ?>
                                </td>
                                <td class="px-4 py-4 text-sm text-gray-900">
                                    <?php 
                                    // Handle multiple countries for church registrations
                                    if (isset($request['countries']) && is_string($request['countries']) && !empty($request['countries'])) {
                                        // Multiple countries stored as comma-separated string
                                        $countries = explode(',', $request['countries']);
                                        $countries = array_filter(array_map('trim', $countries)); // Clean up
                                        $displayCountries = array_slice($countries, 0, 3); // Show first 3
                                        foreach ($displayCountries as $country) {
                                            echo '<span class="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full mr-1 mb-1">';
                                            echo htmlspecialchars(ucwords(str_replace('-', ' ', $country)), ENT_QUOTES, 'UTF-8');
                                            echo '</span>';
                                        }
                                        if (count($countries) > 3) {
                                            echo '<span class="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">+' . (count($countries) - 3) . ' more</span>';
                                        }
                                    } elseif (isset($request['countries_array']) && is_array($request['countries_array']) && !empty($request['countries_array'])) {
                                        // Handle countries stored as array (fallback)
                                        $countries = $request['countries_array'];
                                        $displayCountries = array_slice($countries, 0, 3); // Show first 3
                                        foreach ($displayCountries as $country) {
                                            echo '<span class="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full mr-1 mb-1">';
                                            echo htmlspecialchars(ucwords(str_replace('-', ' ', $country)), ENT_QUOTES, 'UTF-8');
                                            echo '</span>';
                                        }
                                        if (count($countries) > 3) {
                                            echo '<span class="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">+' . (count($countries) - 3) . ' more</span>';
                                        }
                                    } else {
                                        // Single country (individual/organisation registration)
                                        $countryString = $request['country'] ?? 'N/A';
                                        echo htmlspecialchars(ucwords(str_replace('-', ' ', $countryString)), ENT_QUOTES, 'UTF-8');
                                    }
                                    ?>
                                </td>
                                <td class="px-4 py-4 text-sm text-gray-900">
                                    <?php echo htmlspecialchars($request['location'] ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?>
                                </td>
                                <td class="px-4 py-4 text-sm text-gray-900">
                                    <?php echo htmlspecialchars($request['venue'] ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo htmlspecialchars($request['attendance'] ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?>
                                </td>
                                <td class="px-4 py-4 text-sm text-gray-900">
                                    <?php echo htmlspecialchars($request['email'] ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo htmlspecialchars($request['phone'] ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo htmlspecialchars($request['kingschat_username'] ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php 
                                    $zone = $request['zone'] ?? '';
                                    $group = $request['group'] ?? '';
                                    $church = $request['church'] ?? '';
                                    $ministry = $request['ministry_name'] ?? '';
                                    $groupName = $request['group_name'] ?? '';

                                    if (!empty($zone)) {
                                        echo htmlspecialchars($zone, ENT_QUOTES, 'UTF-8');
                                    } elseif (!empty($group)) {
                                        echo htmlspecialchars($group, ENT_QUOTES, 'UTF-8');
                                    } elseif (!empty($church)) {
                                        echo htmlspecialchars($church, ENT_QUOTES, 'UTF-8');
                                    } elseif (!empty($ministry)) {
                                        echo htmlspecialchars($ministry, ENT_QUOTES, 'UTF-8');
                                    } elseif (!empty($groupName)) {
                                        echo htmlspecialchars($groupName, ENT_QUOTES, 'UTF-8');
                                    } else {
                                        echo 'N/A';
                                    }
                                    ?>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo htmlspecialchars($request['number_of_crusades'] ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo date('M j, Y', strtotime($request['created_at'])); ?>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                        <?php 
                                        switch($request['status'] ?? 'pending') {
                                            case 'approved': echo 'bg-green-100 text-green-800'; break;
                                            case 'contacted': echo 'bg-blue-100 text-blue-800'; break;
                                            case 'rejected': echo 'bg-red-100 text-red-800'; break;
                                            case 'pending': 
                                            default: echo 'bg-yellow-100 text-yellow-800';
                                        }
                                        ?>">
                                        <?php echo ucfirst($request['status'] ?? 'pending'); ?>
                                    </span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex flex-col space-y-1">
                                        <button onclick="openStatusModal(<?php echo $request['id']; ?>, '<?php echo htmlspecialchars($request['status'] ?? 'pending', ENT_QUOTES, 'UTF-8'); ?>', '<?php echo htmlspecialchars($request['admin_notes'] ?? '', ENT_QUOTES, 'UTF-8'); ?>')" 
                                                class="text-red-600 hover:text-red-900 text-left text-xs">
                                            Update Status
                                        </button>
                                        <button onclick="viewRequestDetails(<?php echo htmlspecialchars(json_encode($request, JSON_HEX_APOS | JSON_HEX_QUOT), ENT_QUOTES, 'UTF-8'); ?>)" 
                                                class="text-green-600 hover:text-green-900 text-left text-xs">
                                            View Details
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Status Update Modal -->
<div id="statusModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Update Request Status</h3>
            <form id="statusForm" method="POST" action="">
                <input type="hidden" name="action" value="update_world_status">
                <input type="hidden" name="request_id" id="modal_request_id">
                
                <div class="mb-4">
                    <label for="modal_status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select name="status" id="modal_status" required 
                            class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-red-600 focus:border-red-600">
                        <option value="pending">Pending</option>
                        <option value="approved">Approved</option>
                        <option value="contacted">Contacted</option>
                        <option value="rejected">Rejected</option>
                    </select>
                </div>
                
                <div class="mb-4">
                    <label for="modal_notes" class="block text-sm font-medium text-gray-700 mb-2">Admin Notes</label>
                    <textarea name="notes" id="modal_notes" rows="3" 
                              class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-red-600 focus:border-red-600"
                              placeholder="Enter any notes about this request..."></textarea>
                </div>
                
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeStatusModal()" 
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700">
                        Update Status
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Request Details Modal -->
<div id="detailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-4xl max-w-4xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Request Details</h3>
                <button onclick="closeDetailsModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="detailsContent">
                <!-- Details will be populated by JavaScript -->
            </div>
        </div>
    </div>
</div>

<!-- DataTables JavaScript -->
<script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.tailwindcss.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/vfs_fonts.js"></script>

<script>
// Initialize DataTable
$(document).ready(function() {
    if ($('#worldCrusadeRequestsTable').length) {
        $('#worldCrusadeRequestsTable').DataTable({
            responsive: true,
            pageLength: 25,
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
            order: [[0, 'desc']], // Order by ID descending (newest first)
            dom: '<"flex flex-wrap items-center justify-between mb-4"<"flex items-center"l><"flex items-center space-x-2"Bf>>t<"flex flex-wrap items-center justify-between mt-4"<"flex items-center"i><"flex"p>>',
            buttons: [
                {
                    extend: 'excel',
                    text: '<i class="fas fa-file-excel mr-2"></i>Excel',
                    className: 'dt-button',
                    exportOptions: {
                        columns: [0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16] // Exclude actions column
                    }
                },
                {
                    extend: 'pdf',
                    text: '<i class="fas fa-file-pdf mr-2"></i>PDF',
                    className: 'dt-button',
                    orientation: 'landscape',
                    pageSize: 'A3',
                    exportOptions: {
                        columns: [0,1,2,4,5,6,7,12,13,14] // Select key columns for PDF (excluding Status and Date)
                    }
                },
                {
                    extend: 'print',
                    text: '<i class="fas fa-print mr-2"></i>Print',
                    className: 'dt-button',
                    exportOptions: {
                        columns: [0,1,2,4,5,6,7,12,13,14] // Select key columns for print (excluding Status and Date)
                    }
                }
            ],
            language: {
                search: "_INPUT_",
                searchPlaceholder: "Search requests...",
                lengthMenu: "Show _MENU_ entries",
                info: "Showing _START_ to _END_ of _TOTAL_ entries",
                infoEmpty: "Showing 0 to 0 of 0 entries",
                infoFiltered: "(filtered from _MAX_ total entries)",
                zeroRecords: "No matching records found",
                emptyTable: "No data available in table",
                paginate: {
                    first: "First",
                    last: "Last",
                    next: "Next",
                    previous: "Previous"
                }
            },
                         columnDefs: [
                 {
                     targets: [17], // Actions column
                     orderable: false,
                     searchable: false
                 }
             ]
        });
    }
});

function openStatusModal(requestId, currentStatus, currentNotes) {
    document.getElementById('modal_request_id').value = requestId;
    document.getElementById('modal_status').value = currentStatus;
    document.getElementById('modal_notes').value = currentNotes;
    document.getElementById('statusModal').classList.remove('hidden');
}

function closeStatusModal() {
    document.getElementById('statusModal').classList.add('hidden');
}

function viewRequestDetails(request) {
    const content = `
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h4 class="font-medium text-gray-900 mb-3">Personal Information</h4>
                <div class="space-y-2 text-sm">
                    <div><span class="font-medium">Name:</span> ${request.designation ? request.designation + ' ' : ''}${request.first_name || 'N/A'} ${request.last_name || ''}</div>
                    <div><span class="font-medium">Email:</span> ${request.email || 'N/A'}</div>
                    <div><span class="font-medium">Phone:</span> ${request.phone || 'N/A'}</div>
                    <div><span class="font-medium">KingsChat Username:</span> ${request.kingschat_username || 'N/A'}</div>
                    <div><span class="font-medium">Address:</span> ${request.address || request.contact_address || 'N/A'}</div>
                </div>
            </div>
            
            <div>
                <h4 class="font-medium text-gray-900 mb-3">Organization Information</h4>
                <div class="space-y-2 text-sm">
                    <div><span class="font-medium">Organization:</span> ${request.organization_name || 'N/A'}</div>
                    <div><span class="font-medium">Designation:</span> ${request.designation || 'N/A'}</div>
                    <div><span class="font-medium">Zone:</span> ${request.zone || 'N/A'}</div>
                    <div><span class="font-medium">Group:</span> ${request.group || request.group_name || 'N/A'}</div>
                    <div><span class="font-medium">Church:</span> ${request.church || request.church_name || 'N/A'}</div>
                    <div><span class="font-medium">Ministry:</span> ${request.ministry_name || 'N/A'}</div>
                    <div><span class="font-medium">Church Type:</span> ${request.church_type || 'N/A'}</div>
                </div>
            </div>
            
            <div>
                <h4 class="font-medium text-gray-900 mb-3">Crusade Details</h4>
                <div class="space-y-2 text-sm">
                    <div><span class="font-medium">Title:</span> ${request.crusade_title || 'N/A'}</div>
                    <div><span class="font-medium">Type:</span> 
                        ${(() => {
                            if (Array.isArray(request.crusade_types) && request.crusade_types.length > 0) {
                                // Handle array of crusade types (church registration)
                                return request.crusade_types.map(type => type.charAt(0).toUpperCase() + type.slice(1).replace(/-/g, ' ')).join(', ');
                            } else {
                                // Single crusade type (individual/organisation registration)
                                const type = request.crusade_type || 'N/A';
                                return type.charAt(0).toUpperCase() + type.slice(1).replace(/-/g, ' ');
                            }
                        })()}
                    </div>
                    <div><span class="font-medium">Venue:</span> ${request.venue || 'N/A'}</div>
                    <div><span class="font-medium">Expected Attendance:</span> ${request.attendance || 'N/A'}</div>
                    ${request.number_of_crusades ? `<div><span class="font-medium">Number of Crusades:</span> ${request.number_of_crusades}</div>` : ''}
                    ${request.countries ? `<div><span class="font-medium">Countries:</span> ${Array.isArray(request.countries) ? request.countries.join(', ') : request.countries}</div>` : ''}
                    ${request.countries_string ? `<div><span class="font-medium">Countries:</span> ${request.countries_string}</div>` : ''}
                </div>
            </div>
            
            <div>
                <h4 class="font-medium text-gray-900 mb-3">Location</h4>
                <div class="space-y-2 text-sm">
                    <div><span class="font-medium">Country:</span> 
                        ${(() => {
                            if (request.countries && typeof request.countries === 'string' && request.countries.trim() !== '') {
                                // Handle comma-separated string
                                const countries = request.countries.split(',').map(c => c.trim()).filter(c => c !== '');
                                return countries.map(country => country.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())).join(', ');
                            } else if (Array.isArray(request.countries_array) && request.countries_array.length > 0) {
                                // Handle array format
                                return request.countries_array.map(country => country.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())).join(', ');
                            } else {
                                // Single country fallback
                                const country = request.country || 'N/A';
                                return country.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                            }
                        })()}
                    </div>
                    <div><span class="font-medium">City/Location:</span> ${request.location || 'N/A'}</div>
                </div>
            </div>
            
            ${request.comments || request.additional_comments ? `
            <div class="md:col-span-2">
                <h4 class="font-medium text-gray-900 mb-3">Comments</h4>
                <div class="space-y-2 text-sm">
                    ${request.comments ? `<div><span class="font-medium">Comments:</span> ${request.comments}</div>` : ''}
                    ${request.additional_comments ? `<div><span class="font-medium">Additional Comments:</span> ${request.additional_comments}</div>` : ''}
                </div>
            </div>
            ` : ''}
            
            <div class="md:col-span-2">
                <h4 class="font-medium text-gray-900 mb-3">Request Information</h4>
                <div class="space-y-2 text-sm">
                    <div><span class="font-medium">Registration Type:</span> <span class="capitalize">${request.registration_type || 'individual'}</span></div>
                    <div><span class="font-medium">Status:</span> <span class="capitalize">${request.status || 'pending'}</span></div>
                    <div><span class="font-medium">Created:</span> ${new Date(request.created_at).toLocaleString()}</div>
                    <div><span class="font-medium">Last Updated:</span> ${new Date(request.updated_at).toLocaleString()}</div>
                    ${request.admin_notes ? `<div><span class="font-medium">Admin Notes:</span> ${request.admin_notes}</div>` : ''}
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('detailsContent').innerHTML = content;
    document.getElementById('detailsModal').classList.remove('hidden');
}

function closeDetailsModal() {
    document.getElementById('detailsModal').classList.add('hidden');
}

// Close modals when clicking outside
window.onclick = function(event) {
    const statusModal = document.getElementById('statusModal');
    const detailsModal = document.getElementById('detailsModal');
    
    if (event.target === statusModal) {
        closeStatusModal();
    }
    if (event.target === detailsModal) {
        closeDetailsModal();
    }
}
</script> 