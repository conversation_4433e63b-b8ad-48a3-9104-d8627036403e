<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Load configuration
$config = require_once '../config/geonames.php';

$query = $_GET['q'] ?? '';
$maxRows = min((int)($_GET['maxRows'] ?? 10), 20); // Limit to 20 max

if (strlen($query) < 2) {
    echo json_encode(['cities' => [], 'message' => 'Query too short']);
    exit;
}

$query = htmlspecialchars(trim($query), ENT_QUOTES, 'UTF-8');

// Create cache directory if it doesn't exist
$cacheDir = $config['cache']['directory'];
if (!file_exists($cacheDir)) {
    mkdir($cacheDir, 0755, true);
}

// Check cache first
$cacheKey = md5($query . '_' . $maxRows);
$cacheFile = $cacheDir . $cacheKey . '.json';

if ($config['cache']['enabled'] && file_exists($cacheFile)) {
    $cacheAge = time() - filemtime($cacheFile);
    if ($cacheAge < $config['cache']['duration']) {
        $cachedData = file_get_contents($cacheFile);
        if ($cachedData) {
            echo $cachedData;
            exit;
        }
    }
}

// Make API request to GeoNames
$apiUrl = $config['api_url'] . '?' . http_build_query([
    'q' => $query,
    'maxRows' => $maxRows,
    'featureClass' => $config['defaults']['feature_class'],
    'style' => $config['defaults']['style'],
    'orderby' => $config['defaults']['order_by'],
    'username' => $config['username']
]);

$context = stream_context_create([
    'http' => [
        'timeout' => 10,
        'user_agent' => 'Crusades Registration System'
    ]
]);

$response = @file_get_contents($apiUrl, false, $context);

if ($response === false) {
    echo json_encode(['error' => 'Failed to connect to GeoNames API', 'cities' => []]);
    exit;
}

$data = json_decode($response, true);

if (!$data) {
    echo json_encode(['error' => 'Invalid response from GeoNames API', 'cities' => []]);
    exit;
}

// Check for GeoNames API errors
if (isset($data['status'])) {
    $errorMsg = $data['status']['message'] ?? 'Unknown API error';
    echo json_encode(['error' => 'GeoNames API Error: ' . $errorMsg, 'cities' => []]);
    exit;
}

$cities = [];
if (isset($data['geonames']) && is_array($data['geonames'])) {
    foreach ($data['geonames'] as $city) {
        $cities[] = [
            'name' => $city['name'] ?? '',
            'country' => $city['countryName'] ?? '',
            'countryCode' => $city['countryCode'] ?? '',
            'admin1' => $city['adminName1'] ?? '',
            'population' => $city['population'] ?? 0,
            'latitude' => $city['lat'] ?? '',
            'longitude' => $city['lng'] ?? '',
            'geonameId' => $city['geonameId'] ?? '',
            'timezone' => $city['timezone']['timeZoneId'] ?? ''
        ];
    }
    
    // Sort by population (descending)
    usort($cities, function($a, $b) {
        return ($b['population'] ?? 0) - ($a['population'] ?? 0);
    });
}

$result = [
    'cities' => $cities,
    'query' => $query,
    'total' => count($cities),
    'source' => 'geonames_api'
];

$jsonResult = json_encode($result);

// Cache the result
if ($config['cache']['enabled'] && !empty($cities)) {
    file_put_contents($cacheFile, $jsonResult);
}

echo $jsonResult; 