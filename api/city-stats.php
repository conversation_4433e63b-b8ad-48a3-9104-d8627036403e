<?php
header('Content-Type: application/json');
require_once '../config/geonames.php';

function getCityStats() {
    global $geonames_username;
    
    $cache_file = '../cache/city_stats.json';
    $cache_expiry = 24 * 60 * 60; // 24 hours

    // Check if we have valid cached data
    if (file_exists($cache_file)) {
        $cache_data = json_decode(file_get_contents($cache_file), true);
        if ($cache_data && isset($cache_data['timestamp']) && (time() - $cache_data['timestamp'] < $cache_expiry)) {
            return $cache_data;
        }
    }

    // Get total cities from GeoNames
    $url = "http://api.geonames.org/searchJSON?featureClass=P&maxRows=0&username=" . $geonames_username;
    
    try {
        $response = file_get_contents($url);
        if ($response === false) {
            throw new Exception("Failed to fetch data from GeoNames");
        }
        
        $data = json_decode($response, true);
        if (!$data || !isset($data['totalResultsCount'])) {
            throw new Exception("Invalid response from GeoNames");
        }

        // Get registered cities count
        $registrations_file = '../notc/data.json';
        $registered_cities = 0;
        if (file_exists($registrations_file)) {
            $registrations = json_decode(file_get_contents($registrations_file), true);
            if ($registrations && isset($registrations['registrations'])) {
                foreach ($registrations['registrations'] as $reg) {
                    if (isset($reg['cities_count'])) {
                        $registered_cities += intval($reg['cities_count']);
                    }
                }
            }
        }

        $stats = [
            'total_cities' => $data['totalResultsCount'],
            'registered_cities' => $registered_cities,
            'available_cities' => $data['totalResultsCount'] - $registered_cities,
            'timestamp' => time()
        ];

        // Cache the results
        if (!file_exists('../cache')) {
            mkdir('../cache', 0777, true);
        }
        file_put_contents($cache_file, json_encode($stats));

        return $stats;
    } catch (Exception $e) {
        // Fallback to cached data if available
        if (file_exists($cache_file)) {
            return json_decode(file_get_contents($cache_file), true);
        }
        
        // Return error if no cache available
        http_response_code(500);
        return ['error' => $e->getMessage()];
    }
}

echo json_encode(getCityStats()); 