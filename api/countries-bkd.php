<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');

// Include the countries data file
$countriesData = file_get_contents(__DIR__ . '/../data/countriesbkd.json');
$countries = json_decode($countriesData, true)['countries'];

// Optional filtering by continent
$continent = isset($_GET['continent']) ? $_GET['continent'] : null;
if ($continent) {
    $countries = array_filter($countries, function($country) use ($continent) {
        return strtolower($country['continent']) === strtolower($continent);
    });
}

// Optional search by name
$search = isset($_GET['search']) ? $_GET['search'] : null;
if ($search) {
    $countries = array_filter($countries, function($country) use ($search) {
        return stripos($country['name'], $search) !== false;
    });
}

echo json_encode([
    'success' => true,
    'total' => count($countries),
    'results' => array_values($countries)
]); 