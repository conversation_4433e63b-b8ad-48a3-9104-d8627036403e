<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Simple country search API
$countries = [
    'nigeria' => ['name' => 'Nigeria', 'continent' => 'Africa'],
    'venezuela' => ['name' => 'Venezuela', 'continent' => 'South America'],
    'india' => ['name' => 'India', 'continent' => 'Asia'],
    'usa' => ['name' => 'United States', 'continent' => 'North America'],
    'united states' => ['name' => 'United States', 'continent' => 'North America'],
    'uk' => ['name' => 'United Kingdom', 'continent' => 'Europe'],
    'united kingdom' => ['name' => 'United Kingdom', 'continent' => 'Europe'],
    'germany' => ['name' => 'Germany', 'continent' => 'Europe'],
    'france' => ['name' => 'France', 'continent' => 'Europe'],
    'italy' => ['name' => 'Italy', 'continent' => 'Europe'],
    'spain' => ['name' => 'Spain', 'continent' => 'Europe'],
    'canada' => ['name' => 'Canada', 'continent' => 'North America'],
    'australia' => ['name' => 'Australia', 'continent' => 'Oceania'],
    'brazil' => ['name' => 'Brazil', 'continent' => 'South America'],
    'argentina' => ['name' => 'Argentina', 'continent' => 'South America'],
    'chile' => ['name' => 'Chile', 'continent' => 'South America'],
    'colombia' => ['name' => 'Colombia', 'continent' => 'South America'],
    'peru' => ['name' => 'Peru', 'continent' => 'South America'],
    'south africa' => ['name' => 'South Africa', 'continent' => 'Africa'],
    'kenya' => ['name' => 'Kenya', 'continent' => 'Africa'],
    'ghana' => ['name' => 'Ghana', 'continent' => 'Africa'],
    'egypt' => ['name' => 'Egypt', 'continent' => 'Africa'],
    'china' => ['name' => 'China', 'continent' => 'Asia'],
    'japan' => ['name' => 'Japan', 'continent' => 'Asia'],
    'south korea' => ['name' => 'South Korea', 'continent' => 'Asia'],
    'thailand' => ['name' => 'Thailand', 'continent' => 'Asia'],
    'singapore' => ['name' => 'Singapore', 'continent' => 'Asia'],
    'malaysia' => ['name' => 'Malaysia', 'continent' => 'Asia'],
    'philippines' => ['name' => 'Philippines', 'continent' => 'Asia'],
    'indonesia' => ['name' => 'Indonesia', 'continent' => 'Asia'],
    'russia' => ['name' => 'Russia', 'continent' => 'Europe'],
    'mexico' => ['name' => 'Mexico', 'continent' => 'North America'],
    'new zealand' => ['name' => 'New Zealand', 'continent' => 'Oceania']
];

$query = $_GET['q'] ?? '';
$query = strtolower(trim($query));

if (empty($query)) {
    echo json_encode(['error' => 'Query parameter required']);
    exit;
}

// Search for countries that match the query
$results = [];
foreach ($countries as $key => $country) {
    if (strpos($key, $query) !== false || strpos(strtolower($country['name']), $query) !== false) {
        $results[] = [
            'name' => $country['name'],
            'continent' => $country['continent'],
            'match_key' => $key
        ];
    }
}

// Limit to top 10 results
$results = array_slice($results, 0, 10);

echo json_encode([
    'query' => $query,
    'results' => $results,
    'count' => count($results)
], JSON_PRETTY_PRINT);
?>
