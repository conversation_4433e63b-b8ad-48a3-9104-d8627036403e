<?php
// Bulk update status for world crusade requests
header('Content-Type: application/json');

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Get POST data
$ids = $_POST['ids'] ?? [];
$status = $_POST['status'] ?? '';

// Validate input
if (empty($ids) || !is_array($ids)) {
    echo json_encode(['success' => false, 'message' => 'No IDs provided']);
    exit;
}

if (!in_array($status, ['pending', 'approved', 'contacted', 'rejected'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid status']);
    exit;
}

// Sanitize IDs (ensure they are integers)
$ids = array_map('intval', $ids);
$ids = array_filter($ids, function($id) { return $id > 0; });

if (empty($ids)) {
    echo json_encode(['success' => false, 'message' => 'No valid IDs provided']);
    exit;
}

try {
    // Load current data
    $dataFile = __DIR__ . '/data/world_crusade_requests.json';
    
    if (!file_exists($dataFile)) {
        echo json_encode(['success' => false, 'message' => 'Data file not found']);
        exit;
    }
    
    $jsonData = file_get_contents($dataFile);
    $data = json_decode($jsonData, true);
    
    if (!$data || !isset($data['requests'])) {
        echo json_encode(['success' => false, 'message' => 'Invalid data format']);
        exit;
    }
    
    $updated = 0;
    $currentTime = date('Y-m-d H:i:s');
    
    // Update matching requests
    foreach ($data['requests'] as &$request) {
        if (in_array($request['id'], $ids)) {
            $request['status'] = $status;
            $request['updated_at'] = $currentTime;
            $updated++;
        }
    }
    
    if ($updated > 0) {
        // Create backup
        $backupFile = $dataFile . '.backup.' . date('Y-m-d-H-i-s');
        copy($dataFile, $backupFile);
        
        // Save updated data
        $newJsonData = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        
        if (file_put_contents($dataFile, $newJsonData) !== false) {
            echo json_encode([
                'success' => true, 
                'updated' => $updated,
                'message' => "Successfully updated {$updated} request(s) to '{$status}' status"
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to save data']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'No matching requests found']);
    }
    
} catch (Exception $e) {
    error_log('Bulk update error: ' . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Server error occurred']);
}
?>