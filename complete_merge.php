<?php

function completeNetworkMerge() {
    // File paths
    $transformedFile = 'migrate_transformed.json';
    $notcDataFile = 'notc/data.json';
    
    echo "Starting complete merge process...\n";
    
    // Restore original notc/data.json from backup
    echo "Restoring original notc/data.json from backup...\n";
    if (file_exists('notc/data.json.backup')) {
        copy('notc/data.json.backup', $notcDataFile);
        echo "Original data restored.\n";
    } else {
        echo "Warning: No backup found, using current data.\n";
    }
    
    // Read the transformed network data
    echo "Reading transformed network data...\n";
    $networkContent = file_get_contents($transformedFile);
    $networkData = json_decode($networkContent, true);
    
    if (!$networkData || !isset($networkData['registrations'])) {
        die("Error: Could not read transformed network data\n");
    }
    
    $networkRegistrations = $networkData['registrations'];
    echo "Found " . count($networkRegistrations) . " network registrations\n";
    
    // Read existing church data from notc/data.json
    echo "Reading existing church data...\n";
    $churchContent = file_get_contents($notcDataFile);
    $churchData = json_decode($churchContent, true);
    
    if (!$churchData || !isset($churchData['registrations'])) {
        die("Error: Could not read church data from notc/data.json\n");
    }
    
    $churchRegistrations = $churchData['registrations'];
    echo "Found " . count($churchRegistrations) . " existing church registrations\n";
    
    // Merge the data - church registrations first, then network registrations
    $mergedRegistrations = array_merge($churchRegistrations, $networkRegistrations);
    
    // Create final merged structure
    $mergedData = [
        'registrations' => $mergedRegistrations
    ];
    
    echo "Total registrations after merge: " . count($mergedRegistrations) . "\n";
    
    // Create a new backup before writing
    $backupFile = 'notc/data_backup_complete_' . date('Y-m-d_H-i-s') . '.json';
    copy($notcDataFile, $backupFile);
    echo "Backup created: $backupFile\n";
    
    // Write merged data back to notc/data.json
    echo "Writing complete merged data to notc/data.json...\n";
    $success = file_put_contents($notcDataFile, json_encode($mergedData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    
    if ($success) {
        echo "SUCCESS: Complete merge completed successfully!\n";
        echo "- Church registrations: " . count($churchRegistrations) . "\n";
        echo "- Network registrations: " . count($networkRegistrations) . "\n";
        echo "- Total registrations: " . count($mergedRegistrations) . "\n";
        echo "- Backup saved as: $backupFile\n";
    } else {
        echo "ERROR: Failed to write merged data to file\n";
    }
}

// Run the complete merge
if (php_sapi_name() === 'cli' || !empty($_GET['run'])) {
    completeNetworkMerge();
}

?>