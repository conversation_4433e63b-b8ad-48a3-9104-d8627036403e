<?php
/**
 * GeoNames API Configuration
 * 
 * To use the cities autocomplete feature, you need a free GeoNames account:
 * 1. Go to https://www.geonames.org/login
 * 2. Click "create a new user account" 
 * 3. Fill in the registration form
 * 4. Verify your email address
 * 5. Go to https://www.geonames.org/manageaccount and enable web services
 * 6. Replace 'demo' below with your username
 */

// GeoNames API Configuration
return [
    // Your GeoNames username (replace 'demo' with your actual username)
    'username' => 'maxwellvn',
    
    // API endpoint (usually no need to change this)
    'api_url' => 'http://api.geonames.org/searchJSON',
    
    // Default settings
    'defaults' => [
        'max_rows' => 10,
        'feature_class' => 'P', // P = populated places (cities, towns, villages)
        'style' => 'FULL', // FULL = detailed information
        'order_by' => 'population'
    ],
    
    // Cache settings
    'cache' => [
        'enabled' => true,
        'duration' => 24 * 60 * 60, // 24 hours in seconds
        'directory' => __DIR__ . '/../cache/cities/'
    ]
];

/**
 * Alternative: You can also set the username as an environment variable
 * 
 * Method 1: In your .htaccess file:
 * SetEnv GEONAMES_USERNAME your_username_here
 * 
 * Method 2: In your web server configuration
 * 
 * Method 3: In a .env file (if you're using vlucas/phpdotenv)
 * GEONAMES_USERNAME=your_username_here
 */
?> 