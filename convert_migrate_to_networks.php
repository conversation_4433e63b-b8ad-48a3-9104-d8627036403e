<?php

// Read migrate.json
$migrateContent = file_get_contents('/Applications/XAMPP/xamppfiles/htdocs/crusades/migrate.json');
$lines = explode("\n", trim($migrateContent));

// Initialize networks structure
$networks = [
    "registrations" => []
];

// Process each line (skip header)
for ($i = 1; $i < count($lines); $i++) {
    $line = trim($lines[$i]);
    if (empty($line)) continue;
    
    // Split by tab
    $fields = explode("\t", $line);
    
    // Skip if not enough fields
    if (count($fields) < 13) continue;
    
    // Map fields based on the header structure
    $registrationType = trim($fields[0]);
    $name = trim($fields[1]);
    $organizationName = trim($fields[2]);
    $designation = trim($fields[3]);
    $email = trim($fields[4]);
    $phone = trim($fields[5]);
    $zone = trim($fields[6]);
    $group = trim($fields[7]);
    $church = trim($fields[8]);
    $churchType = trim($fields[9]);
    $ministryName = trim($fields[10]);
    $crusadeTitle = trim($fields[11]);
    $country = trim($fields[12]);
    $city = isset($fields[13]) ? trim($fields[13]) : '';
    $crusadeType = isset($fields[14]) ? trim($fields[14]) : '';
    $numberOfCrusades = isset($fields[15]) ? trim($fields[15]) : '1';
    $venue = isset($fields[16]) ? trim($fields[16]) : '';
    $expectedAttendance = isset($fields[17]) ? trim($fields[17]) : '';
    $comments = isset($fields[18]) ? trim($fields[18]) : '';
    $additionalComments = isset($fields[19]) ? trim($fields[19]) : '';
    
    // Skip if registration type is not "Network"
    if (strtolower($registrationType) !== 'network') continue;
    
    // Generate unique ID
    $id = "network_" . uniqid();
    
    // Split name into first and last name
    $nameParts = explode(' ', $name, 2);
    $firstName = $nameParts[0];
    $lastName = isset($nameParts[1]) ? $nameParts[1] : '';
    
    // Clean up country name for consistent formatting
    $countryLower = strtolower(str_replace([' ', '-'], ['_', '_'], $country));
    
    // Map crusade type to array format
    $crusadeTypes = [];
    if (!empty($crusadeType)) {
        $crusadeTypeMap = [
            'OPEN' => 'open',
            'Mega Crusade' => 'mega',
            'City Crusade' => 'city',
            'Crusade' => 'crusade',
            'ONLINE' => 'online'
        ];
        
        $crusadeTypes[] = isset($crusadeTypeMap[$crusadeType]) ? $crusadeTypeMap[$crusadeType] : strtolower(str_replace(' ', '-', $crusadeType));
    }
    
    // Parse expected attendance
    $expectedAttendanceNumeric = 0;
    if (!empty($expectedAttendance)) {
        if (strpos($expectedAttendance, '-') !== false) {
            $parts = explode('-', $expectedAttendance);
            $expectedAttendanceNumeric = intval(str_replace(',', '', $parts[0]));
        } else {
            $expectedAttendanceNumeric = intval(str_replace(',', '', $expectedAttendance));
        }
    }
    
    // Create selected cities data if city is provided
    $selectedCitiesData = [];
    if (!empty($city) && !empty($country)) {
        $selectedCitiesData[] = [
            "name" => $city,
            "country" => $country,
            "countryCode" => getCountryCode($country),
            "admin1" => "",
            "latitude" => 0,
            "longitude" => 0
        ];
    }
    
    // Create registration timestamp (current time as placeholder)
    $timestamp = time();
    
    // Build the network registration record
    $registration = [
        "id" => $id,
        "type" => "network",
        "registration_type" => "network",
        "network_type" => "network",
        "zone" => $zone ?: "",
        "network_group_name" => $group ?: "",
        "network_name" => $organizationName ?: "",
        "organization_name" => $organizationName ?: "",
        "first_name" => $firstName,
        "last_name" => $lastName,
        "designation" => $designation,
        "email" => $email ?: "",
        "phone_country_code" => "+1", // Default, should be updated based on country
        "phone" => $phone ?: "",
        "selected_countries" => $countryLower,
        "crusade_types" => $crusadeTypes,
        "other_crusade_types" => "",
        "selected_cities_data" => $selectedCitiesData,
        "number_of_crusades" => $numberOfCrusades,
        "expected_attendance" => $expectedAttendance ?: "unsure",
        "venue" => $venue,
        "crusade_title" => $crusadeTitle,
        "ministry_name" => $ministryName,
        "additional_comments" => $comments . ' ' . $additionalComments,
        "status" => "approved",
        "registration_date" => date('Y-m-d H:i:s', $timestamp),
        "registration_timestamp" => $timestamp,
        "ip_address" => "0.0.0.0",
        "user_agent" => "Migration Script",
        "form_source" => "network_migration",
        "submission_method" => "MIGRATION",
        "full_name" => trim($firstName . ' ' . $lastName),
        "full_phone" => "+1" . $phone, // Default country code
        "phone_display" => "+1" . $phone,
        "registration_date_formatted" => date('F j, Y g:i A', $timestamp),
        "registration_date_iso" => date('c', $timestamp),
        "display_name" => $organizationName ?: $name,
        "primary_identifier" => "Network: " . ($organizationName ?: $name),
        "selected_countries_list" => [$countryLower],
        "countries_count" => 1,
        "countries_display" => "1 countries: " . $countryLower,
        "cities_count" => count($selectedCitiesData),
        "cities_display" => count($selectedCitiesData) > 0 ? "1 cities: " . $city . " (" . $country . ")" : "No specific cities selected",
        "cities_list" => count($selectedCitiesData) > 0 ? [$city] : [],
        "cities_by_country" => count($selectedCitiesData) > 0 ? [$country => [$city]] : [],
        "crusade_types_string" => implode(', ', $crusadeTypes),
        "crusade_types_display" => implode(', ', $crusadeTypes),
        "expected_attendance_numeric" => $expectedAttendanceNumeric,
        "server_info" => [
            "server_name" => "rhapsodycrusades.org",
            "request_uri" => "/network/migration.php",
            "http_host" => "rhapsodycrusades.org"
        ]
    ];
    
    $networks["registrations"][] = $registration;
}

// Function to get country code (simplified mapping)
function getCountryCode($country) {
    $countryCodes = [
        'Ecuador' => 'EC',
        'Venezuela' => 'VE',
        'Honduras' => 'HN',
        'Guatemala' => 'GT',
        'Colombia' => 'CO',
        'Nicaragua' => 'NI',
        'Burundi' => 'BI',
        'Gabon' => 'GA',
        'Costa Rica' => 'CR',
        'Pakistan' => 'PK',
        'Cambodia' => 'KH',
        'Afghanistan' => 'AF',
        'Albania' => 'AL',
        'Algeria' => 'DZ',
        'American Samoa (US)' => 'AS',
        'Andorra' => 'AD',
        'Angola' => 'AO',
        'Nigeria' => 'NG'
    ];
    
    return isset($countryCodes[$country]) ? $countryCodes[$country] : 'XX';
}

// Write the converted data to a new file
$outputFile = '/Applications/XAMPP/xamppfiles/htdocs/crusades/networks_data.json';
file_put_contents($outputFile, json_encode($networks, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

echo "Migration completed! Networks data has been written to: " . $outputFile . "\n";
echo "Total networks converted: " . count($networks["registrations"]) . "\n";

?>