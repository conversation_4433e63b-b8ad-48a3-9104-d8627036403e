<?php
require_once 'includes/config.php';
require_once 'includes/languages.php';
require_once 'includes/TranslationService.php';

// Get registration type from URL parameter
$registrationType = isset($_GET['type']) ? $_GET['type'] : 'individual';

// Validate registration type
$validTypes = ['individual', 'church', 'organisation'];
if (!in_array($registrationType, $validTypes)) {
    $registrationType = 'individual';
}

// Get translation service
$translationService = TranslationService::getInstance();
$currentLanguage = Language::getCurrentLanguage();

include 'includes/header.php';
?>

<div class="min-h-screen bg-gray-50 py-8 sm:py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6">
        <!-- Header -->
        <div class="text-center mb-8 sm:mb-12">
            <h1 class="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                <?php 
                if ($currentLanguage === 'en') {
                    echo 'Host Your Own Crusade';
                } else {
                    echo $translationService->translateText('Host Your Own Crusade', 'en', $currentLanguage) ?? 'Host Your Own Crusade';
                }
                ?>
            </h1>
            <div class="w-16 h-px bg-gradient-to-r from-primary via-accent to-gold mx-auto mb-6"></div>
            
            <!-- Registration Type Indicator -->
            <div class="inline-flex items-center px-4 py-2 bg-primary bg-opacity-10 rounded-full">
                <span class="text-sm font-medium text-primary">
                    <?php 
                    switch($registrationType) {
                        case 'individual':
                            echo $currentLanguage === 'en' ? 'Individual Registration' : ($translationService->translateText('Individual Registration', 'en', $currentLanguage) ?? 'Individual Registration');
                            break;
                        case 'church':
                            echo $currentLanguage === 'en' ? 'Church/Group/Zone Registration' : ($translationService->translateText('Church/Group/Zone Registration', 'en', $currentLanguage) ?? 'Church/Group/Zone Registration');
                            break;
                        case 'organisation':
                            echo $currentLanguage === 'en' ? 'Organisation/Network Registration' : ($translationService->translateText('Organisation/Network Registration', 'en', $currentLanguage) ?? 'Organisation/Network Registration');
                            break;
                    }
                    ?>
                </span>
            </div>
        </div>

        <!-- Registration Form -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <?php
            // Display errors if any
            if (isset($_SESSION['errors']) && !empty($_SESSION['errors'])) {
                echo '<div class="bg-red-50 border border-red-200 rounded-lg p-4 m-6 mb-0">';
                echo '<div class="flex">';
                echo '<div class="flex-shrink-0">';
                echo '<svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">';
                echo '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />';
                echo '</svg>';
                echo '</div>';
                echo '<div class="ml-3">';
                echo '<h3 class="text-sm font-medium text-red-800">Please correct the following errors:</h3>';
                echo '<div class="mt-2 text-sm text-red-700">';
                echo '<ul class="list-disc pl-5 space-y-1">';
                foreach ($_SESSION['errors'] as $error) {
                    echo '<li>' . htmlspecialchars($error) . '</li>';
                }
                echo '</ul>';
                echo '</div>';
                echo '</div>';
                echo '</div>';
                echo '</div>';
                unset($_SESSION['errors']);
            }
            ?>
            <form id="crusadeRegistrationForm" method="POST" action="register-handler.php" class="space-y-8 p-6 sm:p-8">
                <!-- Hidden field for registration type -->
                <input type="hidden" name="registration_type" value="<?php echo htmlspecialchars($registrationType); ?>">
                
                <!-- Personal Information Section (Only for Individual) -->
                <?php if ($registrationType === 'individual'): ?>
                <div class="registration-section" id="personalSection">
                    <div class="flex items-center mb-6">
                        <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                        <h2 class="text-xl font-semibold text-gray-900">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Personal Information';
                            } else {
                                echo $translationService->translateText('Personal Information', 'en', $currentLanguage) ?? 'Personal Information';
                            }
                            ?>
                        </h2>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="firstName" class="block text-sm font-medium text-gray-700 mb-2">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'First Name';
                                } else {
                                    echo $translationService->translateText('First Name', 'en', $currentLanguage) ?? 'First Name';
                                }
                                ?> <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="firstName" name="first_name" required 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200"
                                   placeholder="<?php echo $currentLanguage === 'en' ? 'Enter your first name' : ($translationService->translateText('Enter your first name', 'en', $currentLanguage) ?? 'Enter your first name'); ?>">
                        </div>

                        <div>
                            <label for="lastName" class="block text-sm font-medium text-gray-700 mb-2">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Last Name';
                                } else {
                                    echo $translationService->translateText('Last Name', 'en', $currentLanguage) ?? 'Last Name';
                                }
                                ?> <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="lastName" name="last_name" required 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200"
                                   placeholder="<?php echo $currentLanguage === 'en' ? 'Enter your last name' : ($translationService->translateText('Enter your last name', 'en', $currentLanguage) ?? 'Enter your last name'); ?>">
                        </div>

                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Email Address';
                                } else {
                                    echo $translationService->translateText('Email Address', 'en', $currentLanguage) ?? 'Email Address';
                                }
                                ?> <span class="text-red-500">*</span>
                            </label>
                            <input type="email" id="email" name="email" required 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200"
                                   placeholder="<?php echo $currentLanguage === 'en' ? 'Enter your email address' : ($translationService->translateText('Enter your email address', 'en', $currentLanguage) ?? 'Enter your email address'); ?>">
                        </div>

                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Phone Number';
                                } else {
                                    echo $translationService->translateText('Phone Number', 'en', $currentLanguage) ?? 'Phone Number';
                                }
                                ?> <span class="text-red-500">*</span>
                            </label>
                            <div class="flex">
                                <select id="country-code" name="country_code" class="w-32 px-2 py-3 border border-gray-300 rounded-l-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200 border-r-0" required>
                                    <option value="+1" data-country="US">+1</option>
                                    <option value="+1" data-country="CA">+1</option>
                                    <option value="******" data-country="BS">******</option>
                                    <option value="******" data-country="BB">******</option>
                                    <option value="******" data-country="AI">******</option>
                                    <option value="******" data-country="AG">******</option>
                                    <option value="+1 284" data-country="VG">+1 284</option>
                                    <option value="+1 340" data-country="VI">+1 340</option>
                                    <option value="+1 345" data-country="KY">+1 345</option>
                                    <option value="+1 441" data-country="BM">+1 441</option>
                                    <option value="+1 473" data-country="GD">+1 473</option>
                                    <option value="+1 649" data-country="TC">+1 649</option>
                                    <option value="+1 664" data-country="MS">+1 664</option>
                                    <option value="+1 670" data-country="MP">+1 670</option>
                                    <option value="+1 671" data-country="GU">+1 671</option>
                                    <option value="+1 684" data-country="AS">+1 684</option>
                                    <option value="+1 758" data-country="LC">+1 758</option>
                                    <option value="+1 767" data-country="DM">+1 767</option>
                                    <option value="+1 784" data-country="VC">+1 784</option>
                                    <option value="+1 787" data-country="PR">+1 787</option>
                                    <option value="+1 809" data-country="DO">+1 809</option>
                                    <option value="+1 868" data-country="TT">+1 868</option>
                                    <option value="+1 869" data-country="KN">+1 869</option>
                                    <option value="+1 876" data-country="JM">+1 876</option>
                                    <option value="+7" data-country="RU">+7</option>
                                    <option value="+7" data-country="KZ">+7</option>
                                    <option value="+7 840" data-country="AB">+7 840</option>
                                    <option value="+20" data-country="EG">+20</option>
                                    <option value="+27" data-country="ZA">+27</option>
                                    <option value="+30" data-country="GR">+30</option>
                                    <option value="+31" data-country="NL">+31</option>
                                    <option value="+32" data-country="BE">+32</option>
                                    <option value="+33" data-country="FR">+33</option>
                                    <option value="+34" data-country="ES">+34</option>
                                    <option value="+36" data-country="HU">+36</option>
                                    <option value="+39" data-country="IT">+39</option>
                                    <option value="+40" data-country="RO">+40</option>
                                    <option value="+41" data-country="CH">+41</option>
                                    <option value="+43" data-country="AT">+43</option>
                                    <option value="+44" data-country="GB">+44</option>
                                    <option value="+45" data-country="DK">+45</option>
                                    <option value="+46" data-country="SE">+46</option>
                                    <option value="+47" data-country="NO">+47</option>
                                    <option value="+48" data-country="PL">+48</option>
                                    <option value="+49" data-country="DE">+49</option>
                                    <option value="+51" data-country="PE">+51</option>
                                    <option value="+52" data-country="MX">+52</option>
                                    <option value="+53" data-country="CU">+53</option>
                                    <option value="+54" data-country="AR">+54</option>
                                    <option value="+55" data-country="BR">+55</option>
                                    <option value="+56" data-country="CL">+56</option>
                                    <option value="+57" data-country="CO">+57</option>
                                    <option value="+58" data-country="VE">+58</option>
                                    <option value="+60" data-country="MY">+60</option>
                                    <option value="+61" data-country="AU">+61</option>
                                    <option value="+61" data-country="CX">+61</option>
                                    <option value="+61" data-country="CC">+61</option>
                                    <option value="+62" data-country="ID">+62</option>
                                    <option value="+63" data-country="PH">+63</option>
                                    <option value="+64" data-country="NZ">+64</option>
                                    <option value="+65" data-country="SG">+65</option>
                                    <option value="+66" data-country="TH">+66</option>
                                    <option value="+81" data-country="JP">+81</option>
                                    <option value="+82" data-country="KR">+82</option>
                                    <option value="+84" data-country="VN">+84</option>
                                    <option value="+86" data-country="CN">+86</option>
                                    <option value="+90" data-country="TR">+90</option>
                                    <option value="+91" data-country="IN">+91</option>
                                    <option value="+92" data-country="PK">+92</option>
                                    <option value="+93" data-country="AF">+93</option>
                                    <option value="+94" data-country="LK">+94</option>
                                    <option value="+95" data-country="MM">+95</option>
                                    <option value="+98" data-country="IR">+98</option>
                                    <option value="+212" data-country="MA">+212</option>
                                    <option value="+212" data-country="EH">+212</option>
                                    <option value="+213" data-country="DZ">+213</option>
                                    <option value="+216" data-country="TN">+216</option>
                                    <option value="+218" data-country="LY">+218</option>
                                    <option value="+220" data-country="GM">+220</option>
                                    <option value="+221" data-country="SN">+221</option>
                                    <option value="+222" data-country="MR">+222</option>
                                    <option value="+223" data-country="ML">+223</option>
                                    <option value="+224" data-country="GN">+224</option>
                                    <option value="+225" data-country="CI">+225</option>
                                    <option value="+226" data-country="BF">+226</option>
                                    <option value="+227" data-country="NE">+227</option>
                                    <option value="+228" data-country="TG">+228</option>
                                    <option value="+229" data-country="BJ">+229</option>
                                    <option value="+230" data-country="MU">+230</option>
                                    <option value="+231" data-country="LR">+231</option>
                                    <option value="+232" data-country="SL">+232</option>
                                    <option value="+233" data-country="GH">+233</option>
                                    <option value="+234" data-country="NG">+234</option>
                                    <option value="+235" data-country="TD">+235</option>
                                    <option value="+236" data-country="CF">+236</option>
                                    <option value="+237" data-country="CM">+237</option>
                                    <option value="+238" data-country="CV">+238</option>
                                    <option value="+239" data-country="ST">+239</option>
                                    <option value="+240" data-country="GQ">+240</option>
                                    <option value="+241" data-country="GA">+241</option>
                                    <option value="+242" data-country="CG">+242</option>
                                    <option value="+243" data-country="CD">+243</option>
                                    <option value="+244" data-country="AO">+244</option>
                                    <option value="+245" data-country="GW">+245</option>
                                    <option value="+246" data-country="IO">+246</option>
                                    <option value="+248" data-country="SC">+248</option>
                                    <option value="+249" data-country="SD">+249</option>
                                    <option value="+250" data-country="RW">+250</option>
                                    <option value="+251" data-country="ET">+251</option>
                                    <option value="+252" data-country="SO">+252</option>
                                    <option value="+253" data-country="DJ">+253</option>
                                    <option value="+254" data-country="KE">+254</option>
                                    <option value="+255" data-country="TZ">+255</option>
                                    <option value="+256" data-country="UG">+256</option>
                                    <option value="+257" data-country="BI">+257</option>
                                    <option value="+258" data-country="MZ">+258</option>
                                    <option value="+260" data-country="ZM">+260</option>
                                    <option value="+261" data-country="MG">+261</option>
                                    <option value="+262" data-country="YT">+262</option>
                                    <option value="+262" data-country="RE">+262</option>
                                    <option value="+263" data-country="ZW">+263</option>
                                    <option value="+264" data-country="NA">+264</option>
                                    <option value="+265" data-country="MW">+265</option>
                                    <option value="+266" data-country="LS">+266</option>
                                    <option value="+267" data-country="BW">+267</option>
                                    <option value="+268" data-country="SZ">+268</option>
                                    <option value="+269" data-country="KM">+269</option>
                                    <option value="+290" data-country="SH">+290</option>
                                    <option value="+291" data-country="ER">+291</option>
                                    <option value="+297" data-country="AW">+297</option>
                                    <option value="+298" data-country="FO">+298</option>
                                    <option value="+299" data-country="GL">+299</option>
                                    <option value="+350" data-country="GI">+350</option>
                                    <option value="+351" data-country="PT">+351</option>
                                    <option value="+352" data-country="LU">+352</option>
                                    <option value="+353" data-country="IE">+353</option>
                                    <option value="+354" data-country="IS">+354</option>
                                    <option value="+355" data-country="AL">+355</option>
                                    <option value="+356" data-country="MT">+356</option>
                                    <option value="+357" data-country="CY">+357</option>
                                    <option value="+358" data-country="FI">+358</option>
                                    <option value="+359" data-country="BG">+359</option>
                                    <option value="+370" data-country="LT">+370</option>
                                    <option value="+371" data-country="LV">+371</option>
                                    <option value="+372" data-country="EE">+372</option>
                                    <option value="+373" data-country="MD">+373</option>
                                    <option value="+374" data-country="AM">+374</option>
                                    <option value="+375" data-country="BY">+375</option>
                                    <option value="+376" data-country="AD">+376</option>
                                    <option value="+377" data-country="MC">+377</option>
                                    <option value="+378" data-country="SM">+378</option>
                                    <option value="+380" data-country="UA">+380</option>
                                    <option value="+381" data-country="RS">+381</option>
                                    <option value="+382" data-country="ME">+382</option>
                                    <option value="+385" data-country="HR">+385</option>
                                    <option value="+386" data-country="SI">+386</option>
                                    <option value="+387" data-country="BA">+387</option>
                                    <option value="+389" data-country="MK">+389</option>
                                    <option value="+420" data-country="CZ">+420</option>
                                    <option value="+421" data-country="SK">+421</option>
                                    <option value="+423" data-country="LI">+423</option>
                                    <option value="+500" data-country="FK">+500</option>
                                    <option value="+501" data-country="BZ">+501</option>
                                    <option value="+502" data-country="GT">+502</option>
                                    <option value="+503" data-country="SV">+503</option>
                                    <option value="+504" data-country="HN">+504</option>
                                    <option value="+505" data-country="NI">+505</option>
                                    <option value="+506" data-country="CR">+506</option>
                                    <option value="+507" data-country="PA">+507</option>
                                    <option value="+508" data-country="PM">+508</option>
                                    <option value="+509" data-country="HT">+509</option>
                                    <option value="+590" data-country="GP">+590</option>
                                    <option value="+591" data-country="BO">+591</option>
                                    <option value="+592" data-country="GY">+592</option>
                                    <option value="+593" data-country="EC">+593</option>
                                    <option value="+594" data-country="GF">+594</option>
                                    <option value="+595" data-country="PY">+595</option>
                                    <option value="+596" data-country="MQ">+596</option>
                                    <option value="+597" data-country="SR">+597</option>
                                    <option value="+598" data-country="UY">+598</option>
                                    <option value="+599" data-country="AN">+599</option>
                                    <option value="+670" data-country="TL">+670</option>
                                    <option value="+672" data-country="AQ">+672</option>
                                    <option value="+672" data-country="NF">+672</option>
                                    <option value="+673" data-country="BN">+673</option>
                                    <option value="+674" data-country="NR">+674</option>
                                    <option value="+675" data-country="PG">+675</option>
                                    <option value="+676" data-country="TO">+676</option>
                                    <option value="+677" data-country="SB">+677</option>
                                    <option value="+678" data-country="VU">+678</option>
                                    <option value="+679" data-country="FJ">+679</option>
                                    <option value="+680" data-country="PW">+680</option>
                                    <option value="+681" data-country="WF">+681</option>
                                    <option value="+682" data-country="CK">+682</option>
                                    <option value="+683" data-country="NU">+683</option>
                                    <option value="+685" data-country="WS">+685</option>
                                    <option value="+686" data-country="KI">+686</option>
                                    <option value="+687" data-country="NC">+687</option>
                                    <option value="+688" data-country="TV">+688</option>
                                    <option value="+689" data-country="PF">+689</option>
                                    <option value="+690" data-country="TK">+690</option>
                                    <option value="+691" data-country="FM">+691</option>
                                    <option value="+692" data-country="MH">+692</option>
                                    <option value="+850" data-country="KP">+850</option>
                                    <option value="+852" data-country="HK">+852</option>
                                    <option value="+853" data-country="MO">+853</option>
                                    <option value="+855" data-country="KH">+855</option>
                                    <option value="+856" data-country="LA">+856</option>
                                    <option value="+880" data-country="BD">+880</option>
                                    <option value="+886" data-country="TW">+886</option>
                                    <option value="+960" data-country="MV">+960</option>
                                    <option value="+961" data-country="LB">+961</option>
                                    <option value="+962" data-country="JO">+962</option>
                                    <option value="+963" data-country="SY">+963</option>
                                    <option value="+964" data-country="IQ">+964</option>
                                    <option value="+965" data-country="KW">+965</option>
                                    <option value="+966" data-country="SA">+966</option>
                                    <option value="+967" data-country="YE">+967</option>
                                    <option value="+968" data-country="OM">+968</option>
                                    <option value="+970" data-country="PS">+970</option>
                                    <option value="+971" data-country="AE">+971</option>
                                    <option value="+972" data-country="IL">+972</option>
                                    <option value="+973" data-country="BH">+973</option>
                                    <option value="+974" data-country="QA">+974</option>
                                    <option value="+975" data-country="BT">+975</option>
                                    <option value="+976" data-country="MN">+976</option>
                                    <option value="+977" data-country="NP">+977</option>
                                    <option value="+992" data-country="TJ">+992</option>
                                    <option value="+993" data-country="TM">+993</option>
                                    <option value="+994" data-country="AZ">+994</option>
                                    <option value="+995" data-country="GE">+995</option>
                                    <option value="+996" data-country="KG">+996</option>
                                    <option value="+998" data-country="UZ">+998</option>
                                </select>
                                <input type="tel" id="phone" name="phone" required 
                                       class="flex-1 px-4 py-3 border border-gray-300 rounded-r-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200 border-l-0"
                                       placeholder="<?php echo $currentLanguage === 'en' ? 'Enter your phone number' : ($translationService->translateText('Enter your phone number', 'en', $currentLanguage) ?? 'Enter your phone number'); ?>">
                            </div>
                        </div>

                        <div class="md:col-span-2">
                            <label for="address" class="block text-sm font-medium text-gray-700 mb-2">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Address';
                                } else {
                                    echo $translationService->translateText('Address', 'en', $currentLanguage) ?? 'Address';
                                }
                                ?>
                            </label>
                            <textarea id="address" name="address" rows="3" 
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200"
                                      placeholder="<?php echo $currentLanguage === 'en' ? 'Enter your full address' : ($translationService->translateText('Enter your full address', 'en', $currentLanguage) ?? 'Enter your full address'); ?>"></textarea>
                        </div>

                        <?php if ($registrationType === 'individual'): ?>
                        <!-- Optional Church/Group Information for Individual Registration -->
                        <div class="md:col-span-2 mt-6 pt-6 border-t border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Church/Group Information (Optional)';
                                } else {
                                    echo $translationService->translateText('Church/Group Information (Optional)', 'en', $currentLanguage) ?? 'Church/Group Information (Optional)';
                                }
                                ?>
                            </h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="individualZone" class="block text-sm font-medium text-gray-700 mb-2">
                                        <?php 
                                        if ($currentLanguage === 'en') {
                                            echo 'Zone';
                                        } else {
                                            echo $translationService->translateText('Zone', 'en', $currentLanguage) ?? 'Zone';
                                        }
                                        ?>
                                    </label>
                                    <select id="individualZone" name="individual_zone" 
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200">
                                        <option value="">Select your zone (optional)</option>
                                        <?php
                                        // Load zones from JSON file
                                        $zonesJson = file_get_contents(__DIR__ . '/world/zones.json');
                                        $zonesData = json_decode($zonesJson, true);
                                        
                                        if ($zonesData && isset($zonesData['zones'])) {
                                            foreach ($zonesData['zones'] as $zone) {
                                                echo '<option value="' . htmlspecialchars($zone) . '">' . htmlspecialchars($zone) . '</option>';
                                            }
                                        }
                                        ?>
                                    </select>
                                </div>

                                <div>
                                    <label for="individualGroup" class="block text-sm font-medium text-gray-700 mb-2">
                                        <?php 
                                        if ($currentLanguage === 'en') {
                                            echo 'Group';
                                        } else {
                                            echo $translationService->translateText('Group', 'en', $currentLanguage) ?? 'Group';
                                        }
                                        ?>
                                    </label>
                                    <input type="text" id="individualGroup" name="individual_group" 
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200"
                                           placeholder="<?php echo $currentLanguage === 'en' ? 'Enter your group name (optional)' : ($translationService->translateText('Enter your group name (optional)', 'en', $currentLanguage) ?? 'Enter your group name (optional)'); ?>">
                                </div>

                                <div class="md:col-span-2">
                                    <label for="individualChurch" class="block text-sm font-medium text-gray-700 mb-2">
                                        <?php 
                                        if ($currentLanguage === 'en') {
                                            echo 'Church';
                                        } else {
                                            echo $translationService->translateText('Church', 'en', $currentLanguage) ?? 'Church';
                                        }
                                        ?>
                                    </label>
                                    <input type="text" id="individualChurch" name="individual_church" 
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200"
                                           placeholder="<?php echo $currentLanguage === 'en' ? 'Enter your church name (optional)' : ($translationService->translateText('Enter your church name (optional)', 'en', $currentLanguage) ?? 'Enter your church name (optional)'); ?>">
                                </div>
                            </div>
                        </div>

                        <!-- Optional Network/Organisation Information for Individual Registration -->
                        <div class="md:col-span-2 mt-6 pt-6 border-t border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Network/Organisation Information (Optional)';
                                } else {
                                    echo $translationService->translateText('Network/Organisation Information (Optional)', 'en', $currentLanguage) ?? 'Network/Organisation Information (Optional)';
                                }
                                ?>
                            </h3>
                            <div class="grid grid-cols-1 gap-6">
                                <div>
                                    <label for="individualOrganization" class="block text-sm font-medium text-gray-700 mb-2">
                                        <?php 
                                        if ($currentLanguage === 'en') {
                                            echo 'Organisation/Network Name';
                                        } else {
                                            echo $translationService->translateText('Organisation/Network Name', 'en', $currentLanguage) ?? 'Organisation/Network Name';
                                        }
                                        ?>
                                    </label>
                                    <input type="text" id="individualOrganization" name="individual_organization" 
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200"
                                           placeholder="<?php echo $currentLanguage === 'en' ? 'Enter organisation/network name (optional)' : ($translationService->translateText('Enter organisation/network name (optional)', 'en', $currentLanguage) ?? 'Enter organisation/network name (optional)'); ?>">
                                </div>

                                <div>
                                    <label for="individualOrgAddress" class="block text-sm font-medium text-gray-700 mb-2">
                                        <?php 
                                        if ($currentLanguage === 'en') {
                                            echo 'Organisation Address';
                                        } else {
                                            echo $translationService->translateText('Organisation Address', 'en', $currentLanguage) ?? 'Organisation Address';
                                        }
                                        ?>
                                    </label>
                                    <textarea id="individualOrgAddress" name="individual_organization_address" rows="3" 
                                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200"
                                              placeholder="<?php echo $currentLanguage === 'en' ? 'Enter organisation address (optional)' : ($translationService->translateText('Enter organisation address (optional)', 'en', $currentLanguage) ?? 'Enter organisation address (optional)'); ?>"></textarea>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Church/Organization Information Section -->
                <div class="registration-section" id="churchSection" style="<?php echo $registrationType === 'individual' ? 'display: none;' : ''; ?>">
                    <div class="flex items-center mb-6">
                        <div class="w-8 h-8 bg-accent rounded-full flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-gray-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                        <h2 class="text-xl font-semibold text-gray-900">
                            <?php 
                            if ($registrationType === 'organisation') {
                                echo $currentLanguage === 'en' ? 'Organisation/Network Information' : ($translationService->translateText('Organisation/Network Information', 'en', $currentLanguage) ?? 'Organisation/Network Information');
                            } elseif ($registrationType === 'church') {
                                echo $currentLanguage === 'en' ? 'Church/Group/Zone Information' : ($translationService->translateText('Church/Group/Zone Information', 'en', $currentLanguage) ?? 'Church/Group/Zone Information');
                            } else {
                                echo $currentLanguage === 'en' ? 'Church/Group Information' : ($translationService->translateText('Church/Group Information', 'en', $currentLanguage) ?? 'Church/Group Information');
                            }
                            ?>
                            <?php if ($registrationType === 'individual'): ?>
                                <span class="text-sm font-normal text-gray-500 ml-2">(Optional)</span>
                            <?php else: ?>
                                <span class="text-red-500 ml-1">*</span>
                            <?php endif; ?>
                        </h2>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <?php if ($registrationType === 'church'): ?>
                        <!-- Registration Type Selection -->
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-3">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Registering as';
                                } else {
                                    echo $translationService->translateText('Registering as', 'en', $currentLanguage) ?? 'Registering as';
                                }
                                ?> <span class="text-red-500">*</span>
                            </label>
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                                <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:border-primary transition-colors duration-200">
                                    <input type="radio" name="church_type" value="zone" required class="mr-3" onchange="handleChurchTypeSelection()">
                                    <span class="text-sm font-medium">Zone</span>
                                </label>
                                <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:border-primary transition-colors duration-200">
                                    <input type="radio" name="church_type" value="group" required class="mr-3" onchange="handleChurchTypeSelection()">
                                    <span class="text-sm font-medium">Group</span>
                                </label>
                                <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:border-primary transition-colors duration-200">
                                    <input type="radio" name="church_type" value="church" required class="mr-3" onchange="handleChurchTypeSelection()">
                                    <span class="text-sm font-medium">Church</span>
                                </label>
                                <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:border-primary transition-colors duration-200">
                                    <input type="radio" name="church_type" value="other" required class="mr-3" onchange="handleChurchTypeSelection()">
                                    <span class="text-sm font-medium">Other</span>
                                </label>
                            </div>
                        </div>

                        <!-- Zone Selection for Church Registration -->
                        <div id="zoneSelectionContainer" class="md:col-span-2">
                            <label for="zoneSelect" class="block text-sm font-medium text-gray-700 mb-2">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Select Zone';
                                } else {
                                    echo $translationService->translateText('Select Zone', 'en', $currentLanguage) ?? 'Select Zone';
                                }
                                ?> <span class="text-red-500">*</span>
                            </label>
                            <select id="zoneSelect" name="zone" required 
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200"
                                    onchange="handleZoneSelection()">
                                <option value="">Select your zone</option>
                                <?php
                                // Load zones from JSON file
                                $zonesJson = file_get_contents(__DIR__ . '/world/zones.json');
                                $zonesData = json_decode($zonesJson, true);
                                
                                if ($zonesData && isset($zonesData['zones'])) {
                                    foreach ($zonesData['zones'] as $zone) {
                                        echo '<option value="' . htmlspecialchars($zone) . '">' . htmlspecialchars($zone) . '</option>';
                                    }
                                }
                                ?>
                            </select>
                        </div>

                        <!-- Dynamic Fields Based on Selection -->
                        <div id="zoneFields" class="md:col-span-2 hidden">
                            <!-- No additional fields needed for Zone registration -->
                        </div>

                        <div id="groupFields" class="md:col-span-2 hidden">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="md:col-span-2">
                                    <label for="groupName" class="block text-sm font-medium text-gray-700 mb-2">
                                        Group Name <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" id="groupName" name="group_name" 
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200"
                                           placeholder="<?php echo $currentLanguage === 'en' ? 'Enter group name' : ($translationService->translateText('Enter group name', 'en', $currentLanguage) ?? 'Enter group name'); ?>">
                                </div>
                            </div>
                        </div>

                        <div id="churchFields" class="md:col-span-2 hidden">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Group field for Church -->
                                <div class="md:col-span-2">
                                    <label for="churchGroupName" class="block text-sm font-medium text-gray-700 mb-2">
                                        Group Name <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" id="churchGroupName" name="church_group_name" 
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200"
                                           placeholder="<?php echo $currentLanguage === 'en' ? 'Enter group name' : ($translationService->translateText('Enter group name', 'en', $currentLanguage) ?? 'Enter group name'); ?>">
                                </div>
                                <!-- Church field -->
                                <div class="md:col-span-2">
                                    <label for="churchName" class="block text-sm font-medium text-gray-700 mb-2">
                                        Church Name <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" id="churchName" name="church_name" 
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200"
                                           placeholder="<?php echo $currentLanguage === 'en' ? 'Enter church name' : ($translationService->translateText('Enter church name', 'en', $currentLanguage) ?? 'Enter church name'); ?>">
                                </div>
                            </div>
                        </div>

                        <div id="otherFields" class="md:col-span-2 hidden">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="md:col-span-2">
                                    <label for="ministryName" class="block text-sm font-medium text-gray-700 mb-2">
                                        Ministry Name <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" id="ministryName" name="ministry_name" 
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200"
                                           placeholder="<?php echo $currentLanguage === 'en' ? 'Enter ministry name' : ($translationService->translateText('Enter ministry name', 'en', $currentLanguage) ?? 'Enter ministry name'); ?>">
                                </div>
                            </div>
                        </div>

                        <!-- Common Contact Fields -->
                        <div class="md:col-span-2">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="contactEmail" class="block text-sm font-medium text-gray-700 mb-2">
                                        Contact Email <span class="text-red-500">*</span>
                                    </label>
                                    <input type="email" id="contactEmail" name="contact_email" required 
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200"
                                           placeholder="<?php echo $currentLanguage === 'en' ? 'Enter contact email' : ($translationService->translateText('Enter contact email', 'en', $currentLanguage) ?? 'Enter contact email'); ?>">
                                </div>

                                <div>
                                    <label for="contactPhone" class="block text-sm font-medium text-gray-700 mb-2">
                                        Contact Phone <span class="text-red-500">*</span>
                                    </label>
                                    <div class="flex">
                                        <select id="contact-country-code" name="contact_country_code" class="w-32 px-2 py-3 border border-gray-300 rounded-l-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200 border-r-0" required>
                                            <option value="+1" data-country="US">+1</option>
                                            <option value="+1" data-country="CA">+1</option>
                                            <option value="******" data-country="BS">******</option>
                                            <option value="******" data-country="BB">******</option>
                                            <option value="******" data-country="AI">******</option>
                                            <option value="******" data-country="AG">******</option>
                                            <option value="+1 284" data-country="VG">+1 284</option>
                                            <option value="+1 340" data-country="VI">+1 340</option>
                                            <option value="+1 345" data-country="KY">+1 345</option>
                                            <option value="+1 441" data-country="BM">+1 441</option>
                                            <option value="+1 473" data-country="GD">+1 473</option>
                                            <option value="+1 649" data-country="TC">+1 649</option>
                                            <option value="+1 664" data-country="MS">+1 664</option>
                                            <option value="+1 670" data-country="MP">+1 670</option>
                                            <option value="+1 671" data-country="GU">+1 671</option>
                                            <option value="+1 684" data-country="AS">+1 684</option>
                                            <option value="+1 758" data-country="LC">+1 758</option>
                                            <option value="+1 767" data-country="DM">+1 767</option>
                                            <option value="+1 784" data-country="VC">+1 784</option>
                                            <option value="+1 787" data-country="PR">+1 787</option>
                                            <option value="+1 809" data-country="DO">+1 809</option>
                                            <option value="+1 868" data-country="TT">+1 868</option>
                                            <option value="+1 869" data-country="KN">+1 869</option>
                                            <option value="+1 876" data-country="JM">+1 876</option>
                                            <option value="+7" data-country="RU">+7</option>
                                            <option value="+7" data-country="KZ">+7</option>
                                            <option value="+7 840" data-country="AB">+7 840</option>
                                            <option value="+20" data-country="EG">+20</option>
                                            <option value="+27" data-country="ZA">+27</option>
                                            <option value="+30" data-country="GR">+30</option>
                                            <option value="+31" data-country="NL">+31</option>
                                            <option value="+32" data-country="BE">+32</option>
                                            <option value="+33" data-country="FR">+33</option>
                                            <option value="+34" data-country="ES">+34</option>
                                            <option value="+36" data-country="HU">+36</option>
                                            <option value="+39" data-country="IT">+39</option>
                                            <option value="+40" data-country="RO">+40</option>
                                            <option value="+41" data-country="CH">+41</option>
                                            <option value="+43" data-country="AT">+43</option>
                                            <option value="+44" data-country="GB">+44</option>
                                            <option value="+45" data-country="DK">+45</option>
                                            <option value="+46" data-country="SE">+46</option>
                                            <option value="+47" data-country="NO">+47</option>
                                            <option value="+48" data-country="PL">+48</option>
                                            <option value="+49" data-country="DE">+49</option>
                                            <option value="+51" data-country="PE">+51</option>
                                            <option value="+52" data-country="MX">+52</option>
                                            <option value="+53" data-country="CU">+53</option>
                                            <option value="+54" data-country="AR">+54</option>
                                            <option value="+55" data-country="BR">+55</option>
                                            <option value="+56" data-country="CL">+56</option>
                                            <option value="+57" data-country="CO">+57</option>
                                            <option value="+58" data-country="VE">+58</option>
                                            <option value="+60" data-country="MY">+60</option>
                                            <option value="+61" data-country="AU">+61</option>
                                            <option value="+61" data-country="CX">+61</option>
                                            <option value="+61" data-country="CC">+61</option>
                                            <option value="+62" data-country="ID">+62</option>
                                            <option value="+63" data-country="PH">+63</option>
                                            <option value="+64" data-country="NZ">+64</option>
                                            <option value="+65" data-country="SG">+65</option>
                                            <option value="+66" data-country="TH">+66</option>
                                            <option value="+81" data-country="JP">+81</option>
                                            <option value="+82" data-country="KR">+82</option>
                                            <option value="+84" data-country="VN">+84</option>
                                            <option value="+86" data-country="CN">+86</option>
                                            <option value="+90" data-country="TR">+90</option>
                                            <option value="+91" data-country="IN">+91</option>
                                            <option value="+92" data-country="PK">+92</option>
                                            <option value="+93" data-country="AF">+93</option>
                                            <option value="+94" data-country="LK">+94</option>
                                            <option value="+95" data-country="MM">+95</option>
                                            <option value="+98" data-country="IR">+98</option>
                                            <option value="+212" data-country="MA">+212</option>
                                            <option value="+212" data-country="EH">+212</option>
                                            <option value="+213" data-country="DZ">+213</option>
                                            <option value="+216" data-country="TN">+216</option>
                                            <option value="+218" data-country="LY">+218</option>
                                            <option value="+220" data-country="GM">+220</option>
                                            <option value="+221" data-country="SN">+221</option>
                                            <option value="+222" data-country="MR">+222</option>
                                            <option value="+223" data-country="ML">+223</option>
                                            <option value="+224" data-country="GN">+224</option>
                                            <option value="+225" data-country="CI">+225</option>
                                            <option value="+226" data-country="BF">+226</option>
                                            <option value="+227" data-country="NE">+227</option>
                                            <option value="+228" data-country="TG">+228</option>
                                            <option value="+229" data-country="BJ">+229</option>
                                            <option value="+230" data-country="MU">+230</option>
                                            <option value="+231" data-country="LR">+231</option>
                                            <option value="+232" data-country="SL">+232</option>
                                            <option value="+233" data-country="GH">+233</option>
                                            <option value="+234" data-country="NG">+234</option>
                                            <option value="+235" data-country="TD">+235</option>
                                            <option value="+236" data-country="CF">+236</option>
                                            <option value="+237" data-country="CM">+237</option>
                                            <option value="+238" data-country="CV">+238</option>
                                            <option value="+239" data-country="ST">+239</option>
                                            <option value="+240" data-country="GQ">+240</option>
                                            <option value="+241" data-country="GA">+241</option>
                                            <option value="+242" data-country="CG">+242</option>
                                            <option value="+243" data-country="CD">+243</option>
                                            <option value="+244" data-country="AO">+244</option>
                                            <option value="+245" data-country="GW">+245</option>
                                            <option value="+246" data-country="IO">+246</option>
                                            <option value="+248" data-country="SC">+248</option>
                                            <option value="+249" data-country="SD">+249</option>
                                            <option value="+250" data-country="RW">+250</option>
                                            <option value="+251" data-country="ET">+251</option>
                                            <option value="+252" data-country="SO">+252</option>
                                            <option value="+253" data-country="DJ">+253</option>
                                            <option value="+254" data-country="KE">+254</option>
                                            <option value="+255" data-country="TZ">+255</option>
                                            <option value="+256" data-country="UG">+256</option>
                                            <option value="+257" data-country="BI">+257</option>
                                            <option value="+258" data-country="MZ">+258</option>
                                            <option value="+260" data-country="ZM">+260</option>
                                            <option value="+261" data-country="MG">+261</option>
                                            <option value="+262" data-country="YT">+262</option>
                                            <option value="+262" data-country="RE">+262</option>
                                            <option value="+263" data-country="ZW">+263</option>
                                            <option value="+264" data-country="NA">+264</option>
                                            <option value="+265" data-country="MW">+265</option>
                                            <option value="+266" data-country="LS">+266</option>
                                            <option value="+267" data-country="BW">+267</option>
                                            <option value="+268" data-country="SZ">+268</option>
                                            <option value="+269" data-country="KM">+269</option>
                                            <option value="+290" data-country="SH">+290</option>
                                            <option value="+291" data-country="ER">+291</option>
                                            <option value="+297" data-country="AW">+297</option>
                                            <option value="+298" data-country="FO">+298</option>
                                            <option value="+299" data-country="GL">+299</option>
                                            <option value="+350" data-country="GI">+350</option>
                                            <option value="+351" data-country="PT">+351</option>
                                            <option value="+352" data-country="LU">+352</option>
                                            <option value="+353" data-country="IE">+353</option>
                                            <option value="+354" data-country="IS">+354</option>
                                            <option value="+355" data-country="AL">+355</option>
                                            <option value="+356" data-country="MT">+356</option>
                                            <option value="+357" data-country="CY">+357</option>
                                            <option value="+358" data-country="FI">+358</option>
                                            <option value="+359" data-country="BG">+359</option>
                                            <option value="+370" data-country="LT">+370</option>
                                            <option value="+371" data-country="LV">+371</option>
                                            <option value="+372" data-country="EE">+372</option>
                                            <option value="+373" data-country="MD">+373</option>
                                            <option value="+374" data-country="AM">+374</option>
                                            <option value="+375" data-country="BY">+375</option>
                                            <option value="+376" data-country="AD">+376</option>
                                            <option value="+377" data-country="MC">+377</option>
                                            <option value="+378" data-country="SM">+378</option>
                                            <option value="+380" data-country="UA">+380</option>
                                            <option value="+381" data-country="RS">+381</option>
                                            <option value="+382" data-country="ME">+382</option>
                                            <option value="+385" data-country="HR">+385</option>
                                            <option value="+386" data-country="SI">+386</option>
                                            <option value="+387" data-country="BA">+387</option>
                                            <option value="+389" data-country="MK">+389</option>
                                            <option value="+420" data-country="CZ">+420</option>
                                            <option value="+421" data-country="SK">+421</option>
                                            <option value="+423" data-country="LI">+423</option>
                                            <option value="+500" data-country="FK">+500</option>
                                            <option value="+501" data-country="BZ">+501</option>
                                            <option value="+502" data-country="GT">+502</option>
                                            <option value="+503" data-country="SV">+503</option>
                                            <option value="+504" data-country="HN">+504</option>
                                            <option value="+505" data-country="NI">+505</option>
                                            <option value="+506" data-country="CR">+506</option>
                                            <option value="+507" data-country="PA">+507</option>
                                            <option value="+508" data-country="PM">+508</option>
                                            <option value="+509" data-country="HT">+509</option>
                                            <option value="+590" data-country="GP">+590</option>
                                            <option value="+591" data-country="BO">+591</option>
                                            <option value="+592" data-country="GY">+592</option>
                                            <option value="+593" data-country="EC">+593</option>
                                            <option value="+594" data-country="GF">+594</option>
                                            <option value="+595" data-country="PY">+595</option>
                                            <option value="+596" data-country="MQ">+596</option>
                                            <option value="+597" data-country="SR">+597</option>
                                            <option value="+598" data-country="UY">+598</option>
                                            <option value="+599" data-country="AN">+599</option>
                                            <option value="+670" data-country="TL">+670</option>
                                            <option value="+672" data-country="AQ">+672</option>
                                            <option value="+672" data-country="NF">+672</option>
                                            <option value="+673" data-country="BN">+673</option>
                                            <option value="+674" data-country="NR">+674</option>
                                            <option value="+675" data-country="PG">+675</option>
                                            <option value="+676" data-country="TO">+676</option>
                                            <option value="+677" data-country="SB">+677</option>
                                            <option value="+678" data-country="VU">+678</option>
                                            <option value="+679" data-country="FJ">+679</option>
                                            <option value="+680" data-country="PW">+680</option>
                                            <option value="+681" data-country="WF">+681</option>
                                            <option value="+682" data-country="CK">+682</option>
                                            <option value="+683" data-country="NU">+683</option>
                                            <option value="+685" data-country="WS">+685</option>
                                            <option value="+686" data-country="KI">+686</option>
                                            <option value="+687" data-country="NC">+687</option>
                                            <option value="+688" data-country="TV">+688</option>
                                            <option value="+689" data-country="PF">+689</option>
                                            <option value="+690" data-country="TK">+690</option>
                                            <option value="+691" data-country="FM">+691</option>
                                            <option value="+692" data-country="MH">+692</option>
                                            <option value="+850" data-country="KP">+850</option>
                                            <option value="+852" data-country="HK">+852</option>
                                            <option value="+853" data-country="MO">+853</option>
                                            <option value="+855" data-country="KH">+855</option>
                                            <option value="+856" data-country="LA">+856</option>
                                            <option value="+880" data-country="BD">+880</option>
                                            <option value="+886" data-country="TW">+886</option>
                                            <option value="+960" data-country="MV">+960</option>
                                            <option value="+961" data-country="LB">+961</option>
                                            <option value="+962" data-country="JO">+962</option>
                                            <option value="+963" data-country="SY">+963</option>
                                            <option value="+964" data-country="IQ">+964</option>
                                            <option value="+965" data-country="KW">+965</option>
                                            <option value="+966" data-country="SA">+966</option>
                                            <option value="+967" data-country="YE">+967</option>
                                            <option value="+968" data-country="OM">+968</option>
                                            <option value="+970" data-country="PS">+970</option>
                                            <option value="+971" data-country="AE">+971</option>
                                            <option value="+972" data-country="IL">+972</option>
                                            <option value="+973" data-country="BH">+973</option>
                                            <option value="+974" data-country="QA">+974</option>
                                            <option value="+975" data-country="BT">+975</option>
                                            <option value="+976" data-country="MN">+976</option>
                                            <option value="+977" data-country="NP">+977</option>
                                            <option value="+992" data-country="TJ">+992</option>
                                            <option value="+993" data-country="TM">+993</option>
                                            <option value="+994" data-country="AZ">+994</option>
                                            <option value="+995" data-country="GE">+995</option>
                                            <option value="+996" data-country="KG">+996</option>
                                            <option value="+998" data-country="UZ">+998</option>
                                        </select>
                                        <input type="tel" id="contactPhone" name="contact_phone" required 
                                               class="flex-1 px-4 py-3 border border-gray-300 rounded-r-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200 border-l-0"
                                               placeholder="<?php echo $currentLanguage === 'en' ? 'Enter contact phone' : ($translationService->translateText('Enter contact phone', 'en', $currentLanguage) ?? 'Enter contact phone'); ?>">
                                    </div>
                                </div>

                                <div>
                                    <label for="contactKingsChat" class="block text-sm font-medium text-gray-700 mb-2">
                                        KingsChat Username (Optional)
                                    </label>
                                    <input type="text" id="contactKingsChat" name="kingschat_username" 
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200"
                                           placeholder="<?php echo $currentLanguage === 'en' ? 'Enter KingsChat username' : ($translationService->translateText('Enter KingsChat username', 'en', $currentLanguage) ?? 'Enter KingsChat username'); ?>">
                                </div>

                                <div class="md:col-span-2">
                                    <label for="contactAddress" class="block text-sm font-medium text-gray-700 mb-2">
                                        Address
                                    </label>
                                    <textarea id="contactAddress" name="contact_address" rows="3" 
                                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200"
                                              placeholder="<?php echo $currentLanguage === 'en' ? 'Enter address' : ($translationService->translateText('Enter address', 'en', $currentLanguage) ?? 'Enter address'); ?>"></textarea>
                                </div>
                            </div>
                        </div>

                        <?php else: ?>
                        <!-- Organisation Fields -->
                        <div class="md:col-span-2">
                            <label for="orgName" class="block text-sm font-medium text-gray-700 mb-2">
                                <?php 
                                if ($registrationType === 'organisation') {
                                    echo $currentLanguage === 'en' ? 'Organisation/Network Name' : ($translationService->translateText('Organisation/Network Name', 'en', $currentLanguage) ?? 'Organisation/Network Name');
                                } else {
                                    echo $currentLanguage === 'en' ? 'Church/Group Name' : ($translationService->translateText('Church/Group Name', 'en', $currentLanguage) ?? 'Church/Group Name');
                                }
                                ?>
                                <?php if ($registrationType !== 'individual'): ?>
                                    <span class="text-red-500">*</span>
                                <?php endif; ?>
                            </label>
                            <input type="text" id="orgName" name="organization_name" 
                                   <?php echo $registrationType !== 'individual' ? 'required' : ''; ?>
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200"
                                   placeholder="<?php 
                                   if ($registrationType === 'organisation') {
                                       echo $currentLanguage === 'en' ? 'Enter organisation/network name' : ($translationService->translateText('Enter organisation/network name', 'en', $currentLanguage) ?? 'Enter organisation/network name');
                                   } else {
                                       echo $currentLanguage === 'en' ? 'Enter church/group name' : ($translationService->translateText('Enter church/group name', 'en', $currentLanguage) ?? 'Enter church/group name');
                                   }
                                   ?>">
                        </div>



                        <div>
                            <label for="orgEmail" class="block text-sm font-medium text-gray-700 mb-2">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Email Address';
                                } else {
                                    echo $translationService->translateText('Email Address', 'en', $currentLanguage) ?? 'Email Address';
                                }
                                ?>
                            </label>
                            <input type="email" id="orgEmail" name="organization_email" 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200"
                                   placeholder="<?php echo $currentLanguage === 'en' ? 'Enter email address' : ($translationService->translateText('Enter email address', 'en', $currentLanguage) ?? 'Enter email address'); ?>">
                        </div>

                        <div>
                            <label for="orgPhone" class="block text-sm font-medium text-gray-700 mb-2">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Phone Number';
                                } else {
                                    echo $translationService->translateText('Phone Number', 'en', $currentLanguage) ?? 'Phone Number';
                                }
                                ?>
                            </label>
                            <div class="flex">
                                <select id="org-country-code" name="organization_country_code" class="w-32 px-2 py-3 border border-gray-300 rounded-l-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200 border-r-0">
                                    <option value="+1" data-country="US">+1</option>
                                    <option value="+1" data-country="CA">+1</option>
                                    <option value="******" data-country="BS">******</option>
                                    <option value="******" data-country="BB">******</option>
                                    <option value="******" data-country="AI">******</option>
                                    <option value="******" data-country="AG">******</option>
                                    <option value="+1 284" data-country="VG">+1 284</option>
                                    <option value="+1 340" data-country="VI">+1 340</option>
                                    <option value="+1 345" data-country="KY">+1 345</option>
                                    <option value="+1 441" data-country="BM">+1 441</option>
                                    <option value="+1 473" data-country="GD">+1 473</option>
                                    <option value="+1 649" data-country="TC">+1 649</option>
                                    <option value="+1 664" data-country="MS">+1 664</option>
                                    <option value="+1 670" data-country="MP">+1 670</option>
                                    <option value="+1 671" data-country="GU">+1 671</option>
                                    <option value="+1 684" data-country="AS">+1 684</option>
                                    <option value="+1 758" data-country="LC">+1 758</option>
                                    <option value="+1 767" data-country="DM">+1 767</option>
                                    <option value="+1 784" data-country="VC">+1 784</option>
                                    <option value="+1 787" data-country="PR">+1 787</option>
                                    <option value="+1 809" data-country="DO">+1 809</option>
                                    <option value="+1 868" data-country="TT">+1 868</option>
                                    <option value="+1 869" data-country="KN">+1 869</option>
                                    <option value="+1 876" data-country="JM">+1 876</option>
                                    <option value="+7" data-country="RU">+7</option>
                                    <option value="+7" data-country="KZ">+7</option>
                                    <option value="+7 840" data-country="AB">+7 840</option>
                                    <option value="+20" data-country="EG">+20</option>
                                    <option value="+27" data-country="ZA">+27</option>
                                    <option value="+30" data-country="GR">+30</option>
                                    <option value="+31" data-country="NL">+31</option>
                                    <option value="+32" data-country="BE">+32</option>
                                    <option value="+33" data-country="FR">+33</option>
                                    <option value="+34" data-country="ES">+34</option>
                                    <option value="+36" data-country="HU">+36</option>
                                    <option value="+39" data-country="IT">+39</option>
                                    <option value="+40" data-country="RO">+40</option>
                                    <option value="+41" data-country="CH">+41</option>
                                    <option value="+43" data-country="AT">+43</option>
                                    <option value="+44" data-country="GB">+44</option>
                                    <option value="+45" data-country="DK">+45</option>
                                    <option value="+46" data-country="SE">+46</option>
                                    <option value="+47" data-country="NO">+47</option>
                                    <option value="+48" data-country="PL">+48</option>
                                    <option value="+49" data-country="DE">+49</option>
                                    <option value="+51" data-country="PE">+51</option>
                                    <option value="+52" data-country="MX">+52</option>
                                    <option value="+53" data-country="CU">+53</option>
                                    <option value="+54" data-country="AR">+54</option>
                                    <option value="+55" data-country="BR">+55</option>
                                    <option value="+56" data-country="CL">+56</option>
                                    <option value="+57" data-country="CO">+57</option>
                                    <option value="+58" data-country="VE">+58</option>
                                    <option value="+60" data-country="MY">+60</option>
                                    <option value="+61" data-country="AU">+61</option>
                                    <option value="+61" data-country="CX">+61</option>
                                    <option value="+61" data-country="CC">+61</option>
                                    <option value="+62" data-country="ID">+62</option>
                                    <option value="+63" data-country="PH">+63</option>
                                    <option value="+64" data-country="NZ">+64</option>
                                    <option value="+65" data-country="SG">+65</option>
                                    <option value="+66" data-country="TH">+66</option>
                                    <option value="+81" data-country="JP">+81</option>
                                    <option value="+82" data-country="KR">+82</option>
                                    <option value="+84" data-country="VN">+84</option>
                                    <option value="+86" data-country="CN">+86</option>
                                    <option value="+90" data-country="TR">+90</option>
                                    <option value="+91" data-country="IN">+91</option>
                                    <option value="+92" data-country="PK">+92</option>
                                    <option value="+93" data-country="AF">+93</option>
                                    <option value="+94" data-country="LK">+94</option>
                                    <option value="+95" data-country="MM">+95</option>
                                    <option value="+98" data-country="IR">+98</option>
                                    <option value="+212" data-country="MA">+212</option>
                                    <option value="+212" data-country="EH">+212</option>
                                    <option value="+213" data-country="DZ">+213</option>
                                    <option value="+216" data-country="TN">+216</option>
                                    <option value="+218" data-country="LY">+218</option>
                                    <option value="+220" data-country="GM">+220</option>
                                    <option value="+221" data-country="SN">+221</option>
                                    <option value="+222" data-country="MR">+222</option>
                                    <option value="+223" data-country="ML">+223</option>
                                    <option value="+224" data-country="GN">+224</option>
                                    <option value="+225" data-country="CI">+225</option>
                                    <option value="+226" data-country="BF">+226</option>
                                    <option value="+227" data-country="NE">+227</option>
                                    <option value="+228" data-country="TG">+228</option>
                                    <option value="+229" data-country="BJ">+229</option>
                                    <option value="+230" data-country="MU">+230</option>
                                    <option value="+231" data-country="LR">+231</option>
                                    <option value="+232" data-country="SL">+232</option>
                                    <option value="+233" data-country="GH">+233</option>
                                    <option value="+234" data-country="NG">+234</option>
                                    <option value="+235" data-country="TD">+235</option>
                                    <option value="+236" data-country="CF">+236</option>
                                    <option value="+237" data-country="CM">+237</option>
                                    <option value="+238" data-country="CV">+238</option>
                                    <option value="+239" data-country="ST">+239</option>
                                    <option value="+240" data-country="GQ">+240</option>
                                    <option value="+241" data-country="GA">+241</option>
                                    <option value="+242" data-country="CG">+242</option>
                                    <option value="+243" data-country="CD">+243</option>
                                    <option value="+244" data-country="AO">+244</option>
                                    <option value="+245" data-country="GW">+245</option>
                                    <option value="+246" data-country="IO">+246</option>
                                    <option value="+248" data-country="SC">+248</option>
                                    <option value="+249" data-country="SD">+249</option>
                                    <option value="+250" data-country="RW">+250</option>
                                    <option value="+251" data-country="ET">+251</option>
                                    <option value="+252" data-country="SO">+252</option>
                                    <option value="+253" data-country="DJ">+253</option>
                                    <option value="+254" data-country="KE">+254</option>
                                    <option value="+255" data-country="TZ">+255</option>
                                    <option value="+256" data-country="UG">+256</option>
                                    <option value="+257" data-country="BI">+257</option>
                                    <option value="+258" data-country="MZ">+258</option>
                                    <option value="+260" data-country="ZM">+260</option>
                                    <option value="+261" data-country="MG">+261</option>
                                    <option value="+262" data-country="YT">+262</option>
                                    <option value="+262" data-country="RE">+262</option>
                                    <option value="+263" data-country="ZW">+263</option>
                                    <option value="+264" data-country="NA">+264</option>
                                    <option value="+265" data-country="MW">+265</option>
                                    <option value="+266" data-country="LS">+266</option>
                                    <option value="+267" data-country="BW">+267</option>
                                    <option value="+268" data-country="SZ">+268</option>
                                    <option value="+269" data-country="KM">+269</option>
                                    <option value="+290" data-country="SH">+290</option>
                                    <option value="+291" data-country="ER">+291</option>
                                    <option value="+297" data-country="AW">+297</option>
                                    <option value="+298" data-country="FO">+298</option>
                                    <option value="+299" data-country="GL">+299</option>
                                    <option value="+350" data-country="GI">+350</option>
                                    <option value="+351" data-country="PT">+351</option>
                                    <option value="+352" data-country="LU">+352</option>
                                    <option value="+353" data-country="IE">+353</option>
                                    <option value="+354" data-country="IS">+354</option>
                                    <option value="+355" data-country="AL">+355</option>
                                    <option value="+356" data-country="MT">+356</option>
                                    <option value="+357" data-country="CY">+357</option>
                                    <option value="+358" data-country="FI">+358</option>
                                    <option value="+359" data-country="BG">+359</option>
                                    <option value="+370" data-country="LT">+370</option>
                                    <option value="+371" data-country="LV">+371</option>
                                    <option value="+372" data-country="EE">+372</option>
                                    <option value="+373" data-country="MD">+373</option>
                                    <option value="+374" data-country="AM">+374</option>
                                    <option value="+375" data-country="BY">+375</option>
                                    <option value="+376" data-country="AD">+376</option>
                                    <option value="+377" data-country="MC">+377</option>
                                    <option value="+378" data-country="SM">+378</option>
                                    <option value="+380" data-country="UA">+380</option>
                                    <option value="+381" data-country="RS">+381</option>
                                    <option value="+382" data-country="ME">+382</option>
                                    <option value="+385" data-country="HR">+385</option>
                                    <option value="+386" data-country="SI">+386</option>
                                    <option value="+387" data-country="BA">+387</option>
                                    <option value="+389" data-country="MK">+389</option>
                                    <option value="+420" data-country="CZ">+420</option>
                                    <option value="+421" data-country="SK">+421</option>
                                    <option value="+423" data-country="LI">+423</option>
                                    <option value="+500" data-country="FK">+500</option>
                                    <option value="+501" data-country="BZ">+501</option>
                                    <option value="+502" data-country="GT">+502</option>
                                    <option value="+503" data-country="SV">+503</option>
                                    <option value="+504" data-country="HN">+504</option>
                                    <option value="+505" data-country="NI">+505</option>
                                    <option value="+506" data-country="CR">+506</option>
                                    <option value="+507" data-country="PA">+507</option>
                                    <option value="+508" data-country="PM">+508</option>
                                    <option value="+509" data-country="HT">+509</option>
                                    <option value="+590" data-country="GP">+590</option>
                                    <option value="+591" data-country="BO">+591</option>
                                    <option value="+592" data-country="GY">+592</option>
                                    <option value="+593" data-country="EC">+593</option>
                                    <option value="+594" data-country="GF">+594</option>
                                    <option value="+595" data-country="PY">+595</option>
                                    <option value="+596" data-country="MQ">+596</option>
                                    <option value="+597" data-country="SR">+597</option>
                                    <option value="+598" data-country="UY">+598</option>
                                    <option value="+599" data-country="AN">+599</option>
                                    <option value="+670" data-country="TL">+670</option>
                                    <option value="+672" data-country="AQ">+672</option>
                                    <option value="+672" data-country="NF">+672</option>
                                    <option value="+673" data-country="BN">+673</option>
                                    <option value="+674" data-country="NR">+674</option>
                                    <option value="+675" data-country="PG">+675</option>
                                    <option value="+676" data-country="TO">+676</option>
                                    <option value="+677" data-country="SB">+677</option>
                                    <option value="+678" data-country="VU">+678</option>
                                    <option value="+679" data-country="FJ">+679</option>
                                    <option value="+680" data-country="PW">+680</option>
                                    <option value="+681" data-country="WF">+681</option>
                                    <option value="+682" data-country="CK">+682</option>
                                    <option value="+683" data-country="NU">+683</option>
                                    <option value="+685" data-country="WS">+685</option>
                                    <option value="+686" data-country="KI">+686</option>
                                    <option value="+687" data-country="NC">+687</option>
                                    <option value="+688" data-country="TV">+688</option>
                                    <option value="+689" data-country="PF">+689</option>
                                    <option value="+690" data-country="TK">+690</option>
                                    <option value="+691" data-country="FM">+691</option>
                                    <option value="+692" data-country="MH">+692</option>
                                    <option value="+850" data-country="KP">+850</option>
                                    <option value="+852" data-country="HK">+852</option>
                                    <option value="+853" data-country="MO">+853</option>
                                    <option value="+855" data-country="KH">+855</option>
                                    <option value="+856" data-country="LA">+856</option>
                                    <option value="+880" data-country="BD">+880</option>
                                    <option value="+886" data-country="TW">+886</option>
                                    <option value="+960" data-country="MV">+960</option>
                                    <option value="+961" data-country="LB">+961</option>
                                    <option value="+962" data-country="JO">+962</option>
                                    <option value="+963" data-country="SY">+963</option>
                                    <option value="+964" data-country="IQ">+964</option>
                                    <option value="+965" data-country="KW">+965</option>
                                    <option value="+966" data-country="SA">+966</option>
                                    <option value="+967" data-country="YE">+967</option>
                                    <option value="+968" data-country="OM">+968</option>
                                    <option value="+970" data-country="PS">+970</option>
                                    <option value="+971" data-country="AE">+971</option>
                                    <option value="+972" data-country="IL">+972</option>
                                    <option value="+973" data-country="BH">+973</option>
                                    <option value="+974" data-country="QA">+974</option>
                                    <option value="+975" data-country="BT">+975</option>
                                    <option value="+976" data-country="MN">+976</option>
                                    <option value="+977" data-country="NP">+977</option>
                                    <option value="+992" data-country="TJ">+992</option>
                                    <option value="+993" data-country="TM">+993</option>
                                    <option value="+994" data-country="AZ">+994</option>
                                    <option value="+995" data-country="GE">+995</option>
                                    <option value="+996" data-country="KG">+996</option>
                                    <option value="+998" data-country="UZ">+998</option>
                                </select>
                                <input type="tel" id="orgPhone" name="organization_phone" 
                                       class="flex-1 px-4 py-3 border border-gray-300 rounded-r-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200 border-l-0"
                                       placeholder="<?php echo $currentLanguage === 'en' ? 'Enter phone number' : ($translationService->translateText('Enter phone number', 'en', $currentLanguage) ?? 'Enter phone number'); ?>">
                            </div>
                        </div>

                        <div class="md:col-span-2">
                            <label for="orgAddress" class="block text-sm font-medium text-gray-700 mb-2">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Address';
                                } else {
                                    echo $translationService->translateText('Address', 'en', $currentLanguage) ?? 'Address';
                                }
                                ?>
                            </label>
                            <textarea id="orgAddress" name="organization_address" rows="3" 
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200"
                                      placeholder="<?php echo $currentLanguage === 'en' ? 'Enter address' : ($translationService->translateText('Enter address', 'en', $currentLanguage) ?? 'Enter address'); ?>"></textarea>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Crusade Details Section -->
                <div class="registration-section" id="crusadeSection">
                    <div class="flex items-center mb-6">
                        <div class="w-8 h-8 bg-gold rounded-full flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-gray-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <h2 class="text-xl font-semibold text-gray-900">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Crusade Details';
                            } else {
                                echo $translationService->translateText('Crusade Details', 'en', $currentLanguage) ?? 'Crusade Details';
                            }
                            ?> <span class="text-red-500">*</span>
                        </h2>
                    </div>

                    <?php if ($registrationType === 'church'): ?>
                    <!-- Church/Group/Zone Specific Fields -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="md:col-span-2">
                            <label for="numberOfCrusades" class="block text-sm font-medium text-gray-700 mb-2">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Number of Crusades';
                                } else {
                                    echo $translationService->translateText('Number of Crusades', 'en', $currentLanguage) ?? 'Number of Crusades';
                                }
                                ?> <span class="text-red-500">*</span>
                            </label>
                            <input type="number" id="numberOfCrusades" name="number_of_crusades" required min="1" max="100"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200"
                                   placeholder="<?php echo $currentLanguage === 'en' ? 'Enter number of crusades planned' : ($translationService->translateText('Enter number of crusades planned', 'en', $currentLanguage) ?? 'Enter number of crusades planned'); ?>">
                        </div>
                        
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-3">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Select Countries';
                                } else {
                                    echo $translationService->translateText('Select Countries', 'en', $currentLanguage) ?? 'Select Countries';
                                }
                                ?> <span class="text-red-500">*</span>
                            </label>
                            
                            <!-- Search box for countries -->
                            <div class="mb-4">
                                <input type="text" id="countrySearch" 
                                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200"
                                       placeholder="<?php echo $currentLanguage === 'en' ? 'Search countries...' : ($translationService->translateText('Search countries...', 'en', $currentLanguage) ?? 'Search countries...'); ?>">
                            </div>
                            
                            <!-- Selected countries display -->
                            <div class="mb-4 p-3 bg-gray-50 rounded-lg min-h-[40px]">
                                <div class="text-sm text-gray-600 mb-2">
                                    <span id="selectedCountriesCount">0</span> countries selected:
                                </div>
                                <div id="selectedCountriesDisplay" class="flex flex-wrap gap-2">
                                    <!-- Selected countries will appear here as badges -->
                                </div>
                            </div>
                            
                            <!-- Countries checkbox list -->
                            <div class="border border-gray-300 rounded-lg max-h-60 overflow-y-auto">
                                <div id="countriesCheckboxList" class="p-2">
                                    <!-- Countries will be loaded here as checkboxes -->
                                </div>
                            </div>
                            
                            <!-- Hidden input to store selected countries for form submission -->
                            <input type="hidden" id="selectedCountriesInput" name="countries" required>
                        </div>

                        <!-- Crusade Type Field for Church/Group/Zone -->
                        <div class="md:col-span-2">
                            <label for="churchCrusadeTypes" class="block text-sm font-medium text-gray-700 mb-2">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Crusade Type(s)';
                                } else {
                                    echo $translationService->translateText('Crusade Type(s)', 'en', $currentLanguage) ?? 'Crusade Type(s)';
                                }
                                ?> <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <select id="churchCrusadeTypes" name="crusade_types[]" multiple required 
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200"
                                        style="display: none;">
                                    <option value="mega">Mega Crusades (10,000+ people)</option>
                                    <option value="tap2read">TAP2READ Crusades - promoting TAP2READ app with evangelism</option>
                                    <option value="youths-aglow">Youths Aglow Crusades</option>
                                    <option value="teevolution">Teevolution Crusades (Teens-focused)</option>
                                    <option value="say-yes-to-kids">Say Yes To Kids Crusades</option>
                                    <option value="nolb">No One Left Behind Crusades (for the visually/hearing impaired)</option>
                                    <option value="leading-ladies">Leading Ladies Crusades</option>
                                    <option value="mighty-men">Mighty Men Crusades</option>
                                    <option value="professionals">Specialized Crusades to Professionals</option>
                                    <option value="tv">TV Crusades</option>
                                    <option value="social-media">Social Media Crusades</option>
                                    <option value="online">Online Crusades - targeting digital audiences</option>
                                    <option value="mystreamspace">MyStreamSpace Crusades</option>
                                    <option value="mall">Mall Crusades</option>
                                    <option value="school">School Crusades</option>
                                    <option value="hospital">Hospital Crusades</option>
                                    <option value="street">Street Crusades</option>
                                    <option value="village">Village Crusades</option>
                                    <option value="prison">Prison Crusades</option>
                                    <option value="football">Football Crusades</option>
                                    <option value="community">Community Crusades</option>
                                    <option value="transport-station">Bus Station/Train Station Crusades</option>
                                    <option value="other">Other</option>
                                </select>
                                
                                <!-- Custom Dropdown Interface -->
                                <div class="church-crusade-types-dropdown">
                                    <div class="church-crusade-types-selected w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200 cursor-pointer min-h-[3rem] flex items-center justify-between bg-white">
                                        <span class="church-crusade-types-placeholder text-gray-500">
                                            <?php echo $currentLanguage === 'en' ? 'Select crusade type(s) - You can select multiple' : ($translationService->translateText('Select crusade type(s) - You can select multiple', 'en', $currentLanguage) ?? 'Select crusade type(s) - You can select multiple'); ?>
                                        </span>
                                        <svg class="w-5 h-5 text-gray-400 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </div>
                                    <div class="church-crusade-types-options absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto hidden">
                                        <div class="p-2">
                                            <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                                <input type="checkbox" value="mega" class="church-crusade-type-checkbox mr-3 text-primary focus:ring-primary border-gray-300 rounded">
                                                <span>Mega Crusades (10,000+ people)</span>
                                            </label>
                                            <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                                <input type="checkbox" value="tap2read" class="church-crusade-type-checkbox mr-3 text-primary focus:ring-primary border-gray-300 rounded">
                                                <span>TAP2READ Crusades - promoting TAP2READ app with evangelism</span>
                                            </label>
                                            <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                                <input type="checkbox" value="youths-aglow" class="church-crusade-type-checkbox mr-3 text-primary focus:ring-primary border-gray-300 rounded">
                                                <span>Youths Aglow Crusades</span>
                                            </label>
                                            <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                                <input type="checkbox" value="teevolution" class="church-crusade-type-checkbox mr-3 text-primary focus:ring-primary border-gray-300 rounded">
                                                <span>Teevolution Crusades (Teens-focused)</span>
                                            </label>
                                            <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                                <input type="checkbox" value="say-yes-to-kids" class="church-crusade-type-checkbox mr-3 text-primary focus:ring-primary border-gray-300 rounded">
                                                <span>Say Yes To Kids Crusades</span>
                                            </label>
                                            <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                                <input type="checkbox" value="nolb" class="church-crusade-type-checkbox mr-3 text-primary focus:ring-primary border-gray-300 rounded">
                                                <span>No One Left Behind Crusades (for the visually/hearing impaired)</span>
                                            </label>
                                            <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                                <input type="checkbox" value="leading-ladies" class="church-crusade-type-checkbox mr-3 text-primary focus:ring-primary border-gray-300 rounded">
                                                <span>Leading Ladies Crusades</span>
                                            </label>
                                            <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                                <input type="checkbox" value="mighty-men" class="church-crusade-type-checkbox mr-3 text-primary focus:ring-primary border-gray-300 rounded">
                                                <span>Mighty Men Crusades</span>
                                            </label>
                                            <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                                <input type="checkbox" value="professionals" class="church-crusade-type-checkbox mr-3 text-primary focus:ring-primary border-gray-300 rounded">
                                                <span>Specialized Crusades to Professionals</span>
                                            </label>
                                            <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                                <input type="checkbox" value="tv" class="church-crusade-type-checkbox mr-3 text-primary focus:ring-primary border-gray-300 rounded">
                                                <span>TV Crusades</span>
                                            </label>
                                            <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                                <input type="checkbox" value="social-media" class="church-crusade-type-checkbox mr-3 text-primary focus:ring-primary border-gray-300 rounded">
                                                <span>Social Media Crusades</span>
                                            </label>
                                            <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                                <input type="checkbox" value="online" class="church-crusade-type-checkbox mr-3 text-primary focus:ring-primary border-gray-300 rounded">
                                                <span>Online Crusades - targeting digital audiences</span>
                                            </label>
                                            <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                                <input type="checkbox" value="mystreamspace" class="church-crusade-type-checkbox mr-3 text-primary focus:ring-primary border-gray-300 rounded">
                                                <span>MyStreamSpace Crusades</span>
                                            </label>
                                            <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                                <input type="checkbox" value="mall" class="church-crusade-type-checkbox mr-3 text-primary focus:ring-primary border-gray-300 rounded">
                                                <span>Mall Crusades</span>
                                            </label>
                                            <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                                <input type="checkbox" value="school" class="church-crusade-type-checkbox mr-3 text-primary focus:ring-primary border-gray-300 rounded">
                                                <span>School Crusades</span>
                                            </label>
                                            <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                                <input type="checkbox" value="hospital" class="church-crusade-type-checkbox mr-3 text-primary focus:ring-primary border-gray-300 rounded">
                                                <span>Hospital Crusades</span>
                                            </label>
                                            <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                                <input type="checkbox" value="street" class="church-crusade-type-checkbox mr-3 text-primary focus:ring-primary border-gray-300 rounded">
                                                <span>Street Crusades</span>
                                            </label>
                                            <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                                <input type="checkbox" value="village" class="church-crusade-type-checkbox mr-3 text-primary focus:ring-primary border-gray-300 rounded">
                                                <span>Village Crusades</span>
                                            </label>
                                            <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                                <input type="checkbox" value="prison" class="church-crusade-type-checkbox mr-3 text-primary focus:ring-primary border-gray-300 rounded">
                                                <span>Prison Crusades</span>
                                            </label>
                                            <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                                <input type="checkbox" value="football" class="church-crusade-type-checkbox mr-3 text-primary focus:ring-primary border-gray-300 rounded">
                                                <span>Football Crusades</span>
                                            </label>
                                            <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                                <input type="checkbox" value="community" class="church-crusade-type-checkbox mr-3 text-primary focus:ring-primary border-gray-300 rounded">
                                                <span>Community Crusades</span>
                                            </label>
                                            <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                                <input type="checkbox" value="transport-station" class="church-crusade-type-checkbox mr-3 text-primary focus:ring-primary border-gray-300 rounded">
                                                <span>Bus Station/Train Station Crusades</span>
                                            </label>
                                            <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                                <input type="checkbox" value="other" class="church-crusade-type-checkbox mr-3 text-primary focus:ring-primary border-gray-300 rounded">
                                                <span>Other</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Other Crusade Types Input Field (Hidden by default) -->
                        <div id="churchOtherCrusadeTypesField" class="md:col-span-2 hidden">
                            <label for="churchOtherCrusadeTypes" class="block text-sm font-medium text-gray-700 mb-2">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Please specify other crusade type(s)';
                                } else {
                                    echo $translationService->translateText('Please specify other crusade type(s)', 'en', $currentLanguage) ?? 'Please specify other crusade type(s)';
                                }
                                ?> <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="churchOtherCrusadeTypes" name="church_other_crusade_types" 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200"
                                   placeholder="<?php echo $currentLanguage === 'en' ? 'Enter crusade types separated by commas (e.g., Business Crusade, Sports Crusade)' : ($translationService->translateText('Enter crusade types separated by commas (e.g., Business Crusade, Sports Crusade)', 'en', $currentLanguage) ?? 'Enter crusade types separated by commas (e.g., Business Crusade, Sports Crusade)'); ?>">
                        </div>
                    </div>
                    
                    <?php else: ?>
                    <!-- Individual/Organisation Fields (Original) -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="md:col-span-2">
                            <label for="crusadeTitle" class="block text-sm font-medium text-gray-700 mb-2">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Crusade Title';
                                } else {
                                    echo $translationService->translateText('Crusade Title', 'en', $currentLanguage) ?? 'Crusade Title';
                                }
                                ?> <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <input type="text" id="crusadeTitle" name="crusade_title" required 
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200 pr-32"
                                       placeholder="<?php echo $currentLanguage === 'en' ? 'Enter the proposed crusade title' : ($translationService->translateText('Enter the proposed crusade title', 'en', $currentLanguage) ?? 'Enter the proposed crusade title'); ?>">
                                <button type="button" id="unsure-btn" class="absolute right-3 top-1/2 transform -translate-y-1/2 bg-gray-200 hover:bg-gray-300 text-gray-700 text-xs px-3 py-2 transition-all duration-300 hover:scale-105 focus:outline-none whitespace-nowrap rounded">
                                    Unsure? Click here
                                </button>
                            </div>
                        </div>
                        
                        <div>
                            <label for="countrySelect" class="block text-sm font-medium text-gray-700 mb-2">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Country';
                                } else {
                                    echo $translationService->translateText('Country', 'en', $currentLanguage) ?? 'Country';
                                }
                                ?> <span class="text-red-500">*</span>
                            </label>
                            <select id="countrySelect" name="country" required 
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200">
                                <option value="">Select country</option>
                                <!-- Countries will be loaded from countries.json -->
                            </select>
                        </div>
                        
                        <div>
                            <label for="cityLocation" class="block text-sm font-medium text-gray-700 mb-2">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'City/Location';
                                } else {
                                    echo $translationService->translateText('City/Location', 'en', $currentLanguage) ?? 'City/Location';
                                }
                                ?> <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <input type="text" id="cityLocation" name="location" required 
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200 pr-32"
                                       placeholder="<?php echo $currentLanguage === 'en' ? 'Enter city or specific location' : ($translationService->translateText('Enter city or specific location', 'en', $currentLanguage) ?? 'Enter city or specific location'); ?>">
                                <button type="button" id="city-unsure-btn" class="absolute right-3 top-1/2 transform -translate-y-1/2 bg-gray-200 hover:bg-gray-300 text-gray-700 text-xs px-3 py-2 transition-all duration-300 hover:scale-105 focus:outline-none whitespace-nowrap rounded">
                                    Unsure? Click here
                                </button>
                            </div>
                        </div>
                        
                        <div>
                            <label for="crusadeType" class="block text-sm font-medium text-gray-700 mb-2">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Crusade Type';
                                } else {
                                    echo $translationService->translateText('Crusade Type', 'en', $currentLanguage) ?? 'Crusade Type';
                                }
                                ?> <span class="text-red-500">*</span>
                            </label>
                            <select id="crusadeType" name="crusade_type" required 
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200"
                                    onchange="handleCrusadeTypeSelection()">
                                <option value="">Select crusade type</option>
                                <option value="mega">Mega Crusades (10,000+ people)</option>
                                <option value="tap2read">TAP2READ Crusades - promoting TAP2READ app with evangelism</option>
                                <option value="youths-aglow">Youths Aglow Crusades</option>
                                <option value="teevolution">Teevolution Crusades (Teens-focused)</option>
                                <option value="say-yes-to-kids">Say Yes To Kids Crusades</option>
                                <option value="nolb">No One Left Behind Crusades (for the visually/hearing impaired)</option>
                                <option value="leading-ladies">Leading Ladies Crusades</option>
                                <option value="mighty-men">Mighty Men Crusades</option>
                                <option value="professionals">Specialized Crusades to Professionals</option>
                                <option value="tv">TV Crusades</option>
                                <option value="social-media">Social Media Crusades</option>
                                <option value="online">Online Crusades - targeting digital audiences</option>
                                <option value="mystreamspace">MyStreamSpace Crusades</option>
                                <option value="mall">Mall Crusades</option>
                                <option value="school">School Crusades</option>
                                <option value="hospital">Hospital Crusades</option>
                                <option value="street">Street Crusades</option>
                                <option value="village">Village Crusades</option>
                                <option value="prison">Prison Crusades</option>
                                <option value="football">Football Crusades</option>
                                <option value="community">Community Crusades</option>
                                <option value="transport-station">Bus Station/Train Station Crusades</option>
                                <option value="others">Others</option>
                            </select>
                        </div>
                        
                        <!-- Others Crusade Type Field (Hidden by default) -->
                        <div id="otherCrusadeTypeField" class="md:col-span-2 hidden">
                            <label for="otherCrusadeType" class="block text-sm font-medium text-gray-700 mb-2">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Please specify the crusade type';
                                } else {
                                    echo $translationService->translateText('Please specify the crusade type', 'en', $currentLanguage) ?? 'Please specify the crusade type';
                                }
                                ?> <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="otherCrusadeType" name="other_crusade_type" 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200"
                                   placeholder="<?php echo $currentLanguage === 'en' ? 'Enter the specific type of crusade' : ($translationService->translateText('Enter the specific type of crusade', 'en', $currentLanguage) ?? 'Enter the specific type of crusade'); ?>">
                        </div>
                        
                        <div class="md:col-span-2">
                            <label for="crusadeVenue" class="block text-sm font-medium text-gray-700 mb-2">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Venue';
                                } else {
                                    echo $translationService->translateText('Venue', 'en', $currentLanguage) ?? 'Venue';
                                }
                                ?> <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <input type="text" id="crusadeVenue" name="venue" required 
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200 pr-32"
                                       placeholder="<?php echo $currentLanguage === 'en' ? 'Enter venue (stadium, auditorium, etc.)' : ($translationService->translateText('Enter venue (stadium, auditorium, etc.)', 'en', $currentLanguage) ?? 'Enter venue (stadium, auditorium, etc.)'); ?>">
                                <button type="button" id="venue-unsure-btn" class="absolute right-3 top-1/2 transform -translate-y-1/2 bg-gray-200 hover:bg-gray-300 text-gray-700 text-xs px-3 py-2 transition-all duration-300 hover:scale-105 focus:outline-none whitespace-nowrap rounded">
                                    Unsure? Click here
                                </button>
                            </div>
                        </div>
                        
                        <div class="md:col-span-2">
                            <label for="expectedAttendance" class="block text-sm font-medium text-gray-700 mb-2">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Expected Attendance';
                                } else {
                                    echo $translationService->translateText('Expected Attendance', 'en', $currentLanguage) ?? 'Expected Attendance';
                                }
                                ?> <span class="text-red-500">*</span>
                            </label>
                            <select id="expectedAttendance" name="attendance" required 
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200">
                                <option value="">Select expected attendance</option>
                                <option value="100-500">100 - 500</option>
                                <option value="500-1000">500 - 1,000</option>
                                <option value="1000-5000">1,000 - 5,000</option>
                                <option value="5000-10000">5,000 - 10,000</option>
                                <option value="10000+">10,000+</option>
                                <option value="unsure">Unsure</option>
                            </select>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Additional Information Section -->
                <div class="registration-section" id="additionalSection">
                    <div class="flex items-center mb-6">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <h2 class="text-xl font-semibold text-gray-900">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Additional Information';
                            } else {
                                echo $translationService->translateText('Additional Information', 'en', $currentLanguage) ?? 'Additional Information';
                            }
                            ?>
                        </h2>
                    </div>

                    <div>
                        <label for="additionalComments" class="block text-sm font-medium text-gray-700 mb-2">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Additional Comments or Special Requests';
                            } else {
                                echo $translationService->translateText('Additional Comments or Special Requests', 'en', $currentLanguage) ?? 'Additional Comments or Special Requests';
                            }
                            ?>
                        </label>
                        <textarea id="additionalComments" name="additional_comments" rows="6" 
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200 resize-none"
                                  placeholder="<?php echo $currentLanguage === 'en' ? 'Any additional information, special requirements, or comments about your crusade request' : ($translationService->translateText('Any additional information, special requirements, or comments about your crusade request', 'en', $currentLanguage) ?? 'Any additional information, special requirements, or comments about your crusade request'); ?>"></textarea>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-center pt-6">
                    <button type="submit" class="px-8 py-4 bg-gradient-to-r from-primary to-primaryDark text-white font-medium rounded-lg hover:from-primaryDark hover:to-primary transition-all duration-200 shadow-lg hover:shadow-xl">
                        <?php 
                        if ($currentLanguage === 'en') {
                            echo 'Submit Registration';
                        } else {
                            echo $translationService->translateText('Submit Registration', 'en', $currentLanguage) ?? 'Submit Registration';
                        }
                        ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* Custom dropdown styles for church crusade types */
.church-crusade-types-dropdown {
    position: relative;
}

.church-crusade-types-options {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 50;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    max-height: 15rem;
    overflow-y: auto;
}

.church-crusade-types-options.hidden {
    display: none;
}

.church-crusade-types-selected svg {
    transition: transform 0.2s ease-in-out;
}

.church-crusade-types-selected svg.rotate-180 {
    transform: rotate(180deg);
}

/* Ensure dropdown appears above other elements */
.church-crusade-types-dropdown .church-crusade-types-options {
    z-index: 9999;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const registrationType = '<?php echo $registrationType; ?>';
    const churchSection = document.getElementById('churchSection');
    
    console.log('Registration type:', registrationType);
    
    // Show/hide church section based on registration type
    if (registrationType === 'individual') {
        if (churchSection) churchSection.style.display = 'none';
    } else {
        if (churchSection) churchSection.style.display = 'block';
    }
    
    // Load countries from JSON file
    loadCountries();
    
    // Setup unsure button functionality (only for non-church registrations)
    if (registrationType !== 'church') {
        setupUnsureButton();
        setupCityUnsureButton();
        setupVenueUnsureButton();
    }
    
    // Setup crusade types multi-select dropdown for church registrations
    if (registrationType === 'church') {
        setupChurchCrusadeTypesDropdown();
    }
    
    // Debug: Check if church elements exist
    if (registrationType === 'church') {
        const checkboxList = document.getElementById('countriesCheckboxList');
        const searchInput = document.getElementById('countrySearch');
        const selectedDisplay = document.getElementById('selectedCountriesDisplay');
        
        console.log('Church elements check:');
        console.log('- Checkbox list:', checkboxList ? 'Found' : 'NOT FOUND');
        console.log('- Search input:', searchInput ? 'Found' : 'NOT FOUND');
        console.log('- Selected display:', selectedDisplay ? 'Found' : 'NOT FOUND');
    }
    
    // Setup phone number formatting
    setupPhoneNumberFormatting();
    
    // Form validation
    const form = document.getElementById('crusadeRegistrationForm');
    form.addEventListener('submit', function(e) {
        // Combine country codes with phone numbers before submission
        combinePhoneNumbers();
        
        // Additional validation for church registrations
        if (registrationType === 'church') {
            const selectedCountriesInput = document.getElementById('selectedCountriesInput');
            
            if (selectedCountriesInput) {
                const selectedCountries = selectedCountriesInput.value.split(',').filter(c => c.trim() !== '');
                
                if (selectedCountries.length === 0) {
                    e.preventDefault();
                    alert('Please select at least one country.');
                    return false;
                }
            }
            
            // Validate crusade types for church registration
            const crusadeTypesSelect = document.getElementById('churchCrusadeTypes');
            if (crusadeTypesSelect) {
                const selectedOptions = Array.from(crusadeTypesSelect.selectedOptions);
                if (selectedOptions.length === 0) {
                    e.preventDefault();
                    alert('Please select at least one crusade type.');
                    return false;
                }
                
                // Check if "other" is selected and validate the custom input
                const hasOther = selectedOptions.some(option => option.value === 'other');
                if (hasOther) {
                    const otherInput = document.getElementById('churchOtherCrusadeTypes');
                    if (!otherInput || !otherInput.value.trim()) {
                        e.preventDefault();
                        alert('Please specify the other crusade type(s).');
                        return false;
                    }
                }
            }
        }
        
        console.log('Form submitted for registration type:', registrationType);
        
        // Debug: Log form data before submission
        const formData = new FormData(form);
        console.log('Form data being submitted:');
        for (let [key, value] of formData.entries()) {
            console.log(key + ':', value);
        }
    });
});

// Load countries from JSON file
function loadCountries() {
    console.log('loadCountries function called');
    
    const registrationType = '<?php echo $registrationType; ?>';
    
    fetch('world/countries.json')
        .then(response => {
            console.log('Countries fetch response:', response.status, response.statusText);
            if (!response.ok) {
                throw new Error(`Network response was not ok: ${response.status} ${response.statusText}`);
            }
            return response.json();
        })
        .then(countries => {
            console.log('Countries data loaded:', countries);
            if (!countries || !Array.isArray(countries)) {
                throw new Error('Invalid countries data format');
            }
            
            if (registrationType === 'church') {
                setupCountryCheckboxes(countries);
            } else {
                // Handle single select for individual/organisation
                const countrySelect = document.getElementById('countrySelect');
                if (countrySelect) {
                    countrySelect.innerHTML = '';
                    
                    const placeholderOption = document.createElement('option');
                    placeholderOption.value = '';
                    placeholderOption.textContent = 'Select country';
                    countrySelect.appendChild(placeholderOption);
                    
                    countries.forEach(country => {
                        const option = document.createElement('option');
                        option.value = country.toLowerCase().replace(/\s+/g, '-');
                        option.textContent = country;
                        countrySelect.appendChild(option);
                    });
                }
            }
            
            console.log(`Successfully loaded ${countries.length} countries`);
        })
        .catch(error => {
            console.error('Error loading countries:', error);
            // Fallback countries if JSON fails to load
            const fallbackCountries = [
                'United States', 'United Kingdom', 'Canada', 'Australia', 'Nigeria', 'South Africa', 
                'Ghana', 'Kenya', 'India', 'Philippines', 'Brazil', 'Germany', 'France', 'Jamaica'
            ];
            
            if (registrationType === 'church') {
                setupCountryCheckboxes(fallbackCountries);
            } else {
                const countrySelect = document.getElementById('countrySelect');
                if (countrySelect) {
                    countrySelect.innerHTML = '';
                    
                    const placeholderOption = document.createElement('option');
                    placeholderOption.value = '';
                    placeholderOption.textContent = 'Select country';
                    countrySelect.appendChild(placeholderOption);
                    
                    fallbackCountries.forEach(country => {
                        const option = document.createElement('option');
                        option.value = country.toLowerCase().replace(/\s+/g, '-');
                        option.textContent = country;
                        countrySelect.appendChild(option);
                    });
                }
            }
            
            console.log(`Using fallback countries`);
        });
}

// Setup unsure button for crusade title
function setupUnsureButton() {
    const unsureBtn = document.getElementById('unsure-btn');
    if (unsureBtn) {
        unsureBtn.addEventListener('click', function() {
            const crusadeTitleInput = document.getElementById('crusadeTitle');
            if (crusadeTitleInput) {
                crusadeTitleInput.value = 'Unsure';
            }
        });
    }
}

// Setup unsure button for city/location
function setupCityUnsureButton() {
    const cityUnsureBtn = document.getElementById('city-unsure-btn');
    if (cityUnsureBtn) {
        cityUnsureBtn.addEventListener('click', function() {
            const cityLocationInput = document.getElementById('cityLocation');
            if (cityLocationInput) {
                cityLocationInput.value = 'Unsure';
            }
        });
    }
}

// Setup country checkboxes for church registrations
function setupCountryCheckboxes(countries) {
    console.log('setupCountryCheckboxes called with', countries.length, 'countries');
    
    const checkboxList = document.getElementById('countriesCheckboxList');
    const searchInput = document.getElementById('countrySearch');
    
    console.log('Elements found:');
    console.log('- checkboxList:', checkboxList);
    console.log('- searchInput:', searchInput);
    
    if (!checkboxList) {
        console.error('countriesCheckboxList element not found!');
        return;
    }
    
    let selectedCountries = [];
    
    // Create checkboxes for all countries
    function renderCountries(countriesToShow) {
        checkboxList.innerHTML = '';
        
        countriesToShow.forEach(country => {
            const countryValue = country.toLowerCase().replace(/\s+/g, '-');
            const isSelected = selectedCountries.includes(countryValue);
            
            const checkboxContainer = document.createElement('div');
            checkboxContainer.className = 'flex items-center p-2 hover:bg-gray-100 rounded cursor-pointer';
            
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.id = `country-${countryValue}`;
            checkbox.value = countryValue;
            checkbox.checked = isSelected;
            checkbox.className = 'mr-3 h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded';
            
            const label = document.createElement('label');
            label.htmlFor = `country-${countryValue}`;
            label.textContent = country;
            label.className = 'flex-1 cursor-pointer text-sm';
            
            // Handle checkbox change
            checkbox.addEventListener('change', function() {
                updateSelectedCountriesDisplay(countries);
            });
            
            // Make entire container clickable
            checkboxContainer.addEventListener('click', function(e) {
                if (e.target !== checkbox) {
                    checkbox.checked = !checkbox.checked;
                    checkbox.dispatchEvent(new Event('change'));
                }
            });
            
            checkboxContainer.appendChild(checkbox);
            checkboxContainer.appendChild(label);
            checkboxList.appendChild(checkboxContainer);
        });
    }
    
    // Initial render
    renderCountries(countries);
    
    // Setup search functionality
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const filteredCountries = countries.filter(country => 
                country.toLowerCase().includes(searchTerm)
            );
            renderCountries(filteredCountries);
        });
    }
}

// Update the selected countries display
function updateSelectedCountriesDisplay(allCountries) {
    const selectedCountriesCount = document.getElementById('selectedCountriesCount');
    const selectedCountriesDisplay = document.getElementById('selectedCountriesDisplay');
    const selectedCountriesInput = document.getElementById('selectedCountriesInput');
    
    if (!selectedCountriesCount || !selectedCountriesDisplay || !selectedCountriesInput) {
        console.error('Required elements not found for updateSelectedCountriesDisplay');
        return;
    }
    
    // Get selected countries from checkboxes
    const selectedCheckboxes = document.querySelectorAll('#countriesCheckboxList input[type="checkbox"]:checked');
    const selectedCountries = Array.from(selectedCheckboxes).map(cb => cb.value);
    
    console.log('Selected countries:', selectedCountries);
    
    // Update count
    selectedCountriesCount.textContent = selectedCountries.length;
    
    // Update display badges
    selectedCountriesDisplay.innerHTML = '';
    selectedCountries.forEach(countryValue => {
        // Find the country name from the value
        const countryName = allCountries.find(country => 
            country.toLowerCase().replace(/\s+/g, '-') === countryValue
        ) || countryValue.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        
        const badge = document.createElement('span');
        badge.className = 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary text-white';
        badge.innerHTML = `
            ${countryName}
            <button type="button" class="ml-1 text-white hover:text-gray-200" onclick="removeCountry('${countryValue}')">
                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
        `;
        selectedCountriesDisplay.appendChild(badge);
    });
    
    // Update hidden input for form submission
    selectedCountriesInput.value = selectedCountries.join(',');
    
    console.log('Hidden input value set to:', selectedCountriesInput.value);
    
    // Update form validation
    if (selectedCountries.length > 0) {
        selectedCountriesInput.setCustomValidity('');
    } else {
        selectedCountriesInput.setCustomValidity('Please select at least one country');
    }
}

// Remove country function (called from badge close button)
function removeCountry(countryValue) {
    const checkbox = document.getElementById(`country-${countryValue}`);
    if (checkbox) {
        checkbox.checked = false;
        checkbox.dispatchEvent(new Event('change'));
    }
}

// Setup unsure button for venue
function setupVenueUnsureButton() {
    const venueUnsureBtn = document.getElementById('venue-unsure-btn');
    if (venueUnsureBtn) {
        venueUnsureBtn.addEventListener('click', function() {
            const venueInput = document.getElementById('crusadeVenue');
            if (venueInput) {
                venueInput.value = 'Unsure';
            }
        });
    }
}

// Setup crusade types multi-select dropdown
function setupCrusadeTypesDropdown() {
    const dropdown = document.querySelector('.crusade-types-dropdown');
    const selected = document.querySelector('.crusade-types-selected');
    const options = document.querySelector('.crusade-types-options');
    const placeholder = document.querySelector('.crusade-types-placeholder');
    const checkboxes = document.querySelectorAll('.crusade-type-checkbox');
    const hiddenSelect = document.getElementById('crusadeTypes');
    const chevron = selected.querySelector('svg');
    
    if (!dropdown || !selected || !options || !placeholder || !hiddenSelect) {
        console.log('Crusade types dropdown elements not found');
        return;
    }
    
    // Toggle dropdown
    selected.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        options.classList.toggle('hidden');
        chevron.classList.toggle('rotate-180');
    });
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!dropdown.contains(e.target)) {
            options.classList.add('hidden');
            chevron.classList.remove('rotate-180');
        }
    });
    
    // Handle checkbox changes
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectedDisplay();
            updateHiddenSelect();
        });
    });
    
    function updateSelectedDisplay() {
        const selectedValues = [];
        const selectedTexts = [];
        
        checkboxes.forEach(checkbox => {
            if (checkbox.checked) {
                selectedValues.push(checkbox.value);
                selectedTexts.push(checkbox.parentElement.querySelector('span').textContent);
            }
        });
        
        if (selectedValues.length === 0) {
            placeholder.textContent = placeholder.getAttribute('data-original') || 'Select crusade type(s) - You can select multiple';
            placeholder.classList.add('text-gray-500');
            placeholder.classList.remove('text-gray-900');
        } else if (selectedValues.length === 1) {
            placeholder.textContent = selectedTexts[0];
            placeholder.classList.remove('text-gray-500');
            placeholder.classList.add('text-gray-900');
        } else {
            placeholder.textContent = selectedTexts[0] + ' and ' + (selectedValues.length - 1) + ' more';
            placeholder.classList.remove('text-gray-500');
            placeholder.classList.add('text-gray-900');
        }
    }
    
    function updateHiddenSelect() {
        // Clear all selected options
        Array.from(hiddenSelect.options).forEach(option => {
            option.selected = false;
        });
        
        // Select the checked options
        checkboxes.forEach(checkbox => {
            if (checkbox.checked) {
                const option = hiddenSelect.querySelector(`option[value="${checkbox.value}"]`);
                if (option) {
                    option.selected = true;
                }
            }
        });
        
        // Trigger change event for validation
        hiddenSelect.dispatchEvent(new Event('change'));
    }
    
    // Store original placeholder text
    placeholder.setAttribute('data-original', placeholder.textContent);
}

// Setup phone number formatting for all phone inputs
function setupPhoneNumberFormatting() {
    const phoneInputs = document.querySelectorAll('input[type="tel"]');
    
    phoneInputs.forEach(input => {
        // Prevent non-numeric input (allow spaces, dashes, parentheses for international formats)
        input.addEventListener('keypress', function(e) {
            // Allow backspace, delete, tab, escape, enter, space, dash, parentheses
            if ([8, 9, 27, 13, 46, 32, 45, 40, 41].indexOf(e.keyCode) !== -1 ||
                // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
                (e.keyCode === 65 && e.ctrlKey === true) ||
                (e.keyCode === 67 && e.ctrlKey === true) ||
                (e.keyCode === 86 && e.ctrlKey === true) ||
                (e.keyCode === 88 && e.ctrlKey === true)) {
                return;
            }
            // Ensure that it is a number and stop the keypress for letters
            if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
                e.preventDefault();
            }
        });
        
        // Get the associated country code dropdown
        const getCountryCode = () => {
            const inputId = input.id;
            let countryCodeSelect = null;
            
            if (inputId === 'phone') {
                countryCodeSelect = document.getElementById('country-code');
            } else if (inputId === 'contactPhone') {
                countryCodeSelect = document.getElementById('contact-country-code');
            } else if (inputId === 'orgPhone') {
                countryCodeSelect = document.getElementById('org-country-code');
            }
            
            return countryCodeSelect ? countryCodeSelect.value : '+1';
        };
        
        // Format phone number based on country
        const formatPhoneNumber = (value, countryCode) => {
            // Remove all non-digit characters
            const digits = value.replace(/\D/g, '');
            
            // Different formatting based on country code
            if (countryCode.startsWith('+1')) {
                // North American format (US, Canada, etc.)
                if (digits.length > 10) return digits.slice(0, 10);
                if (digits.length <= 3) return digits;
                if (digits.length <= 6) return digits.slice(0, 3) + '-' + digits.slice(3);
                return digits.slice(0, 3) + '-' + digits.slice(3, 6) + '-' + digits.slice(6);
            } else if (countryCode.startsWith('+44')) {
                // UK format
                if (digits.length > 11) return digits.slice(0, 11);
                if (digits.length <= 4) return digits;
                if (digits.length <= 7) return digits.slice(0, 4) + ' ' + digits.slice(4);
                return digits.slice(0, 4) + ' ' + digits.slice(4, 7) + ' ' + digits.slice(7);
            } else if (countryCode.startsWith('+234') || countryCode.startsWith('+233') || countryCode.startsWith('+254')) {
                // Nigerian, Ghanaian, Kenyan format
                if (digits.length > 10) return digits.slice(0, 10);
                if (digits.length <= 3) return digits;
                if (digits.length <= 6) return digits.slice(0, 3) + ' ' + digits.slice(3);
                return digits.slice(0, 3) + ' ' + digits.slice(3, 6) + ' ' + digits.slice(6);
            } else if (countryCode.startsWith('+91')) {
                // Indian format
                if (digits.length > 10) return digits.slice(0, 10);
                if (digits.length <= 5) return digits;
                return digits.slice(0, 5) + ' ' + digits.slice(5);
            } else if (countryCode.startsWith('+86')) {
                // Chinese format
                if (digits.length > 11) return digits.slice(0, 11);
                if (digits.length <= 3) return digits;
                if (digits.length <= 7) return digits.slice(0, 3) + ' ' + digits.slice(3);
                return digits.slice(0, 3) + ' ' + digits.slice(3, 7) + ' ' + digits.slice(7);
            } else {
                // Generic international format - just group digits
                if (digits.length > 15) return digits.slice(0, 15); // Max international length
                if (digits.length <= 3) return digits;
                if (digits.length <= 6) return digits.slice(0, 3) + ' ' + digits.slice(3);
                if (digits.length <= 9) return digits.slice(0, 3) + ' ' + digits.slice(3, 6) + ' ' + digits.slice(6);
                return digits.slice(0, 3) + ' ' + digits.slice(3, 6) + ' ' + digits.slice(6, 9) + ' ' + digits.slice(9);
            }
        };
        
        // Format phone number on input
        input.addEventListener('input', function(e) {
            const countryCode = getCountryCode();
            const formattedValue = formatPhoneNumber(e.target.value, countryCode);
            e.target.value = formattedValue;
        });
        
        // Handle paste events
        input.addEventListener('paste', function(e) {
            setTimeout(() => {
                const countryCode = getCountryCode();
                const formattedValue = formatPhoneNumber(e.target.value, countryCode);
                e.target.value = formattedValue;
            }, 10);
        });
        
        // Re-format when country code changes
        const countryCodeSelect = input.id === 'phone' ? document.getElementById('country-code') :
                                 input.id === 'contactPhone' ? document.getElementById('contact-country-code') :
                                 input.id === 'orgPhone' ? document.getElementById('org-country-code') : null;
        
        if (countryCodeSelect) {
            countryCodeSelect.addEventListener('change', function() {
                if (input.value) {
                    const formattedValue = formatPhoneNumber(input.value, this.value);
                    input.value = formattedValue;
                }
            });
        }
    });
}

// Combine country codes with phone numbers before form submission
function combinePhoneNumbers() {
    // Personal phone number
    const countryCode = document.getElementById('country-code');
    const phone = document.getElementById('phone');
    if (countryCode && phone && phone.value) {
        phone.value = countryCode.value + ' ' + phone.value;
    }
    
    // Contact phone number (for church registration)
    const contactCountryCode = document.getElementById('contact-country-code');
    const contactPhone = document.getElementById('contactPhone');
    if (contactCountryCode && contactPhone && contactPhone.value) {
        contactPhone.value = contactCountryCode.value + ' ' + contactPhone.value;
    }
    
    // Organization phone number
    const orgCountryCode = document.getElementById('org-country-code');
    const orgPhone = document.getElementById('orgPhone');
    if (orgCountryCode && orgPhone && orgPhone.value) {
        orgPhone.value = orgCountryCode.value + ' ' + orgPhone.value;
    }
}

// Handle zone selection
function handleZoneSelection() {
    const zoneSelect = document.getElementById('zoneSelect');
    console.log('Zone selected:', zoneSelect.value);
    // You can add additional logic here if needed
}

// Handle church type selection
function handleChurchTypeSelection() {
    const churchTypeRadios = document.querySelectorAll('input[name="church_type"]');
    const zoneFields = document.getElementById('zoneFields');
    const groupFields = document.getElementById('groupFields');
    const churchFields = document.getElementById('churchFields');
    const otherFields = document.getElementById('otherFields');
    
    // Get selected church type
    let selectedType = '';
    churchTypeRadios.forEach(radio => {
        if (radio.checked) {
            selectedType = radio.value;
        }
    });
    
    console.log('Church type selected:', selectedType);
    
    // Hide all dynamic fields first
    if (zoneFields) zoneFields.classList.add('hidden');
    if (groupFields) groupFields.classList.add('hidden');
    if (churchFields) churchFields.classList.add('hidden');
    if (otherFields) otherFields.classList.add('hidden');
    
    // Show relevant fields based on selection
    switch(selectedType) {
        case 'zone':
            if (zoneFields) zoneFields.classList.remove('hidden');
            // Clear required attributes from other fields
            clearRequiredFields(['groupName', 'churchGroupName', 'churchName', 'ministryName']);
            break;
            
        case 'group':
            if (groupFields) groupFields.classList.remove('hidden');
            // Set required attribute for group name
            setRequiredFields(['groupName']);
            clearRequiredFields(['churchGroupName', 'churchName', 'ministryName']);
            break;
            
        case 'church':
            if (churchFields) churchFields.classList.remove('hidden');
            // Set required attributes for church fields
            setRequiredFields(['churchGroupName', 'churchName']);
            clearRequiredFields(['groupName', 'ministryName']);
            break;
            
        case 'other':
            if (otherFields) otherFields.classList.remove('hidden');
            // Set required attribute for ministry name
            setRequiredFields(['ministryName']);
            clearRequiredFields(['groupName', 'churchGroupName', 'churchName']);
            break;
            
        default:
            // Clear all required attributes if no selection
            clearRequiredFields(['groupName', 'churchGroupName', 'churchName', 'ministryName']);
            break;
    }
}

// Helper function to set required attributes
function setRequiredFields(fieldIds) {
    fieldIds.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.setAttribute('required', 'required');
        }
    });
}

// Helper function to clear required attributes
function clearRequiredFields(fieldIds) {
    fieldIds.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.removeAttribute('required');
            field.value = ''; // Clear the value as well
        }
    });
}

// Handle crusade type selection (for individual/organisation registrations)
function handleCrusadeTypeSelection() {
    const crusadeTypeSelect = document.getElementById('crusadeType');
    const otherCrusadeTypeField = document.getElementById('otherCrusadeTypeField');
    const otherCrusadeTypeInput = document.getElementById('otherCrusadeType');
    
    if (!crusadeTypeSelect) return;
    
    const selectedValue = crusadeTypeSelect.value;
    console.log('Crusade type selected:', selectedValue);
    
    // Show/hide the "other" field based on selection
    if (selectedValue === 'others') {
        if (otherCrusadeTypeField) {
            otherCrusadeTypeField.classList.remove('hidden');
        }
        if (otherCrusadeTypeInput) {
            otherCrusadeTypeInput.setAttribute('required', 'required');
        }
    } else {
        if (otherCrusadeTypeField) {
            otherCrusadeTypeField.classList.add('hidden');
        }
        if (otherCrusadeTypeInput) {
            otherCrusadeTypeInput.removeAttribute('required');
            otherCrusadeTypeInput.value = '';
        }
    }
}

// Setup church crusade types dropdown functionality
function setupChurchCrusadeTypesDropdown() {
    const dropdown = document.querySelector('.church-crusade-types-dropdown');
    const selected = document.querySelector('.church-crusade-types-selected');
    const options = document.querySelector('.church-crusade-types-options');
    const placeholder = document.querySelector('.church-crusade-types-placeholder');
    const checkboxes = document.querySelectorAll('.church-crusade-type-checkbox');
    const hiddenSelect = document.getElementById('churchCrusadeTypes');
    
    if (!dropdown || !selected || !options || !placeholder || !hiddenSelect) {
        console.error('Church crusade types dropdown elements not found');
        return;
    }
    
    // Toggle dropdown visibility
    selected.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        const isOpen = !options.classList.contains('hidden');
        
        if (isOpen) {
            options.classList.add('hidden');
            selected.querySelector('svg').classList.remove('rotate-180');
        } else {
            options.classList.remove('hidden');
            selected.querySelector('svg').classList.add('rotate-180');
        }
    });
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!dropdown.contains(e.target)) {
            options.classList.add('hidden');
            selected.querySelector('svg').classList.remove('rotate-180');
        }
    });
    
    // Prevent dropdown from closing when clicking inside options
    options.addEventListener('click', function(e) {
        e.stopPropagation();
    });
    
    // Handle checkbox changes
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectedCrusadeTypes();
            
            // Handle "Other" option
            if (this.value === 'other') {
                const otherField = document.getElementById('churchOtherCrusadeTypesField');
                const otherInput = document.getElementById('churchOtherCrusadeTypes');
                
                if (this.checked) {
                    if (otherField) otherField.classList.remove('hidden');
                    if (otherInput) otherInput.setAttribute('required', 'required');
                } else {
                    if (otherField) otherField.classList.add('hidden');
                    if (otherInput) {
                        otherInput.removeAttribute('required');
                        otherInput.value = '';
                    }
                }
            }
        });
        
        // Prevent checkbox clicks from closing the dropdown
        checkbox.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    });
    
    // Update selected types display and hidden select
    function updateSelectedCrusadeTypes() {
        const selectedTypes = [];
        const selectedLabels = [];
        
        checkboxes.forEach(checkbox => {
            if (checkbox.checked) {
                selectedTypes.push(checkbox.value);
                let label = checkbox.parentElement.querySelector('span').textContent;
                
                // If "Other" is selected, check if there's custom input
                if (checkbox.value === 'other') {
                    const otherInput = document.getElementById('churchOtherCrusadeTypes');
                    if (otherInput && otherInput.value.trim()) {
                        label = otherInput.value.trim();
                    }
                }
                
                selectedLabels.push(label);
            }
        });
        
        // Update hidden select for form submission
        Array.from(hiddenSelect.options).forEach(option => {
            option.selected = selectedTypes.includes(option.value);
        });
        
        // Trigger change event for validation
        hiddenSelect.dispatchEvent(new Event('change'));
        
        // Update display
        if (selectedTypes.length === 0) {
            placeholder.textContent = 'Select crusade type(s) - You can select multiple';
            placeholder.classList.add('text-gray-500');
            placeholder.classList.remove('text-gray-900');
            hiddenSelect.setCustomValidity('Please select at least one crusade type');
        } else {
            placeholder.textContent = selectedLabels.join(', ');
            placeholder.classList.remove('text-gray-500');
            placeholder.classList.add('text-gray-900');
            hiddenSelect.setCustomValidity('');
        }
        
        console.log('Selected crusade types:', selectedTypes);
        console.log('Hidden select values:', Array.from(hiddenSelect.selectedOptions).map(o => o.value));
    }
    
    // Initialize display
    updateSelectedCrusadeTypes();
    
    // Handle changes to the "Other" input field
    const otherInput = document.getElementById('churchOtherCrusadeTypes');
    if (otherInput) {
        otherInput.addEventListener('input', function() {
            updateSelectedCrusadeTypes();
        });
    }
}


</script>

<?php include 'includes/footer.php'; ?>   
