{"countries": [{"code": "AF", "name": "Afghanistan", "continent": "Asia", "timezone": "Asia/Kabul", "coordinates": {"lat": 33.9391, "lng": 67.71}, "major_cities": [{"name": "Kabul", "coordinates": {"lat": 34.5553, "lng": 69.2075}}, {"name": "Kandahar", "coordinates": {"lat": 31.608, "lng": 65.7384}}, {"name": "Herat", "coordinates": {"lat": 34.3482, "lng": 62.1997}}]}, {"code": "AR", "name": "Argentina", "continent": "South America", "timezone": "America/Argentina/Buenos_Aires", "coordinates": {"lat": -38.4161, "lng": -63.6167}, "major_cities": [{"name": "Buenos Aires", "coordinates": {"lat": -34.6118, "lng": -58.396}}, {"name": "Córdoba", "coordinates": {"lat": -31.4201, "lng": -64.1888}}, {"name": "Rosario", "coordinates": {"lat": -32.9442, "lng": -60.6505}}]}, {"code": "AU", "name": "Australia", "continent": "Oceania", "timezone": "Australia/Sydney", "coordinates": {"lat": -25.2744, "lng": 133.7751}, "major_cities": [{"name": "Sydney", "coordinates": {"lat": -33.8688, "lng": 151.2093}}, {"name": "Melbourne", "coordinates": {"lat": -37.8136, "lng": 144.9631}}, {"name": "Brisbane", "coordinates": {"lat": -27.4705, "lng": 153.026}}]}, {"code": "BR", "name": "Brazil", "continent": "South America", "timezone": "America/Sao_Paulo", "coordinates": {"lat": -14.235, "lng": -51.9253}, "major_cities": [{"name": "São Paulo", "coordinates": {"lat": -23.5558, "lng": -46.6396}}, {"name": "Rio de Janeiro", "coordinates": {"lat": -22.9068, "lng": -43.1729}}, {"name": "Brasília", "coordinates": {"lat": -15.8267, "lng": -47.9218}}]}, {"code": "CA", "name": "Canada", "continent": "North America", "timezone": "America/Toronto", "coordinates": {"lat": 56.1304, "lng": -106.3468}, "major_cities": [{"name": "Toronto", "coordinates": {"lat": 43.6532, "lng": -79.3832}}, {"name": "Vancouver", "coordinates": {"lat": 49.2827, "lng": -123.1207}}, {"name": "Montreal", "coordinates": {"lat": 45.5017, "lng": -73.5673}}]}, {"code": "CL", "name": "Chile", "continent": "South America", "timezone": "America/Santiago", "coordinates": {"lat": -35.6751, "lng": -71.543}, "major_cities": [{"name": "Santiago", "coordinates": {"lat": -33.4489, "lng": -70.6693}}, {"name": "Valparaíso", "coordinates": {"lat": -33.0472, "lng": -71.6127}}, {"name": "Concepción", "coordinates": {"lat": -36.8201, "lng": -73.0444}}]}, {"code": "CN", "name": "China", "continent": "Asia", "timezone": "Asia/Shanghai", "coordinates": {"lat": 35.8617, "lng": 104.1954}, "major_cities": [{"name": "Beijing", "coordinates": {"lat": 39.9042, "lng": 116.4074}}, {"name": "Shanghai", "coordinates": {"lat": 31.2304, "lng": 121.4737}}, {"name": "Guangzhou", "coordinates": {"lat": 23.1291, "lng": 113.2644}}]}, {"code": "CO", "name": "Colombia", "continent": "South America", "timezone": "America/Bogota", "coordinates": {"lat": 4.5709, "lng": -74.2973}, "major_cities": [{"name": "Bogotá", "coordinates": {"lat": 4.711, "lng": -74.0721}}, {"name": "Medellín", "coordinates": {"lat": 6.2486, "lng": -75.5742}}, {"name": "Cali", "coordinates": {"lat": 3.4516, "lng": -76.532}}]}, {"code": "CG", "name": "Republic of the Congo", "continent": "Africa", "timezone": "Africa/Brazzaville", "coordinates": {"lat": -0.228, "lng": 15.8277}, "major_cities": [{"name": "Brazzaville", "coordinates": {"lat": -4.2634, "lng": 15.2429}}, {"name": "Pointe-Noire", "coordinates": {"lat": -4.7692, "lng": 11.8636}}, {"name": "<PERSON><PERSON><PERSON>", "coordinates": {"lat": -4.1987, "lng": 12.6732}}]}, {"code": "EG", "name": "Egypt", "continent": "Africa", "timezone": "Africa/Cairo", "coordinates": {"lat": 26.0975, "lng": 31.4867}, "major_cities": [{"name": "Cairo", "coordinates": {"lat": 30.0444, "lng": 31.2357}}, {"name": "Alexandria", "coordinates": {"lat": 31.2001, "lng": 29.9187}}, {"name": "Giza", "coordinates": {"lat": 30.0131, "lng": 31.2089}}]}, {"code": "FJ", "name": "Fiji", "continent": "Oceania", "timezone": "Pacific/Fiji", "coordinates": {"lat": -16.578, "lng": 179.4144}, "major_cities": [{"name": "<PERSON><PERSON>", "coordinates": {"lat": -18.1416, "lng": 178.4419}}, {"name": "<PERSON><PERSON>", "coordinates": {"lat": -17.7765, "lng": 177.4162}}, {"name": "Lautoka", "coordinates": {"lat": -17.6203, "lng": 177.4567}}]}, {"code": "FR", "name": "France", "continent": "Europe", "timezone": "Europe/Paris", "coordinates": {"lat": 46.6034, "lng": 1.8883}, "major_cities": [{"name": "Paris", "coordinates": {"lat": 48.8566, "lng": 2.3522}}, {"name": "Lyon", "coordinates": {"lat": 45.764, "lng": 4.8357}}, {"name": "Marseille", "coordinates": {"lat": 43.2965, "lng": 5.3698}}]}, {"code": "DE", "name": "Germany", "continent": "Europe", "timezone": "Europe/Berlin", "coordinates": {"lat": 51.1657, "lng": 10.4515}, "major_cities": [{"name": "Berlin", "coordinates": {"lat": 52.52, "lng": 13.405}}, {"name": "Munich", "coordinates": {"lat": 48.1351, "lng": 11.582}}, {"name": "Hamburg", "coordinates": {"lat": 53.5511, "lng": 9.9937}}]}, {"code": "GH", "name": "Ghana", "continent": "Africa", "timezone": "Africa/Accra", "coordinates": {"lat": 7.9465, "lng": -1.0232}, "major_cities": [{"name": "Accra", "coordinates": {"lat": 5.6037, "lng": -0.187}}, {"name": "<PERSON><PERSON><PERSON>", "coordinates": {"lat": 6.6885, "lng": -1.6244}}, {"name": "Tamale", "coordinates": {"lat": 9.4008, "lng": -0.8393}}]}, {"code": "HT", "name": "Haiti", "continent": "North America", "timezone": "America/Port-au-Prince", "coordinates": {"lat": 18.9712, "lng": -72.2852}, "major_cities": [{"name": "Port-au-Prince", "coordinates": {"lat": 18.5944, "lng": -72.3074}}, {"name": "Cap-<PERSON><PERSON><PERSON>", "coordinates": {"lat": 19.757, "lng": -72.2014}}, {"name": "Gonaïves", "coordinates": {"lat": 19.4515, "lng": -72.689}}]}, {"code": "IN", "name": "India", "continent": "Asia", "timezone": "Asia/Kolkata", "coordinates": {"lat": 20.5937, "lng": 78.9629}, "major_cities": [{"name": "Mumbai", "coordinates": {"lat": 19.076, "lng": 72.8777}}, {"name": "Delhi", "coordinates": {"lat": 28.7041, "lng": 77.1025}}, {"name": "Bangalore", "coordinates": {"lat": 12.9716, "lng": 77.5946}}, {"name": "Chennai", "coordinates": {"lat": 13.0827, "lng": 80.2707}}, {"name": "Kolkata", "coordinates": {"lat": 22.5726, "lng": 88.3639}}]}, {"code": "ID", "name": "Indonesia", "continent": "Asia", "timezone": "Asia/Jakarta", "coordinates": {"lat": -0.7893, "lng": 113.9213}, "major_cities": [{"name": "Jakarta", "coordinates": {"lat": -6.2088, "lng": 106.8456}}, {"name": "Surabaya", "coordinates": {"lat": -7.2575, "lng": 112.7521}}, {"name": "Bandung", "coordinates": {"lat": -6.9175, "lng": 107.6191}}]}, {"code": "IT", "name": "Italy", "continent": "Europe", "timezone": "Europe/Rome", "coordinates": {"lat": 41.8719, "lng": 12.5674}, "major_cities": [{"name": "Rome", "coordinates": {"lat": 41.9028, "lng": 12.4964}}, {"name": "Milan", "coordinates": {"lat": 45.4642, "lng": 9.19}}, {"name": "Naples", "coordinates": {"lat": 40.8518, "lng": 14.2681}}]}, {"code": "JP", "name": "Japan", "continent": "Asia", "timezone": "Asia/Tokyo", "coordinates": {"lat": 36.2048, "lng": 138.2529}, "major_cities": [{"name": "Tokyo", "coordinates": {"lat": 35.6762, "lng": 139.6503}}, {"name": "Osaka", "coordinates": {"lat": 34.6937, "lng": 135.5023}}, {"name": "Kyoto", "coordinates": {"lat": 35.0116, "lng": 135.7681}}]}, {"code": "KE", "name": "Kenya", "continent": "Africa", "timezone": "Africa/Nairobi", "coordinates": {"lat": -0.0236, "lng": 37.9062}, "major_cities": [{"name": "Nairobi", "coordinates": {"lat": -1.2921, "lng": 36.8219}}, {"name": "Mombasa", "coordinates": {"lat": -4.0435, "lng": 39.6682}}, {"name": "<PERSON><PERSON><PERSON>", "coordinates": {"lat": -0.1022, "lng": 34.7617}}]}, {"code": "MY", "name": "Malaysia", "continent": "Asia", "timezone": "Asia/Kuala_Lumpur", "coordinates": {"lat": 4.2105, "lng": 101.9758}, "major_cities": [{"name": "Kuala Lumpur", "coordinates": {"lat": 3.139, "lng": 101.6869}}, {"name": "George Town", "coordinates": {"lat": 5.4164, "lng": 100.3327}}, {"name": "<PERSON><PERSON>", "coordinates": {"lat": 1.4927, "lng": 103.7414}}]}, {"code": "MX", "name": "Mexico", "continent": "North America", "timezone": "America/Mexico_City", "coordinates": {"lat": 23.6345, "lng": -102.5528}, "major_cities": [{"name": "Mexico City", "coordinates": {"lat": 19.4326, "lng": -99.1332}}, {"name": "Guadalajara", "coordinates": {"lat": 20.6597, "lng": -103.3496}}, {"name": "Monterrey", "coordinates": {"lat": 25.6866, "lng": -100.3161}}]}, {"code": "NG", "name": "Nigeria", "continent": "Africa", "timezone": "Africa/Lagos", "coordinates": {"lat": 9.082, "lng": 8.6753}, "major_cities": [{"name": "Lagos", "coordinates": {"lat": 6.5244, "lng": 3.3792}}, {"name": "<PERSON>ja", "coordinates": {"lat": 9.0765, "lng": 7.3986}}, {"name": "<PERSON><PERSON>", "coordinates": {"lat": 12.0022, "lng": 8.592}}, {"name": "Ibadan", "coordinates": {"lat": 7.3775, "lng": 3.947}}, {"name": "Port Harcourt", "coordinates": {"lat": 4.8156, "lng": 7.0498}}]}, {"code": "NZ", "name": "New Zealand", "continent": "Oceania", "timezone": "Pacific/Auckland", "coordinates": {"lat": -40.9006, "lng": 174.886}, "major_cities": [{"name": "Auckland", "coordinates": {"lat": -36.8485, "lng": 174.7633}}, {"name": "Wellington", "coordinates": {"lat": -41.2865, "lng": 174.7762}}, {"name": "Christchurch", "coordinates": {"lat": -43.5321, "lng": 172.6362}}]}, {"code": "PE", "name": "Peru", "continent": "South America", "timezone": "America/Lima", "coordinates": {"lat": -9.19, "lng": -75.0152}, "major_cities": [{"name": "Lima", "coordinates": {"lat": -12.0464, "lng": -77.0428}}, {"name": "Arequipa", "coordinates": {"lat": -16.409, "lng": -71.5375}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "coordinates": {"lat": -8.1116, "lng": -79.0287}}]}, {"code": "PH", "name": "Philippines", "continent": "Asia", "timezone": "Asia/Manila", "coordinates": {"lat": 12.8797, "lng": 121.774}, "major_cities": [{"name": "Manila", "coordinates": {"lat": 14.5995, "lng": 120.9842}}, {"name": "Quezon City", "coordinates": {"lat": 14.676, "lng": 121.0437}}, {"name": "Cebu City", "coordinates": {"lat": 10.3157, "lng": 123.8854}}]}, {"code": "RU", "name": "Russia", "continent": "Europe", "timezone": "Europe/Moscow", "coordinates": {"lat": 61.524, "lng": 105.3188}, "major_cities": [{"name": "Moscow", "coordinates": {"lat": 55.7558, "lng": 37.6176}}, {"name": "Saint Petersburg", "coordinates": {"lat": 59.9311, "lng": 30.3609}}, {"name": "Novosibirsk", "coordinates": {"lat": 55.0084, "lng": 82.9357}}]}, {"code": "SG", "name": "Singapore", "continent": "Asia", "timezone": "Asia/Singapore", "coordinates": {"lat": 1.3521, "lng": 103.8198}, "major_cities": [{"name": "Singapore", "coordinates": {"lat": 1.3521, "lng": 103.8198}}]}, {"code": "ZA", "name": "South Africa", "continent": "Africa", "timezone": "Africa/Johannesburg", "coordinates": {"lat": -30.5595, "lng": 22.9375}, "major_cities": [{"name": "Johannesburg", "coordinates": {"lat": -26.2041, "lng": 28.0473}}, {"name": "Cape Town", "coordinates": {"lat": -33.9249, "lng": 18.4241}}, {"name": "Durban", "coordinates": {"lat": -29.8587, "lng": 31.0218}}]}, {"code": "KR", "name": "South Korea", "continent": "Asia", "timezone": "Asia/Seoul", "coordinates": {"lat": 35.9078, "lng": 127.7669}, "major_cities": [{"name": "Seoul", "coordinates": {"lat": 37.5665, "lng": 126.978}}, {"name": "Busan", "coordinates": {"lat": 35.1796, "lng": 129.0756}}, {"name": "Incheon", "coordinates": {"lat": 37.4563, "lng": 126.7052}}]}, {"code": "ES", "name": "Spain", "continent": "Europe", "timezone": "Europe/Madrid", "coordinates": {"lat": 40.4637, "lng": -3.7492}, "major_cities": [{"name": "Madrid", "coordinates": {"lat": 40.4168, "lng": -3.7038}}, {"name": "Barcelona", "coordinates": {"lat": 41.3851, "lng": 2.1734}}, {"name": "Valencia", "coordinates": {"lat": 39.4699, "lng": -0.3763}}]}, {"code": "TH", "name": "Thailand", "continent": "Asia", "timezone": "Asia/Bangkok", "coordinates": {"lat": 15.87, "lng": 100.9925}, "major_cities": [{"name": "Bangkok", "coordinates": {"lat": 13.7563, "lng": 100.5018}}, {"name": "<PERSON>", "coordinates": {"lat": 18.7883, "lng": 98.9853}}, {"name": "Phuke<PERSON>", "coordinates": {"lat": 7.8804, "lng": 98.3923}}]}, {"code": "GB", "name": "United Kingdom", "continent": "Europe", "timezone": "Europe/London", "coordinates": {"lat": 55.3781, "lng": -3.436}, "major_cities": [{"name": "London", "coordinates": {"lat": 51.5074, "lng": -0.1278}}, {"name": "Birmingham", "coordinates": {"lat": 52.4862, "lng": -1.8904}}, {"name": "Manchester", "coordinates": {"lat": 53.4808, "lng": -2.2426}}]}, {"code": "US", "name": "United States", "continent": "North America", "timezone": "America/New_York", "coordinates": {"lat": 37.0902, "lng": -95.7129}, "major_cities": [{"name": "New York", "coordinates": {"lat": 40.7128, "lng": -74.006}}, {"name": "Los Angeles", "coordinates": {"lat": 34.0522, "lng": -118.2437}}, {"name": "Chicago", "coordinates": {"lat": 41.8781, "lng": -87.6298}}, {"name": "Houston", "coordinates": {"lat": 29.7604, "lng": -95.3698}}, {"name": "Miami", "coordinates": {"lat": 25.7617, "lng": -80.1918}}]}, {"code": "VE", "name": "Venezuela", "continent": "South America", "timezone": "America/Caracas", "coordinates": {"lat": 6.4238, "lng": -66.5897}, "major_cities": [{"name": "Caracas", "coordinates": {"lat": 10.4806, "lng": -66.9036}}, {"name": "Maracaibo", "coordinates": {"lat": 10.6316, "lng": -71.6444}}, {"name": "Valencia", "coordinates": {"lat": 10.1621, "lng": -68.0077}}, {"name": "Barquisimeto", "coordinates": {"lat": 10.0647, "lng": -69.357}}, {"name": "<PERSON><PERSON>", "coordinates": {"lat": 8.6226, "lng": -70.2093}}, {"name": "Portuguesa", "coordinates": {"lat": 9.0417, "lng": -69.7417}}, {"name": "Sta Teresa <PERSON>", "coordinates": {"lat": 10.2297, "lng": -66.6502}}]}]}