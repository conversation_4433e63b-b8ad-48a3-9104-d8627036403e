<?php
// Director's Dashboard - Public view for sharing with management
// No authentication required for easy sharing

// Load world crusade requests data
$worldRequestsFile = __DIR__ . '/data/world_crusade_requests.json';
$worldRequests = [];

if (file_exists($worldRequestsFile)) {
    $worldRequestsData = json_decode(file_get_contents($worldRequestsFile), true);
    $worldRequests = $worldRequestsData['requests'] ?? [];
}

// Calculate statistics
$totalRequests = count($worldRequests);

// Calculate mega crusades statistics
$megaCrusadeRequests = array_filter($worldRequests, fn($r) => strtolower($r['crusade_type'] ?? '') === 'mega');
$totalMegaCrusades = count($megaCrusadeRequests);

// Top countries
$byCountry = [];
foreach ($worldRequests as $request) {
    $country = ucwords(str_replace('-', ' ', $request['country'] ?? 'Unknown'));
    $byCountry[$country] = ($byCountry[$country] ?? 0) + 1;
}
arsort($byCountry);
$topCountries = array_slice($byCountry, 0, 10, true);

// Last updated timestamp
$lastUpdated = date('Y-m-d H:i:s');
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>World Crusade Requests - Director's Dashboard</title>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Oswald:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    screens: {
                        'xs': '475px',
                    },
                    colors: {
                        primary: '#2563eb', // Blue-600
                        primaryDark: '#1d4ed8', // Blue-700
                        secondary: '#ffffff', // White
                        accent: '#fbbf24', // Yellow-400
                        gold: '#d97706', // Amber-600
                        'blue-25': '#f0f9ff', // Very light blue
                    },
                    fontFamily: {
                        'sans': ['Oswald', 'system-ui', 'sans-serif'],
                        'display': ['Oswald', 'serif'],
                    },
                    boxShadow: {
                        'soft': '0 4px 20px rgba(0, 0, 0, 0.08)',
                        'soft-lg': '0 10px 40px rgba(0, 0, 0, 0.1)',
                    }
                }
            }
        }
    </script>
    
    
    <style>
        body {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            min-height: 100vh;
            font-family: 'Oswald', system-ui, sans-serif;
        }
        
        .executive-header {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            box-shadow: 0 10px 40px rgba(37, 99, 235, 0.2);
        }
        
        .metric-card {
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
        }
        
        .metric-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .metric-card.pending { border-left-color: #f59e0b; }
        .metric-card.approved { border-left-color: #10b981; }
        .metric-card.contacted { border-left-color: #3b82f6; }
        .metric-card.total { border-left-color: #8b5cf6; }
        
        .content-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }
        
        .country-bar {
            background: linear-gradient(90deg, #2563eb 0%, #3b82f6 100%);
            transition: all 0.3s ease;
        }
        
        .country-item:hover .country-bar {
            background: linear-gradient(90deg, #1d4ed8 0%, #2563eb 100%);
        }
        
        .avatar-gradient {
            background: linear-gradient(135deg, #2563eb 0%, #8b5cf6 100%);
        }
        
        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
        
        /* DataTables Pure Tailwind Styling */
        .dataTables_wrapper {
            width: 100% !important;
            padding: 0 !important;
        }
        
        .dataTables_length {
            margin-bottom: 1rem !important;
            float: none !important;
        }
        
        .dataTables_length label {
            display: flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
            font-size: 0.875rem !important;
            font-weight: 500 !important;
            color: #374151 !important;
        }
        
        .dataTables_length select {
            border: 1px solid #d1d5db !important;
            border-radius: 0.5rem !important;
            padding: 0.5rem 0.75rem !important;
            font-size: 0.875rem !important;
            background-color: white !important;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
            margin-left: 0.5rem !important;
        }
        
        .dataTables_length select:focus {
            outline: 2px solid #3b82f6 !important;
            outline-offset: 2px !important;
            border-color: #3b82f6 !important;
        }
        
        .dataTables_filter {
            margin-bottom: 1rem !important;
            float: none !important;
            text-align: left !important;
        }
        
        .dataTables_filter label {
            display: flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
            font-size: 0.875rem !important;
            font-weight: 500 !important;
            color: #374151 !important;
        }
        
        .dataTables_filter input {
            border: 1px solid #d1d5db !important;
            border-radius: 0.5rem !important;
            padding: 0.5rem 1rem !important;
            font-size: 0.875rem !important;
            background-color: white !important;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
            width: 16rem !important;
            margin-left: 0.5rem !important;
        }
        
        .dataTables_filter input:focus {
            outline: 2px solid #3b82f6 !important;
            outline-offset: 2px !important;
            border-color: #3b82f6 !important;
        }
        
        .dataTables_info {
            font-size: 0.875rem !important;
            color: #6b7280 !important;
            font-weight: 500 !important;
            padding: 0.5rem 0 !important;
            float: none !important;
        }
        
        .dataTables_paginate {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            gap: 0.25rem !important;
            padding: 0.5rem 0 !important;
            float: none !important;
        }
        
        .dataTables_paginate .paginate_button {
            padding: 0.5rem 0.75rem !important;
            font-size: 0.875rem !important;
            font-weight: 500 !important;
            color: #374151 !important;
            background-color: white !important;
            border: 1px solid #d1d5db !important;
            border-radius: 0.375rem !important;
            margin: 0 0.125rem !important;
            text-decoration: none !important;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
            transition: all 0.2s ease !important;
        }
        
        .dataTables_paginate .paginate_button:hover {
            background-color: #f9fafb !important;
            color: #111827 !important;
        }
        
        .dataTables_paginate .paginate_button.current {
            background-color: #2563eb !important;
            color: white !important;
            border-color: #2563eb !important;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
        }
        
        .dataTables_paginate .paginate_button.current:hover {
            background-color: #1d4ed8 !important;
        }
        
        .dataTables_paginate .paginate_button.disabled {
            color: #9ca3af !important;
            cursor: not-allowed !important;
        }
        
        .dataTables_paginate .paginate_button.disabled:hover {
            background-color: white !important;
            color: #9ca3af !important;
        }
        
        .dt-buttons {
            display: flex !important;
            gap: 0.5rem !important;
            margin-bottom: 1rem sm:1.5rem !important;
            flex-wrap: wrap !important;
            justify-content: center !important;
            padding: 0 0.5rem !important;
        }
        
        @media (min-width: 640px) {
            .dt-buttons {
                justify-content: flex-start !important;
                padding: 0 !important;
            }
        }
        
        /* Mobile DataTables controls */
        @media (max-width: 639px) {
            .dataTables_length {
                margin-bottom: 0.5rem !important;
            }
            
            .dataTables_length label {
                font-size: 0.75rem !important;
            }
            
            .dataTables_length select {
                padding: 0.25rem 0.5rem !important;
                font-size: 0.75rem !important;
            }
            
            .dataTables_filter {
                margin-bottom: 0.5rem !important;
            }
            
            .dataTables_filter label {
                font-size: 0.75rem !important;
            }
            
            .dataTables_filter input {
                width: 100% !important;
                max-width: none !important;
                padding: 0.5rem !important;
                font-size: 0.875rem !important;
            }
            
            .dataTables_info {
                font-size: 0.75rem !important;
            }
            
            .dataTables_paginate .paginate_button {
                padding: 0.375rem 0.5rem !important;
                font-size: 0.75rem !important;
                margin: 0 0.0625rem !important;
            }
        }
        
        table.dataTable {
            width: 100% !important;
            border-collapse: collapse !important;
            background-color: white !important;
            border-radius: 0.5rem !important;
            overflow: hidden !important;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
            border: 1px solid #e5e7eb !important;
        }
        
        /* Desktop table styling */
        @media (min-width: 768px) {
            table.dataTable thead th {
                background: linear-gradient(90deg, #f9fafb 0%, #eff6ff 100%) !important;
                padding: 1rem 1.5rem !important;
                text-align: left !important;
                font-size: 0.75rem !important;
                font-weight: 700 !important;
                color: #4b5563 !important;
                text-transform: uppercase !important;
                letter-spacing: 0.05em !important;
                border-bottom: 1px solid #e5e7eb !important;
                border-right: 1px solid #e5e7eb !important;
            }
            
            table.dataTable tbody td {
                padding: 1rem 1.5rem !important;
                font-size: 0.875rem !important;
                color: #111827 !important;
                border-right: 1px solid #f3f4f6 !important;
            }
        }
        
        /* Mobile table styling */
        @media (max-width: 767px) {
            table.dataTable {
                min-width: 800px !important; /* Force minimum width for proper spacing */
            }
            
            table.dataTable thead th {
                background: linear-gradient(90deg, #f9fafb 0%, #eff6ff 100%) !important;
                padding: 1rem 0.75rem !important;
                text-align: left !important;
                font-size: 0.625rem !important;
                font-weight: 700 !important;
                color: #4b5563 !important;
                text-transform: uppercase !important;
                letter-spacing: 0.05em !important;
                border-bottom: 1px solid #e5e7eb !important;
                border-right: 1px solid #e5e7eb !important;
                white-space: nowrap !important;
                min-width: 100px !important;
            }
            
            table.dataTable tbody td {
                padding: 1rem 0.75rem !important;
                font-size: 0.8rem !important;
                color: #111827 !important;
                border-right: 1px solid #f3f4f6 !important;
                line-height: 1.5 !important;
                min-width: 100px !important;
                white-space: nowrap !important;
                overflow: hidden !important;
                text-overflow: ellipsis !important;
            }
            
            /* Specific column widths for mobile */
            table.dataTable thead th:nth-child(1), /* Requester */
            table.dataTable tbody td:nth-child(1) {
                min-width: 140px !important;
            }
            
            table.dataTable thead th:nth-child(2), /* Organization */
            table.dataTable tbody td:nth-child(2) {
                min-width: 120px !important;
            }
            
            table.dataTable thead th:nth-child(3), /* Crusade Title */
            table.dataTable tbody td:nth-child(3) {
                min-width: 150px !important;
            }
            
            table.dataTable thead th:nth-child(4), /* Type */
            table.dataTable tbody td:nth-child(4) {
                min-width: 80px !important;
            }
            
            table.dataTable thead th:nth-child(5), /* Country */
            table.dataTable tbody td:nth-child(5) {
                min-width: 100px !important;
            }
            
            table.dataTable thead th:nth-child(13), /* Actions */
            table.dataTable tbody td:nth-child(13) {
                min-width: 120px !important;
            }
            
            /* Make buttons appropriately sized on mobile */
            table.dataTable tbody td button {
                padding: 0.625rem 1rem !important;
                font-size: 0.75rem !important;
                white-space: nowrap !important;
            }
            
            /* Adjust avatar size on mobile */
            .h-10.w-10 {
                height: 2.5rem !important;
                width: 2.5rem !important;
            }
            
            /* Icons sized appropriately */
            table.dataTable tbody td i {
                font-size: 0.75rem !important;
            }
            
            /* Badge sizing */
            table.dataTable tbody td .inline-flex {
                font-size: 0.75rem !important;
                padding: 0.375rem 0.75rem !important;
            }
        }
        
        table.dataTable thead th:last-child {
            border-right: none !important;
        }
        
        table.dataTable tbody tr {
            border-bottom: 1px solid #f3f4f6 !important;
            transition: all 0.2s ease !important;
        }
        
        table.dataTable tbody tr:hover {
            background: linear-gradient(90deg, #f0f9ff 0%, #e0f2fe 100%) !important;
        }
        
        table.dataTable tbody td:last-child {
            border-right: none !important;
        }
    </style>
</head>
<body class="bg-white text-gray-800 font-sans">
    <!-- Executive Header -->
    <div class="executive-header text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                <div class="text-center lg:text-left">
                    <h1 class="text-3xl sm:text-4xl lg:text-5xl font-bold mb-2">World Crusade Requests</h1>
                    <p class="text-xl sm:text-2xl opacity-90 mb-2">Executive Dashboard</p>
                    <p class="text-sm opacity-75">
                        <i class="fas fa-clock mr-2"></i>
                        Last Updated: <?php echo $lastUpdated; ?>
                    </p>
                </div>
                <div class="mt-6 lg:mt-0 text-center lg:text-right">
                    <div class="inline-block bg-white bg-opacity-20 rounded-xl p-6">
                        <div class="text-4xl sm:text-5xl font-bold"><?php echo $totalRequests; ?></div>
                        <div class="text-sm opacity-90 uppercase tracking-wider">Total Number of Crusades</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Key Metrics -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="metric-card total rounded-xl p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-14 h-14 bg-purple-100 rounded-xl flex items-center justify-center">
                            <i class="fas fa-globe text-purple-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-semibold text-gray-600 uppercase tracking-wider">Total Number of Crusades</p>
                        <p class="text-3xl font-bold text-gray-900"><?php echo $totalRequests; ?></p>
                    </div>
                </div>
            </div>

            <div class="metric-card rounded-xl p-6 border-l-4 border-green-500">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-14 h-14 bg-green-100 rounded-xl flex items-center justify-center">
                            <i class="fas fa-church text-green-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-semibold text-gray-600 uppercase tracking-wider">Church Crusades</p>
                        <p class="text-3xl font-bold text-gray-900"><?php echo count(array_filter($worldRequests, fn($r) => ($r['registration_type'] ?? '') === 'church')); ?></p>
                    </div>
                </div>
            </div>

            <div class="metric-card rounded-xl p-6 border-l-4 border-blue-500">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-14 h-14 bg-blue-100 rounded-xl flex items-center justify-center">
                            <i class="fas fa-user text-blue-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-semibold text-gray-600 uppercase tracking-wider">Individual Crusades</p>
                        <p class="text-3xl font-bold text-gray-900"><?php echo count(array_filter($worldRequests, fn($r) => ($r['registration_type'] ?? 'individual') === 'individual')); ?></p>
                    </div>
                </div>
            </div>

            <div class="metric-card rounded-xl p-6 border-l-4 border-orange-500">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-14 h-14 bg-orange-100 rounded-xl flex items-center justify-center">
                            <i class="fas fa-building text-orange-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-semibold text-gray-600 uppercase tracking-wider">Organization Crusades</p>
                        <p class="text-3xl font-bold text-gray-900"><?php echo count(array_filter($worldRequests, fn($r) => in_array(($r['registration_type'] ?? ''), ['organisation', 'network']))); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mega Crusades Stats (only show if there are mega crusades) -->
        <?php if ($totalMegaCrusades > 0): ?>
        <div class="content-card rounded-xl p-6 mb-8">
            <h3 class="text-xl font-bold text-gray-900 mb-6 flex items-center">
                <i class="fas fa-star mr-3 text-red-600 text-xl"></i>
                Mega Crusades Overview
                <span class="ml-2 inline-flex px-3 py-1 text-xs font-bold rounded-full bg-red-100 text-red-800">
                    <?php echo $totalMegaCrusades; ?> Total
                </span>
            </h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div class="metric-card total rounded-xl p-4 border-l-4 border-red-500">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
                                <i class="fas fa-bullhorn text-red-600 text-lg"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-semibold text-gray-600 uppercase tracking-wider">Total Mega</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo $totalMegaCrusades; ?></p>
                        </div>
                    </div>
                </div>

                <div class="metric-card rounded-xl p-4 border-l-4 border-orange-500">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                                <i class="fas fa-map-marked-alt text-orange-600 text-lg"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-semibold text-gray-600 uppercase tracking-wider">Countries</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo count(array_unique(array_filter(array_column($megaCrusadeRequests, 'country')))); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Top Countries -->
        <div class="content-card rounded-xl p-6 mb-8">
            <h3 class="text-xl font-bold text-gray-900 mb-6 flex items-center">
                <i class="fas fa-map-marked-alt mr-3 text-primary text-xl"></i>
                Top Countries by Requests
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <?php foreach (array_slice($topCountries, 0, 10) as $country => $count): ?>
                <div class="country-item flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors">
                    <span class="text-sm font-semibold text-gray-700"><?php echo $country; ?></span>
                    <div class="flex items-center">
                        <div class="w-32 bg-gray-200 rounded-full h-3 mr-3">
                            <div class="country-bar h-3 rounded-full" style="width: <?php echo max($topCountries) > 0 ? ($count/max($topCountries))*100 : 0; ?>%"></div>
                        </div>
                        <span class="text-sm font-bold text-gray-900 w-8 text-right"><?php echo $count; ?></span>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Detailed Data Table -->
        <div class="bg-white rounded-xl overflow-hidden shadow-soft-lg border border-gray-200">
            <div class="px-4 sm:px-6 py-4 sm:py-6 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                    <h3 class="text-lg sm:text-xl font-bold text-gray-900 flex items-center">
                        <i class="fas fa-table mr-2 sm:mr-3 text-primary text-lg sm:text-xl"></i>
                        All Crusade Requests
                    </h3>
                    <div class="text-sm text-gray-600 font-medium">
                        <i class="fas fa-database mr-2"></i>
                        <?php echo $totalRequests; ?> Total Crusades
                    </div>
                </div>
            </div>
            
            <div class="p-2 sm:p-6">
                <!-- Helpful instruction -->
                <div class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div class="flex flex-col sm:flex-row sm:items-center">
                        <i class="fas fa-info-circle text-blue-600 mr-2 mb-2 sm:mb-0"></i>
                        <span class="text-sm text-blue-800 font-medium">
                            <span class="block sm:hidden">👈 Swipe left on the table to see all columns including the blue "View Details" button at the end of each row.</span>
                            <span class="hidden sm:block">Scroll horizontally to see all columns, then click the "View Details" button in any row to see complete information about that crusade request.</span>
                        </span>
                    </div>
                </div>

                <!-- Mobile-optimized table container -->
                <div class="overflow-x-auto -mx-2 sm:mx-0 rounded-lg border border-gray-200 sm:border-0" style="scroll-behavior: smooth; -webkit-overflow-scrolling: touch;">
                    <table id="directorsTable" class="border-collapse bg-white sm:rounded-lg overflow-hidden shadow-sm">
                    <thead class="bg-gradient-to-r from-gray-50 to-blue-50">
                        <tr>
                            <th class="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider border-r border-gray-200">Requester</th>
                            <th class="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider border-r border-gray-200">Organization</th>
                            <th class="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider border-r border-gray-200">Crusade Title</th>
                            <th class="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider border-r border-gray-200">Type</th>
                            <th class="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider border-r border-gray-200">Country</th>
                            <th class="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider border-r border-gray-200">Location</th>
                            <th class="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider border-r border-gray-200">Expected Attendance</th>
                            <th class="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider border-r border-gray-200">Number of Crusades</th>
                            <th class="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider border-r border-gray-200">Zone/Group/Church</th>
                            <th class="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider border-r border-gray-200">Contact</th>
                            <th class="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider border-r border-gray-200">Date</th>
                            <th class="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-100">
                        <?php foreach ($worldRequests as $request): ?>
                        <tr class="hover:bg-gradient-to-r hover:from-blue-25 hover:to-indigo-25 transition-all duration-200 border-b border-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap border-r border-gray-100">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-full avatar-gradient flex items-center justify-center shadow-sm border-2 border-white">
                                            <span class="text-sm font-bold text-white">
                                                <?php 
                                                $firstName = $request['first_name'] ?? '';
                                                $lastName = $request['last_name'] ?? '';
                                                echo strtoupper(substr($firstName, 0, 1) . substr($lastName, 0, 1));
                                                ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-semibold text-gray-900">
                                            <?php 
                                            $registrationType = $request['registration_type'] ?? 'individual';
                                            
                                            if ($registrationType === 'organisation' || $registrationType === 'network') {
                                                echo htmlspecialchars($request['organization_name'] ?? 'Organisation', ENT_QUOTES, 'UTF-8');
                                            } elseif ($registrationType === 'church') {
                                                if (!empty($request['first_name']) && !empty($request['last_name'])) {
                                                    $designation = !empty($request['designation']) ? ucfirst($request['designation']) . ' ' : '';
                                                    echo htmlspecialchars($designation . $request['first_name'] . ' ' . $request['last_name'], ENT_QUOTES, 'UTF-8');
                                                } else {
                                                    $churchName = $request['church_name'] ?? $request['ministry_name'] ?? $request['group_name'] ?? 'Church Contact';
                                                    echo htmlspecialchars($churchName, ENT_QUOTES, 'UTF-8');
                                                }
                                            } else {
                                                $designation = !empty($request['designation']) ? ucfirst($request['designation']) . ' ' : '';
                                                $firstName = $request['first_name'] ?? '';
                                                $lastName = $request['last_name'] ?? '';
                                                echo htmlspecialchars($designation . $firstName . ' ' . $lastName, ENT_QUOTES, 'UTF-8');
                                            }
                                            ?>
                                        </div>
                                        <div class="text-xs text-gray-500 font-medium bg-gray-50 px-2 py-1 rounded-full inline-block mt-1">
                                            <?php echo ucfirst($registrationType); ?>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-r border-gray-100">
                                <div class="max-w-xs truncate" title="<?php echo htmlspecialchars($request['organization_name'] ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?>">
                                    <?php echo htmlspecialchars($request['organization_name'] ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 text-sm font-medium text-gray-900 border-r border-gray-100">
                                <div class="max-w-xs truncate font-semibold" title="<?php echo htmlspecialchars($request['crusade_title'] ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?>">
                                    <?php echo htmlspecialchars($request['crusade_title'] ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 border-r border-gray-100">
                                <?php 
                                $crusadeType = $request['crusade_type'] ?? 'Open';
                                $crusadeTypes = $request['crusade_types'] ?? [];
                                
                                // If it's an array (church registration), display multiple badges
                                if (is_array($crusadeTypes) && !empty($crusadeTypes)) {
                                    foreach ($crusadeTypes as $type) {
                                        $badgeClass = 'bg-gray-100 text-gray-800 border border-gray-200';
                                        switch(strtolower($type)) {
                                            case 'mega': $badgeClass = 'bg-red-100 text-red-800 border border-red-200'; break;
                                            case 'tap2read': $badgeClass = 'bg-purple-100 text-purple-800 border border-purple-200'; break;
                                            case 'youths-aglow': $badgeClass = 'bg-blue-100 text-blue-800 border border-blue-200'; break;
                                            case 'teevolution': $badgeClass = 'bg-indigo-100 text-indigo-800 border border-indigo-200'; break;
                                            case 'say-yes-to-kids': $badgeClass = 'bg-yellow-100 text-yellow-800 border border-yellow-200'; break;
                                            case 'nolb': $badgeClass = 'bg-green-100 text-green-800 border border-green-200'; break;
                                            case 'leading-ladies': $badgeClass = 'bg-pink-100 text-pink-800 border border-pink-200'; break;
                                            case 'mighty-men': $badgeClass = 'bg-gray-100 text-gray-800 border border-gray-200'; break;
                                            case 'professionals': $badgeClass = 'bg-teal-100 text-teal-800 border border-teal-200'; break;
                                            case 'tv': $badgeClass = 'bg-purple-100 text-purple-800 border border-purple-200'; break;
                                            case 'social-media': $badgeClass = 'bg-blue-100 text-blue-800 border border-blue-200'; break;
                                            case 'online': $badgeClass = 'bg-blue-100 text-blue-800 border border-blue-200'; break;
                                            case 'mystreamspace': $badgeClass = 'bg-indigo-100 text-indigo-800 border border-indigo-200'; break;
                                            case 'mall': $badgeClass = 'bg-orange-100 text-orange-800 border border-orange-200'; break;
                                            case 'school': $badgeClass = 'bg-green-100 text-green-800 border border-green-200'; break;
                                            case 'hospital': $badgeClass = 'bg-red-100 text-red-800 border border-red-200'; break;
                                            case 'street': $badgeClass = 'bg-orange-100 text-orange-800 border border-orange-200'; break;
                                            case 'village': $badgeClass = 'bg-green-100 text-green-800 border border-green-200'; break;
                                            case 'prison': $badgeClass = 'bg-gray-100 text-gray-800 border border-gray-200'; break;
                                            case 'football': $badgeClass = 'bg-green-100 text-green-800 border border-green-200'; break;
                                            case 'community': $badgeClass = 'bg-blue-100 text-blue-800 border border-blue-200'; break;
                                            case 'transport-station': $badgeClass = 'bg-yellow-100 text-yellow-800 border border-yellow-200'; break;
                                        }
                                        echo '<span class="inline-flex px-2 py-1 text-xs font-bold rounded-full shadow-sm mr-1 mb-1 ' . $badgeClass . '">';
                                        echo ucfirst(htmlspecialchars($type, ENT_QUOTES, 'UTF-8'));
                                        echo '</span>';
                                    }
                                } else {
                                    // Single crusade type (individual/organisation registration)
                                    $badgeClass = 'bg-gray-100 text-gray-800 border border-gray-200';
                                    switch(strtolower($crusadeType)) {
                                        case 'online': $badgeClass = 'bg-blue-100 text-blue-800 border border-blue-200'; break;
                                        case 'open': $badgeClass = 'bg-green-100 text-green-800 border border-green-200'; break;
                                        case 'mega': $badgeClass = 'bg-red-100 text-red-800 border border-red-200'; break;
                                        case 'street': $badgeClass = 'bg-orange-100 text-orange-800 border border-orange-200'; break;
                                    }
                                    echo '<span class="inline-flex px-3 py-1 text-xs font-bold rounded-full shadow-sm ' . $badgeClass . '">';
                                    echo ucfirst(htmlspecialchars($crusadeType, ENT_QUOTES, 'UTF-8'));
                                    echo '</span>';
                                }
                                ?>
                            </td>
                            <td class="px-6 py-4 text-sm font-medium text-gray-900 border-r border-gray-100">
                                <?php 
                                // Handle multiple countries for church registrations
                                if (isset($request['countries']) && is_string($request['countries']) && !empty($request['countries'])) {
                                    // Multiple countries stored as comma-separated string
                                    $countries = explode(',', $request['countries']);
                                    $countries = array_filter(array_map('trim', $countries)); // Clean up
                                    $displayCountries = array_slice($countries, 0, 2); // Show first 2
                                    foreach ($displayCountries as $country) {
                                        echo '<div class="flex items-center mb-1">';
                                        echo '<i class="fas fa-globe-americas mr-2 text-primary text-xs"></i>';
                                        echo '<span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">';
                                        echo htmlspecialchars(ucwords(str_replace('-', ' ', $country)), ENT_QUOTES, 'UTF-8');
                                        echo '</span></div>';
                                    }
                                    if (count($countries) > 2) {
                                        echo '<div class="text-xs text-gray-500">+' . (count($countries) - 2) . ' more countries</div>';
                                    }
                                } elseif (isset($request['countries_array']) && is_array($request['countries_array']) && !empty($request['countries_array'])) {
                                    // Handle countries stored as array (fallback)
                                    $countries = $request['countries_array'];
                                    $displayCountries = array_slice($countries, 0, 2); // Show first 2
                                    foreach ($displayCountries as $country) {
                                        echo '<div class="flex items-center mb-1">';
                                        echo '<i class="fas fa-globe-americas mr-2 text-primary text-xs"></i>';
                                        echo '<span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">';
                                        echo htmlspecialchars(ucwords(str_replace('-', ' ', $country)), ENT_QUOTES, 'UTF-8');
                                        echo '</span></div>';
                                    }
                                    if (count($countries) > 2) {
                                        echo '<div class="text-xs text-gray-500">+' . (count($countries) - 2) . ' more countries</div>';
                                    }
                                } else {
                                    // Single country (individual/organisation registration)
                                    $countryString = $request['country'] ?? 'N/A';
                                    echo '<div class="flex items-center">';
                                    echo '<i class="fas fa-globe-americas mr-2 text-primary text-xs"></i>';
                                    echo htmlspecialchars(ucwords(str_replace('-', ' ', $countryString)), ENT_QUOTES, 'UTF-8');
                                    echo '</div>';
                                }
                                ?>
                            </td>
                            <td class="px-6 py-4 text-sm font-medium text-gray-900 border-r border-gray-100">
                                <div class="max-w-xs truncate" title="<?php echo htmlspecialchars($request['location'] ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?>">
                                    <i class="fas fa-map-marker-alt mr-2 text-primary text-xs"></i>
                                    <?php echo htmlspecialchars($request['location'] ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-r border-gray-100">
                                <div class="flex items-center">
                                    <i class="fas fa-users mr-2 text-primary text-xs"></i>
                                    <?php echo htmlspecialchars($request['attendance'] ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-r border-gray-100">
                                <div class="flex items-center">
                                    <i class="fas fa-calendar-check mr-2 text-primary text-xs"></i>
                                    <?php echo htmlspecialchars($request['number_of_crusades'] ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-r border-gray-100">
                                <?php 
                                $zone = $request['zone'] ?? '';
                                $group = $request['group'] ?? $request['church_group_name'] ?? '';
                                $church = $request['church'] ?? $request['church_name'] ?? '';
                                $ministry = $request['ministry_name'] ?? '';
                                $groupName = $request['group_name'] ?? '';

                                if (!empty($zone)) {
                                    echo '<div class="flex items-center"><i class="fas fa-map-marked-alt mr-2 text-primary text-xs"></i>' . htmlspecialchars($zone, ENT_QUOTES, 'UTF-8') . '</div>';
                                } elseif (!empty($group)) {
                                    echo '<div class="flex items-center"><i class="fas fa-users mr-2 text-primary text-xs"></i>' . htmlspecialchars($group, ENT_QUOTES, 'UTF-8') . '</div>';
                                } elseif (!empty($church)) {
                                    echo '<div class="flex items-center"><i class="fas fa-church mr-2 text-primary text-xs"></i>' . htmlspecialchars($church, ENT_QUOTES, 'UTF-8') . '</div>';
                                } elseif (!empty($ministry)) {
                                    echo '<div class="flex items-center"><i class="fas fa-hands-praying mr-2 text-primary text-xs"></i>' . htmlspecialchars($ministry, ENT_QUOTES, 'UTF-8') . '</div>';
                                } elseif (!empty($groupName)) {
                                    echo '<div class="flex items-center"><i class="fas fa-users mr-2 text-primary text-xs"></i>' . htmlspecialchars($groupName, ENT_QUOTES, 'UTF-8') . '</div>';
                                } else {
                                    echo '<span class="text-gray-400">N/A</span>';
                                }
                                ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 border-r border-gray-100">
                                <div class="space-y-1 max-w-xs">
                                    <?php if (!empty($request['email'])): ?>
                                    <div class="flex items-center truncate"><i class="fas fa-envelope mr-2 text-primary text-xs flex-shrink-0"></i><span class="truncate"><?php echo htmlspecialchars($request['email'], ENT_QUOTES, 'UTF-8'); ?></span></div>
                                    <?php endif; ?>
                                    <?php if (!empty($request['phone'])): ?>
                                    <div class="flex items-center truncate"><i class="fas fa-phone mr-2 text-primary text-xs flex-shrink-0"></i><span class="truncate"><?php echo htmlspecialchars($request['phone'], ENT_QUOTES, 'UTF-8'); ?></span></div>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-500 border-r border-gray-100">
                                <div class="flex items-center">
                                    <i class="fas fa-calendar-alt mr-2 text-primary text-xs"></i>
                                    <?php echo date('M j, Y', strtotime($request['created_at'])); ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                <button onclick="openRequestModal(<?php echo $request['id']; ?>)" 
                                        data-request='<?php echo htmlspecialchars(json_encode($request, JSON_HEX_APOS | JSON_HEX_QUOT), ENT_QUOTES, 'UTF-8'); ?>'
                                        class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors shadow-sm hover:shadow-md transform hover:scale-105">
                                    <i class="fas fa-eye mr-2"></i>
                                    View Details
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Request Details Modal -->
    <div id="detailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-10 mx-auto p-5 border w-4xl max-w-4xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900 flex items-center">
                        <i class="fas fa-info-circle mr-2 text-blue-600"></i>
                        Request Details
                    </h3>
                    <button onclick="closeDetailsModal()" class="text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full p-2 transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <div id="detailsContent">
                    <!-- Details will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200 mt-12">
        <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <p class="text-sm font-semibold text-gray-600">
                    <i class="fas fa-shield-alt mr-2 text-primary"></i>
                    Director's Dashboard - World Crusade Requests Management System
                </p>
                <p class="text-xs text-gray-500 mt-2 font-medium">
                    Generated on <?php echo date('F j, Y \a\t g:i A'); ?>
                </p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>

    <script>
    $(document).ready(function() {
        // Initialize DataTable with custom styling
        $('#directorsTable').DataTable({
            responsive: {
                details: {
                    display: $.fn.dataTable.Responsive.display.childRowImmediate,
                    type: 'none',
                    target: ''
                }
            },
            scrollX: true,
            pageLength: 25,
            order: [[10, 'desc']], // Order by date descending
            dom: '<"flex flex-col lg:flex-row lg:justify-between lg:items-start mb-4 sm:mb-6 gap-2 sm:gap-4 px-2 sm:px-0"<"dataTables_length_container"l><"dataTables_filter_container w-full sm:w-auto"f>>B<"table-container"t><"flex flex-col sm:flex-row justify-between items-center mt-4 sm:mt-6 pt-4 border-t border-gray-200 gap-2 sm:gap-4 px-2 sm:px-0"<"dataTables_info_container text-center sm:text-left"i><"dataTables_paginate_container"p>>',
            buttons: [
                {
                    extend: 'excel',
                    text: '<i class="fas fa-file-excel mr-2"></i>Excel Export',
                    className: 'bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors shadow-sm',
                    title: 'World Crusade Requests - Executive Report',
                    exportOptions: {
                        columns: [0,2,3,4,5,6,7,8] // Include Zone/Group/Church column (excluding contact, date, status)
                    }
                },
                {
                    extend: 'pdf',
                    text: '<i class="fas fa-file-pdf mr-2"></i>PDF Report',
                    className: 'bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium ml-2 transition-colors shadow-sm',
                    orientation: 'landscape',
                    pageSize: 'A3',
                    title: 'World Crusade Requests - Executive Report',
                    exportOptions: {
                        columns: [0,2,3,4,5,6,7,8] // Include Zone/Group/Church column (excluding contact, date, status)
                    }
                }
            ],
            columnDefs: [
                {
                    targets: [11], // Actions column (was 12, now 11 after removing Status)
                    orderable: false,
                    searchable: false
                }
            ],
            language: {
                search: "_INPUT_",
                searchPlaceholder: "Search all requests...",
                lengthMenu: "Show _MENU_ requests per page",
                info: "Showing _START_ to _END_ of _TOTAL_ total requests",
                infoEmpty: "No requests found",
                infoFiltered: "(filtered from _MAX_ total requests)",
                zeroRecords: "No matching requests found",
                emptyTable: "No crusade requests available",
                paginate: {
                    first: "First",
                    last: "Last",
                    next: "Next",
                    previous: "Previous"
                }
            },
            initComplete: function() {
                // Apply additional custom styling
                console.log('DataTable initialized successfully');
                
                // Force layout corrections
                $('.dataTables_length_container .dataTables_length').css({
                    'float': 'none',
                    'margin-bottom': '0'
                });
                
                $('.dataTables_filter_container .dataTables_filter').css({
                    'float': 'none',
                    'text-align': 'left',
                    'margin-bottom': '0'
                });
                
                $('.dataTables_info_container .dataTables_info').css({
                    'float': 'none'
                });
                
                $('.dataTables_paginate_container .dataTables_paginate').css({
                    'float': 'none'
                });
            }
        });

        // Simplified interface - removed bulk actions for easier navigation
    });
</script>

<script>
    // View request details function
    function openRequestModal(requestId) {
        // Find the button that was clicked and get the request data
        const button = event.target.closest('button');
        const requestData = button.getAttribute('data-request');
        const request = JSON.parse(requestData);
        
        viewRequestDetails(request);
    }
    
    function viewRequestDetails(request) {
        const content = `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-medium text-gray-900 mb-3 flex items-center">
                        <i class="fas fa-user mr-2 text-blue-600"></i>
                        Personal Information
                    </h4>
                    <div class="space-y-2 text-sm bg-gray-50 p-4 rounded-lg">
                        <div><span class="font-medium text-gray-700">Name:</span> ${request.designation ? request.designation + ' ' : ''}${request.first_name || 'N/A'} ${request.last_name || ''}</div>
                        <div><span class="font-medium text-gray-700">Email:</span> <a href="mailto:${request.email || ''}" class="text-blue-600 hover:underline">${request.email || 'N/A'}</a></div>
                        <div><span class="font-medium text-gray-700">Phone:</span> <a href="tel:${request.phone || ''}" class="text-blue-600 hover:underline">${request.phone || 'N/A'}</a></div>
                        <div><span class="font-medium text-gray-700">KingsChat:</span> ${request.kingschat_username || 'N/A'}</div>
                        <div><span class="font-medium text-gray-700">Address:</span> ${request.address || request.contact_address || 'N/A'}</div>
                    </div>
                </div>
                
                <div>
                    <h4 class="font-medium text-gray-900 mb-3 flex items-center">
                        <i class="fas fa-building mr-2 text-green-600"></i>
                        Organization Information
                    </h4>
                    <div class="space-y-2 text-sm bg-gray-50 p-4 rounded-lg">
                        <div><span class="font-medium text-gray-700">Organization:</span> ${request.organization_name || 'N/A'}</div>
                        <div><span class="font-medium text-gray-700">Designation:</span> ${request.designation || 'N/A'}</div>
                        <div><span class="font-medium text-gray-700">Zone:</span> ${request.zone || 'N/A'}</div>
                        <div><span class="font-medium text-gray-700">Group:</span> ${request.group || request.group_name || 'N/A'}</div>
                        <div><span class="font-medium text-gray-700">Church:</span> ${request.church || request.church_name || 'N/A'}</div>
                        <div><span class="font-medium text-gray-700">Ministry:</span> ${request.ministry_name || 'N/A'}</div>
                        <div><span class="font-medium text-gray-700">Church Type:</span> ${request.church_type || 'N/A'}</div>
                    </div>
                </div>
                
                <div>
                    <h4 class="font-medium text-gray-900 mb-3 flex items-center">
                        <i class="fas fa-calendar-alt mr-2 text-purple-600"></i>
                        Crusade Details
                    </h4>
                    <div class="space-y-2 text-sm bg-gray-50 p-4 rounded-lg">
                        <div><span class="font-medium text-gray-700">Title:</span> ${request.crusade_title || 'N/A'}</div>
                        <div><span class="font-medium text-gray-700">Type:</span> 
                            ${(() => {
                                if (Array.isArray(request.crusade_types) && request.crusade_types.length > 0) {
                                    // Handle array of crusade types (church registration)
                                    return request.crusade_types.map(type => `<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 mr-1">${type.charAt(0).toUpperCase() + type.slice(1).replace(/-/g, ' ')}</span>`).join('');
                                } else {
                                    // Single crusade type (individual/organisation registration)
                                    const type = request.crusade_type || 'N/A';
                                    return `<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">${type.charAt(0).toUpperCase() + type.slice(1).replace(/-/g, ' ')}</span>`;
                                }
                            })()}
                        </div>
                        <div><span class="font-medium text-gray-700">Venue:</span> ${request.venue || 'N/A'}</div>
                        <div><span class="font-medium text-gray-700">Expected Attendance:</span> <span class="font-semibold text-blue-600">${request.attendance || 'N/A'}</span></div>
                        ${request.number_of_crusades ? `<div><span class="font-medium text-gray-700">Number of Crusades:</span> <span class="font-semibold text-green-600">${request.number_of_crusades}</span></div>` : ''}
                        ${request.countries ? `<div><span class="font-medium text-gray-700">Countries:</span> ${Array.isArray(request.countries) ? request.countries.join(', ') : request.countries}</div>` : ''}
                        ${request.countries_string ? `<div><span class="font-medium text-gray-700">Countries:</span> ${request.countries_string}</div>` : ''}
                    </div>
                </div>
                
                <div>
                    <h4 class="font-medium text-gray-900 mb-3 flex items-center">
                        <i class="fas fa-map-marker-alt mr-2 text-red-600"></i>
                        Location
                    </h4>
                    <div class="space-y-2 text-sm bg-gray-50 p-4 rounded-lg">
                        <div><span class="font-medium text-gray-700">Country:</span> 
                            ${(() => {
                                if (request.countries && typeof request.countries === 'string' && request.countries.trim() !== '') {
                                    // Handle comma-separated string
                                    const countries = request.countries.split(',').map(c => c.trim()).filter(c => c !== '');
                                    return countries.map(country => `<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 mr-1">${country.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>`).join('');
                                } else if (Array.isArray(request.countries_array) && request.countries_array.length > 0) {
                                    // Handle array format
                                    return request.countries_array.map(country => `<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 mr-1">${country.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>`).join('');
                                } else {
                                    // Single country fallback
                                    const country = request.country || 'N/A';
                                    return `<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">${country.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>`;
                                }
                            })()}
                        </div>
                        <div><span class="font-medium text-gray-700">City/Location:</span> ${request.location || 'N/A'}</div>
                    </div>
                </div>
                
                ${request.comments || request.additional_comments ? `
                <div class="md:col-span-2">
                    <h4 class="font-medium text-gray-900 mb-3 flex items-center">
                        <i class="fas fa-comment mr-2 text-yellow-600"></i>
                        Comments
                    </h4>
                    <div class="space-y-2 text-sm bg-gray-50 p-4 rounded-lg">
                        ${request.comments ? `<div><span class="font-medium text-gray-700">Comments:</span> ${request.comments}</div>` : ''}
                        ${request.additional_comments ? `<div><span class="font-medium text-gray-700">Additional Comments:</span> ${request.additional_comments}</div>` : ''}
                    </div>
                </div>
                ` : ''}
                
                <div class="md:col-span-2">
                    <h4 class="font-medium text-gray-900 mb-3 flex items-center">
                        <i class="fas fa-info-circle mr-2 text-indigo-600"></i>
                        Request Information
                    </h4>
                    <div class="space-y-2 text-sm bg-gray-50 p-4 rounded-lg">
                        <div><span class="font-medium text-gray-700">Registration Type:</span> <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800 capitalize">${request.registration_type || 'individual'}</span></div>
                        <div><span class="font-medium text-gray-700">Created:</span> ${new Date(request.created_at).toLocaleString()}</div>
                        <div><span class="font-medium text-gray-700">Last Updated:</span> ${new Date(request.updated_at).toLocaleString()}</div>
                        ${request.admin_notes ? `<div><span class="font-medium text-gray-700">Admin Notes:</span> <div class="mt-1 p-2 bg-yellow-50 border border-yellow-200 rounded">${request.admin_notes}</div></div>` : ''}
                    </div>
                </div>
            </div>
        `;
        
        document.getElementById('detailsContent').innerHTML = content;
        document.getElementById('detailsModal').classList.remove('hidden');
    }

    function closeDetailsModal() {
        document.getElementById('detailsModal').classList.add('hidden');
    }

    // Close modal when clicking outside
    window.onclick = function(event) {
        const detailsModal = document.getElementById('detailsModal');
        if (event.target === detailsModal) {
            closeDetailsModal();
        }
    }
</script>
</body>
</html> 