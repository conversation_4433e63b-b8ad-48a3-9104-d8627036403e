<?php
/**
 * Translation Service for Crusades Content
 * Provides automatic translation using Google Translate API
 */

require_once __DIR__ . '/../vendor/autoload.php';
require_once 'languages.php';

use Stichoza\GoogleTranslate\GoogleTranslate;

class TranslationService {
    private static $instance = null;
    private $translator;
    private $cacheDir;
    private $defaultLanguage = 'en';
    
    private function __construct() {
        $this->translator = new GoogleTranslate();
        $this->cacheDir = __DIR__ . '/../cache/translations/';
        
        // Ensure cache directory exists
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new TranslationService();
        }
        return self::$instance;
    }
    
    /**
     * Translate text from source to target language
     */
    public function translateText($text, $sourceLanguage = 'en', $targetLanguage = 'en') {
        // If target language is the same as source, return original
        if ($targetLanguage === $sourceLanguage) {
            return $text;
        }
        
        try {
            $this->translator->setSource($sourceLanguage);
            $this->translator->setTarget($targetLanguage);
            return $this->translator->translate($text);
        } catch (Exception $e) {
            // Log error and return original text as fallback
            error_log("Translation failed: " . $e->getMessage());
            return $text;
        }
    }
    
    /**
     * Translate crusade content to target language
     */
    public function translateCrusade($crusade, $targetLanguage) {
        // If target language is the same as current or default, return original
        if ($targetLanguage === $this->defaultLanguage) {
            return $crusade;
        }
        
        $crusadeId = $crusade['id'];
        $cacheKey = $this->getCacheKey($crusadeId, $targetLanguage);
        
        // Check cache first
        $cachedTranslation = $this->getFromCache($cacheKey);
        if ($cachedTranslation !== null) {
            return $cachedTranslation;
        }
        
        // Create translated version
        $translatedCrusade = $crusade;
        
        try {
            // Translate title
            if (!empty($crusade['title'])) {
                $translatedCrusade['title'] = $this->translateText($crusade['title'], $this->defaultLanguage, $targetLanguage);
            }
            
            // Translate description
            if (!empty($crusade['description'])) {
                $translatedCrusade['description'] = $this->translateText($crusade['description'], $this->defaultLanguage, $targetLanguage);
            }
            
            // Translate venue
            if (!empty($crusade['venue'])) {
                $translatedCrusade['venue'] = $this->translateText($crusade['venue'], $this->defaultLanguage, $targetLanguage);
            }
            
            // Keep address as-is (addresses usually shouldn't be translated)
            // Keep other fields unchanged (id, date, time, image, register_link)
            
            // Cache the translation
            $this->saveToCache($cacheKey, $translatedCrusade);
            
        } catch (Exception $e) {
            // If translation fails, return original
            error_log("Translation failed for crusade {$crusadeId} to {$targetLanguage}: " . $e->getMessage());
            return $crusade;
        }
        
        return $translatedCrusade;
    }
    
    /**
     * Generate cache key for crusade translation
     */
    private function getCacheKey($crusadeId, $language) {
        return "crusade_{$crusadeId}_{$language}";
    }
    
    /**
     * Get translation from cache
     */
    private function getFromCache($cacheKey) {
        $cacheFile = $this->cacheDir . $cacheKey . '.json';
        
        if (file_exists($cacheFile)) {
            $cacheData = json_decode(file_get_contents($cacheFile), true);
            
            // Check if cache is still valid (24 hours)
            if (isset($cacheData['timestamp']) && 
                time() - $cacheData['timestamp'] < 86400) {
                return $cacheData['data'];
            }
        }
        
        return null;
    }
    
    /**
     * Save translation to cache
     */
    private function saveToCache($cacheKey, $data) {
        $cacheFile = $this->cacheDir . $cacheKey . '.json';
        
        $cacheData = [
            'timestamp' => time(),
            'data' => $data
        ];
        
        file_put_contents($cacheFile, json_encode($cacheData, JSON_UNESCAPED_UNICODE));
    }
    
    /**
     * Clear cache for a specific crusade
     */
    public function clearCrusadeCache($crusadeId) {
        $pattern = $this->cacheDir . "crusade_{$crusadeId}_*.json";
        $files = glob($pattern);
        
        foreach ($files as $file) {
            unlink($file);
        }
    }
    
    /**
     * Clear all translation cache
     */
    public function clearAllCache() {
        $files = glob($this->cacheDir . '*.json');
        
        foreach ($files as $file) {
            unlink($file);
        }
    }
    
    /**
     * Get all available languages for translation
     */
    public function getAvailableLanguages() {
        return Language::getAvailableLanguages();
    }
    
    /**
     * Check if a language needs translation
     */
    public function needsTranslation($language) {
        return $language !== $this->defaultLanguage;
    }
}
?>