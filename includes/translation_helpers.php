<?php
/**
 * Translation helper functions
 */

require_once 'TranslationService.php';

/**
 * Get translated crusade content for current language
 */
function getTranslatedCrusade($crusade) {
    $translationService = TranslationService::getInstance();
    $currentLanguage = Language::getCurrentLanguage();
    return $translationService->translateCrusade($crusade, $currentLanguage);
}

/**
 * Get translated crusades array for current language
 */
function getTranslatedCrusades($crusadesArray) {
    $translationService = TranslationService::getInstance();
    $currentLanguage = Language::getCurrentLanguage();
    
    $translatedCrusades = [];
    foreach ($crusadesArray as $crusade) {
        $translatedCrusades[] = $translationService->translateCrusade($crusade, $currentLanguage);
    }
    
    return $translatedCrusades;
}

/**
 * Clear translation cache for specific crusade
 */
function clearCrusadeTranslationCache($crusadeId) {
    $translationService = TranslationService::getInstance();
    $translationService->clearCrusadeCache($crusadeId);
}

/**
 * Clear all translation cache
 */
function clearAllTranslationCache() {
    $translationService = TranslationService::getInstance();
    $translationService->clearAllCache();
}

/**
 * Check if current language needs translation
 */
function needsTranslation() {
    $translationService = TranslationService::getInstance();
    $currentLanguage = Language::getCurrentLanguage();
    return $translationService->needsTranslation($currentLanguage);
}
?>