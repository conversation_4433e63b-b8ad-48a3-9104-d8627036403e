<?php
require_once 'includes/config.php';
require_once 'includes/languages.php';
require_once 'includes/TranslationService.php';
include 'includes/header.php';
?>

<!-- Hero Carousel Section -->
<div class="relative overflow-hidden min-h-screen" id="heroCarousel">
    <!-- Video Background -->
    <div class="absolute inset-0 w-full h-full">
        <video
            autoplay
            muted
            loop
            playsinline
            class="absolute inset-0 w-full h-full object-cover">
            <source src="https://rorcloud.org/serve.php?id=279&name=output(compress-video-online.com).mp4" type="video/mp4">
        </video>
        <!-- Dark Overlay for better text readability -->
        <div class="absolute inset-0 bg-black bg-opacity-60 hero-overlay"></div>
    </div>

    <div class="hero-slides-container" style="display: flex; width: 400%; transition: transform 0.7s ease-in-out;">
        <!-- Hero Slide 1: Night of a Thousand Crusades -->
        <div class="hero-slide min-h-screen flex items-center relative" 
             data-slide="0" 
             style="width: 25%;">
            <!-- Content overlay -->
            <div class="absolute inset-0 bg-transparent"></div>
            
            <main class="w-full relative z-10">
                <div class="max-w-6xl mx-auto px-4 sm:px-6 py-12 sm:py-16 md:py-20">
                    <div class="text-center">
                        <!-- Hero Content -->
                        <div class="space-y-6 sm:space-y-8 max-w-4xl mx-auto">
                            <div class="hero-title-container">
                                <!-- Date -->
                                <div class="mb-4 sm:mb-6">
                                    <p class="text-lg sm:text-xl md:text-2xl lg:text-3xl text-white font-light tracking-wide">
                                        <?php 
                                        if ($currentLanguage === 'en') {
                                            echo '22nd of August';
                                        } else {
                                            echo $translationService->translateText('22nd of August', 'en', $currentLanguage) ?? '22nd of August';
                                        }
                                        ?>
                                    </p>
                                </div>
                                <h1 class="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold leading-tight text-white hero-title">
                                    <span class="text-accent block">
                                        <?php 
                                        if ($currentLanguage === 'en') {
                                            echo 'Night of a Thousand';
                                        } else {
                                            echo $translationService->translateText('Night of a Thousand', 'en', $currentLanguage) ?? 'Night of a Thousand';
                                        }
                                        ?>
                                    </span> 
                                    <span class="block mt-2 sm:mt-3 md:mt-4">
                                        <?php 
                                        if ($currentLanguage === 'en') {
                                            echo 'Crusades';
                                        } else {
                                            echo $translationService->translateText('Crusades', 'en', $currentLanguage) ?? 'Crusades';
                                        }
                                        ?>
                                    </span>
                                </h1>
                            </div>

                            <div class="hero-description-container px-4 sm:px-0">
                                <p class="text-xl sm:text-2xl md:text-3xl lg:text-4xl text-white leading-relaxed font-light max-w-4xl mx-auto drop-shadow-lg">
                                    <?php 
                                    if ($currentLanguage === 'en') {
                                        echo 'A global simultaneous outreach event bringing the Gospel to billions across the world in one extraordinary night of ministry and impact.';
                                    } else {
                                        echo $translationService->translateText('A global simultaneous outreach event bringing the Gospel to billions across the world in one extraordinary night of ministry and impact.', 'en', $currentLanguage) ?? 'A global simultaneous outreach event bringing the Gospel to billions across the world in one extraordinary night of ministry and impact.';
                                    }
                                    ?>
                                </p>
                            </div>

                            <div class="pt-6 sm:pt-8 flex flex-col sm:flex-row gap-3 sm:gap-4 items-center justify-center px-4 sm:px-0">
                                <a href="#" onclick="openCrusadeRegistrationModal(); return false;" class="w-full sm:w-auto inline-flex items-center justify-center bg-accent text-gray-900 px-6 sm:px-8 py-3 sm:py-4 font-medium hover:bg-yellow-300 transition-colors duration-200 shadow-lg hover:shadow-xl border border-gold hover:border-yellow-300 rounded-lg sm:rounded-none text-sm sm:text-base">
                                    <?php 
                                    if ($currentLanguage === 'en') {
                                        echo 'Host Your Own Crusade';
                                    } else {
                                        echo $translationService->translateText('Host Your Own Crusade', 'en', $currentLanguage) ?? 'Host Your Own Crusade';
                                    }
                                    ?>
                                    <svg class="ml-2 w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <!-- Hero Slide 2: Main Rhapsody of Realities -->
        <div class="hero-slide min-h-screen flex items-center relative" 
             data-slide="1" 
             style="width: 25%;">
            <!-- Content overlay -->
            <div class="absolute inset-0 bg-transparent"></div>
            
            <main class="w-full relative z-10">
                <div class="max-w-6xl mx-auto px-4 sm:px-6 py-12 sm:py-16 md:py-20">
                    <div class="text-center">
                        <!-- Hero Content -->
                        <div class="space-y-6 sm:space-y-8 max-w-4xl mx-auto">
                            <div class="hero-title-container">
                                <h1 class="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold leading-tight text-white hero-title">
                                    <span class="text-accent block sm:inline hero-rhapsody"><?php echo __('hero_title_rhapsody'); ?></span> 
                                    <div class="mt-2 sm:mt-3 md:mt-4 flex justify-center hero-rotating-container">
                                        <div class="rotating-text-container">
                                            <span class="text-rotate" id="rotatingText">
                                                <span class="text-rotate-sr-only"><?php echo __('typewriter_crusades'); ?></span>
                                            </span>
                                        </div>
                                    </div>
                                </h1>
                            </div>

                            <div class="hero-description-container px-4 sm:px-0 hero-description">
                                <p class="text-xl sm:text-2xl md:text-3xl lg:text-4xl text-white leading-relaxed font-light max-w-4xl mx-auto drop-shadow-lg">
                                    <span class="hero-text-preview"><?php 
                                        $description = __('hero_description');
                                        $words = explode(' ', $description);
                                        $preview = implode(' ', array_slice($words, 0, 15));
                                        echo $preview . (count($words) > 15 ? '...' : '');
                                    ?></span>
                                    <?php if (count(explode(' ', __('hero_description'))) > 15): ?>
                                    <span class="hero-text-full hidden"><?php echo __('hero_description'); ?></span>
                                    <button class="hero-read-more text-accent hover:text-gold font-medium ml-2 underline text-base sm:text-lg" onclick="toggleHeroText(event)"><?php 
                                        $currentLanguage = Language::getCurrentLanguage();
                                        if ($currentLanguage === 'en') {
                                            echo 'Read More';
                                        } else {
                                            $translationService = TranslationService::getInstance();
                                            echo $translationService->translateText('Read More', 'en', $currentLanguage) ?? 'Read More';
                                        }
                                    ?></button>
                                    <button class="hero-read-less text-accent hover:text-gold font-medium ml-2 underline hidden text-base sm:text-lg" onclick="toggleHeroText(event)"><?php 
                                        if ($currentLanguage === 'en') {
                                            echo 'Read Less';
                                        } else {
                                            echo $translationService->translateText('Read Less', 'en', $currentLanguage) ?? 'Read Less';
                                        }
                                    ?></button>
                                    <?php endif; ?>
                                </p>
                            </div>

                            <div class="pt-6 sm:pt-8 flex flex-col sm:flex-row gap-3 sm:gap-4 items-center justify-center px-4 sm:px-0 hero-buttons">
                                <a href="#about" class="w-full sm:w-auto inline-flex items-center justify-center bg-accent text-gray-900 px-6 sm:px-8 py-3 sm:py-4 font-medium hover:bg-yellow-300 transition-colors duration-200 shadow-lg hover:shadow-xl border border-gold hover:border-yellow-300 rounded-lg sm:rounded-none text-sm sm:text-base hero-btn-1">
                                    <?php
                                    if ($currentLanguage === 'en') {
                                        echo 'View Upcoming Crusades';
                                    } else {
                                        echo $translationService->translateText('View Upcoming Crusades', 'en', $currentLanguage) ?? 'View Upcoming Crusades';
                                    }
                                    ?>
                                    <svg class="ml-2 w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </a>
                                <a href="#highlights" class="w-full sm:w-auto inline-flex items-center justify-center bg-primary text-white px-6 sm:px-8 py-3 sm:py-4 font-medium hover:bg-primaryDark transition-colors duration-200 shadow-lg hover:shadow-xl border border-accent hover:border-gold rounded-lg sm:rounded-none text-sm sm:text-base hero-btn-2">
                                    <?php echo __('view_highlights'); ?>
                                    <svg class="ml-2 w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                    </svg>
                                </a>
                                <a href="#" onclick="openCrusadeRegistrationModal(); return false;" class="w-full sm:w-auto inline-flex items-center justify-center bg-white text-gray-900 px-6 sm:px-8 py-3 sm:py-4 font-medium hover:bg-gray-100 transition-colors duration-200 shadow-lg hover:shadow-xl border border-gray-200 hover:border-gray-300 rounded-lg sm:rounded-none text-sm sm:text-base">
                                    <?php
                                    if ($currentLanguage === 'en') {
                                        echo 'Host Your Own Crusade';
                                    } else {
                                        echo $translationService->translateText('Host Your Own Crusade', 'en', $currentLanguage) ?? 'Host Your Own Crusade';
                                    }
                                    ?>
                                    <svg class="ml-2 w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                </a>
                                <a href="https://crusades.mystreamspace.org" target="_blank" class="w-full sm:w-auto inline-flex items-center justify-center bg-yellow-400 text-gray-900 px-6 sm:px-8 py-3 sm:py-4 font-medium hover:bg-yellow-300 transition-colors duration-200 shadow-lg hover:shadow-xl border border-yellow-500 hover:border-yellow-300 rounded-lg sm:rounded-none text-sm sm:text-base">
                                    <?php
                                    if ($currentLanguage === 'en') {
                                        echo 'Host Online Crusades';
                                    } else {
                                        echo $translationService->translateText('Host Online Crusades', 'en', $currentLanguage) ?? 'Host Online Crusades';
                                    }
                                    ?>
                                    <svg class="ml-2 w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <!-- Duplicate slides for seamless loop -->
        <!-- Hero Slide 1 Clone: Night of a Thousand Crusades -->
        <div class="hero-slide min-h-screen flex items-center relative" 
             data-slide="2" 
             style="width: 25%;">
            <!-- Content overlay -->
            <div class="absolute inset-0 bg-transparent"></div>
            
            <main class="w-full relative z-10">
                <div class="max-w-6xl mx-auto px-4 sm:px-6 py-12 sm:py-16 md:py-20">
                    <div class="text-center">
                        <!-- Hero Content -->
                        <div class="space-y-6 sm:space-y-8 max-w-4xl mx-auto">
                            <div class="hero-title-container">
                                <!-- Date -->
                                <div class="mb-4 sm:mb-6">
                                    <p class="text-lg sm:text-xl md:text-2xl lg:text-3xl text-white font-light tracking-wide">
                                        <?php 
                                        if ($currentLanguage === 'en') {
                                            echo '22nd of August';
                                        } else {
                                            echo $translationService->translateText('22nd of August', 'en', $currentLanguage) ?? '22nd of August';
                                        }
                                        ?>
                                    </p>
                                </div>
                                <h1 class="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold leading-tight text-white hero-title">
                                    <span class="text-accent block">
                                        <?php 
                                        if ($currentLanguage === 'en') {
                                            echo 'Night of a Thousand';
                                        } else {
                                            echo $translationService->translateText('Night of a Thousand', 'en', $currentLanguage) ?? 'Night of a Thousand';
                                        }
                                        ?>
                                    </span> 
                                    <span class="block mt-2 sm:mt-3 md:mt-4">
                                        <?php 
                                        if ($currentLanguage === 'en') {
                                            echo 'Crusades';
                                        } else {
                                            echo $translationService->translateText('Crusades', 'en', $currentLanguage) ?? 'Crusades';
                                        }
                                        ?>
                                    </span>
                                </h1>
                            </div>

                            <div class="hero-description-container px-4 sm:px-0">
                                <p class="text-xl sm:text-2xl md:text-3xl lg:text-4xl text-white leading-relaxed font-light max-w-4xl mx-auto drop-shadow-lg">
                                    <?php 
                                    if ($currentLanguage === 'en') {
                                        echo 'A global simultaneous outreach event bringing the Gospel to billions across the world in one extraordinary night of ministry and impact.';
                                    } else {
                                        echo $translationService->translateText('A global simultaneous outreach event bringing the Gospel to billions across the world in one extraordinary night of ministry and impact.', 'en', $currentLanguage) ?? 'A global simultaneous outreach event bringing the Gospel to billions across the world in one extraordinary night of ministry and impact.';
                                    }
                                    ?>
                                </p>
                            </div>

                            <div class="pt-6 sm:pt-8 flex flex-col sm:flex-row gap-3 sm:gap-4 items-center justify-center px-4 sm:px-0">
                                <a href="#" onclick="openCrusadeRegistrationModal(); return false;" class="w-full sm:w-auto inline-flex items-center justify-center bg-accent text-gray-900 px-6 sm:px-8 py-3 sm:py-4 font-medium hover:bg-yellow-300 transition-colors duration-200 shadow-lg hover:shadow-xl border border-gold hover:border-yellow-300 rounded-lg sm:rounded-none text-sm sm:text-base">
                                    <?php 
                                    if ($currentLanguage === 'en') {
                                        echo 'Host Your Own Crusade';
                                    } else {
                                        echo $translationService->translateText('Host Your Own Crusade', 'en', $currentLanguage) ?? 'Host Your Own Crusade';
                                    }
                                    ?>
                                    <svg class="ml-2 w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                </a>
                                <button onclick="redirectToWorldIndex()" class="w-full sm:w-auto inline-flex items-center justify-center bg-primary text-white px-6 sm:px-8 py-3 sm:py-4 font-medium hover:bg-primaryDark transition-colors duration-200 shadow-lg hover:shadow-xl border border-accent hover:border-gold rounded-lg sm:rounded-none text-sm sm:text-base">
                                    <?php 
                                    if ($currentLanguage === 'en') {
                                        echo 'Learn More';
                                    } else {
                                        echo $translationService->translateText('Learn More', 'en', $currentLanguage) ?? 'Learn More';
                                    }
                                    ?>
                                    <svg class="ml-2 w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <!-- Hero Slide 2 Clone: Main Rhapsody of Realities -->
        <div class="hero-slide min-h-screen flex items-center relative" 
             data-slide="3" 
             style="width: 25%;">
            <!-- Content overlay -->
            <div class="absolute inset-0 bg-transparent"></div>
            
            <main class="w-full relative z-10">
                <div class="max-w-6xl mx-auto px-4 sm:px-6 py-12 sm:py-16 md:py-20">
                    <div class="text-center">
                        <!-- Hero Content -->
                        <div class="space-y-6 sm:space-y-8 max-w-4xl mx-auto">
                            <div class="hero-title-container">
                                <h1 class="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold leading-tight text-white hero-title">
                                    <span class="text-accent block sm:inline hero-rhapsody"><?php echo __('hero_title_rhapsody'); ?></span> 
                                    <div class="mt-2 sm:mt-3 md:mt-4 flex justify-center hero-rotating-container">
                                        <div class="rotating-text-container">
                                            <span class="text-rotate" id="rotatingText2">
                                                <span class="text-rotate-sr-only"><?php echo __('typewriter_crusades'); ?></span>
                                            </span>
                                        </div>
                                    </div>
                                </h1>
                            </div>

                            <div class="hero-description-container px-4 sm:px-0 hero-description">
                                <p class="text-xl sm:text-2xl md:text-3xl lg:text-4xl text-white leading-relaxed font-light max-w-4xl mx-auto drop-shadow-lg">
                                    <span class="hero-text-preview"><?php 
                                        echo $preview . (count($words) > 15 ? '...' : '');
                                    ?></span>
                                    <?php if (count(explode(' ', __('hero_description'))) > 15): ?>
                                    <span class="hero-text-full hidden"><?php echo __('hero_description'); ?></span>
                                    <button class="hero-read-more text-accent hover:text-gold font-medium ml-2 underline text-base sm:text-lg" onclick="toggleHeroText(event)"><?php 
                                        if ($currentLanguage === 'en') {
                                            echo 'Read More';
                                        } else {
                                            echo $translationService->translateText('Read More', 'en', $currentLanguage) ?? 'Read More';
                                        }
                                    ?></button>
                                    <button class="hero-read-less text-accent hover:text-gold font-medium ml-2 underline hidden text-base sm:text-lg" onclick="toggleHeroText(event)"><?php 
                                        if ($currentLanguage === 'en') {
                                            echo 'Read Less';
                                        } else {
                                            echo $translationService->translateText('Read Less', 'en', $currentLanguage) ?? 'Read Less';
                                        }
                                    ?></button>
                                    <?php endif; ?>
                                </p>
                            </div>

                            <div class="pt-6 sm:pt-8 flex flex-col sm:flex-row gap-3 sm:gap-4 items-center justify-center px-4 sm:px-0 hero-buttons">
                                <a href="#about" class="w-full sm:w-auto inline-flex items-center justify-center bg-accent text-gray-900 px-6 sm:px-8 py-3 sm:py-4 font-medium hover:bg-yellow-300 transition-colors duration-200 shadow-lg hover:shadow-xl border border-gold hover:border-yellow-300 rounded-lg sm:rounded-none text-sm sm:text-base hero-btn-1">
                                    <?php
                                    if ($currentLanguage === 'en') {
                                        echo 'View Upcoming Crusades';
                                    } else {
                                        echo $translationService->translateText('View Upcoming Crusades', 'en', $currentLanguage) ?? 'View Upcoming Crusades';
                                    }
                                    ?>
                                    <svg class="ml-2 w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </a>
                                <a href="#highlights" class="w-full sm:w-auto inline-flex items-center justify-center bg-primary text-white px-6 sm:px-8 py-3 sm:py-4 font-medium hover:bg-primaryDark transition-colors duration-200 shadow-lg hover:shadow-xl border border-accent hover:border-gold rounded-lg sm:rounded-none text-sm sm:text-base hero-btn-2">
                                    <?php echo __('view_highlights'); ?>
                                    <svg class="ml-2 w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                    </svg>
                                </a>
                                <a href="#" onclick="openCrusadeRegistrationModal(); return false;" class="w-full sm:w-auto inline-flex items-center justify-center bg-white text-gray-900 px-6 sm:px-8 py-3 sm:py-4 font-medium hover:bg-gray-100 transition-colors duration-200 shadow-lg hover:shadow-xl border border-gray-200 hover:border-gray-300 rounded-lg sm:rounded-none text-sm sm:text-base">
                                    <?php
                                    if ($currentLanguage === 'en') {
                                        echo 'Host Your Own Crusade';
                                    } else {
                                        echo $translationService->translateText('Host Your Own Crusade', 'en', $currentLanguage) ?? 'Host Your Own Crusade';
                                    }
                                    ?>
                                    <svg class="ml-2 w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                </a>
                                <a href="https://crusades.mystreamspace.org" target="_blank" class="w-full sm:w-auto inline-flex items-center justify-center bg-yellow-400 text-gray-900 px-6 sm:px-8 py-3 sm:py-4 font-medium hover:bg-yellow-300 transition-colors duration-200 shadow-lg hover:shadow-xl border border-yellow-500 hover:border-yellow-300 rounded-lg sm:rounded-none text-sm sm:text-base">
                                    <?php
                                    if ($currentLanguage === 'en') {
                                        echo 'Host Online Crusades';
                                    } else {
                                        echo $translationService->translateText('Host Online Crusades', 'en', $currentLanguage) ?? 'Host Online Crusades';
                                    }
                                    ?>
                                    <svg class="ml-2 w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Hero Navigation -->
    <button id="heroPrevBtn" class="absolute top-1/2 left-2 sm:left-4 md:left-6 lg:left-8 xl:left-12 transform -translate-y-1/2 w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 bg-white/20 backdrop-blur-sm border border-white/30 text-white hover:bg-white/30 hover:border-white/50 transition-all duration-200 focus:outline-none z-30 shadow-lg rounded-full items-center justify-center hidden sm:flex">
        <svg class="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
    </button>
    
    <button id="heroNextBtn" class="absolute top-1/2 right-2 sm:right-4 md:right-6 lg:right-8 xl:right-12 transform -translate-y-1/2 w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 bg-white/20 backdrop-blur-sm border border-white/30 text-white hover:bg-white/30 hover:border-white/50 transition-all duration-200 focus:outline-none z-30 shadow-lg rounded-full items-center justify-center hidden sm:flex">
        <svg class="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
    </button>

    <!-- Hero Indicators -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30 flex space-x-3 hidden sm:flex">
        <button class="hero-indicator w-3 h-3 rounded-full bg-white/50 hover:bg-white/80 transition-all duration-200 active" data-slide="0"></button>
        <button class="hero-indicator w-3 h-3 rounded-full bg-white/50 hover:bg-white/80 transition-all duration-200" data-slide="1"></button>
    </div>

    <!-- Mobile Swipe Indicator -->
    <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-30 flex sm:hidden">
        <div class="flex items-center space-x-2 px-4 py-2 bg-black/30 backdrop-blur-md rounded-full border border-white/20">
            <div class="flex space-x-1">
                <div class="w-1.5 h-1.5 bg-white/60 rounded-full swipe-dot"></div>
                <div class="w-1.5 h-1.5 bg-white/40 rounded-full swipe-dot" style="animation-delay: 0.2s;"></div>
                <div class="w-1.5 h-1.5 bg-white/40 rounded-full swipe-dot" style="animation-delay: 0.4s;"></div>
                <div class="w-1.5 h-1.5 bg-white/40 rounded-full swipe-dot" style="animation-delay: 0.6s;"></div>
            </div>
            <div class="w-px h-4 bg-white/30"></div>
            <div class="text-white/80 text-xs font-medium tracking-wide">SWIPE</div>
        </div>
    </div>
</div>

<style>
@keyframes swipeDot {
    0%, 60%, 100% { 
        background-color: rgba(255, 255, 255, 0.4);
        transform: scale(1);
    }
    30% { 
        background-color: rgba(255, 255, 255, 0.9);
        transform: scale(1.2);
    }
}

.swipe-dot {
    animation: swipeDot 2s ease-in-out infinite;
}

@keyframes crusadeSwipeDot {
    0%, 60%, 100% { 
        background-color: rgb(156, 163, 175);
        transform: scale(1);
    }
    30% { 
        background-color: rgb(75, 85, 99);
        transform: scale(1.2);
    }
}

.crusade-swipe-dot {
    animation: crusadeSwipeDot 2s ease-in-out infinite;
}
</style>



<!-- Mission Trips CTA Section -->
<section class="py-12 sm:py-16 md:py-20 relative overflow-hidden lg:bg-fixed mission-trips-section" style="background-image: url('https://rorcloud.org/serve.php?id=121&name=F421CA1F-18D8-42F5-A75E-6742CD745DEC.jpg'); background-size: cover; background-position: center 40%; background-repeat: no-repeat;">
    <!-- Enhanced blue overlay for better text contrast -->
    <div class="absolute inset-0 bg-blue-900 bg-opacity-85"></div>

    <div class="max-w-4xl mx-auto px-4 sm:px-6 text-center relative z-10">
        <!-- Additional background container for extra text contrast -->
        <div class="bg-black bg-opacity-20 rounded-lg p-4 sm:p-6 md:p-8 backdrop-blur-sm mission-trips-glass-overlay">
            <h3 class="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 sm:mb-6" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.8);">
                <?php 
                $currentLanguage = Language::getCurrentLanguage();
                if ($currentLanguage === 'en') {
                    echo 'Join Us on Our Mission Trips';
                } else {
                    $translationService = TranslationService::getInstance();
                    echo $translationService->translateText('Join Us on Our Mission Trips', 'en', $currentLanguage) ?? 'Join Us on Our Mission Trips';
                }
                ?>
            </h3>
            <p class="text-white text-base sm:text-lg md:text-xl mb-4 sm:mb-6 leading-relaxed" style="text-shadow: 1px 1px 3px rgba(0,0,0,0.7);">
                <?php 
                if ($currentLanguage === 'en') {
                    echo 'Travel with us to nations around the world and be part of God\'s redemptive work in these last days as we complete the full preaching of the Gospel in all the earth.';
                } else {
                    echo $translationService->translateText('Travel with us to nations around the world and be part of God\'s redemptive work in these last days as we complete the full preaching of the Gospel in all the earth.', 'en', $currentLanguage) ?? 'Travel with us to nations around the world and be part of God\'s redemptive work in these last days as we complete the full preaching of the Gospel in all the earth.';
                }
                ?>
            </p>
            <p class="text-white text-sm sm:text-base mb-6 sm:mb-8 leading-relaxed" style="text-shadow: 1px 1px 3px rgba(0,0,0,0.7);">
                <?php 
                if ($currentLanguage === 'en') {
                    echo 'Join organized mission trips to distribute Rhapsody of Realities, conduct evangelistic outreaches, and participate in fulfilling the Great Commission by ensuring every person on earth hears the Gospel.';
                } else {
                    echo $translationService->translateText('Join organized mission trips to distribute Rhapsody of Realities, conduct evangelistic outreaches, and participate in fulfilling the Great Commission by ensuring every person on earth hears the Gospel.', 'en', $currentLanguage) ?? 'Join organized mission trips to distribute Rhapsody of Realities, conduct evangelistic outreaches, and participate in fulfilling the Great Commission by ensuring every person on earth hears the Gospel.';
                }
                ?>
            </p>
                            <a href="mission-trip-enrollment?espees_code=RORETC" 
               class="inline-flex items-center px-6 sm:px-8 py-3 sm:py-4 bg-accent text-gray-900 font-bold hover:bg-yellow-300 transition-colors duration-200 shadow-lg hover:shadow-xl rounded-lg sm:rounded-none w-full sm:w-auto justify-center" style="text-shadow: none;">
                <?php 
                if ($currentLanguage === 'en') {
                    echo 'Join Mission Trip';
                } else {
                    echo $translationService->translateText('Join Mission Trip', 'en', $currentLanguage) ?? 'Join Mission Trip';
                }
                ?>
                <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                </svg>
            </a>
        </div>
    </div>
</section>

<!-- MyStreamSpace Banner Section -->
<section class="py-12 sm:py-16 md:py-20 bg-primary mystreamspace-banner-section">
    <div class="max-w-5xl mx-auto px-4 sm:px-6 text-center">
        <!-- Background container with responsive edges -->
        <div class="bg-gray-900 p-6 sm:p-8 md:p-12 mystreamspace-glass-overlay rounded-lg sm:rounded-none">
            <h3 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 sm:mb-8">
                <?php 
                if ($currentLanguage === 'en') {
                    echo 'Rhapsody End-Time Crusades Online';
                } else {
                    echo $translationService->translateText('Rhapsody End-Time Crusades Online', 'en', $currentLanguage) ?? 'Rhapsody End-Time Crusades Online';
                }
                ?>
            </h3>
            <h4 class="text-xl sm:text-2xl md:text-3xl font-semibold text-accent mb-6 sm:mb-8">
                <?php 
                if ($currentLanguage === 'en') {
                    echo 'with MyStreamSpace';
                } else {
                    echo $translationService->translateText('with MyStreamSpace', 'en', $currentLanguage) ?? 'with MyStreamSpace';
                }
                ?>
            </h4>
            <p class="text-white text-lg sm:text-xl md:text-2xl mb-8 sm:mb-10 leading-relaxed max-w-4xl mx-auto">
                <?php 
                if ($currentLanguage === 'en') {
                    echo 'Experience the power of digital evangelism with live streaming crusades reaching millions worldwide through MyStreamSpace platform.';
                } else {
                    echo $translationService->translateText('Experience the power of digital evangelism with live streaming crusades reaching millions worldwide through MyStreamSpace platform.', 'en', $currentLanguage) ?? 'Experience the power of digital evangelism with live streaming crusades reaching millions worldwide through MyStreamSpace platform.';
                }
                ?>
            </p>
            <a href="https://crusades.mystreamspace.org" 
               target="_blank"
               class="inline-flex items-center px-8 sm:px-10 py-4 sm:py-5 bg-accent text-gray-900 font-bold hover:bg-yellow-300 transition-colors duration-200 shadow-lg hover:shadow-xl rounded-lg sm:rounded-none w-full sm:w-auto justify-center text-lg sm:text-xl">
                <?php 
                if ($currentLanguage === 'en') {
                    echo 'Click Here';
                } else {
                    echo $translationService->translateText('Click Here', 'en', $currentLanguage) ?? 'Click Here';
                }
                ?>
                <svg class="ml-3 w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                </svg>
            </a>
        </div>
    </div>
</section>

<!-- Events Section -->
<section id="about" class="py-16 sm:py-20 md:py-24 bg-gray-50">
    <div class="max-w-6xl mx-auto px-4 sm:px-6">
        <div class="text-center mb-12 sm:mb-16 md:mb-20">
            <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4" id="crusades-title"><?php echo __('upcoming_crusades'); ?></h2>
            <div class="w-16 h-px bg-gradient-to-r from-primary via-accent to-gold mx-auto mb-6"></div>
            
        </div>

        <div class="relative">
            <?php
            // Load crusades from JSON file
            $crusadesJson = file_get_contents(__DIR__ . '/data/crusades.json');
            $crusadesData = json_decode($crusadesJson, true);
            $currentDate = new DateTime();
            
            // Function to convert register links to clean URLs
            function convertToCleanUrl($registerLink) {
                // Handle register.php?event=slug format
                if (strpos($registerLink, 'register.php?event=') === 0) {
                    $slug = substr($registerLink, strlen('register.php?event='));
                    $slug = urldecode($slug);
                    return 'register?event=' . urlencode($slug);
                }
                
                // Handle standalone .php files
                if (strpos($registerLink, '.php') !== false) {
                    return str_replace('.php', '', $registerLink);
                }
                
                // Return as-is if already clean
                return $registerLink;
            }
            
            // Get only upcoming crusades
            $upcomingCrusades = [];
            
            foreach ($crusadesData['crusades'] as $crusade) {
                $eventDate = new DateTime($crusade['date'] . ' ' . $crusade['time']);
                if ($eventDate >= $currentDate) {
                    $upcomingCrusades[] = $crusade;
                }
            }
            
            // Sort upcoming crusades by date (nearest first)
            usort($upcomingCrusades, function($a, $b) {
                $dateA = new DateTime($a['date'] . ' ' . $a['time']);
                $dateB = new DateTime($b['date'] . ' ' . $b['time']);
                return $dateA <=> $dateB;
            });
            
            // Get translation service instance
            $translationService = TranslationService::getInstance();
            $currentLanguage = Language::getCurrentLanguage();
            ?>
            <!-- Carousel container -->
            <div id="crusadesCarousel" class="relative min-h-96 h-auto overflow-visible" style="z-index: 1; padding: 2rem 0;">
                <ul id="crusadesSlider" class="relative w-full max-w-4xl mx-auto min-h-fit">
                    <?php foreach ($upcomingCrusades as $index => $crusade): 
                        // Translate crusade content for current language
                        $translatedCrusade = $translationService->translateCrusade($crusade, $currentLanguage);
                        
                        $eventDate = new DateTime($crusade['date'] . ' ' . $crusade['time']);
                    ?>
                    <li class="crusade-card absolute w-full px-3 sm:px-4 md:px-6" data-index="<?php echo $index; ?>" data-type="upcoming">
                        <div class="bg-white border border-gray-200 rounded-lg sm:rounded-none overflow-hidden shadow-sm hover:shadow-lg transition-shadow duration-300">
                            <div class="relative flex flex-col lg:flex-row">
                                <!-- Image Section -->
                                <div class="w-full lg:w-1/2 relative">
                                    <div class="aspect-[16/9] sm:aspect-[4/3] lg:aspect-auto lg:h-96 overflow-hidden lg:flex lg:items-center lg:justify-center">
                                        <img class="w-full h-full object-cover lg:w-auto lg:h-auto lg:max-w-full lg:max-h-full lg:object-contain" src="<?php echo htmlspecialchars($crusade['image']); ?>" alt="<?php echo htmlspecialchars($translatedCrusade['title']); ?>">
                                    </div>
                                </div>

                                <!-- Content Section -->
                                <div class="w-full lg:w-1/2 p-4 sm:p-6 md:p-8 lg:p-12 flex flex-col justify-center">
                                    <div class="space-y-4 sm:space-y-6 lg:space-y-8">
                                        <h3 class="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-light text-gray-900 leading-tight"><?php echo htmlspecialchars($translatedCrusade['title']); ?></h3>
                                        <div class="crusade-description-container">
                                            <p class="text-sm sm:text-base md:text-lg text-gray-600 leading-relaxed">
                                                <span class="crusade-desc-preview-<?php echo $index; ?>"><?php 
                                                    $description = $translatedCrusade['description'];
                                                    $words = explode(' ', $description);
                                                    $preview = implode(' ', array_slice($words, 0, 15));
                                                    echo htmlspecialchars($preview . (count($words) > 15 ? '...' : ''));
                                                ?></span>
                                                <?php if (count(explode(' ', $translatedCrusade['description'])) > 15): ?>
                                                <span class="crusade-desc-full-<?php echo $index; ?> hidden"><?php echo htmlspecialchars($translatedCrusade['description']); ?></span>
                                                <button class="crusade-desc-read-more-<?php echo $index; ?> text-primary hover:text-primaryDark font-medium ml-2 underline text-xs sm:text-sm" onclick="toggleCrusadeDescription(<?php echo $index; ?>)"><?php 
                                                    if ($currentLanguage === 'en') {
                                                        echo 'Read More';
                                                    } else {
                                                        echo $translationService->translateText('Read More', 'en', $currentLanguage) ?? 'Read More';
                                                    }
                                                ?></button>
                                                <button class="crusade-desc-read-less-<?php echo $index; ?> text-primary hover:text-primaryDark font-medium ml-2 underline hidden text-xs sm:text-sm" onclick="toggleCrusadeDescription(<?php echo $index; ?>)"><?php 
                                                    if ($currentLanguage === 'en') {
                                                        echo 'Read Less';
                                                    } else {
                                                        echo $translationService->translateText('Read Less', 'en', $currentLanguage) ?? 'Read Less';
                                                    }
                                                ?></button>
                                                <?php endif; ?>
                                            </p>
                                        </div>

                                        <!-- Event Details -->
                                        <div class="space-y-2 sm:space-y-3 md:space-y-4">
                                            <div class="flex items-start">
                                                <svg class="w-4 h-4 sm:w-5 sm:h-5 text-primary mr-2 sm:mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                </svg>
                                                <div class="flex flex-col">
                                                    <span class="text-xs sm:text-sm font-medium text-gray-500"><?php echo __('event_date'); ?>:</span>
                                                    <span class="text-sm sm:text-base text-gray-900 font-medium">
                                                        <?php echo $eventDate->format('F j, Y'); ?> at <?php echo $eventDate->format('g:i A'); ?>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="flex items-start">
                                                <svg class="w-4 h-4 sm:w-5 sm:h-5 text-primary mr-2 sm:mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                </svg>
                                                <div class="flex flex-col">
                                                    <span class="text-xs sm:text-sm font-medium text-gray-500"><?php echo __('venue'); ?></span>
                                                    <span class="text-sm sm:text-base text-gray-900 font-medium">
                                                        <?php echo htmlspecialchars($translatedCrusade['venue']); ?><br>
                                                        <span class="text-xs sm:text-sm text-gray-600"><?php echo htmlspecialchars($crusade['address']); ?></span>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="pt-2">
                                            <a href="<?php echo htmlspecialchars(convertToCleanUrl($crusade['register_link'])); ?>" class="inline-flex items-center text-primary font-medium hover:text-primaryDark transition-colors duration-200 bg-blue-50 hover:bg-blue-100 px-3 py-2 rounded-lg sm:bg-transparent sm:hover:bg-transparent sm:px-0 sm:py-0 sm:rounded-none text-sm sm:text-base">
                                                <?php echo __('register_now'); ?>
                                                <svg class="ml-2 w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                                </svg>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                    <?php endforeach; ?>
                </ul>
            </div>

            <!-- Empty State Message -->
            <div id="emptyCrusadesMessage" class="hidden text-center py-16">
                <div class="max-w-md mx-auto">
                    <svg class="mx-auto h-16 w-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2" id="emptyStateTitle">
                        <?php 
                        if ($currentLanguage === 'en') {
                            echo 'No Upcoming Crusades';
                        } else {
                            echo $translationService->translateText('No Upcoming Crusades', 'en', $currentLanguage) ?? 'No Upcoming Crusades';
                        }
                        ?>
                    </h3>
                    <p class="text-gray-600" id="emptyStateDescription">
                        <?php 
                        if ($currentLanguage === 'en') {
                            echo 'There are currently no upcoming crusades scheduled. Please check back later for new events.';
                        } else {
                            echo $translationService->translateText('There are currently no upcoming crusades scheduled. Please check back later for new events.', 'en', $currentLanguage) ?? 'There are currently no upcoming crusades scheduled. Please check back later for new events.';
                        }
                        ?>
                    </p>
                </div>
            </div>

            <!-- Navigation Buttons (Desktop) -->
            <button id="prevButton" class="absolute top-2/3 sm:top-1/2 left-2 sm:-left-2 md:-left-8 lg:-left-16 transform -translate-y-1/2 w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 bg-white/90 backdrop-blur-sm border border-gray-200 text-gray-400 hover:text-primary hover:border-primary hover:bg-white transition-all duration-200 focus:outline-none z-20 shadow-lg rounded-full items-center justify-center hidden sm:flex">
                <svg class="w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <button id="nextButton" class="absolute top-2/3 sm:top-1/2 right-2 sm:-right-2 md:-right-8 lg:-right-16 transform -translate-y-1/2 w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 bg-white/90 backdrop-blur-sm border border-gray-200 text-gray-400 hover:text-primary hover:border-primary hover:bg-white transition-all duration-200 focus:outline-none z-20 shadow-lg rounded-full items-center justify-center hidden sm:flex">
                <svg class="w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </button>

            <!-- Mobile Swipe Indicator -->
            <div id="crusadeSwipeIndicator" class="flex justify-center mt-6 mb-4 sm:hidden">
                <div class="flex items-center space-x-2 px-4 py-2 bg-white/90 backdrop-blur-md rounded-full border border-gray-200 shadow-lg">
                    <div class="flex space-x-1">
                        <div class="w-1.5 h-1.5 bg-gray-400 rounded-full crusade-swipe-dot"></div>
                        <div class="w-1.5 h-1.5 bg-gray-300 rounded-full crusade-swipe-dot" style="animation-delay: 0.2s;"></div>
                        <div class="w-1.5 h-1.5 bg-gray-300 rounded-full crusade-swipe-dot" style="animation-delay: 0.4s;"></div>
                        <div class="w-1.5 h-1.5 bg-gray-300 rounded-full crusade-swipe-dot" style="animation-delay: 0.6s;"></div>
                    </div>
                    <div class="w-px h-4 bg-gray-300"></div>
                    <div class="text-gray-600 text-xs font-medium tracking-wide">SWIPE</div>
                </div>
            </div>

            <!-- View Crusade Highlights Button -->
            <div id="crusadeHighlightsButton" class="flex justify-center mt-4 mb-4" style="display: none;">
                <a href="#highlights" class="inline-flex items-center justify-center bg-accent text-gray-900 px-6 py-3 font-medium hover:bg-yellow-300 transition-colors duration-200 shadow-lg hover:shadow-xl border border-gold hover:border-yellow-300 rounded-lg text-base">
                    <?php 
                    if ($currentLanguage === 'en') {
                        echo 'View Crusade Highlights';
                    } else {
                        echo $translationService->translateText('View Crusade Highlights', 'en', $currentLanguage) ?? 'View Crusade Highlights';
                    }
                    ?>
                    <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                </a>
            </div>

            <!-- Carousel Indicators (Desktop) -->
            <div id="dotsContainer" class="justify-center mt-4 sm:mt-8 md:mt-12 lg:mt-16 space-x-2 sm:space-x-3 md:space-x-4 px-4 hidden sm:flex">
                <?php foreach ($upcomingCrusades as $index => $crusade): ?>
                <button
                    onclick="goToSlide(<?php echo $index; ?>)"
                    aria-label="Go to slide <?php echo $index + 1; ?>"
                    data-index="<?php echo $index; ?>"
                    data-type="upcoming"
                    class="carousel-dot w-2 h-2 sm:w-3 sm:h-3 bg-gray-300 hover:bg-primary focus:outline-none transition-colors duration-200 rounded-full <?php echo $index === 0 ? 'bg-primary' : ''; ?>"
                ></button>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</section>

<!-- Give Section -->
<section id="give" class="py-16 sm:py-20 md:py-24 bg-white relative overflow-hidden">
    <!-- Floating decorative empty boxes -->
    <div class="absolute inset-0 pointer-events-none">
        <!-- Top left floating box -->
        <div class="absolute top-8 sm:top-16 left-2 sm:left-8 transform -rotate-12 opacity-5 sm:opacity-10">
            <div class="bg-primary w-10 h-8 sm:w-16 sm:h-12 rounded border sm:border-2 border-primary"></div>
        </div>

        <!-- Top right floating box -->
        <div class="absolute top-20 sm:top-32 right-4 sm:right-12 transform rotate-6 opacity-5 sm:opacity-10">
            <div class="bg-accent w-12 h-9 sm:w-20 sm:h-14 rounded border sm:border-2 border-gold"></div>
        </div>

        <!-- Middle left floating box -->
        <div class="absolute top-40 sm:top-64 left-6 sm:left-16 transform rotate-3 opacity-5 sm:opacity-10">
            <div class="bg-gold w-8 h-6 sm:w-14 sm:h-10 rounded border sm:border-2 border-accent"></div>
        </div>

        <!-- Middle right floating box -->
        <div class="absolute top-32 sm:top-48 right-2 sm:right-8 transform -rotate-6 opacity-5 sm:opacity-10">
            <div class="bg-blue-400 w-10 h-8 sm:w-18 sm:h-12 rounded border sm:border-2 border-primary"></div>
        </div>

        <!-- Bottom left floating box -->
        <div class="absolute bottom-20 sm:bottom-32 left-1 sm:left-4 transform rotate-12 opacity-5 sm:opacity-10">
            <div class="bg-primary w-10 h-6 sm:w-16 sm:h-10 rounded border sm:border-2 border-primaryDark"></div>
        </div>

        <!-- Bottom right floating box -->
        <div class="absolute bottom-8 sm:bottom-16 right-6 sm:right-16 transform -rotate-3 opacity-5 sm:opacity-10">
            <div class="bg-accent w-12 h-9 sm:w-20 sm:h-14 rounded border sm:border-2 border-gold"></div>
        </div>

        <!-- Additional middle boxes for larger screens -->
        <div class="hidden lg:block absolute top-20 left-1/4 transform rotate-6 opacity-10">
            <div class="bg-blue-300 w-16 h-12 rounded-lg border-2 border-primary"></div>
        </div>

        <div class="hidden lg:block absolute bottom-24 right-1/4 transform -rotate-9 opacity-10">
            <div class="bg-gold w-14 h-10 rounded-lg border-2 border-accent"></div>
        </div>

        <!-- Mobile-only smaller decorative boxes -->
        <div class="block sm:hidden absolute top-48 left-1/2 transform -translate-x-1/2 rotate-45 opacity-5">
            <div class="bg-blue-300 w-6 h-6 rounded border border-primary"></div>
        </div>

        <div class="block sm:hidden absolute bottom-40 left-1/3 transform rotate-12 opacity-5">
            <div class="bg-gold w-7 h-5 rounded border border-accent"></div>
        </div>
    </div>

    <div class="max-w-6xl mx-auto px-4 sm:px-6 relative z-10">
        <div class="text-center mb-12 sm:mb-16 md:mb-20">
            <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4"><?php echo __('give_title'); ?></h2>
            <div class="give-subtitle-container mb-6">
                <p class="text-base sm:text-lg md:text-xl text-gray-600">
                    <?php echo __('give_subtitle'); ?>
                </p>
            </div>
            <div class="w-16 h-px bg-gradient-to-r from-primary via-accent to-gold mx-auto"></div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-12 md:gap-16 items-start lg:items-center">
            <!-- Left Content -->
            <div class="space-y-6 sm:space-y-8 order-1 lg:order-1">
                <h3 class="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900"><?php echo __('give_join_us'); ?></h3>
                <div class="give-description-container">
                <p class="text-gray-600 leading-relaxed text-sm sm:text-base">
                    <?php echo __('give_description'); ?>
                </p>
                </div>

                <div class="space-y-3 sm:space-y-4">
                    <div class="flex items-start text-gray-600">
                        <svg class="w-5 h-5 text-primary mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span class="text-sm sm:text-base"><?php echo __('give_sponsor_crusades'); ?></span>
                    </div>
                    <div class="flex items-start text-gray-600">
                        <svg class="w-5 h-5 text-primary mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span class="text-sm sm:text-base"><?php echo __('give_fund_outreach'); ?></span>
                    </div>
                    <div class="flex items-start text-gray-600">
                        <svg class="w-5 h-5 text-primary mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span class="text-sm sm:text-base"><?php echo __('give_enable_distribution'); ?></span>
                    </div>
                </div>
            </div>

            <!-- Right Payment Form -->
            <div class="bg-gray-50 p-4 sm:p-6 md:p-8 border border-gray-200 rounded-lg sm:rounded-none order-2 lg:order-2">
                <h4 class="text-lg sm:text-xl md:text-2xl font-light text-gray-900 mb-4 sm:mb-6"><?php echo __('give_select_amount'); ?></h4>

                <!-- Payment Method Selection -->
                <div class="mb-4 sm:mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">Choose Payment Method</label>
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        <label class="flex flex-col items-center p-3 sm:p-4 border border-gray-200 rounded-lg cursor-pointer hover:border-primary transition-colors duration-200 payment-method-option">
                            <input type="radio" name="payment_method" value="card_payment" checked class="sr-only">
                            <svg class="w-6 h-6 sm:w-8 sm:h-8 mb-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                            </svg>
                            <span class="text-xs sm:text-sm font-medium text-gray-900">Card Payment</span>
                        </label>
                        <label class="flex flex-col items-center p-3 sm:p-4 border border-gray-200 rounded-lg cursor-pointer hover:border-primary transition-colors duration-200 payment-method-option">
                            <input type="radio" name="payment_method" value="espees" class="sr-only">
                            <img src="https://web.espees.org/espeesCoinOption5.png" alt="ESPEES" class="w-6 h-6 sm:w-8 sm:h-8 mb-2">
                            <span class="text-xs sm:text-sm font-medium text-gray-900">ESPEES</span>
                        </label>
                    </div>
                </div>

                <!-- Card Payment Section (shown by default) -->
                <div id="card-payment-section">
                    <!-- Preset Amounts -->
                    <div class="grid grid-cols-3 gap-2 sm:gap-3 mb-4 sm:mb-6">
                        <button onclick="selectAmount(100)" class="amount-btn p-2 sm:p-3 md:p-4 border border-gray-200 text-center hover:border-primary hover:bg-primary hover:text-white transition-all duration-200 rounded-lg">
                            <div class="text-sm sm:text-base md:text-lg font-medium">$100</div>
                        </button>
                        <button onclick="selectAmount(500)" class="amount-btn p-2 sm:p-3 md:p-4 border border-gray-200 text-center hover:border-primary hover:bg-primary hover:text-white transition-all duration-200 rounded-lg">
                            <div class="text-sm sm:text-base md:text-lg font-medium">$500</div>
                        </button>
                        <button onclick="selectAmount(1000)" class="amount-btn p-2 sm:p-3 md:p-4 border border-gray-200 text-center hover:border-primary hover:bg-primary hover:text-white transition-all duration-200 rounded-lg">
                            <div class="text-sm sm:text-base md:text-lg font-medium">$1000</div>
                        </button>
                    </div>

                    <!-- Custom Amount -->
                    <div class="mb-4 sm:mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('give_custom_amount'); ?></label>
                        <div class="relative">
                            <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                            <input type="number" id="customAmount" placeholder="<?php echo __('give_enter_amount'); ?>"
                                   class="w-full pl-8 pr-4 py-2 sm:py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200 rounded-lg"
                                   min="1" step="0.01">
                        </div>
                    </div>
                </div>

                <!-- Donor Information (shown only for card payment) -->
                <div id="donor-info-section" class="space-y-3 sm:space-y-4 mb-4 sm:mb-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('give_full_name'); ?></label>
                        <input type="text" id="donorName"
                               class="w-full px-4 py-2 sm:py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200 rounded-lg"
                               placeholder="<?php echo __('give_enter_full_name'); ?>">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('give_email_address'); ?></label>
                        <input type="email" id="donorEmail"
                               class="w-full px-4 py-2 sm:py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200 rounded-lg"
                               placeholder="<?php echo __('give_enter_email'); ?>">
                    </div>
                </div>

                <!-- Card Payment Button -->
                <button id="donate-button" onclick="processDonation()"
                        class="w-full py-3 sm:py-4 bg-gradient-to-r from-primary to-primaryDark text-white font-medium hover:from-primaryDark hover:to-primary transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed border border-accent rounded-lg">
                    <?php echo __('give_donate_now'); ?>
                </button>

                <!-- ESPEES Code Display (hidden by default) -->
                <div id="espees-donation-section" class="hidden">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                        <h4 class="text-lg font-medium text-blue-900 mb-3">ESPEES Payment Code</h4>
                        <div class="bg-white border border-blue-300 rounded-lg p-4 text-center">
                            <span class="text-2xl font-bold text-blue-700 tracking-wider">RORETC</span>
                        </div>
                        <p class="text-sm text-blue-700 mt-3">
                            Use this code for your ESPEES payment. No verification required.
                        </p>
                    </div>
                </div>

                <!-- Security Notice -->
                <div id="security-notice" class="mt-4 text-center">
                    <p class="text-xs text-gray-500">
                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                        <?php echo __('give_secure_payment'); ?>
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
// Load highlights from JSON file
$highlightsJson = file_get_contents(__DIR__ . '/data/highlights.json');
$highlightsData = json_decode($highlightsJson, true);

// Check if there are any highlights to display
$hasHighlights = !empty($highlightsData['highlights']);
?>

<?php if ($hasHighlights): ?>
<!-- Highlights Section -->
<section id="highlights" class="py-16 sm:py-20 md:py-24 bg-gray-50">
    <div class="max-w-6xl mx-auto px-4 sm:px-6">
        <div class="text-center mb-12 sm:mb-16 md:mb-20">
            <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4">R.E.C.C.O.R.D.s Highlights</h2>
            <div class="highlights-subtitle-container mb-6">
                <p class="text-base sm:text-lg md:text-xl text-gray-600">
                    <span class="highlights-subtitle-preview"><?php 
                        $subtitle = __('highlights_subtitle');
                        $words = explode(' ', $subtitle);
                        $preview = implode(' ', array_slice($words, 0, 10));
                        echo $preview . (count($words) > 10 ? '...' : '');
                    ?></span>
                    <?php if (count(explode(' ', __('highlights_subtitle'))) > 10): ?>
                    <span class="highlights-subtitle-full hidden"><?php echo __('highlights_subtitle'); ?></span>
                    <button class="highlights-subtitle-read-more text-primary hover:text-primaryDark font-medium ml-2 underline" onclick="toggleHighlightsSubtitle()"><?php 
                        if ($currentLanguage === 'en') {
                            echo 'Read More';
                        } else {
                            echo $translationService->translateText('Read More', 'en', $currentLanguage) ?? 'Read More';
                        }
                    ?></button>
                    <button class="highlights-subtitle-read-less text-primary hover:text-primaryDark font-medium ml-2 underline hidden" onclick="toggleHighlightsSubtitle()"><?php 
                        if ($currentLanguage === 'en') {
                            echo 'Read Less';
                        } else {
                            echo $translationService->translateText('Read Less', 'en', $currentLanguage) ?? 'Read Less';
                        }
                    ?></button>
                    <?php endif; ?>
                </p>
            </div>
            <div class="w-16 h-px bg-gradient-to-r from-primary via-accent to-gold mx-auto"></div>
        </div>

        <div class="relative">
            <?php
            // Get translation service instance
            $translationService = TranslationService::getInstance();
            $currentLanguage = Language::getCurrentLanguage();
            ?>
            <!-- Highlights Carousel -->
            <div id="highlightsCarousel" class="overflow-hidden relative">
                <div id="highlightsSlider" class="flex transition-transform duration-500 ease-in-out">
                    <?php foreach ($highlightsData['highlights'] as $highlight): 
                        // Auto-translate content if enabled and not in English
                        $translatedTitle = $highlight['title'];
                        $translatedDescription = $highlight['description'];
                        $translatedCategory = $highlight['category'];
                        
                        if (isset($highlight['auto_translate']) && $highlight['auto_translate'] && $currentLanguage !== 'en') {
                            // Here you would implement your API translation service
                            // For now, we'll use the original content as fallback
                            $translatedTitle = $translationService->translateText($highlight['title'], 'en', $currentLanguage) ?? $highlight['title'];
                            $translatedDescription = $translationService->translateText($highlight['description'], 'en', $currentLanguage) ?? $highlight['description'];
                            $translatedCategory = $translationService->translateText($highlight['category'], 'en', $currentLanguage) ?? $highlight['category'];
                        }
                            
                        // Set badge color classes
                        $badgeColorClasses = [
                            'red' => 'bg-blue-600',
                            'green' => 'bg-green-600',
                            'blue' => 'bg-blue-600',
                            'yellow' => 'bg-accent',
                            'purple' => 'bg-purple-600'
                        ];
                        $badgeClass = isset($badgeColorClasses[$highlight['badge_color']]) ? 
                            $badgeColorClasses[$highlight['badge_color']] : 'bg-blue-600';
                            
                        // Set category color classes
                        $categoryColorClasses = [
                            'red' => 'bg-blue-100 text-blue-800',
                            'green' => 'bg-green-100 text-green-800',
                            'blue' => 'bg-blue-100 text-blue-800',
                            'yellow' => 'bg-yellow-100 text-yellow-800',
                            'purple' => 'bg-purple-100 text-purple-800'
                        ];
                        $categoryClass = isset($categoryColorClasses[$highlight['badge_color']]) ? 
                            $categoryColorClasses[$highlight['badge_color']] : 'bg-blue-100 text-blue-800';
                            
                        // Format date
                        $highlightDate = new DateTime($highlight['date']);
                    ?>
                    <div class="w-full flex-shrink-0 px-2 sm:px-4 md:px-6">
                        <div class="bg-white border border-gray-200 overflow-hidden rounded-lg sm:rounded-none shadow-sm sm:shadow-none">
                            <div class="relative flex flex-col lg:flex-row">
                                <!-- Badge -->
                                <div class="absolute top-3 sm:top-4 right-3 sm:right-4 <?php echo $badgeClass; ?> text-white text-xs font-bold px-2 sm:px-3 py-1 rounded-full z-10 shadow-md">
                                    <?php echo htmlspecialchars($highlight['badge']); ?>
                                </div>
                                
                                <!-- Image Section -->
                                <div class="w-full lg:w-1/2 relative">
                                    <div class="aspect-[16/9] sm:aspect-[4/3] lg:aspect-auto lg:h-full overflow-hidden">
                                        <?php 
                                        $hasVideos = !empty($highlight['videos']) && count($highlight['videos']) > 0;
                                        $hasImages = !empty($highlight['images']) && count($highlight['images']) > 0;
                                        $primaryImage = $hasImages ? $highlight['images'][0] : 'assets/images/default-highlight.jpg';
                                        $mediaCount = count($highlight['images'] ?? []) + count($highlight['videos'] ?? []);
                                        ?>
                                        <img class="w-full h-full object-cover" 
                                             src="<?php echo htmlspecialchars($primaryImage); ?>" 
                                             alt="<?php echo htmlspecialchars($translatedTitle); ?>">
                                    </div>
                                </div>

                                <!-- Content Section -->
                                <div class="w-full lg:w-1/2 p-4 sm:p-6 md:p-8 lg:p-12 flex flex-col justify-center">
                                    <div class="space-y-4 sm:space-y-6 lg:space-y-8">
                                        <h3 class="text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl font-bold text-gray-900 leading-tight"><?php echo htmlspecialchars($translatedTitle); ?></h3>
                                        <div class="highlight-description-container">
                                            <p class="text-base sm:text-lg text-gray-600 leading-relaxed">
                                                <span class="highlight-desc-preview-<?php echo $highlight['id']; ?>"><?php 
                                                    $words = explode(' ', $translatedDescription);
                                                    $preview = implode(' ', array_slice($words, 0, 15));
                                                    echo htmlspecialchars($preview . (count($words) > 15 ? '...' : ''));
                                                ?></span>
                                                <?php if (count(explode(' ', $translatedDescription)) > 15): ?>
                                                <span class="highlight-desc-full-<?php echo $highlight['id']; ?> hidden"><?php echo htmlspecialchars($translatedDescription); ?></span>
                                                <button class="highlight-desc-read-more-<?php echo $highlight['id']; ?> text-primary hover:text-primaryDark font-medium ml-2 underline" onclick="toggleHighlightDescription(<?php echo $highlight['id']; ?>)"><?php 
                                                    if ($currentLanguage === 'en') {
                                                        echo 'Read More';
                                                    } else {
                                                        echo $translationService->translateText('Read More', 'en', $currentLanguage) ?? 'Read More';
                                                    }
                                                ?></button>
                                                <button class="highlight-desc-read-less-<?php echo $highlight['id']; ?> text-primary hover:text-primaryDark font-medium ml-2 underline hidden" onclick="toggleHighlightDescription(<?php echo $highlight['id']; ?>)"><?php 
                                                    if ($currentLanguage === 'en') {
                                                        echo 'Read Less';
                                                    } else {
                                                        echo $translationService->translateText('Read Less', 'en', $currentLanguage) ?? 'Read Less';
                                                    }
                                                ?></button>
                                                <?php endif; ?>
                                            </p>
                                        </div>

                                        <!-- Event Details -->
                                        <div class="space-y-2 sm:space-y-3">
                                            <div class="flex items-start">
                                                <svg class="w-4 h-4 sm:w-5 sm:h-5 text-primary mr-2 sm:mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                </svg>
                                                <div class="flex flex-col min-w-0">
                                                    <span class="text-xs sm:text-sm font-medium text-gray-500"><?php 
                                                        if ($currentLanguage === 'en') {
                                                            echo 'Event Date:';
                                                        } else {
                                                            echo $translationService->translateText('Event Date:', 'en', $currentLanguage) ?? 'Event Date:';
                                                        }
                                                    ?></span>
                                                    <span class="text-sm sm:text-base text-gray-900 font-medium break-words">
                                                        <?php echo $highlightDate->format('F j, Y'); ?>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="flex items-start">
                                                <svg class="w-4 h-4 sm:w-5 sm:h-5 text-primary mr-2 sm:mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                </svg>
                                                <div class="flex flex-col min-w-0">
                                                    <span class="text-xs sm:text-sm font-medium text-gray-500"><?php 
                                                        if ($currentLanguage === 'en') {
                                                            echo 'Venue';
                                                        } else {
                                                            echo $translationService->translateText('Venue', 'en', $currentLanguage) ?? 'Venue';
                                                        }
                                                    ?></span>
                                                    <span class="text-sm sm:text-base text-gray-900 font-medium break-words">
                                                        <?php 
                                                            if ($currentLanguage === 'en') {
                                                                echo htmlspecialchars($highlight['location']);
                                                            } else {
                                                                $translatedLocation = $translationService->translateText($highlight['location'], 'en', $currentLanguage) ?? $highlight['location'];
                                                                echo htmlspecialchars($translatedLocation);
                                                            }
                                                        ?>
                                                    </span>
                                                </div>
                                            </div>
                                            <?php if ($translatedCategory): ?>
                                            <div class="flex items-start">
                                                <svg class="w-4 h-4 sm:w-5 sm:h-5 text-primary mr-2 sm:mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                                </svg>
                                                <div class="flex flex-col min-w-0">
                                                    <span class="text-xs sm:text-sm font-medium text-gray-500"><?php 
                                                        if ($currentLanguage === 'en') {
                                                            echo 'Category:';
                                                        } else {
                                                            echo $translationService->translateText('Category:', 'en', $currentLanguage) ?? 'Category:';
                                                        }
                                                    ?></span>
                                                    <span class="text-sm sm:text-base text-gray-900 font-medium break-words">
                                                        <?php echo htmlspecialchars($translatedCategory); ?>
                                                    </span>
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                            <?php if ($hasImages || $hasVideos): ?>
                                            <div class="flex items-start">
                                                <svg class="w-4 h-4 sm:w-5 sm:h-5 text-primary mr-2 sm:mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                                </svg>
                                                <div class="flex flex-col min-w-0">
                                                    <span class="text-xs sm:text-sm font-medium text-gray-500"><?php 
                                                        if ($currentLanguage === 'en') {
                                                            echo 'Media:';
                                                        } else {
                                                            echo $translationService->translateText('Media:', 'en', $currentLanguage) ?? 'Media:';
                                                        }
                                                    ?></span>
                                                    <span class="text-sm sm:text-base text-gray-900 font-medium break-words">
                                                        <?php if ($hasImages): ?>
                                                            <?php echo count($highlight['images']); ?> <?php 
                                                                $photoText = count($highlight['images']) > 1 ? 'Photos' : 'Photo';
                                                                if ($currentLanguage === 'en') {
                                                                    echo $photoText;
                                                                } else {
                                                                    echo $translationService->translateText($photoText, 'en', $currentLanguage) ?? $photoText;
                                                                }
                                                            ?>
                                                        <?php endif; ?>
                                                        <?php if ($hasImages && $hasVideos): ?>, <?php endif; ?>
                                                        <?php if ($hasVideos): ?>
                                                            <?php echo count($highlight['videos']); ?> <?php 
                                                                $videoText = count($highlight['videos']) > 1 ? 'Videos' : 'Video';
                                                                if ($currentLanguage === 'en') {
                                                                    echo $videoText;
                                                                } else {
                                                                    echo $translationService->translateText($videoText, 'en', $currentLanguage) ?? $videoText;
                                                                }
                                                            ?>
                                                        <?php endif; ?>
                                                    </span>
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                        </div>

                                        <!-- CTA Button -->
                                        <div class="pt-3 sm:pt-4">
                                            <a href="highlight-details?id=<?php echo $highlight['id']; ?>" 
                                               class="inline-flex items-center text-primary font-medium hover:text-primaryDark transition-colors duration-200 text-sm sm:text-base">
                                                <?php 
                                                    if ($currentLanguage === 'en') {
                                                        echo 'Read Full Report';
                                                    } else {
                                                        echo $translationService->translateText('Read Full Report', 'en', $currentLanguage) ?? 'Read Full Report';
                                                    }
                                                ?>
                                                <svg class="ml-2 w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                                </svg>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Navigation Buttons -->
            <button id="highlightsPrevBtn" class="absolute top-2/3 left-2 sm:top-1/2 sm:-left-4 md:-left-8 lg:-left-16 transform -translate-y-1/2 w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 bg-white bg-opacity-90 border border-gray-200 text-gray-400 hover:text-primary hover:border-primary transition-colors duration-200 focus:outline-none z-10 shadow-sm rounded-full">
                <svg class="w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <button id="highlightsNextBtn" class="absolute top-2/3 right-2 sm:top-1/2 sm:-right-4 md:-right-8 lg:-right-16 transform -translate-y-1/2 w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 bg-white bg-opacity-90 border border-gray-200 text-gray-400 hover:text-primary hover:border-primary transition-colors duration-200 focus:outline-none z-10 shadow-sm rounded-full">
                <svg class="w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </button>

            <!-- Carousel Indicators -->
            <div id="highlightsDotsContainer" class="flex justify-center mt-8 sm:mt-12 md:mt-16 space-x-2 sm:space-x-4">
                <?php foreach ($highlightsData['highlights'] as $index => $highlight): ?>
                <button
                    onclick="goToHighlightSlide(<?php echo $index; ?>)"
                    aria-label="Go to highlight slide <?php echo $index + 1; ?>"
                    data-index="<?php echo $index; ?>"
                    class="w-2 h-2 sm:w-3 sm:h-3 bg-gray-300 hover:bg-primary focus:outline-none transition-colors duration-200 rounded-full <?php echo $index === 0 ? 'bg-primary' : ''; ?>"
                ></button>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<?php
// Load media from JSON file
$mediaJsonFile = __DIR__ . '/data/media.json';
$hasMediaData = false;
$mediaData = ['media' => []];

if (file_exists($mediaJsonFile)) {
    $mediaJson = file_get_contents($mediaJsonFile);
    $mediaData = json_decode($mediaJson, true);
    $hasMediaData = !empty($mediaData['media']);
}

if ($hasMediaData) {
    // Filter to only show videos (not documents) on homepage
    $videoMedia = array_filter($mediaData['media'], function($media) {
        return (!isset($media['type']) || $media['type'] === 'video'); // Default to video for backward compatibility
    });
    
    // Sort media by featured status first, then by date_added (newest first)
    usort($videoMedia, function($a, $b) {
        // First priority: featured items
        $aFeatured = isset($a['featured']) && $a['featured'] ? 1 : 0;
        $bFeatured = isset($b['featured']) && $b['featured'] ? 1 : 0;
        
        if ($aFeatured !== $bFeatured) {
            return $bFeatured - $aFeatured; // Featured items first
        }
        
        // Second priority: date (newest first)
        return strtotime($b['date_added']) - strtotime($a['date_added']);
    });
    $recentMedia = array_slice($videoMedia, 0, 5);
?>
<!-- Media Section -->
<section id="media" class="py-16 sm:py-20 md:py-24 bg-white">
    <div class="max-w-6xl mx-auto px-4 sm:px-6">
        <div class="text-center mb-12 sm:mb-16 md:mb-20">
            <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4"><?php 
                $currentLanguage = Language::getCurrentLanguage();
                if ($currentLanguage === 'en') {
                    echo 'Media Resources';
                } else {
                    $translationService = TranslationService::getInstance();
                    echo $translationService->translateText('Media Resources', 'en', $currentLanguage) ?? 'Media Resources';
                }
            ?></h2>
            <p class="text-base sm:text-lg md:text-xl text-gray-600 mb-6"><?php 
                if ($currentLanguage === 'en') {
                    echo 'View, download and access inspiring videos and resources from our global crusades and outreach events.';
                } else {
                    echo $translationService->translateText('View, download and access inspiring videos and resources from our global crusades and outreach events.', 'en', $currentLanguage) ?? 'View, download and access inspiring videos and resources from our global crusades and outreach events.';
                }
            ?></p>
            <div class="w-16 h-px bg-gradient-to-r from-primary via-accent to-gold mx-auto"></div>
        </div>

        <div class="relative">
            <!-- Media Carousel -->
            <div id="mediaCarousel" class="overflow-hidden">
                <div id="mediaSlider" class="flex transition-transform duration-500 ease-in-out">
                    <?php foreach ($recentMedia as $media): 
                        // Auto-translate content if not in English
                        $translatedTitle = $media['title'];
                        $translatedDescription = $media['description'];
                        
                        if ($currentLanguage !== 'en') {
                            $translatedTitle = $translationService->translateText($media['title'], 'en', $currentLanguage) ?? $media['title'];
                            $translatedDescription = $translationService->translateText($media['description'], 'en', $currentLanguage) ?? $media['description'];
                        }
                    ?>
                    <div class="w-full flex-shrink-0 px-2 sm:px-4 md:px-6">
                        <div class="bg-white border border-gray-200 overflow-hidden rounded-lg sm:rounded-none shadow-sm sm:shadow-none">
                            <div class="relative flex flex-col lg:flex-row">
                                <!-- Video Section -->
                                <div class="w-full lg:w-1/2 relative">
                                    <div class="aspect-video overflow-hidden bg-gray-100 relative group cursor-pointer" onclick="openVideoModal(<?php echo htmlspecialchars(json_encode($media['video_url']), ENT_QUOTES, 'UTF-8'); ?>, <?php echo htmlspecialchars(json_encode($translatedTitle), ENT_QUOTES, 'UTF-8'); ?>)">
                                        <?php if (!empty($media['thumbnail'])): ?>
                                            <img src="<?php echo htmlspecialchars($media['thumbnail']); ?>" alt="<?php echo htmlspecialchars($translatedTitle); ?>" class="w-full h-full object-cover">
                                        <?php else: ?>
                                            <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                                                <svg class="w-12 h-12 sm:w-16 sm:h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                                </svg>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <!-- Play Button Overlay -->
                                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                                            <div class="w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 bg-primary bg-opacity-90 rounded-full flex items-center justify-center transition-all duration-300">
                                                <svg class="w-6 h-6 sm:w-7 sm:h-7 md:w-8 md:h-8 text-white ml-1" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M8 5v14l11-7z"/>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Content Section -->
                                <div class="w-full lg:w-1/2 p-4 sm:p-6 md:p-8 lg:p-12 flex flex-col justify-center">
                                    <div class="space-y-4 sm:space-y-6 lg:space-y-8">
                                        <h3 class="text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl font-bold text-gray-900 leading-tight">
                                            <?php echo htmlspecialchars($translatedTitle); ?>
                                        </h3>
                                        <div class="media-description-container">
                                        <p class="text-base sm:text-lg text-gray-600 leading-relaxed">
                                                <span class="media-desc-preview-<?php echo $media['id']; ?>"><?php 
                                                    $words = explode(' ', $translatedDescription);
                                                    $preview = implode(' ', array_slice($words, 0, 15));
                                                    echo htmlspecialchars($preview . (count($words) > 15 ? '...' : ''));
                                                ?></span>
                                                <?php if (count(explode(' ', $translatedDescription)) > 15): ?>
                                                <span class="media-desc-full-<?php echo $media['id']; ?> hidden"><?php echo htmlspecialchars($translatedDescription); ?></span>
                                                <button class="media-desc-read-more-<?php echo $media['id']; ?> text-primary hover:text-primaryDark font-medium ml-2 underline" onclick="toggleMediaDescription(<?php echo $media['id']; ?>)"><?php 
                                                    if ($currentLanguage === 'en') {
                                                        echo 'Read More';
                                                    } else {
                                                        echo $translationService->translateText('Read More', 'en', $currentLanguage) ?? 'Read More';
                                                    }
                                                ?></button>
                                                <button class="media-desc-read-less-<?php echo $media['id']; ?> text-primary hover:text-primaryDark font-medium ml-2 underline hidden" onclick="toggleMediaDescription(<?php echo $media['id']; ?>)"><?php 
                                                    if ($currentLanguage === 'en') {
                                                        echo 'Read Less';
                                                    } else {
                                                        echo $translationService->translateText('Read Less', 'en', $currentLanguage) ?? 'Read Less';
                                                    }
                                                ?></button>
                                                <?php endif; ?>
                                            </p>
                                        </div>

                                        <!-- Media Details -->
                                        <div class="space-y-2 sm:space-y-3">
                                            <div class="flex items-start">
                                                <svg class="w-4 h-4 sm:w-5 sm:h-5 text-primary mr-2 sm:mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                </svg>
                                                <div class="flex flex-col min-w-0">
                                                    <span class="text-xs sm:text-sm font-medium text-gray-500"><?php 
                                                        if ($currentLanguage === 'en') {
                                                            echo 'Date:';
                                                        } else {
                                                            echo $translationService->translateText('Date:', 'en', $currentLanguage) ?? 'Date:';
                                                        }
                                                    ?></span>
                                                    <span class="text-sm sm:text-base text-gray-900 font-medium break-words">
                                                        <?php echo date('F j, Y', strtotime($media['date_added'])); ?>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="flex items-start">
                                                <svg class="w-4 h-4 sm:w-5 sm:h-5 text-primary mr-2 sm:mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                                </svg>
                                                <div class="flex flex-col min-w-0">
                                                    <span class="text-xs sm:text-sm font-medium text-gray-500"><?php 
                                                        if ($currentLanguage === 'en') {
                                                            echo 'Category:';
                                                        } else {
                                                            echo $translationService->translateText('Category:', 'en', $currentLanguage) ?? 'Category:';
                                                        }
                                                    ?></span>
                                                    <span class="text-sm sm:text-base text-gray-900 font-medium capitalize break-words">
                                                        <?php 
                                                            if ($currentLanguage === 'en') {
                                                                echo htmlspecialchars($media['category']);
                                                            } else {
                                                                $translatedCategory = $translationService->translateText($media['category'], 'en', $currentLanguage) ?? $media['category'];
                                                                echo htmlspecialchars($translatedCategory);
                                                            }
                                                        ?>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <?php if (count($recentMedia) > 1): ?>
            <!-- Navigation Buttons -->
            <button id="mediaPrevBtn" class="absolute top-2/3 left-2 sm:top-1/2 sm:-left-4 md:-left-8 lg:-left-16 transform -translate-y-1/2 w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 bg-white bg-opacity-90 border border-gray-200 text-gray-400 hover:text-primary hover:border-primary transition-colors duration-200 focus:outline-none z-10 shadow-sm rounded-full">
                <svg class="w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <button id="mediaNextBtn" class="absolute top-2/3 right-2 sm:top-1/2 sm:-right-4 md:-right-8 lg:-right-16 transform -translate-y-1/2 w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 bg-white bg-opacity-90 border border-gray-200 text-gray-400 hover:text-primary hover:border-primary transition-colors duration-200 focus:outline-none z-10 shadow-sm rounded-full">
                <svg class="w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </button>

            <!-- Carousel Indicators -->
            <div id="mediaDotsContainer" class="flex justify-center mt-8 sm:mt-12 md:mt-16 space-x-2 sm:space-x-4">
                <?php foreach ($recentMedia as $index => $media): ?>
                <button
                    onclick="goToMediaSlide(<?php echo $index; ?>)"
                    aria-label="Go to media slide <?php echo $index + 1; ?>"
                    data-index="<?php echo $index; ?>"
                    class="w-2 h-2 sm:w-3 sm:h-3 bg-gray-300 hover:bg-primary focus:outline-none transition-colors duration-200 rounded-full <?php echo $index === 0 ? 'bg-primary' : ''; ?>"></button>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>

        <!-- View More Button -->
        <div class="text-center mt-8 sm:mt-12 md:mt-16">
            <div class="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center items-center">
                <a href="media" class="inline-flex items-center px-4 sm:px-6 py-2 sm:py-3 bg-gradient-to-r from-primary to-primaryDark text-white font-medium hover:from-primaryDark hover:to-primary transition-all duration-200 shadow-md hover:shadow-lg border border-accent rounded-lg sm:rounded-none w-full sm:w-auto justify-center">
                <?php 
                    if ($currentLanguage === 'en') {
                        echo 'View More Resources';
                    } else {
                        echo $translationService->translateText('View More Resources', 'en', $currentLanguage) ?? 'View More Resources';
                    }
                ?>
                    <svg class="ml-2 w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                </svg>
            </a>
                <a href="live" class="inline-flex items-center px-4 sm:px-6 py-2 sm:py-3 bg-gradient-to-r from-accent to-gold text-white font-medium hover:from-gold hover:to-accent transition-all duration-200 shadow-md hover:shadow-lg border border-primary rounded-lg sm:rounded-none w-full sm:w-auto justify-center">
                    <?php 
                        if ($currentLanguage === 'en') {
                            echo 'Live TV';
                        } else {
                            echo $translationService->translateText('Live TV', 'en', $currentLanguage) ?? 'Live TV';
                        }
                    ?>
                    <svg class="ml-2 w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                </a>
            </div>
        </div>

    </div>
</section>
<?php } ?>

<!-- Products Section -->
<section id="products" class="py-16 sm:py-20 md:py-24 bg-white">
    <div class="max-w-6xl mx-auto px-4 sm:px-6">
        <div class="text-center mb-12 sm:mb-16 md:mb-20">
            <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4"><?php echo __('our_products'); ?></h2>
            <div class="w-16 h-px bg-gradient-to-r from-primary via-accent to-gold mx-auto"></div>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 md:gap-8">
            <!-- Rhapsody Daily Card -->
            <div class="group bg-white border-2 border-gray-100 p-4 sm:p-6 transition-all duration-300 hover:border-primary hover:shadow-xl hover:transform hover:-translate-y-2 relative overflow-hidden rounded-lg">
                <!-- Decorative corner accent -->
                <div class="absolute top-0 right-0 w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-accent to-gold opacity-20"></div>
                <div class="aspect-[4/3] overflow-hidden mb-4 sm:mb-6 border border-gray-200 group-hover:border-accent transition-colors duration-300 rounded-lg">
                    <img class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300" src="assets/images/rhapsody.png" alt="<?php echo __('rhapsody_daily'); ?>">
                </div>
                <div class="space-y-3 sm:space-y-4 relative z-10">
                    <h3 class="text-lg sm:text-xl font-bold text-gray-900"><?php echo __('rhapsody_daily'); ?></h3>
                    <div class="rhapsody-desc-container">
                        <p class="text-gray-600 text-xs sm:text-sm leading-relaxed">
                            <span class="rhapsody-desc-preview"><?php 
                                $desc = __('rhapsody_desc');
                                $words = explode(' ', $desc);
                                $preview = implode(' ', array_slice($words, 0, 15));
                                echo $preview . (count($words) > 15 ? '...' : '');
                            ?></span>
                            <?php if (count(explode(' ', __('rhapsody_desc'))) > 15): ?>
                            <span class="rhapsody-desc-full hidden"><?php echo __('rhapsody_desc'); ?></span>
                            <button class="rhapsody-desc-read-more text-primary hover:text-primaryDark font-medium ml-2 underline" onclick="toggleRhapsodyDescription()"><?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Read More';
                                } else {
                                    echo $translationService->translateText('Read More', 'en', $currentLanguage) ?? 'Read More';
                                }
                            ?></button>
                            <button class="rhapsody-desc-read-less text-primary hover:text-primaryDark font-medium ml-2 underline hidden" onclick="toggleRhapsodyDescription()"><?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Read Less';
                                } else {
                                    echo $translationService->translateText('Read Less', 'en', $currentLanguage) ?? 'Read Less';
                                }
                            ?></button>
                            <?php endif; ?>
                        </p>
                    </div>
                    <a href="https://rhapsodyofrealities.org/" target="_blank" class="inline-flex items-center text-primary font-medium hover:text-primaryDark transition-colors duration-200 text-sm">
                        <?php echo __('learn_more_btn'); ?>
                        <svg class="ml-2 w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </a>
                </div>
            </div>

            <!-- Rhapsody TV Card -->
            <div class="group bg-white border-2 border-gray-100 p-4 sm:p-6 transition-all duration-300 hover:border-primary hover:shadow-xl hover:transform hover:-translate-y-2 relative overflow-hidden rounded-lg">
                <!-- Decorative corner accent -->
                <div class="absolute top-0 right-0 w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-primary to-accent opacity-20"></div>
                <div class="aspect-[4/3] overflow-hidden mb-4 sm:mb-6 border border-gray-200 group-hover:border-accent transition-colors duration-300 rounded-lg">
                    <img class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300" src="assets/images/rtv.jpg" alt="<?php echo __('rhapsody_tv_title'); ?>">
                </div>
                <div class="space-y-3 sm:space-y-4 relative z-10">
                    <h3 class="text-lg sm:text-xl font-bold text-gray-900"><?php echo __('rhapsody_tv_title'); ?></h3>
                    <div class="rtv-desc-container">
                        <p class="text-gray-600 text-xs sm:text-sm leading-relaxed">
                            <span class="rtv-desc-preview"><?php 
                                $desc = __('rhapsody_tv_desc');
                                $words = explode(' ', $desc);
                                $preview = implode(' ', array_slice($words, 0, 15));
                                echo $preview . (count($words) > 15 ? '...' : '');
                            ?></span>
                            <?php if (count(explode(' ', __('rhapsody_tv_desc'))) > 15): ?>
                            <span class="rtv-desc-full hidden"><?php echo __('rhapsody_tv_desc'); ?></span>
                            <button class="rtv-desc-read-more text-primary hover:text-primaryDark font-medium ml-2 underline" onclick="toggleRtvDescription()"><?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Read More';
                                } else {
                                    echo $translationService->translateText('Read More', 'en', $currentLanguage) ?? 'Read More';
                                }
                            ?></button>
                            <button class="rtv-desc-read-less text-primary hover:text-primaryDark font-medium ml-2 underline hidden" onclick="toggleRtvDescription()"><?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Read Less';
                                } else {
                                    echo $translationService->translateText('Read Less', 'en', $currentLanguage) ?? 'Read Less';
                                }
                            ?></button>
                            <?php endif; ?>
                        </p>
                    </div>
                    <a href="https://rhapsodytv.live/" target="_blank" class="inline-flex items-center text-primary font-medium hover:text-primaryDark transition-colors duration-200 text-sm">
                        <?php echo __('learn_more_btn'); ?>
                        <svg class="ml-2 w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </a>
                </div>
            </div>

            <!-- Reach Out World Card -->
            <div class="group bg-white border-2 border-gray-100 p-4 sm:p-6 transition-all duration-300 hover:border-primary hover:shadow-xl hover:transform hover:-translate-y-2 relative overflow-hidden rounded-lg">
                <!-- Decorative corner accent -->
                <div class="absolute top-0 right-0 w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-gold to-accent opacity-20"></div>
                <div class="aspect-[4/3] overflow-hidden mb-4 sm:mb-6 border border-gray-200 group-hover:border-accent transition-colors duration-300 rounded-lg">
                    <img class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300" src="assets/images/reachout.jpg" alt="<?php echo __('reachout_title'); ?>">
                </div>
                <div class="space-y-3 sm:space-y-4 relative z-10">
                    <h3 class="text-lg sm:text-xl font-bold text-gray-900"><?php echo __('reachout_title'); ?></h3>
                    <div class="reachout-desc-container">
                        <p class="text-gray-600 text-xs sm:text-sm leading-relaxed">
                            <span class="reachout-desc-preview"><?php 
                                $desc = __('reachout_desc');
                                $words = explode(' ', $desc);
                                $preview = implode(' ', array_slice($words, 0, 15));
                                echo $preview . (count($words) > 15 ? '...' : '');
                            ?></span>
                            <?php if (count(explode(' ', __('reachout_desc'))) > 15): ?>
                            <span class="reachout-desc-full hidden"><?php echo __('reachout_desc'); ?></span>
                            <button class="reachout-desc-read-more text-primary hover:text-primaryDark font-medium ml-2 underline" onclick="toggleReachoutDescription()"><?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Read More';
                                } else {
                                    echo $translationService->translateText('Read More', 'en', $currentLanguage) ?? 'Read More';
                                }
                            ?></button>
                            <button class="reachout-desc-read-less text-primary hover:text-primaryDark font-medium ml-2 underline hidden" onclick="toggleReachoutDescription()"><?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Read Less';
                                } else {
                                    echo $translationService->translateText('Read Less', 'en', $currentLanguage) ?? 'Read Less';
                                }
                            ?></button>
                            <?php endif; ?>
                        </p>
                    </div>
                    <a href="https://reachoutworld.org/" target="_blank" class="inline-flex items-center text-primary font-medium hover:text-primaryDark transition-colors duration-200 text-sm">
                        <?php echo __('learn_more_btn'); ?>
                        <svg class="ml-2 w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </a>
                </div>
            </div>

            <!-- Tap2Read Card -->
            <div class="group bg-white border-2 border-gray-100 p-4 sm:p-6 transition-all duration-300 hover:border-primary hover:shadow-xl hover:transform hover:-translate-y-2 relative overflow-hidden rounded-lg">
                <!-- Decorative corner accent -->
                <div class="absolute top-0 right-0 w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-accent to-primary opacity-20"></div>
                <div class="aspect-[4/3] overflow-hidden mb-4 sm:mb-6 border border-gray-200 group-hover:border-accent transition-colors duration-300 rounded-lg">
                    <img class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300" src="https://rhapsodyofrealities.b-cdn.net/rhapsodyofrealities.org/card_images/2024/TAP2READAPP%20(1).jpg" alt="<?php echo __('tap2read_title'); ?>">
                </div>
                <div class="space-y-3 sm:space-y-4 relative z-10">
                    <h3 class="text-lg sm:text-xl font-bold text-gray-900"><?php echo __('tap2read_title'); ?></h3>
                    <div class="tap2read-desc-container">
                        <p class="text-gray-600 text-xs sm:text-sm leading-relaxed">
                            <span class="tap2read-desc-preview"><?php 
                                $desc = __('tap2read_desc');
                                $words = explode(' ', $desc);
                                $preview = implode(' ', array_slice($words, 0, 15));
                                echo $preview . (count($words) > 15 ? '...' : '');
                            ?></span>
                            <?php if (count(explode(' ', __('tap2read_desc'))) > 15): ?>
                            <span class="tap2read-desc-full hidden"><?php echo __('tap2read_desc'); ?></span>
                            <button class="tap2read-desc-read-more text-primary hover:text-primaryDark font-medium ml-2 underline" onclick="toggleTap2readDescription()"><?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Read More';
                                } else {
                                    echo $translationService->translateText('Read More', 'en', $currentLanguage) ?? 'Read More';
                                }
                            ?></button>
                            <button class="tap2read-desc-read-less text-primary hover:text-primaryDark font-medium ml-2 underline hidden" onclick="toggleTap2readDescription()"><?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Read Less';
                                } else {
                                    echo $translationService->translateText('Read Less', 'en', $currentLanguage) ?? 'Read Less';
                                }
                            ?></button>
                            <?php endif; ?>
                        </p>
                    </div>
                    <a href="https://shop.rhapsodytap2read.com/" target="_blank" class="inline-flex items-center text-primary font-medium hover:text-primaryDark transition-colors duration-200 text-sm">
                        <?php echo __('order_now'); ?>
                        <svg class="ml-2 w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- App Download Section -->
<section class="py-16 sm:py-20 md:py-24 bg-gray-50 relative overflow-hidden">
    <!-- Background decorative elements -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute top-10 left-10 w-20 h-20 sm:w-32 sm:h-32 bg-primary transform rotate-45"></div>
        <div class="absolute bottom-20 right-20 w-16 h-16 sm:w-24 sm:h-24 bg-accent transform rotate-12"></div>
        <div class="absolute top-1/2 left-1/4 w-12 h-12 sm:w-16 sm:h-16 bg-gold transform -rotate-45"></div>
    </div>
    
    <div class="max-w-6xl mx-auto px-4 sm:px-6 relative z-10">
        <!-- Embellished container -->
        <div class="bg-white border-2 sm:border-4 border-gray-100 hover:border-primary transition-colors duration-500 shadow-xl sm:shadow-2xl relative overflow-hidden rounded-lg sm:rounded-none">
            <!-- Top decorative border -->
            <div class="absolute top-0 left-0 right-0 h-1 sm:h-2 bg-gradient-to-r from-primary via-accent to-gold z-20"></div>
            <!-- Bottom decorative border -->
            <div class="absolute bottom-0 left-0 right-0 h-1 sm:h-2 bg-gradient-to-r from-gold via-accent to-primary z-20"></div>
            <!-- Side accents -->
            <div class="absolute left-0 top-1 sm:top-2 bottom-1 sm:bottom-2 w-0.5 sm:w-1 bg-gradient-to-b from-primary to-accent z-20"></div>
            <div class="absolute right-0 top-1 sm:top-2 bottom-1 sm:bottom-2 w-0.5 sm:w-1 bg-gradient-to-b from-accent to-primary z-20"></div>
            
            <!-- Corner decorations -->
            <div class="absolute top-3 sm:top-6 left-3 sm:left-6 w-4 h-4 sm:w-6 sm:h-6 border-l-2 border-t-2 border-accent z-20"></div>
            <div class="absolute top-3 sm:top-6 right-3 sm:right-6 w-4 h-4 sm:w-6 sm:h-6 border-r-2 border-t-2 border-gold z-20"></div>
            <div class="absolute bottom-3 sm:bottom-6 left-3 sm:left-6 w-4 h-4 sm:w-6 sm:h-6 border-l-2 border-b-2 border-gold z-20"></div>
            <div class="absolute bottom-3 sm:bottom-6 right-3 sm:right-6 w-4 h-4 sm:w-6 sm:h-6 border-r-2 border-b-2 border-accent z-20"></div>
            
            <!-- Content with proper padding -->
            <div class="p-6 sm:p-8 md:p-12 relative z-10">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-12 md:gap-16 items-center">
                <!-- Image on left -->
                <div class="order-2 lg:order-1 hidden lg:block">
                    <div class="aspect-[4/3] overflow-hidden border border-gray-200 sm:border-2 hover:border-accent transition-colors duration-300 relative rounded-lg">
                        <img class="w-full h-full object-cover hover:scale-105 transition-transform duration-500" src="https://lwappstore.com/developers/uploads/1604670119.jpg" alt="Download Rhapsody of Realities App">
                        <!-- Image overlay accent -->
                        <div class="absolute inset-0 bg-gradient-to-tr from-primary via-transparent to-accent opacity-10"></div>
                    </div>
                </div>

                <!-- Text on right -->
                <div class="order-1 lg:order-2 space-y-6 sm:space-y-8">
                    <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 relative">
                        <?php echo __('download_app_title'); ?>
                        <div class="absolute -bottom-1 sm:-bottom-2 left-0 w-16 sm:w-20 h-0.5 sm:h-1 bg-gradient-to-r from-primary to-accent"></div>
                    </h2>
                    <div class="download-app-desc-container">
                        <p class="text-gray-600 leading-relaxed text-sm sm:text-base md:text-lg">
                            <span class="download-app-desc-preview"><?php 
                                $desc = __('download_app_desc');
                                $words = explode(' ', $desc);
                                $preview = implode(' ', array_slice($words, 0, 20));
                                echo $preview . (count($words) > 20 ? '...' : '');
                            ?></span>
                            <?php if (count(explode(' ', __('download_app_desc'))) > 20): ?>
                            <span class="download-app-desc-full hidden"><?php echo __('download_app_desc'); ?></span>
                            <button class="download-app-desc-read-more text-primary hover:text-primaryDark font-medium ml-2 underline" onclick="toggleDownloadAppDescription()"><?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Read More';
                                } else {
                                    echo $translationService->translateText('Read More', 'en', $currentLanguage) ?? 'Read More';
                                }
                            ?></button>
                            <button class="download-app-desc-read-less text-primary hover:text-primaryDark font-medium ml-2 underline hidden" onclick="toggleDownloadAppDescription()"><?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Read Less';
                                } else {
                                    echo $translationService->translateText('Read Less', 'en', $currentLanguage) ?? 'Read Less';
                                }
                            ?></button>
                            <?php endif; ?>
                        </p>
                    </div>
                <div>
                    <button
                        onclick="openDownloadModal()"
                            class="inline-flex items-center bg-gradient-to-r from-primary to-primaryDark text-white px-6 sm:px-8 py-3 sm:py-4 font-medium hover:from-primaryDark hover:to-primary transition-all duration-200 shadow-lg hover:shadow-xl border border-accent sm:border-2 hover:border-gold rounded-lg sm:rounded-none w-full sm:w-auto justify-center"
                    >
                        <?php echo __('download_app_btn'); ?>
                            <svg class="ml-2 w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </button>
                </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Download Modal -->
<div id="downloadModal" class="fixed inset-0 z-50 hidden items-center justify-center bg-black bg-opacity-50 p-4 sm:p-6">
    <div class="bg-white max-w-lg w-full max-h-[90vh] overflow-y-auto border-4 border-gray-100 hover:border-primary transition-colors duration-500 shadow-2xl relative overflow-hidden rounded-lg sm:rounded-none">
        <!-- Top decorative border -->
        <div class="absolute top-0 left-0 right-0 h-1 sm:h-2 bg-gradient-to-r from-primary via-accent to-gold z-20"></div>
        <!-- Bottom decorative border -->
        <div class="absolute bottom-0 left-0 right-0 h-1 sm:h-2 bg-gradient-to-r from-gold via-accent to-primary z-20"></div>
        <!-- Side accents -->
        <div class="absolute left-0 top-1 sm:top-2 bottom-1 sm:bottom-2 w-0.5 sm:w-1 bg-gradient-to-b from-primary to-accent z-20"></div>
        <div class="absolute right-0 top-1 sm:top-2 bottom-1 sm:bottom-2 w-0.5 sm:w-1 bg-gradient-to-b from-accent to-primary z-20"></div>
        
        <!-- Corner decorations -->
        <div class="absolute top-3 sm:top-6 left-3 sm:left-6 w-4 h-4 sm:w-6 sm:h-6 border-l-2 border-t-2 border-accent z-20"></div>
        <div class="absolute top-3 sm:top-6 right-3 sm:right-6 w-4 h-4 sm:w-6 sm:h-6 border-r-2 border-t-2 border-gold z-20"></div>
        <div class="absolute bottom-3 sm:bottom-6 left-3 sm:left-6 w-4 h-4 sm:w-6 sm:h-6 border-l-2 border-b-2 border-gold z-20"></div>
        <div class="absolute bottom-3 sm:bottom-6 right-3 sm:right-6 w-4 h-4 sm:w-6 sm:h-6 border-r-2 border-b-2 border-accent z-20"></div>
        
        <div class="p-4 sm:p-6 md:p-8 lg:p-12 relative z-10">
            <!-- Header -->
            <div class="flex justify-between items-start mb-4 sm:mb-6 md:mb-8">
                <h3 class="text-lg sm:text-xl md:text-2xl font-bold text-gray-900 relative flex-1 pr-4">
                    <?php echo __('download_app_modal_title'); ?>
                    <div class="absolute -bottom-1 left-0 w-12 sm:w-16 h-0.5 bg-gradient-to-r from-primary to-accent"></div>
                </h3>
                <button onclick="closeDownloadModal()" class="text-gray-400 hover:text-primary focus:outline-none transition-colors duration-200 p-1 sm:p-2 hover:bg-blue-50 rounded-lg flex-shrink-0">
                    <svg class="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Download Options -->
            <div class="space-y-3 sm:space-y-4 md:space-y-6">
                <!-- LoveWorld App Store -->
                <div class="group">
                    <a href="https://web.lwappstore.com/share/appId-32181354074e5cf63319371178894acd" target="_blank"
                       class="block p-3 sm:p-4 md:p-6 border-2 border-gray-200 hover:border-primary transition-all duration-200 hover:shadow-lg hover:transform hover:-translate-y-1 relative overflow-hidden rounded-lg">
                        <!-- Decorative accent -->
                        <div class="absolute top-0 right-0 w-8 h-8 sm:w-12 sm:h-12 bg-gradient-to-br from-primary to-accent opacity-10"></div>
                        <div class="flex items-center justify-between relative z-10">
                            <div class="flex-1">
                                <h4 class="text-base sm:text-lg font-bold text-gray-900 mb-1 sm:mb-2"><?php echo __('download_loveworld'); ?></h4>
                                <p class="text-xs sm:text-sm text-gray-600">Official LoveWorld App Store</p>
                            </div>
                            <svg class="w-4 h-4 sm:w-5 sm:h-5 text-gray-400 group-hover:text-primary transition-colors duration-200 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                            </svg>
                        </div>
                    </a>
                </div>

                <!-- Google Play Store -->
                <div class="group">
                    <a href="https://play.google.com/store/apps/details?id=com.rhapsodyreader&pli=1" target="_blank"
                       class="block p-3 sm:p-4 md:p-6 border-2 border-gray-200 hover:border-primary transition-all duration-200 hover:shadow-lg hover:transform hover:-translate-y-1 relative overflow-hidden rounded-lg">
                        <!-- Decorative accent -->
                        <div class="absolute top-0 right-0 w-8 h-8 sm:w-12 sm:h-12 bg-gradient-to-br from-accent to-gold opacity-10"></div>
                        <div class="flex items-center justify-between relative z-10">
                            <div class="flex-1">
                                <h4 class="text-base sm:text-lg font-bold text-gray-900 mb-1 sm:mb-2"><?php echo __('download_playstore'); ?></h4>
                                <p class="text-xs sm:text-sm text-gray-600">Available on Google Play</p>
                            </div>
                            <svg class="w-4 h-4 sm:w-5 sm:h-5 text-gray-400 group-hover:text-primary transition-colors duration-200 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                            </svg>
                        </div>
                    </a>
                </div>

                <!-- App Store (if available) -->
                <div class="group">
                    <a href="https://apps.apple.com/app/rhapsody-of-realities/id1114966583" target="_blank"
                       class="block p-3 sm:p-4 md:p-6 border-2 border-gray-200 hover:border-primary transition-all duration-200 hover:shadow-lg hover:transform hover:-translate-y-1 relative overflow-hidden rounded-lg">
                        <!-- Decorative accent -->
                        <div class="absolute top-0 right-0 w-8 h-8 sm:w-12 sm:h-12 bg-gradient-to-br from-gold to-primary opacity-10"></div>
                        <div class="flex items-center justify-between relative z-10">
                            <div class="flex-1">
                                <h4 class="text-base sm:text-lg font-bold text-gray-900 mb-1 sm:mb-2">Download from App Store</h4>
                                <p class="text-xs sm:text-sm text-gray-600">Available on iOS App Store</p>
                            </div>
                            <svg class="w-4 h-4 sm:w-5 sm:h-5 text-gray-400 group-hover:text-primary transition-colors duration-200 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                            </svg>
                        </div>
                    </a>
                </div>
            </div>

            <!-- Footer Note -->
            <div class="mt-4 sm:mt-6 md:mt-8 pt-3 sm:pt-4 md:pt-6 border-t border-gray-100">
                <p class="text-xs sm:text-sm text-gray-500 text-center">Choose your preferred platform to download the app</p>
            </div>
        </div>
    </div>
</div>

<style>
/* Custom Animations for Tailwind */
@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-left {
    animation: fadeInLeft 0.8s ease-out forwards;
}

.animate-fade-in-right {
    animation: fadeInRight 0.8s ease-out forwards;
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
}

/* Default state for scroll animations */
.scroll-element, .scroll-element-left, .scroll-element-right, .scroll-card {
    opacity: 0;
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.scroll-element.visible {
    opacity: 1;
    transform: translateY(0);
}

.scroll-element-left.visible {
    opacity: 1;
    transform: translateX(0);
}

.scroll-element-right.visible {
    opacity: 1;
    transform: translateX(0);
}

.scroll-card.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Initial positions */
.scroll-element {
    transform: translateY(30px);
}

.scroll-element-left {
    transform: translateX(-30px);
}

.scroll-element-right {
    transform: translateX(30px);
}

.scroll-card {
    transform: translateY(30px);
}
</style>

<script>
function openDownloadModal() {
    const modal = document.getElementById('downloadModal');
    
    if (!modal) {
        console.error('Download modal not found');
        return;
    }
    
    // Remove the hidden class and add flex class
    modal.classList.remove('hidden');
    modal.classList.add('flex');
    
    document.body.style.overflow = 'hidden';
}

function closeDownloadModal() {
    const modal = document.getElementById('downloadModal');
    
    if (!modal) {
        console.error('Download modal not found');
        return;
    }
    
    // Add hidden class back and remove flex class
    modal.classList.add('hidden');
    modal.classList.remove('flex');
    
    document.body.style.overflow = 'auto';
}

// Close modal when clicking outside
document.getElementById('downloadModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDownloadModal();
    }
});

// Scroll animation function
function initScrollAnimations() {
    const scrollElements = document.querySelectorAll('.scroll-element, .scroll-element-left, .scroll-element-right');
    const scrollCards = document.querySelectorAll('.scroll-card');

    // Check if element is in viewport
    function isElementInViewport(el) {
        const rect = el.getBoundingClientRect();
        return (
            rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.8
        );
    }

    // Handle scroll animation
    function handleScrollAnimation() {
        // Animate sections
        scrollElements.forEach(element => {
            if (isElementInViewport(element) && !element.classList.contains('visible')) {
                element.classList.add('visible');
            }
        });

        // Animate cards with delay
        scrollCards.forEach(card => {
            if (isElementInViewport(card) && !card.classList.contains('visible')) {
                const delay = card.getAttribute('data-delay') || 0;
                setTimeout(() => {
                    card.classList.add('visible');
                }, delay);
            }
        });
    }

    // Run on scroll
    window.addEventListener('scroll', handleScrollAnimation);

    // Run on load
    handleScrollAnimation();
}

// Smooth scroll for anchors
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced smooth scroll functionality for all navigation buttons
    function setupSmoothScroll() {
        // Get header height for offset calculation
        const getHeaderHeight = () => {
            const header = document.querySelector('header');
            return header ? header.offsetHeight : 0;
        };

        // Generic smooth scroll function
        function smoothScrollTo(targetId, offset = 80) {
            const targetSection = document.getElementById(targetId);
            if (targetSection) {
                const headerHeight = getHeaderHeight();
                const targetPosition = targetSection.getBoundingClientRect().top + window.pageYOffset - headerHeight - offset;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        }

        // Setup View Events button (targets #about section)
        const viewEventsBtn = document.querySelector('a[href="#about"]');
        if (viewEventsBtn) {
            viewEventsBtn.addEventListener('click', function(e) {
                e.preventDefault();
                smoothScrollTo('about', 60); // Extra offset for better viewing
            });
        }

        // Setup View Highlights button (targets #highlights section)
        const viewHighlightsBtn = document.querySelector('a[href="#highlights"]');
        if (viewHighlightsBtn) {
            viewHighlightsBtn.addEventListener('click', function(e) {
                e.preventDefault();
                smoothScrollTo('highlights', 60); // Extra offset for better viewing
            });
        }

        // Setup any other navigation links with hash targets
        const allHashLinks = document.querySelectorAll('a[href^="#"]');
        allHashLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && href !== '#' && !link.dataset.smoothScrollHandled) {
                link.dataset.smoothScrollHandled = 'true';
                
                link.addEventListener('click', function(e) {
                    const targetId = href.substring(1);
                    const targetElement = document.getElementById(targetId);
                    
                    if (targetElement) {
                        e.preventDefault();
                        smoothScrollTo(targetId, 60);
                    }
                });
            }
        });
    }

    // Initialize smooth scroll
    setupSmoothScroll();
    
    // Re-setup on dynamic content changes (if needed)
    window.setupSmoothScroll = setupSmoothScroll;
});

// Seamless Loop Carousel for Crusades
let crusadesIteration = 0;
let crusadesSeamlessLoop, crusadesPlayhead, crusadesScrub;
let currentCrusadeIndex = 0;

function initCrusadesSeamlessLoop() {
    const spacing = 1; // spacing of the cards (1 = one card per step)
    const snapTime = gsap.utils.snap(spacing);
    const cards = gsap.utils.toArray('.crusade-card');
    
    if (cards.length === 0) return; // Exit if no cards found

    // Set initial state of items
    gsap.set('.crusade-card', {xPercent: 400, opacity: 0, scale: 0});

    // Enhanced animation function to handle dynamic heights
    const animateFunc = element => {
        const tl = gsap.timeline();
        
        // Get dynamic height of element
        const elementHeight = element.scrollHeight;
        
        tl.fromTo(element, 
            {scale: 0, opacity: 0, height: 0}, 
            {
                scale: 1, 
                opacity: 1, 
                height: 'auto',
                zIndex: 100, 
                duration: 0.5, 
                yoyo: true, 
                repeat: 1, 
                ease: "power1.in", 
                immediateRender: false,
                onComplete: () => updateCarouselHeight()
            }
        )
        .fromTo(element, 
            {xPercent: 400}, 
            {xPercent: -400, duration: 1, ease: "none", immediateRender: false}, 0
        );
        return tl;
    };

    crusadesSeamlessLoop = buildSeamlessLoop(cards, spacing, animateFunc);
    crusadesPlayhead = {offset: 0};
    const wrapTime = gsap.utils.wrap(0, crusadesSeamlessLoop.duration());

    crusadesScrub = gsap.to(crusadesPlayhead, {
        offset: 0,
        onUpdate() {
            crusadesSeamlessLoop.time(wrapTime(crusadesPlayhead.offset));
        },
        duration: 0.5,
        ease: "power3",
        paused: true
    });

    // Function to scroll to specific offset
    function scrollToOffset(offset) {
        let snappedTime = snapTime(offset);
        let progress = (snappedTime - crusadesSeamlessLoop.duration() * crusadesIteration) / crusadesSeamlessLoop.duration();
        
        if (progress >= 1 || progress < 0) {
            crusadesIteration += Math.floor(progress);
        }
        
        crusadesScrub.vars.offset = offset;
        crusadesScrub.invalidate().restart();
        
        // Update current index and dots
        currentCrusadeIndex = Math.round(offset / spacing) % cards.length;
        if (currentCrusadeIndex < 0) currentCrusadeIndex = cards.length + currentCrusadeIndex;
        updateCrusadeDots();
        
        // Update carousel height for new active card
        setTimeout(() => updateCarouselHeight(), 100);
    }

    // Set up navigation buttons
    const prevButton = document.getElementById('prevButton');
    const nextButton = document.getElementById('nextButton');

    if (prevButton && nextButton) {
        prevButton.addEventListener('click', () => scrollToOffset(crusadesScrub.vars.offset - spacing));
        nextButton.addEventListener('click', () => scrollToOffset(crusadesScrub.vars.offset + spacing));
    }

    // Set up dot navigation
    const dots = document.querySelectorAll('#dotsContainer button');
    dots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
            const targetOffset = index * spacing;
            scrollToOffset(targetOffset);
        });
    });

    // Set up touch/swipe functionality for mobile
    let touchStartX = 0;
    let touchStartY = 0;
    let touchEndX = 0;
    let touchEndY = 0;
    let isSwiping = false;
    
    const crusadesContainer = document.getElementById('crusadesCarousel');
    
    if (crusadesContainer) {
        // Touch start
        crusadesContainer.addEventListener('touchstart', (e) => {
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
            isSwiping = false;
        }, { passive: true });
        
        // Touch move - detect if user is swiping horizontally
        crusadesContainer.addEventListener('touchmove', (e) => {
            if (!touchStartX) return;
            
            const touchCurrentX = e.touches[0].clientX;
            const touchCurrentY = e.touches[0].clientY;
            
            const deltaX = Math.abs(touchCurrentX - touchStartX);
            const deltaY = Math.abs(touchCurrentY - touchStartY);
            
            // If horizontal swipe is more prominent than vertical, prevent default scrolling
            if (deltaX > deltaY && deltaX > 10) {
                e.preventDefault();
                isSwiping = true;
            }
        }, { passive: false });
        
        // Touch end - handle swipe
        crusadesContainer.addEventListener('touchend', (e) => {
            if (!touchStartX || !isSwiping) return;
            
            touchEndX = e.changedTouches[0].clientX;
            touchEndY = e.changedTouches[0].clientY;
            
            const deltaX = touchEndX - touchStartX;
            const deltaY = touchEndY - touchStartY;
            
            // Minimum swipe distance
            const minSwipeDistance = 50;
            
            // Check if it's a horizontal swipe
            if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > minSwipeDistance) {
                if (deltaX > 0) {
                    // Swipe right - go to previous slide
                    scrollToOffset(crusadesScrub.vars.offset - spacing);
                } else {
                    // Swipe left - go to next slide
                    scrollToOffset(crusadesScrub.vars.offset + spacing);
                }
            }
            
            // Reset values
            touchStartX = 0;
            touchStartY = 0;
            touchEndX = 0;
            touchEndY = 0;
            isSwiping = false;
        }, { passive: true });
    }

    // Initialize first card
    scrollToOffset(0);
}

function buildSeamlessLoop(items, spacing, animateFunc) {
    let rawSequence = gsap.timeline({paused: true});
    let seamlessLoop = gsap.timeline({
        paused: true,
        repeat: -1,
        onRepeat() {
            this._time === this._dur && (this._tTime += this._dur - 0.01);
        },
        onReverseComplete() {
            this.totalTime(this.rawTime() + this.duration() * 100);
        }
    });
    
    let cycleDuration = spacing * items.length;
    let dur;

    // Loop through 3 times for seamless effect
    items.concat(items).concat(items).forEach((item, i) => {
        let anim = animateFunc(items[i % items.length]);
        rawSequence.add(anim, i * spacing);
        dur || (dur = anim.duration());
    });

    seamlessLoop.fromTo(rawSequence, {
        time: cycleDuration + dur / 2
    }, {
        time: "+=" + cycleDuration,
        duration: cycleDuration,
        ease: "none"
    });
    
    return seamlessLoop;
}

function updateCrusadeDots() {
    const dots = document.querySelectorAll('#dotsContainer button');
    dots.forEach((dot, i) => {
        if (i === currentCrusadeIndex) {
            dot.classList.add('bg-primary');
            dot.classList.remove('bg-gray-300');
        } else {
            dot.classList.remove('bg-primary');
            dot.classList.add('bg-gray-300');
        }
    });
}

// Function to update carousel container height based on active card
function updateCarouselHeight() {
    const activeCard = document.querySelector('.crusade-card[data-index="' + currentCrusadeIndex + '"]');
    const carousel = document.getElementById('crusadesCarousel');
    
    if (activeCard && carousel) {
        const cardHeight = activeCard.scrollHeight;
        const minHeight = window.innerWidth <= 768 ? 400 : 500;
        const targetHeight = Math.max(cardHeight + 40, minHeight); // Add padding
        
        gsap.to(carousel, {
            height: targetHeight,
            duration: 0.3,
            ease: "power2.out"
        });
    }
}

// Enhanced error handling for GSAP animations
function safeAnimateCard(element) {
    try {
        // Check if element exists and has content
        if (!element || element.scrollHeight === 0) {
            console.warn('Invalid card element for animation');
            return gsap.timeline();
        }
        
        return animateFunc(element);
    } catch (error) {
        console.error('Card animation error:', error);
        // Return basic timeline as fallback
        return gsap.timeline().set(element, {opacity: 1, scale: 1});
    }
}

// Legacy function for dot clicks (maintains compatibility)
function goToSlide(index) {
    if (crusadesScrub) {
        const spacing = 1;
        const targetOffset = index * spacing;
        crusadesScrub.vars.offset = targetOffset;
        crusadesScrub.invalidate().restart();
        currentCrusadeIndex = index;
        updateCrusadeDots();
    }
}


// Hero Carousel functionality
function initHeroCarousel() {
    const heroSlidesContainer = document.querySelector('.hero-slides-container');
    const heroIndicators = document.querySelectorAll('.hero-indicator');
    const heroPrevBtn = document.getElementById('heroPrevBtn');
    const heroNextBtn = document.getElementById('heroNextBtn');
    
    let currentHeroSlide = 0;
    const totalSlides = 2; // Only count the actual slides (not clones)
    let heroInterval;
    let isTransitioning = false;
    
    function showHeroSlide(index, instantJump = false) {
        if (isTransitioning && !instantJump) return;
        
        if (!instantJump) {
            isTransitioning = true;
        }
        
        // Calculate transform percentage for the container
        const translateX = -25 * index; // Each slide is 25% width
        
        if (instantJump) {
            heroSlidesContainer.style.transition = 'none';
            heroSlidesContainer.style.transform = `translateX(${translateX}%)`;
            // Force reflow
            heroSlidesContainer.offsetHeight;
            heroSlidesContainer.style.transition = 'transform 0.7s ease-in-out';
        } else {
            heroSlidesContainer.style.transform = `translateX(${translateX}%)`;
        }
        
        // Update indicators (only for actual slides 0 and 1)
        const realIndex = index >= 2 ? index - 2 : index;
        heroIndicators.forEach((indicator, i) => {
            if (i === realIndex) {
                indicator.classList.add('active');
                indicator.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
            } else {
                indicator.classList.remove('active');
                indicator.style.backgroundColor = 'rgba(255, 255, 255, 0.5)';
            }
        });
        
        currentHeroSlide = index;
        
        // Handle seamless loop transitions
        if (!instantJump) {
            setTimeout(() => {
                if (index >= 2) {
                    // We're at clone slides, jump back to original
                    showHeroSlide(index - 2, true);
                    currentHeroSlide = index - 2;
                }
                isTransitioning = false;
            }, 700);
        }
    }
    
    function nextHeroSlide() {
        if (isTransitioning) return;
        const nextIndex = currentHeroSlide + 1;
        showHeroSlide(nextIndex);
    }
    
    function prevHeroSlide() {
        if (isTransitioning) return;
        let prevIndex = currentHeroSlide - 1;
        
        // If going backwards from slide 0, jump to clone of slide 1 first
        if (prevIndex < 0) {
            showHeroSlide(3, true); // Jump to last clone instantly
            setTimeout(() => {
                showHeroSlide(1); // Then animate to slide 1
            }, 20);
            return;
        }
        
        showHeroSlide(prevIndex);
    }
    
    function startHeroAutoPlay() {
        stopHeroAutoPlay(); // Always clear any existing interval first
        heroInterval = setInterval(nextHeroSlide, 13000); // Change slide every 13 seconds
    }
    
    function stopHeroAutoPlay() {
        if (heroInterval) {
            clearInterval(heroInterval);
            heroInterval = null;
        }
    }
    
    function restartAutoPlay() {
        stopHeroAutoPlay();
        setTimeout(() => {
            startHeroAutoPlay();
        }, 100); // Small delay to ensure clean restart
    }
    
    // Event listeners with debouncing
    let buttonClickTimeout;
    
    heroNextBtn.addEventListener('click', (e) => {
        e.preventDefault();
        if (buttonClickTimeout) return;
        
        buttonClickTimeout = setTimeout(() => {
            buttonClickTimeout = null;
        }, 700);
        
        stopHeroAutoPlay();
        nextHeroSlide();
        restartAutoPlay();
    });
    
    heroPrevBtn.addEventListener('click', (e) => {
        e.preventDefault();
        if (buttonClickTimeout) return;
        
        buttonClickTimeout = setTimeout(() => {
            buttonClickTimeout = null;
        }, 700);
        
        stopHeroAutoPlay();
        prevHeroSlide();
        restartAutoPlay();
    });
    
    // Indicator clicks with debouncing
    heroIndicators.forEach((indicator, index) => {
        indicator.addEventListener('click', (e) => {
            e.preventDefault();
            const currentRealIndex = currentHeroSlide >= 2 ? currentHeroSlide - 2 : currentHeroSlide;
            if (buttonClickTimeout || index === currentRealIndex) return;
            
            buttonClickTimeout = setTimeout(() => {
                buttonClickTimeout = null;
            }, 700);
            
            stopHeroAutoPlay();
            showHeroSlide(index);
            restartAutoPlay();
        });
    });
    
    // Pause autoplay on hover
    const heroCarousel = document.getElementById('heroCarousel');
    let isPaused = false;
    
    heroCarousel.addEventListener('mouseenter', () => {
        isPaused = true;
        stopHeroAutoPlay();
    });
    
    heroCarousel.addEventListener('mouseleave', () => {
        isPaused = false;
        if (!isTransitioning) {
            startHeroAutoPlay();
        }
    });
    
    // Touch/swipe support for mobile with improved handling
    let touchStartX = 0;
    let touchEndX = 0;
    let touchStartTime = 0;
    
    heroCarousel.addEventListener('touchstart', (e) => {
        touchStartX = e.changedTouches[0].screenX;
        touchStartTime = Date.now();
        stopHeroAutoPlay();
    }, { passive: true });
    
    heroCarousel.addEventListener('touchend', (e) => {
        touchEndX = e.changedTouches[0].screenX;
        const touchDuration = Date.now() - touchStartTime;
        
        // Only handle swipe if it was quick enough and not a long press
        if (touchDuration < 300) {
            handleHeroSwipe();
        }
        
        if (!isPaused) {
            restartAutoPlay();
        }
    }, { passive: true });
    
    function handleHeroSwipe() {
        if (isTransitioning) return;
        
        const swipeThreshold = 50;
        const swipeDistance = touchEndX - touchStartX;
        
        if (Math.abs(swipeDistance) > swipeThreshold) {
            if (swipeDistance > 0) {
                // Swipe right - previous slide
                prevHeroSlide();
            } else {
                // Swipe left - next slide
                nextHeroSlide();
            }
        }
    }
    
    // Handle visibility changes to prevent issues when tab is not active
    document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
            stopHeroAutoPlay();
        } else if (!isPaused) {
            restartAutoPlay();
        }
    });
    
    // Initialize
    showHeroSlide(0);
    startHeroAutoPlay();
}

document.addEventListener('DOMContentLoaded', function() {
    // Initialize scroll animations
    initScrollAnimations();
    
    // Initialize highlights carousel
    initHighlightsCarousel();
    
    // Initialize countdown timer
    initCrusadesCountdown();
    
    // Initialize seamless loop carousel
    setTimeout(() => {
        initCrusadesSeamlessLoop();
    }, 100); // Small delay to ensure DOM is ready
    
    // Initialize crusades display
    const crusadeCards = document.querySelectorAll('.crusade-card');
    const emptyCrusadesMessage = document.getElementById('emptyCrusadesMessage');
    const crusadesCarousel = document.getElementById('crusadesCarousel');
    
    if (crusadeCards.length === 0) {
        emptyCrusadesMessage.classList.remove('hidden');
        crusadesCarousel.classList.add('hidden');
    } else {
        emptyCrusadesMessage.classList.add('hidden');
        crusadesCarousel.classList.remove('hidden');
    }
    
    // Initialize hero carousel
    initHeroCarousel();
    
    // Fix mobile read more/less buttons on page load
    fixMobileReadMoreButtons();
});

// Function to fix mobile read more/less button visibility
function fixMobileReadMoreButtons() {
    // Fix all read-less buttons to be hidden initially
    const readLessButtons = document.querySelectorAll('.crusade-desc-read-less, .hero-read-less');
    readLessButtons.forEach(button => {
        if (button.classList.contains('hidden')) {
            button.style.display = 'none';
        }
    });
    
    // Ensure read-more buttons are visible initially
    const readMoreButtons = document.querySelectorAll('.crusade-desc-read-more, .hero-read-more');
    readMoreButtons.forEach(button => {
        if (!button.classList.contains('hidden')) {
            button.style.display = 'inline';
        }
    });
}

// Rotating Text Animation
class RotatingText {
    constructor(element, options = {}) {
        this.element = element;
        this.texts = options.texts || [
    '<?php echo __('typewriter_crusades'); ?>',
    '<?php echo __('typewriter_conferences'); ?>',
    '<?php echo __('typewriter_outreaches'); ?>',
    '<?php echo __('typewriter_rallies'); ?>',
    '<?php echo __('typewriter_distributions'); ?>'
];
        this.rotationInterval = options.rotationInterval || 1500;
        this.staggerDuration = options.staggerDuration || 20;
        this.staggerFrom = options.staggerFrom || 'last';
        this.currentTextIndex = 0;
        this.isAnimating = false;

        // Animation settings for smooth spring-like motion
        this.springConfig = {
            duration: 500,
            easing: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)'
        };

        // Timing for coordinated animations
        this.textAnimationDelay = 50; // Minimal delay for smooth transition

        this.init();
    }

    splitIntoCharacters(text) {
        return Array.from(text);
    }

    getElements(text) {
        const words = text.split(' ');
        return words.map((word, i) => ({
            characters: this.splitIntoCharacters(word),
            needsSpace: i !== words.length - 1
        }));
    }

    getStaggerDelay(index, totalChars) {
        const total = totalChars;
        if (this.staggerFrom === 'first') return index * this.staggerDuration;
        if (this.staggerFrom === 'last') return (total - 1 - index) * this.staggerDuration;
        if (this.staggerFrom === 'center') {
            const center = Math.floor(total / 2);
            return Math.abs(center - index) * this.staggerDuration;
        }
        return Math.abs(this.staggerFrom - index) * this.staggerDuration;
    }

    createTextElements(text, isMeasuring = false) {
        const elements = this.getElements(text);
        const container = document.createElement('div');
        container.className = isMeasuring ? 'text-rotate-content measuring' : 'text-rotate-content';
        if (!isMeasuring) {
            container.setAttribute('aria-hidden', 'true');
        }

        let totalCharIndex = 0;
        const totalChars = elements.reduce((sum, word) => sum + word.characters.length, 0);

        elements.forEach((wordObj, wordIndex) => {
            const wordSpan = document.createElement('span');
            wordSpan.className = 'text-rotate-word';

            wordObj.characters.forEach((char, charIndex) => {
                const charSpan = document.createElement('span');
                charSpan.className = 'text-rotate-element';
                charSpan.textContent = char;

                if (!isMeasuring) {
                    charSpan.style.transform = 'translateY(100%)';
                    charSpan.style.opacity = '0';
                }

                wordSpan.appendChild(charSpan);
                totalCharIndex++;
            });

            if (wordObj.needsSpace) {
                const spaceSpan = document.createElement('span');
                spaceSpan.className = 'text-rotate-space';
                spaceSpan.textContent = ' ';
                wordSpan.appendChild(spaceSpan);
            }

            container.appendChild(wordSpan);
        });

        return container;
    }

    measureTextDimensions(text) {
        // Create a measuring container
        const measuringContainer = this.createTextElements(text, true);
        this.element.appendChild(measuringContainer);

        // Get dimensions
        const rect = measuringContainer.getBoundingClientRect();
        const dimensions = {
            width: rect.width,
            height: rect.height
        };

        // Remove measuring container
        measuringContainer.remove();

        return dimensions;
    }

    animateContainerResize(targetDimensions) {
        const container = this.element.closest('.rotating-text-container');
        if (!container) return;

        // Get current dimensions
        const currentRect = container.getBoundingClientRect();
        const currentWidth = currentRect.width;
        const currentHeight = currentRect.height;

        // Calculate new dimensions (accounting for padding)
        const padding = 32; // 1rem on each side
        const newWidth = targetDimensions.width + padding;
        const newHeight = Math.max(targetDimensions.height + 16, 64); // min-height: 4rem

        // Only animate if dimensions actually change
        if (Math.abs(newWidth - currentWidth) > 1 || Math.abs(newHeight - currentHeight) > 1) {
            // Use Web Animations API for better control and synchronization
            container.animate([
                {
                    width: `${currentWidth}px`,
                    height: `${currentHeight}px`
                },
                {
                    width: `${newWidth}px`,
                    height: `${newHeight}px`
                }
            ], {
                duration: this.springConfig.duration,
                easing: this.springConfig.easing,
                fill: 'forwards'
            });
        }
    }

    animateIn(container) {
        const elements = container.querySelectorAll('.text-rotate-element');
        const totalChars = elements.length;

        elements.forEach((element, index) => {
            const delay = this.getStaggerDelay(index, totalChars);

            // Use Web Animations API for smoother animations
            element.animate([
                {
                    transform: 'translateY(100%)',
                    opacity: 0,
                    offset: 0
                },
                {
                    transform: 'translateY(0)',
                    opacity: 1,
                    offset: 1
                }
            ], {
                duration: this.springConfig.duration,
                easing: this.springConfig.easing,
                delay: delay,
                fill: 'forwards'
            });
        });
    }

    animateOut(container) {
        const elements = container.querySelectorAll('.text-rotate-element');
        const totalChars = elements.length;

        return new Promise(resolve => {
            let completedAnimations = 0;

            elements.forEach((element, index) => {
                const delay = this.getStaggerDelay(index, totalChars);

                const animation = element.animate([
                    {
                        transform: 'translateY(0)',
                        opacity: 1,
                        offset: 0
                    },
                    {
                        transform: 'translateY(-120%)',
                        opacity: 0,
                        offset: 1
                    }
                ], {
                    duration: this.springConfig.duration,
                    easing: this.springConfig.easing,
                    delay: delay,
                    fill: 'forwards'
                });

                animation.addEventListener('finish', () => {
                    completedAnimations++;
                    if (completedAnimations === elements.length) {
                        resolve();
                    }
                });
            });
        });
    }

    async changeText() {
        if (this.isAnimating) return;
        this.isAnimating = true;

        // Update index
        this.currentTextIndex = (this.currentTextIndex + 1) % this.texts.length;
        const newText = this.texts[this.currentTextIndex];

        // Measure new text dimensions
        const newDimensions = this.measureTextDimensions(newText);

        const currentContainer = this.element.querySelector('.text-rotate-content');

        // Animate out current text first
        if (currentContainer) {
            await this.animateOut(currentContainer);
            currentContainer.remove();
        }

        // Update screen reader text
        const srText = this.element.querySelector('.text-rotate-sr-only');
        if (srText) srText.textContent = newText;

        // Create new text container
        const newContainer = this.createTextElements(newText);
        this.element.appendChild(newContainer);

        // Start both animations simultaneously for smooth flow
        // 1. Start container resize
        this.animateContainerResize(newDimensions);

        // 2. Start text animation with coordinated timing to flow with box expansion
        await new Promise(resolve => setTimeout(resolve, this.textAnimationDelay));
        this.animateIn(newContainer);

        // Wait for both animations to complete
        await new Promise(resolve => setTimeout(resolve, this.springConfig.duration + 100));
        this.isAnimating = false;
    }

    init() {
        // Create initial text
        const initialText = this.texts[this.currentTextIndex];

        // Set initial container size
        const initialDimensions = this.measureTextDimensions(initialText);
        this.animateContainerResize(initialDimensions);

        const initialContainer = this.createTextElements(initialText);
        this.element.appendChild(initialContainer);

        // Animate in initial text
        requestAnimationFrame(() => {
            this.animateIn(initialContainer);
        });

        // Start rotation
        setInterval(() => {
            this.changeText();
        }, this.rotationInterval);
    }
}

// Highlights carousel functionality
function goToHighlightSlide(index) {
    const slider = document.getElementById('highlightsSlider');
    const dots = document.querySelectorAll('#highlightsDotsContainer button');

    if (slider) {
        slider.style.transform = `translateX(-${index * 100}%)`;

        // Update active dot
        dots.forEach((dot, i) => {
            if (i === index) {
                dot.classList.add('bg-primary');
                dot.classList.remove('bg-gray-300');
            } else {
                dot.classList.remove('bg-primary');
                dot.classList.add('bg-gray-300');
            }
        });
    }
}

function initHighlightsCarousel() {
    const prevButton = document.getElementById('highlightsPrevBtn');
    const nextButton = document.getElementById('highlightsNextBtn');
    const slider = document.getElementById('highlightsSlider');
    const dots = document.querySelectorAll('#highlightsDotsContainer button');

    if (prevButton && nextButton && slider) {
        let currentIndex = 0;
        let autoAdvanceInterval;
        const slides = slider.children.length;

        // Initialize first dot as active
        if (dots.length > 0) {
            dots[0].classList.add('bg-primary');
            dots[0].classList.remove('bg-gray-300');
        }

        // Function to start auto-advance
        function startAutoAdvance() {
            if (autoAdvanceInterval) clearInterval(autoAdvanceInterval);
            autoAdvanceInterval = setInterval(function() {
                // Check if any video is playing before advancing
                if (!isAnyVideoPlaying()) {
                    currentIndex = (currentIndex + 1) % slides;
                    goToHighlightSlide(currentIndex);
                }
            }, 20000);
        }

        // Function to stop auto-advance
        function stopAutoAdvance() {
            if (autoAdvanceInterval) {
                clearInterval(autoAdvanceInterval);
                autoAdvanceInterval = null;
            }
        }

        prevButton.addEventListener('click', function() {
            currentIndex = (currentIndex - 1 + slides) % slides;
            goToHighlightSlide(currentIndex);
        });

        nextButton.addEventListener('click', function() {
            currentIndex = (currentIndex + 1) % slides;
            goToHighlightSlide(currentIndex);
        });

        // Start auto-advance initially
        startAutoAdvance();
        
        // Add keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') {
                currentIndex = (currentIndex - 1 + slides) % slides;
                goToHighlightSlide(currentIndex);
            } else if (e.key === 'ArrowRight') {
                currentIndex = (currentIndex + 1) % slides;
                goToHighlightSlide(currentIndex);
            }
        });

        // Add video event listeners to pause/resume carousel
        const videos = document.querySelectorAll('.highlight-video');
        videos.forEach(video => {
            video.addEventListener('play', stopAutoAdvance);
            video.addEventListener('pause', startAutoAdvance);
            video.addEventListener('ended', startAutoAdvance);
            
            // Add error handling for video loading
            video.addEventListener('error', function(e) {
                console.error('Video loading error:', e);
                console.error('Video source:', video.src || video.querySelector('source')?.src);
            });
            
            video.addEventListener('loadstart', function() {
                console.log('Video loading started:', video.src || video.querySelector('source')?.src);
            });
            
            video.addEventListener('canplay', function() {
                console.log('Video can play:', video.src || video.querySelector('source')?.src);
            });
        });
    }
}

// Function to check if any video is currently playing
function isAnyVideoPlaying() {
    const videos = document.querySelectorAll('.highlight-video');
    return Array.from(videos).some(video => !video.paused);
}

// Function to handle mixed media navigation in highlights
function showHighlightMedia(highlightId, mediaIndex) {
    const slider = document.querySelector(`[data-highlight-id="${highlightId}"]`);
    if (!slider) return;
    
    const mediaItems = slider.querySelectorAll('.media-item');
    const dots = slider.querySelectorAll('button');
    
    // Hide all media items
    mediaItems.forEach((item, index) => {
        if (index === mediaIndex) {
            item.classList.remove('hidden');
            item.classList.add('active');
        } else {
            item.classList.add('hidden');
            item.classList.remove('active');
            
            // Pause any videos that are now hidden
            const video = item.querySelector('video');
            if (video) {
                video.pause();
            }
        }
    });
    
    // Update dots
    dots.forEach((dot, index) => {
        if (index === mediaIndex) {
            dot.classList.remove('bg-opacity-50');
            dot.classList.add('bg-opacity-100');
        } else {
            dot.classList.remove('bg-opacity-100');
            dot.classList.add('bg-opacity-50');
        }
    });
}

// Function to change media with direction
function changeHighlightMedia(highlightId, direction) {
    const slider = document.querySelector(`[data-highlight-id="${highlightId}"]`);
    if (!slider) return;
    
    const mediaCount = parseInt(slider.getAttribute('data-media-count'));
    const currentActive = slider.querySelector('.media-item.active');
    const currentIndex = Array.from(slider.querySelectorAll('.media-item')).indexOf(currentActive);
    
    let newIndex = currentIndex + direction;
    if (newIndex < 0) {
        newIndex = mediaCount - 1;
    } else if (newIndex >= mediaCount) {
        newIndex = 0;
    }
    
    showHighlightMedia(highlightId, newIndex);
}

// Legacy function for backward compatibility
function showHighlightImage(highlightId, imageIndex) {
    showHighlightMedia(highlightId, imageIndex);
}

// Auto-cycle through media in highlights with multiple items
function initHighlightMediaCycling() {
    const sliders = document.querySelectorAll('.highlight-media-slider');
    
    sliders.forEach(slider => {
        const mediaItems = slider.querySelectorAll('.media-item');
        const mediaCount = parseInt(slider.getAttribute('data-media-count'));
        
        if (mediaCount <= 1) return;
        
        const highlightId = slider.getAttribute('data-highlight-id');
        let currentIndex = 0;
        let cyclingInterval;
        
        function startCycling() {
            if (cyclingInterval) clearInterval(cyclingInterval);
            cyclingInterval = setInterval(() => {
                // Only cycle if no video is currently playing in this slider
                const activeVideo = slider.querySelector('.media-item.active video');
                if (!activeVideo || activeVideo.paused) {
                    currentIndex = (currentIndex + 1) % mediaCount;
                    showHighlightMedia(highlightId, currentIndex);
                }
            }, 6000); // Change media every 6 seconds
        }
        
        function stopCycling() {
            if (cyclingInterval) {
                clearInterval(cyclingInterval);
                cyclingInterval = null;
            }
        }
        
        // Start cycling
        startCycling();
        
        // Add event listeners to pause cycling when videos play
        const videos = slider.querySelectorAll('video');
        videos.forEach(video => {
            video.addEventListener('play', stopCycling);
            video.addEventListener('pause', startCycling);
            video.addEventListener('ended', startCycling);
        });
    });
}

// Handle payment method selection for donations
document.addEventListener('DOMContentLoaded', function() {
    const paymentMethodRadios = document.querySelectorAll('input[name="payment_method"]');
    const paymentMethodOptions = document.querySelectorAll('.payment-method-option');
    const cardPaymentSection = document.getElementById('card-payment-section');
    const donorInfoSection = document.getElementById('donor-info-section');
    const donateButton = document.getElementById('donate-button');
    const espeesSection = document.getElementById('espees-donation-section');
    const securityNotice = document.getElementById('security-notice');

    // Update visual appearance of payment method selection
    function updatePaymentMethodUI() {
        paymentMethodOptions.forEach(option => {
            const radio = option.querySelector('input[type="radio"]');
            if (radio.checked) {
                option.classList.add('border-primary', 'bg-blue-50');
                option.classList.remove('border-gray-200');
            } else {
                option.classList.remove('border-primary', 'bg-blue-50');
                option.classList.add('border-gray-200');
            }
        });
    }

    paymentMethodRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            updatePaymentMethodUI();
            
            if (this.value === 'card_payment') {
                cardPaymentSection.classList.remove('hidden');
                donorInfoSection.classList.remove('hidden');
                donateButton.classList.remove('hidden');
                espeesSection.classList.add('hidden');
                securityNotice.classList.remove('hidden');
            } else if (this.value === 'espees') {
                cardPaymentSection.classList.add('hidden');
                donorInfoSection.classList.add('hidden');
                donateButton.classList.add('hidden');
                espeesSection.classList.remove('hidden');
                securityNotice.classList.add('hidden');
            }
        });
    });

    // Initialize the UI
    updatePaymentMethodUI();

    // Initialize rotating text animation
    const rotatingTextElement = document.getElementById('rotatingText');
    if (rotatingTextElement) {
        new RotatingText(rotatingTextElement, {
            rotationInterval: 4000,
            staggerDuration: 20,
            staggerFrom: 'last'
        });
    }
    
    initHighlightMediaCycling(); // Initialize media cycling
    initMediaCarousel(); // Initialize media carousel
});

// Hero text toggle function
function toggleHeroText(event) {
    // Find the closest hero slide container to target the correct elements
    const heroSlide = event ? event.target.closest('.hero-slide') : document.querySelector('.hero-slide');
    
    if (!heroSlide) return;
    
    const preview = heroSlide.querySelector('.hero-text-preview');
    const full = heroSlide.querySelector('.hero-text-full');
    const readMore = heroSlide.querySelector('.hero-read-more');
    const readLess = heroSlide.querySelector('.hero-read-less');
    
    if (preview && full && readMore && readLess) {
        if (preview.classList.contains('hidden')) {
            // Show preview, hide full
            preview.classList.remove('hidden');
            full.classList.add('hidden');
            readMore.classList.remove('hidden');
            readMore.style.display = 'inline';
            readLess.classList.add('hidden');
            readLess.style.display = 'none';
        } else {
            // Show full, hide preview
            preview.classList.add('hidden');
            full.classList.remove('hidden');
            readMore.classList.add('hidden');
            readMore.style.display = 'none';
            readLess.classList.remove('hidden');
            readLess.style.display = 'inline';
        }
    }
}

// Give subtitle toggle function
function toggleGiveSubtitle() {
    const preview = document.querySelector('.give-subtitle-preview');
    const full = document.querySelector('.give-subtitle-full');
    const readMore = document.querySelector('.give-subtitle-read-more');
    const readLess = document.querySelector('.give-subtitle-read-less');
    
    if (preview && full && readMore && readLess) {
        if (preview.classList.contains('hidden')) {
            preview.classList.remove('hidden');
            full.classList.add('hidden');
            readMore.classList.remove('hidden');
            readLess.classList.add('hidden');
        } else {
            preview.classList.add('hidden');
            full.classList.remove('hidden');
            readMore.classList.add('hidden');
            readLess.classList.remove('hidden');
        }
    }
}

// Give description toggle function
function toggleGiveDescription() {
    const preview = document.querySelector('.give-desc-preview');
    const full = document.querySelector('.give-desc-full');
    const readMore = document.querySelector('.give-desc-read-more');
    const readLess = document.querySelector('.give-desc-read-less');
    
    if (preview && full && readMore && readLess) {
        if (preview.classList.contains('hidden')) {
            preview.classList.remove('hidden');
            full.classList.add('hidden');
            readMore.classList.remove('hidden');
            readLess.classList.add('hidden');
        } else {
            preview.classList.add('hidden');
            full.classList.remove('hidden');
            readMore.classList.add('hidden');
            readLess.classList.remove('hidden');
        }
    }
}

// Crusade description toggle function
function toggleCrusadeDescription(index) {
    const preview = document.querySelector(`.crusade-desc-preview-${index}`);
    const full = document.querySelector(`.crusade-desc-full-${index}`);
    const readMore = document.querySelector(`.crusade-desc-read-more-${index}`);
    const readLess = document.querySelector(`.crusade-desc-read-less-${index}`);
    
    if (preview && full && readMore && readLess) {
        if (preview.classList.contains('hidden')) {
            // Show preview, hide full
            preview.classList.remove('hidden');
            preview.style.display = 'inline';
            full.classList.add('hidden');
            full.style.display = 'none';
            readMore.classList.remove('hidden');
            readMore.style.display = 'inline';
            readLess.classList.add('hidden');
            readLess.style.display = 'none';
        } else {
            // Show full, hide preview
            preview.classList.add('hidden');
            preview.style.display = 'none';
            full.classList.remove('hidden');
            full.style.display = 'inline';
            readMore.classList.add('hidden');
            readMore.style.display = 'none';
            readLess.classList.remove('hidden');
            readLess.style.display = 'inline';
        }
    }
}

// Highlights subtitle toggle function
function toggleHighlightsSubtitle() {
    const preview = document.querySelector('.highlights-subtitle-preview');
    const full = document.querySelector('.highlights-subtitle-full');
    const readMore = document.querySelector('.highlights-subtitle-read-more');
    const readLess = document.querySelector('.highlights-subtitle-read-less');
    
    if (preview && full && readMore && readLess) {
        if (preview.classList.contains('hidden')) {
            preview.classList.remove('hidden');
            full.classList.add('hidden');
            readMore.classList.remove('hidden');
            readLess.classList.add('hidden');
        } else {
            preview.classList.add('hidden');
            full.classList.remove('hidden');
            readMore.classList.add('hidden');
            readLess.classList.remove('hidden');
        }
    }
}

// Highlight description toggle function
function toggleHighlightDescription(id) {
    const preview = document.querySelector(`.highlight-desc-preview-${id}`);
    const full = document.querySelector(`.highlight-desc-full-${id}`);
    const readMore = document.querySelector(`.highlight-desc-read-more-${id}`);
    const readLess = document.querySelector(`.highlight-desc-read-less-${id}`);
    
    if (preview && full && readMore && readLess) {
        if (preview.classList.contains('hidden')) {
            preview.classList.remove('hidden');
            full.classList.add('hidden');
            readMore.classList.remove('hidden');
            readLess.classList.add('hidden');
        } else {
            preview.classList.add('hidden');
            full.classList.remove('hidden');
            readMore.classList.add('hidden');
            readLess.classList.remove('hidden');
        }
    }
}

// Media description toggle function
function toggleMediaDescription(id) {
    const preview = document.querySelector(`.media-desc-preview-${id}`);
    const full = document.querySelector(`.media-desc-full-${id}`);
    const readMore = document.querySelector(`.media-desc-read-more-${id}`);
    const readLess = document.querySelector(`.media-desc-read-less-${id}`);
    
    if (preview && full && readMore && readLess) {
        if (preview.classList.contains('hidden')) {
            preview.classList.remove('hidden');
            full.classList.add('hidden');
            readMore.classList.remove('hidden');
            readLess.classList.add('hidden');
        } else {
            preview.classList.add('hidden');
            full.classList.remove('hidden');
            readMore.classList.add('hidden');
            readLess.classList.remove('hidden');
        }
    }
}

// Product description toggle functions
function toggleRhapsodyDescription() {
    const preview = document.querySelector('.rhapsody-desc-preview');
    const full = document.querySelector('.rhapsody-desc-full');
    const readMore = document.querySelector('.rhapsody-desc-read-more');
    const readLess = document.querySelector('.rhapsody-desc-read-less');
    
    if (preview && full && readMore && readLess) {
        if (preview.classList.contains('hidden')) {
            preview.classList.remove('hidden');
            full.classList.add('hidden');
            readMore.classList.remove('hidden');
            readLess.classList.add('hidden');
        } else {
            preview.classList.add('hidden');
            full.classList.remove('hidden');
            readMore.classList.add('hidden');
            readLess.classList.remove('hidden');
        }
    }
}

function toggleRtvDescription() {
    const preview = document.querySelector('.rtv-desc-preview');
    const full = document.querySelector('.rtv-desc-full');
    const readMore = document.querySelector('.rtv-desc-read-more');
    const readLess = document.querySelector('.rtv-desc-read-less');
    
    if (preview && full && readMore && readLess) {
        if (preview.classList.contains('hidden')) {
            preview.classList.remove('hidden');
            full.classList.add('hidden');
            readMore.classList.remove('hidden');
            readLess.classList.add('hidden');
        } else {
            preview.classList.add('hidden');
            full.classList.remove('hidden');
            readMore.classList.add('hidden');
            readLess.classList.remove('hidden');
        }
    }
}

function toggleReachoutDescription() {
    const preview = document.querySelector('.reachout-desc-preview');
    const full = document.querySelector('.reachout-desc-full');
    const readMore = document.querySelector('.reachout-desc-read-more');
    const readLess = document.querySelector('.reachout-desc-read-less');
    
    if (preview && full && readMore && readLess) {
        if (preview.classList.contains('hidden')) {
            preview.classList.remove('hidden');
            full.classList.add('hidden');
            readMore.classList.remove('hidden');
            readLess.classList.add('hidden');
        } else {
            preview.classList.add('hidden');
            full.classList.remove('hidden');
            readMore.classList.add('hidden');
            readLess.classList.remove('hidden');
        }
    }
}

function toggleTap2readDescription() {
    const preview = document.querySelector('.tap2read-desc-preview');
    const full = document.querySelector('.tap2read-desc-full');
    const readMore = document.querySelector('.tap2read-desc-read-more');
    const readLess = document.querySelector('.tap2read-desc-read-less');
    
    if (preview && full && readMore && readLess) {
        if (preview.classList.contains('hidden')) {
            preview.classList.remove('hidden');
            full.classList.add('hidden');
            readMore.classList.remove('hidden');
            readLess.classList.add('hidden');
        } else {
            preview.classList.add('hidden');
            full.classList.remove('hidden');
            readMore.classList.add('hidden');
            readLess.classList.remove('hidden');
        }
    }
}

function toggleDownloadAppDescription() {
    const preview = document.querySelector('.download-app-desc-preview');
    const full = document.querySelector('.download-app-desc-full');
    const readMore = document.querySelector('.download-app-desc-read-more');
    const readLess = document.querySelector('.download-app-desc-read-less');
    
    if (preview && full && readMore && readLess) {
        if (preview.classList.contains('hidden')) {
            preview.classList.remove('hidden');
            full.classList.add('hidden');
            readMore.classList.remove('hidden');
            readLess.classList.add('hidden');
        } else {
            preview.classList.add('hidden');
            full.classList.remove('hidden');
            readMore.classList.add('hidden');
            readLess.classList.remove('hidden');
        }
    }
}

// Media carousel functionality
function goToMediaSlide(index) {
    const slider = document.getElementById('mediaSlider');
    const dots = document.querySelectorAll('#mediaDotsContainer button');

    if (slider) {
        slider.style.transform = `translateX(-${index * 100}%)`;

        // Update active dot
        dots.forEach((dot, i) => {
            if (i === index) {
                dot.classList.add('bg-primary');
                dot.classList.remove('bg-gray-300');
            } else {
                dot.classList.remove('bg-primary');
                dot.classList.add('bg-gray-300');
            }
        });
    }
}

function initMediaCarousel() {
    const prevButton = document.getElementById('mediaPrevBtn');
    const nextButton = document.getElementById('mediaNextBtn');
    const slider = document.getElementById('mediaSlider');
    const dots = document.querySelectorAll('#mediaDotsContainer button');

    if (prevButton && nextButton && slider) {
        let currentIndex = 0;
        const slides = slider.children.length;

        // Initialize first dot as active
        if (dots.length > 0) {
            dots[0].classList.add('bg-primary');
            dots[0].classList.remove('bg-gray-300');
        }

        prevButton.addEventListener('click', function() {
            currentIndex = (currentIndex - 1 + slides) % slides;
            goToMediaSlide(currentIndex);
        });

        nextButton.addEventListener('click', function() {
            currentIndex = (currentIndex + 1) % slides;
            goToMediaSlide(currentIndex);
        });

        // Auto-advance carousel every 10 seconds
        setInterval(function() {
            currentIndex = (currentIndex + 1) % slides;
            goToMediaSlide(currentIndex);
        }, 10000);
    }
}

// Crusades countdown timer function
function initCrusadesCountdown() {
    // Set the target date: August 22, 2025 at 8:00 PM (20:00)
    const targetDate = new Date('August 22, 2025 20:00:00').getTime();
    
    // Update countdown every second
    const countdownInterval = setInterval(function() {
        const now = new Date().getTime();
        const distance = targetDate - now;
        
        // Calculate time units
        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);
        
        // Update the DOM elements
        const daysElement = document.getElementById('countdown-days');
        const hoursElement = document.getElementById('countdown-hours');
        const minutesElement = document.getElementById('countdown-minutes');
        const secondsElement = document.getElementById('countdown-seconds');
        
        if (daysElement) daysElement.textContent = days >= 0 ? days : 0;
        if (hoursElement) hoursElement.textContent = hours >= 0 ? hours.toString().padStart(2, '0') : '00';
        if (minutesElement) minutesElement.textContent = minutes >= 0 ? minutes.toString().padStart(2, '0') : '00';
        if (secondsElement) secondsElement.textContent = seconds >= 0 ? seconds.toString().padStart(2, '0') : '00';
        
        // If countdown reaches zero or past
        if (distance < 0) {
            clearInterval(countdownInterval);
            if (daysElement) daysElement.textContent = '0';
            if (hoursElement) hoursElement.textContent = '00';
            if (minutesElement) minutesElement.textContent = '00';
            if (secondsElement) secondsElement.textContent = '00';
            
            // Optionally show "Event Started" or "Event Ended" message
            const countdownContainer = document.getElementById('crusades-countdown');
            if (countdownContainer) {
                const eventMessage = document.createElement('div');
                eventMessage.className = 'text-center mt-4 p-4 bg-accent text-gray-900 rounded-lg font-bold';
                eventMessage.textContent = 'Event is Live!';
                countdownContainer.parentNode.appendChild(eventMessage);
            }
        }
    }, 1000);
    
    // Initial call to set countdown immediately
    const now = new Date().getTime();
    const distance = targetDate - now;
    
    const days = Math.floor(distance / (1000 * 60 * 60 * 24));
    const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((distance % (1000 * 60)) / 1000);
    
    const daysElement = document.getElementById('countdown-days');
    const hoursElement = document.getElementById('countdown-hours');
    const minutesElement = document.getElementById('countdown-minutes');
    const secondsElement = document.getElementById('countdown-seconds');
    
    if (daysElement) daysElement.textContent = days >= 0 ? days : 0;
    if (hoursElement) hoursElement.textContent = hours >= 0 ? hours.toString().padStart(2, '0') : '00';
    if (minutesElement) minutesElement.textContent = minutes >= 0 ? minutes.toString().padStart(2, '0') : '00';
    if (secondsElement) secondsElement.textContent = seconds >= 0 ? seconds.toString().padStart(2, '0') : '00';
}

// World loading screen functionality
function showWorldLoadingScreen(callback) {
    // Create loading screen if it doesn't exist
    let loadingScreen = document.getElementById('world-loading-screen');
    if (!loadingScreen) {
        loadingScreen = document.createElement('div');
        loadingScreen.id = 'world-loading-screen';
        loadingScreen.className = 'fixed inset-0 bg-black z-[200] opacity-0 invisible flex items-center justify-center';
        loadingScreen.innerHTML = `
            <div class="text-center">
                <h1 id="world-loading-text" class="text-4xl md:text-6xl lg:text-8xl font-black text-white font-mono tracking-wider">
                    <span class="world-glitch-char">A</span>
                    <span class="world-glitch-char"> </span>
                    <span class="world-glitch-char">T</span>
                    <span class="world-glitch-char">H</span>
                    <span class="world-glitch-char">O</span>
                    <span class="world-glitch-char">U</span>
                    <span class="world-glitch-char">S</span>
                    <span class="world-glitch-char">A</span>
                    <span class="world-glitch-char">N</span>
                    <span class="world-glitch-char">D</span>
                    <span class="world-glitch-char"> </span>
                    <span class="world-glitch-char">C</span>
                    <span class="world-glitch-char">R</span>
                    <span class="world-glitch-char">U</span>
                    <span class="world-glitch-char">S</span>
                    <span class="world-glitch-char">A</span>
                    <span class="world-glitch-char">D</span>
                    <span class="world-glitch-char">E</span>
                    <span class="world-glitch-char">S</span>
                </h1>
                <div class="mt-8">
                    <div class="w-64 h-1 bg-gray-800 mx-auto rounded-full overflow-hidden">
                        <div id="world-loading-bar" class="h-full bg-blue-400 w-0 transition-all duration-3000 ease-out"></div>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(loadingScreen);
    }
    
    const loadingBar = document.getElementById('world-loading-bar');
    
    // Show loading screen
    loadingScreen.classList.remove('invisible');
    loadingScreen.classList.remove('opacity-0');
    
    // Reset loading bar
    loadingBar.style.width = '0%';
    
    // Start glitch animation
    const glitchInterval = startWorldGlitchAnimation();
    
    // Animate loading bar
    setTimeout(() => {
        loadingBar.style.width = '100%';
    }, 100);
    
    // Complete loading after 3 seconds
    setTimeout(() => {
        clearInterval(glitchInterval);
        
        // Execute callback (redirect)
        if (callback) callback();
    }, 3000);
}

// World glitch animation function
function startWorldGlitchAnimation() {
    const glitchChars = document.querySelectorAll('.world-glitch-char');
    const originalChars = Array.from(glitchChars).map(char => char.textContent);
    const glitchSymbols = ['█', '▓', '▒', '░', '▄', '▀', '■', '□', '▪', '▫', '◆', '◇', '●', '○', '◉', '◎', '⬛', '⬜', '▲', '△', '▼', '▽', '◀', '▶', '◄', '►'];

    function glitchChar(char, index) {
        const originalChar = originalChars[index];
        
        // Random glitch effect
        if (Math.random() < 0.3) {
            const randomSymbol = glitchSymbols[Math.floor(Math.random() * glitchSymbols.length)];
            char.textContent = randomSymbol;
            char.style.color = Math.random() < 0.5 ? '#ff0000' : '#00ffff';
            char.style.textShadow = '2px 0 #ff0000, -2px 0 #00ffff';
            
            // Reset after short delay
            setTimeout(() => {
                char.textContent = originalChar;
                char.style.color = 'white';
                char.style.textShadow = 'none';
            }, Math.random() * 200 + 50);
        }
    }

    // Start continuous glitch effect
    const glitchInterval = setInterval(() => {
        glitchChars.forEach((char, index) => {
            if (Math.random() < 0.1) { // 10% chance per character
                glitchChar(char, index);
            }
        });
    }, 100);

    return glitchInterval;
}

// Function to redirect to appropriate crusade registration page based on device
function redirectToCrusadeRegistration() {
    // Show the crusade registration modal instead of direct redirect
    openCrusadeRegistrationModal();
}


// Function to redirect to appropriate world index page based on device
function redirectToWorldIndex() {
    // Simple mobile detection
    const isMobile = window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    
    // Show loading screen before redirect
    showWorldLoadingScreen(() => {
        if (isMobile) {
                                window.location.href = 'world/index-mobile';
        } else {
                                window.location.href = 'world/index';
        }
    });
}

function openVideoModal(videoUrl, title) {
    console.log("Opening video modal with URL:", videoUrl);
    
    const modal = document.getElementById('videoModal');
    const modalTitle = document.getElementById('modalTitle');
    const modalVideo = document.getElementById('modalVideo');
    
    if (!modal || !modalTitle || !modalVideo) {
        console.error('Video modal elements not found');
        return;
    }
    
    modalTitle.textContent = title;
    
    // Store the original video URL for download functionality
    currentVideoUrl = videoUrl;
    
    // Handle different video URL formats
    let finalUrl = videoUrl;
    
    // YouTube URLs
    if (videoUrl.includes('youtube.com') || videoUrl.includes('youtu.be')) {
        finalUrl += (videoUrl.includes('?') ? '&' : '?') + 'autoplay=1&playsinline=0';
    }
    // Amazon S3 URLs
    else if (videoUrl.includes('s3.') || videoUrl.includes('amazonaws.com')) {
        // S3 URLs should work directly
        console.log("Using Amazon S3 URL directly");
    }
    // Loveworld Cloud URLs
    else if (videoUrl.includes('loveworldcloud.com')) {
        // Loveworld URLs should work directly
        console.log("Using Loveworld Cloud URL directly");
    }
    
    console.log("Final video URL:", finalUrl);
    
    // Set the iframe source
    try {
        modalVideo.src = finalUrl;
        
        // Remove hidden class and add flex class
        modal.classList.remove('hidden');
        modal.classList.add('flex');
        
        document.body.style.overflow = 'hidden';
        
        // Force fullscreen on mobile devices
        if (window.innerWidth <= 768) {
            setTimeout(() => {
                try {
                    if (modalVideo.requestFullscreen) {
                        modalVideo.requestFullscreen();
                    } else if (modalVideo.webkitRequestFullscreen) {
                        modalVideo.webkitRequestFullscreen();
                    } else if (modalVideo.mozRequestFullScreen) {
                        modalVideo.mozRequestFullScreen();
                    } else if (modalVideo.msRequestFullscreen) {
                        modalVideo.msRequestFullscreen();
                    }
                } catch (e) {
                    console.error("Error requesting fullscreen:", e);
                }
            }, 500);
        }
    } catch (e) {
        console.error("Error setting video source:", e);
        alert("There was an error loading the video. Please try again.");
    }
}
</script>

<style>
/* Enhanced rotating text animation styles */
.rotating-text-container {
    background: linear-gradient(135deg, #fbbf24, #d97706);
    color: #1f2937;
    overflow: hidden;
    border-radius: 0.5rem;
    font-size: 3rem;
    font-weight: 700;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    min-height: 5.5rem;
    padding: 0.75rem 1.5rem;
    box-shadow: 0 10px 25px rgba(251, 191, 36, 0.3);
    transition: width 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275),
                height 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    will-change: width, height;
    margin: 0 auto;
    width: fit-content;
}

.text-rotate {
    display: flex;
    flex-wrap: wrap;
    white-space: pre-wrap;
    position: relative;
    transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.text-rotate-content {
    display: flex;
    flex-wrap: wrap;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    white-space: nowrap;
}

.text-rotate-content.measuring {
    position: static;
    visibility: hidden;
    transform: none;
    top: auto;
    left: auto;
}

.text-rotate-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.text-rotate-word {
    display: inline-flex;
    overflow: hidden;
    padding-bottom: 4px;
}

.text-rotate-element {
    display: inline-block;
    transform: translateY(100%);
        opacity: 0;
    will-change: transform, opacity;
    backface-visibility: hidden;
    -webkit-font-smoothing: antialiased;
}

.text-rotate-space {
    white-space: pre;
}

/* Mobile responsive styles */
@media (max-width: 1024px) {
    .rotating-text-container {
        font-size: 2.5rem;
        min-height: 5rem;
        padding: 0.625rem 1.25rem;
    }
}

@media (max-width: 768px) {
    .rotating-text-container {
        padding: 0.5rem 1rem;
        font-size: 2rem;
        min-height: 4rem;
        border-radius: 0.75rem;
    }
}

@media (max-width: 480px) {
    .rotating-text-container {
        padding: 0.375rem 0.875rem;
        font-size: 1.75rem;
        min-height: 3.5rem;
        border-radius: 1rem;
    }
}

/* Countdown Mobile Optimization Styles */
.countdown-card {
    transition: all 0.3s ease;
    min-height: 80px;
}

.countdown-number {
    line-height: 1.1;
    margin-bottom: 0.25rem;
}

.countdown-label {
    line-height: 1.2;
    letter-spacing: 0.05em;
}

/* Mobile countdown optimizations */
@media (max-width: 640px) {
    .crusades-countdown-container {
        padding: 1rem !important;
        margin: 0.5rem 0;
    }
    
    .crusades-countdown-title {
        font-size: 1rem !important;
        margin-bottom: 0.75rem !important;
    }
    
    .crusades-countdown-grid {
        gap: 0.5rem !important;
        grid-template-columns: repeat(2, 1fr) !important;
    }
    
    .countdown-card {
        padding: 0.75rem 0.5rem !important;
        min-height: 75px;
        border-radius: 0.5rem !important;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
    
    .countdown-number {
        font-size: 1.5rem !important;
        font-weight: 800 !important;
        margin-bottom: 0.25rem;
    }
    
    .countdown-label {
        font-size: 0.75rem !important;
        font-weight: 600 !important;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        opacity: 0.8;
    }
}

/* Extra small mobile screens */
@media (max-width: 480px) {
    .crusades-countdown-container {
        padding: 0.75rem !important;
    }
    
    .crusades-countdown-title {
        font-size: 0.875rem !important;
        margin-bottom: 0.5rem !important;
    }
    
    .countdown-card {
        padding: 0.5rem 0.25rem !important;
        min-height: 65px;
    }
    
    .countdown-number {
        font-size: 1.25rem !important;
    }
    
    .countdown-label {
        font-size: 0.625rem !important;
    }
}

/* Very small screens (landscape phones) */
@media (max-width: 375px) {
    .countdown-number {
        font-size: 1.125rem !important;
    }
    
    .countdown-label {
        font-size: 0.625rem !important;
        line-height: 1;
    }
}

/* Tablet landscape and desktop - sharp corners per workspace rules */
@media (min-width: 641px) {
    .countdown-card {
        border-radius: 0 !important;
    }
    
    .crusades-countdown-container {
        border-radius: 0 !important;
    }
}

/* Crusade Cards Seamless Loop Styles */
.crusade-card {
    list-style: none;
    padding: 0.75rem 1rem;
    margin: 0;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: auto;
    min-height: fit-content;
    overflow: visible;
}

#crusadesCarousel {
    position: relative;
    min-height: 500px;
    height: auto;
    overflow: visible;
    z-index: 10;
    margin: 2rem 0;
    padding: 1rem 0;
}

#crusadesSlider {
    position: relative;
    width: 100%;
    height: auto;
    min-height: fit-content;
    margin: 0 auto;
}

/* Custom SweetAlert2 styles to use blue instead of red for errors */
.swal2-popup .swal2-icon.swal2-error {
    border-color: #2563eb !important;
    color: #2563eb !important;
}

.swal2-popup .swal2-icon.swal2-error .swal2-x-mark {
    color: #2563eb !important;
}

.swal2-popup .swal2-icon.swal2-error [class^='swal2-x-mark-line'] {
    background-color: #2563eb !important;
}

.swal2-popup .swal2-title {
    color: #1f2937 !important;
}

.swal2-popup .swal2-content {
    color: #374151 !important;
}

/* Override any default red colors in SweetAlert */
.swal2-popup .swal2-styled.swal2-confirm {
    background-color: #2563eb !important;
    border: none !important;
}

.swal2-popup .swal2-styled.swal2-confirm:hover {
    background-color: #1d4ed8 !important;
    }

.swal2-popup .swal2-styled.swal2-confirm:focus {
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.5) !important;
}

/* Responsive Crusade Card Styles */
@media (max-width: 640px) {
    .crusade-card {
        padding: 0.5rem;
    }
    
    .crusade-card .bg-white {
        border-radius: 0.5rem;
    }
    
    /* Mobile image optimizations */
    .crusade-card .aspect-\[16\/9\] {
        aspect-ratio: 16/9;
        height: auto;
    }
    
    /* Mobile text optimizations */
    .crusade-card .text-xl,
    .crusade-card .text-2xl,
    .crusade-card .text-3xl {
        line-height: 1.3;
        word-wrap: break-word;
    }
    
    /* Mobile spacing adjustments */
    .crusade-card .space-y-4 > * + * {
        margin-top: 0.75rem;
    }
    
    .crusade-card .space-y-6 > * + * {
        margin-top: 1rem;
    }
    
    /* Ensure touch targets are accessible */
    .crusade-card a,
    .crusade-card button {
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    /* Mobile-specific read more/less button fixes */
    .hidden {
        display: none !important;
    }
    
    /* Force all read-less buttons to be hidden initially on mobile */
    .crusade-desc-read-less-0,
    .crusade-desc-read-less-1,
    .crusade-desc-read-less-2,
    .crusade-desc-read-less-3,
    .crusade-desc-read-less-4,
    .crusade-desc-read-less-5,
    .crusade-desc-read-less-6,
    .crusade-desc-read-less-7,
    .crusade-desc-read-less-8,
    .crusade-desc-read-less-9,
    [class*="read-less"] {
        display: none !important;
    }
    
    /* Only show read-less when explicitly set by JavaScript */
    [class*="read-less"][style*="display: inline"] {
        display: inline !important;
    }
    
    /* Ensure read-more buttons are visible by default */
    [class*="read-more"]:not(.hidden) {
        display: inline !important;
    }
}

@media (min-width: 641px) and (max-width: 1024px) {
    .crusade-card {
        padding: 0.75rem;
    }
}

@media (min-width: 1025px) {
    .crusade-card {
        padding: 1rem 1.5rem;
    }
    
    .crusade-card .bg-white {
        border-radius: 0;
    }
}

/* Additional responsive enhancements */
@media (max-width: 480px) {
    .crusade-card {
        padding: 0.25rem;
    }
    
    .crusade-card .p-4,
    .crusade-card .p-6,
    .crusade-card .p-8 {
        padding: 0.75rem;
    }
}

/* Ensure no content overflow on any screen size */
.crusade-card * {
    max-width: 100%;
    box-sizing: border-box;
}

/* Handle very wide screens */
@media (min-width: 1440px) {
    #crusadesCarousel {
        max-width: 1200px;
        margin: 0 auto;
    }
}

/* Accessibility improvements */
.crusade-card:focus-within {
    outline: 2px solid #2563eb;
    outline-offset: 2px;
}

.crusade-card a:focus,
.crusade-card button:focus {
    outline: 2px solid #2563eb;
    outline-offset: 2px;
    border-radius: 4px;
}

/* Ensure proper focus management during transitions */
.crusade-card[style*="opacity: 0"] {
    pointer-events: none;
}

.crusade-card[style*="opacity: 1"] {
    pointer-events: auto;
}

/* Screen reader improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Flexible content containers */
.crusade-card .bg-white {
    height: auto;
    min-height: fit-content;
    display: flex;
    flex-direction: column;
}

.crusade-card .lg\:w-1\/2 {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    min-height: fit-content;
    flex: 1;
}

/* Enhanced flexbox layout for card content */
.crusade-card .flex.flex-col.lg\:flex-row {
    align-items: stretch;
    min-height: fit-content;
}

.crusade-card .flex.flex-col.justify-center {
    justify-content: flex-start;
    align-items: stretch;
}

/* Ensure proper text wrapping and spacing */
.crusade-card h3,
.crusade-card p {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
}

/* Flexible spacing containers */
.crusade-card .space-y-4,
.crusade-card .space-y-6,
.crusade-card .space-y-8 {
    display: flex;
    flex-direction: column;
    gap: inherit;
}

.crusade-card .space-y-4,
.crusade-card .space-y-6,
.crusade-card .space-y-8 {
    height: auto;
    min-height: fit-content;
}

/* Image Container Fixes */
.crusade-card .aspect-\[16\/9\] {
    aspect-ratio: 16/9;
    overflow: hidden;
    height: auto;
}

.crusade-card .aspect-\[4\/3\] {
    aspect-ratio: 4/3;
    overflow: hidden;
    height: auto;
}

.crusade-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

/* Responsive image adjustments */
@media (max-width: 768px) {
    .crusade-card .lg\:aspect-auto {
        aspect-ratio: 16/9;
        height: auto;
    }
    
    .crusade-card .lg\:h-96 {
        height: auto;
        aspect-ratio: 16/9;
    }
}

@media (min-width: 1024px) {
    .crusade-card .lg\:h-96 {
        height: auto;
        min-height: 24rem;
        max-height: none;
    }
    
    .crusade-card .lg\:aspect-auto {
        aspect-ratio: auto;
        height: auto;
        min-height: 24rem;
    }
}
</style>

<!-- SweetAlert2 and Animate.css -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css">
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- Make translations available to JavaScript -->
<script>
window.translations = {
    payment_complete_giving: '<?php echo addslashes(__('payment_complete_giving')); ?>',
    payment_giving_amount: '<?php echo addslashes(__('payment_giving_amount')); ?>',
    payment_donor: '<?php echo addslashes(__('payment_donor')); ?>',
    payment_complete_giving_btn: '<?php echo addslashes(__('payment_complete_giving_btn')); ?>',
    payment_processing: '<?php echo addslashes(__('payment_processing')); ?>',
    payment_secure_stripe: '<?php echo addslashes(__('payment_secure_stripe')); ?>',
    success_thank_you: '<?php echo addslashes(__('success_thank_you')); ?>',
    success_message: '<?php echo addslashes(__('success_message')); ?>',
    success_continue: '<?php echo addslashes(__('success_continue')); ?>'
};
</script>

<!-- Video Modal -->
<div id="videoModal" class="fixed inset-0 z-50 hidden items-center justify-center bg-black bg-opacity-30 p-4 sm:p-6">
    <div class="bg-white max-w-4xl w-full max-h-[90vh] overflow-y-auto border border-gray-200 rounded-lg sm:rounded-none shadow-2xl relative overflow-hidden rounded-lg sm:rounded-none">
        <!-- Top decorative border -->
        <div class="absolute top-0 left-0 right-0 h-1 sm:h-2 bg-gradient-to-r from-primary via-accent to-gold z-20"></div>
        <!-- Bottom decorative border -->
        <div class="absolute bottom-0 left-0 right-0 h-1 sm:h-2 bg-gradient-to-r from-gold via-accent to-primary z-20"></div>
        
        <div class="flex justify-between items-start p-4 sm:p-6 md:p-8 lg:p-12">
            <h3 id="modalTitle" class="text-lg sm:text-xl md:text-2xl font-light text-gray-900 flex-1 pr-4"></h3>
            <div class="flex items-center space-x-2 flex-shrink-0">
                <button id="downloadButton" onclick="downloadVideo()" class="text-gray-600 hover:text-primary focus:outline-none transition-colors duration-200 p-1 sm:p-2 hover:bg-blue-50 rounded-lg" title="<?php echo $translationService->translateText('Download Video', 'en', $currentLanguage) ?? 'Download Video'; ?>">
                    <svg class="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </button>
                <button onclick="closeVideoModal()" class="text-gray-400 hover:text-primary focus:outline-none transition-colors duration-200 p-1 sm:p-2 hover:bg-blue-50 rounded-lg">
                    <svg class="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
        <div class="aspect-video px-4 sm:px-6 md:px-8 lg:px-12 pb-4 sm:pb-6 md:pb-8 lg:pb-12">
            <iframe id="modalVideo" class="w-full h-full rounded-lg" src="" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen playsinline webkit-playsinline></iframe>
        </div>
    </div>
</div>

<!-- Crusade Registration Modal -->
<div id="crusadeRegistrationModal" class="fixed inset-0 z-[9999] hidden items-center justify-center bg-black bg-opacity-50 p-4 sm:p-6 md:p-8 overflow-y-auto">
    <div class="bg-white w-full max-w-xs sm:max-w-sm md:max-w-md mx-auto max-h-[90vh] sm:max-h-[85vh] overflow-y-auto border-4 border-gray-100 hover:border-primary transition-colors duration-500 shadow-2xl relative overflow-hidden rounded-xl sm:rounded-lg">
        <!-- Top decorative border -->
        <div class="absolute top-0 left-0 right-0 h-1 sm:h-2 bg-gradient-to-r from-primary via-accent to-gold z-20"></div>
        <!-- Bottom decorative border -->
        <div class="absolute bottom-0 left-0 right-0 h-1 sm:h-2 bg-gradient-to-r from-gold via-accent to-primary z-20"></div>
        <!-- Side accents -->
        <div class="absolute left-0 top-1 sm:top-2 bottom-1 sm:bottom-2 w-0.5 sm:w-1 bg-gradient-to-b from-primary to-accent z-20"></div>
        <div class="absolute right-0 top-1 sm:top-2 bottom-1 sm:bottom-2 w-0.5 sm:w-1 bg-gradient-to-b from-accent to-primary z-20"></div>
        
        <!-- Corner decorations - Hidden on mobile for cleaner look -->
        <div class="hidden sm:block absolute top-1 sm:top-2 left-1 sm:left-2 w-2 sm:w-3 h-2 sm:h-3 bg-primary opacity-70 z-20"></div>
        <div class="hidden sm:block absolute top-1 sm:top-2 right-1 sm:right-2 w-2 sm:w-3 h-2 sm:h-3 bg-accent opacity-70 z-20"></div>
        <div class="hidden sm:block absolute bottom-1 sm:bottom-2 left-1 sm:left-2 w-2 sm:w-3 h-2 sm:h-3 bg-accent opacity-70 z-20"></div>
        <div class="hidden sm:block absolute bottom-1 sm:bottom-2 right-1 sm:right-2 w-2 sm:w-3 h-2 sm:h-3 bg-primary opacity-70 z-20"></div>
        
        <!-- Header -->
        <div class="flex justify-between items-start mb-4 sm:mb-6 p-4 sm:p-6 border-b border-gray-100">
            <h3 class="text-base sm:text-lg md:text-xl font-bold text-gray-900 relative flex-1 pr-3 pb-2 leading-tight">
                Choose Registration Type
                <div class="absolute bottom-0 left-0 w-6 sm:w-8 md:w-12 h-0.5 bg-gradient-to-r from-primary to-accent"></div>
            </h3>
            <button onclick="closeCrusadeRegistrationModal()" class="text-gray-400 hover:text-primary focus:outline-none transition-colors duration-200 p-1.5 sm:p-2 hover:bg-gray-100 rounded-full flex-shrink-0 touch-manipulation min-w-[44px] min-h-[44px] flex items-center justify-center">
                <svg class="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <!-- Content -->
        <div class="px-4 sm:px-6 pb-4 sm:pb-6">
            <p class="text-gray-600 mb-4 sm:mb-6 text-xs sm:text-sm md:text-base leading-relaxed text-center">
                Select how you would like to register for the Night of a Thousand Crusades:
            </p>
            
            <!-- Registration Options -->
            <div class="space-y-2 sm:space-y-3">
                <!-- Individual Option -->
                <a href="#" onclick="selectRegistrationType('individual')" class="block w-full p-3 sm:p-4 border-2 border-gray-200 hover:border-primary bg-gray-50 hover:bg-blue-50 transition-all duration-300 group rounded-lg touch-manipulation active:scale-95 min-h-[60px] sm:min-h-[70px]">
                    <div class="flex items-center justify-between h-full">
                        <div class="flex items-center flex-1 min-w-0">
                            <div class="w-8 h-8 sm:w-10 sm:h-10 bg-primary bg-opacity-10 rounded-full flex items-center justify-center mr-2 sm:mr-3 group-hover:bg-primary group-hover:bg-opacity-20 transition-colors duration-200 flex-shrink-0">
                                <svg class="w-4 h-4 sm:w-5 sm:h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                            <div class="flex-1 min-w-0">
                                <h4 class="text-sm sm:text-base font-semibold text-gray-900 group-hover:text-primary transition-colors duration-200 mb-0.5 leading-tight">
                                    Individual
                                </h4>
                                <p class="text-xs text-gray-600 group-hover:text-gray-700 transition-colors duration-200 leading-tight">
                                    For personal crusade hosting
                                </p>
                            </div>
                        </div>
                        <div class="ml-2 text-gray-400 group-hover:text-primary transition-colors duration-200 flex-shrink-0">
                            <svg class="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </div>
                    </div>
                </a>
                
                <!-- Church/Group/Zone Option -->
                <a href="#" onclick="selectRegistrationType('church')" class="block w-full p-4 sm:p-5 border-2 border-gray-200 hover:border-primary bg-gray-50 hover:bg-blue-50 transition-all duration-300 group rounded-xl touch-manipulation active:scale-95">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center flex-1">
                            <div class="w-10 h-10 sm:w-12 sm:h-12 bg-accent bg-opacity-20 rounded-full flex items-center justify-center mr-3 sm:mr-4 group-hover:bg-accent group-hover:bg-opacity-30 transition-colors duration-200">
                                <svg class="w-5 h-5 sm:w-6 sm:h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <h4 class="text-base sm:text-lg font-semibold text-gray-900 group-hover:text-primary transition-colors duration-200 mb-1">
                                    Church/Group/Zone
                                </h4>
                                <p class="text-xs sm:text-sm text-gray-600 group-hover:text-gray-700 transition-colors duration-200">
                                    For church or group registrations
                                </p>
                            </div>
                        </div>
                        <div class="ml-3 text-gray-400 group-hover:text-primary transition-colors duration-200">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </div>
                    </div>
                </a>

                <!-- Network Option -->
                <a href="#" onclick="selectRegistrationType('network')" class="block w-full p-4 sm:p-5 border-2 border-gray-200 hover:border-primary bg-gray-50 hover:bg-blue-50 transition-all duration-300 group rounded-xl touch-manipulation active:scale-95">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center flex-1">
                            <div class="w-10 h-10 sm:w-12 sm:h-12 bg-gold bg-opacity-20 rounded-full flex items-center justify-center mr-3 sm:mr-4 group-hover:bg-gold group-hover:bg-opacity-30 transition-colors duration-200">
                                <svg class="w-5 h-5 sm:w-6 sm:h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <h4 class="text-base sm:text-lg font-semibold text-gray-900 group-hover:text-primary transition-colors duration-200 mb-1">
                                    Network
                                </h4>
                                <p class="text-xs sm:text-sm text-gray-600 group-hover:text-gray-700 transition-colors duration-200">
                                    For network-wide crusade registrations
                                </p>
                            </div>
                        </div>
                        <div class="ml-3 text-gray-400 group-hover:text-primary transition-colors duration-200">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </div>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>

<script>
function openVideoModal(videoUrl, title) {
    console.log("Opening video modal with URL:", videoUrl);
    
    const modal = document.getElementById('videoModal');
    const modalTitle = document.getElementById('modalTitle');
    const modalVideo = document.getElementById('modalVideo');
    
    if (!modal || !modalTitle || !modalVideo) {
        console.error('Video modal elements not found');
        return;
    }
    
    modalTitle.textContent = title;
    
    // Store the original video URL for download functionality
    currentVideoUrl = videoUrl;
    
    // Handle different video URL formats
    let finalUrl = videoUrl;
    
    // YouTube URLs
    if (videoUrl.includes('youtube.com') || videoUrl.includes('youtu.be')) {
        finalUrl += (videoUrl.includes('?') ? '&' : '?') + 'autoplay=1&playsinline=0';
    }
    // Amazon S3 URLs
    else if (videoUrl.includes('s3.') || videoUrl.includes('amazonaws.com')) {
        // S3 URLs should work directly
        console.log("Using Amazon S3 URL directly");
    }
    // Loveworld Cloud URLs
    else if (videoUrl.includes('loveworldcloud.com')) {
        // Loveworld URLs should work directly
        console.log("Using Loveworld Cloud URL directly");
    }
    
    console.log("Final video URL:", finalUrl);
    
    // Set the iframe source
    try {
        modalVideo.src = finalUrl;
        
        // Remove hidden class and add flex class
        modal.classList.remove('hidden');
        modal.classList.add('flex');
        
        document.body.style.overflow = 'hidden';
        
        // Force fullscreen on mobile devices
        if (window.innerWidth <= 768) {
            setTimeout(() => {
                try {
                    if (modalVideo.requestFullscreen) {
                        modalVideo.requestFullscreen();
                    } else if (modalVideo.webkitRequestFullscreen) {
                        modalVideo.webkitRequestFullscreen();
                    } else if (modalVideo.mozRequestFullScreen) {
                        modalVideo.mozRequestFullScreen();
                    } else if (modalVideo.msRequestFullscreen) {
                        modalVideo.msRequestFullscreen();
                    }
                } catch (e) {
                    console.error("Error requesting fullscreen:", e);
                }
            }, 500);
        }
    } catch (e) {
        console.error("Error setting video source:", e);
        alert("There was an error loading the video. Please try again.");
    }
}

function closeVideoModal() {
    const modal = document.getElementById('videoModal');
    const modalVideo = document.getElementById('modalVideo');
    
    if (!modal || !modalVideo) {
        console.error('Video modal elements not found');
        return;
    }
    
    modalVideo.src = '';
    modal.classList.add('hidden');
    modal.classList.remove('flex');
    document.body.style.overflow = 'auto';
}

let currentVideoUrl = '';

function downloadVideo() {
    if (!currentVideoUrl) {
        alert('<?php echo $translationService->translateText('No video URL available for download', 'en', $currentLanguage) ?? 'No video URL available for download'; ?>');
        return;
    }
    
    // Extract YouTube video ID and create download link
    let videoId = '';
    if (currentVideoUrl.includes('youtube.com/embed/')) {
        videoId = currentVideoUrl.split('youtube.com/embed/')[1].split('?')[0];
    } else if (currentVideoUrl.includes('youtu.be/')) {
        videoId = currentVideoUrl.split('youtu.be/')[1].split('?')[0];
    }
    
    if (videoId) {
        // Open YouTube video in new tab for download
        const youtubeUrl = `https://www.youtube.com/watch?v=${videoId}`;
        window.open(youtubeUrl, '_blank');
    } else {
        // For non-YouTube videos, try to open the URL directly
        window.open(currentVideoUrl, '_blank');
    }
}

// Close modal when clicking outside
document.getElementById('videoModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeVideoModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeVideoModal();
        closeCrusadeRegistrationModal();
    }
});

// Crusade Registration Modal Functions
function openCrusadeRegistrationModal() {
    const modal = document.getElementById('crusadeRegistrationModal');
    
    if (!modal) {
        console.error('Crusade registration modal not found');
        return false;
    }
    
    // Remove the hidden class (which has display: none !important)
    modal.classList.remove('hidden');
    
    // Add flex class to show the modal
    modal.classList.add('flex');
    
    // Prevent body scrolling
    document.body.style.overflow = 'hidden';
    
    return true;
}

function closeCrusadeRegistrationModal() {
    const modal = document.getElementById('crusadeRegistrationModal');
    
    if (!modal) {
        console.error('Crusade registration modal not found');
        return false;
    }
    
    // Add hidden class back and remove flex class
    modal.classList.add('hidden');
    modal.classList.remove('flex');
    
    // Restore body scrolling
    document.body.style.overflow = 'auto';
    
    return true;
}

function selectRegistrationType(type) {
    // Close the modal first
    closeCrusadeRegistrationModal();
    
    // Handle registration type selection
    console.log('Selected registration type:', type);
    
    // Redirect to appropriate registration page
    setTimeout(() => {
        if (type === 'church') {
            window.location.href = 'notc/register-church';
        } else if (type === 'organisation' || type === 'network') {
            window.location.href = 'notc/register-network';
        } else {
            // Individual registration still goes to the dynamic page
            window.location.href = 'crusade-registration?type=' + type;
        }
    }, 400);
}

// Initialize modal event listeners when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('crusadeRegistrationModal');
    if (modal) {
        // Close modal when clicking outside
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeCrusadeRegistrationModal();
            }
        });
        
        // Add touch event support for mobile
        modal.addEventListener('touchstart', function(e) {
            if (e.target === this) {
                closeCrusadeRegistrationModal();
            }
        }, { passive: true });
    }
});
</script>

<!-- GSAP Animations -->
<script>
// Register ScrollTrigger plugin
gsap.registerPlugin(ScrollTrigger);

// Initialize GSAP animations when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Set initial states for hero elements (invisible)
    gsap.set([
        '.hero-rhapsody', 
        '.hero-description',
        '.hero-btn-1',
        '.hero-btn-2',
        '.hero-scroll-indicator'
    ], {
        opacity: 0,
        y: 50
    });

    // Don't set initial state for rotating text container to avoid affecting existing animation
    gsap.set('.hero-rotating-container', {
        opacity: 0,
        scale: 0.8
    });

    // Hero animations timeline
    const heroTl = gsap.timeline({ delay: 0.5 });

    // Animate Rhapsody title
    heroTl.to('.hero-rhapsody', {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: "back.out(1.7)"
    })
    // Animate rotating text container (after it's been initialized)
    .to('.hero-rotating-container', {
        opacity: 1,
        scale: 1,
        duration: 0.8,
        ease: "back.out(1.7)"
    }, '-=0.3')
    // Animate description
    .to('.hero-description', {
        opacity: 1,
        y: 0,
        duration: 0.8,
        ease: "power2.out"
    }, '-=0.2')
    // Animate buttons
    .to('.hero-btn-1', {
        opacity: 1,
        y: 0,
        duration: 0.6,
        ease: "back.out(1.7)"
    }, '-=0.2')
    .to('.hero-btn-2', {
        opacity: 1,
        y: 0,
        duration: 0.6,
        ease: "back.out(1.7)"
    }, '-=0.4')
    // Animate scroll indicator
    .to('.hero-scroll-indicator', {
        opacity: 1,
        y: 0,
        duration: 0.6,
        ease: "power2.out"
    }, '-=0.2');

    // Night of a Thousand Crusades section animations
    // Set initial states
    gsap.set([
        '.crusades-container',
        '.crusades-title-night',
        '.crusades-title-crusades',
        '.crusades-subtitle',
        '.crusades-detail-date',
        '.crusades-detail-location',
        '.crusades-countdown-title',
        '.crusades-btn-1',
        '.crusades-btn-2'
    ], {
        opacity: 0,
        y: 50
    });

    // Set initial state for countdown container
    gsap.set('.crusades-countdown-container', {
        opacity: 0,
        scale: 0.9
    });

    // Set initial state for countdown grid (will be revealed last)
    gsap.set('.crusades-countdown-grid', {
        opacity: 0,
        y: 30
    });

    // ScrollTrigger animation for Night of a Thousand Crusades
    const crusadesTl = gsap.timeline({
        scrollTrigger: {
            trigger: '.crusades-section',
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse',
            onLeave: () => {
                // Animate elements out when scrolling down past the section
                gsap.to([
                    '.crusades-title-night',
                    '.crusades-title-crusades',
                    '.crusades-subtitle',
                    '.crusades-detail-date',
                    '.crusades-detail-location',
                    '.crusades-countdown-title',
                    '.crusades-countdown-grid',
                    '.crusades-btn-1',
                    '.crusades-btn-2'
                ], {
                    opacity: 0,
                    y: -50,
                    duration: 0.6,
                    ease: "power2.in",
                    stagger: 0.1
                });
            },
            onEnterBack: () => {
                // Animate elements back in when scrolling up into the section
                gsap.to([
                    '.crusades-title-night',
                    '.crusades-title-crusades',
                    '.crusades-subtitle',
                    '.crusades-detail-date',
                    '.crusades-detail-location',
                    '.crusades-countdown-title',
                    '.crusades-countdown-grid',
                    '.crusades-btn-1',
                    '.crusades-btn-2'
                ], {
                    opacity: 1,
                    y: 0,
                    duration: 0.6,
                    ease: "back.out(1.7)",
                    stagger: 0.1
                });
            }
        }
    });

    // Animate container first
    crusadesTl.to('.crusades-container', {
        opacity: 1,
        y: 0,
        duration: 0.8,
        ease: "power2.out"
    })
    // Animate title parts
    .to('.crusades-title-night', {
        opacity: 1,
        y: 0,
        duration: 0.6,
        ease: "back.out(1.7)"
    }, '-=0.4')
    .to('.crusades-title-crusades', {
        opacity: 1,
        y: 0,
        duration: 0.6,
        ease: "back.out(1.7)"
    }, '-=0.2')
    // Animate subtitle
    .to('.crusades-subtitle', {
        opacity: 1,
        y: 0,
        duration: 0.6,
        ease: "power2.out"
    }, '-=0.2')
    // Animate event details
    .to('.crusades-detail-date', {
        opacity: 1,
        y: 0,
        duration: 0.5,
        ease: "power2.out"
    }, '-=0.2')
    .to('.crusades-detail-location', {
        opacity: 1,
        y: 0,
        duration: 0.5,
        ease: "power2.out"
    }, '-=0.3')
    // Animate countdown container
    .to('.crusades-countdown-container', {
        opacity: 1,
        scale: 1,
        duration: 0.6,
        ease: "back.out(1.7)"
    }, '-=0.2')
    // Animate countdown title
    .to('.crusades-countdown-title', {
        opacity: 1,
        y: 0,
        duration: 0.5,
        ease: "power2.out"
    }, '-=0.3')
    // Animate countdown grid last (the timer reveal)
    .to('.crusades-countdown-grid', {
        opacity: 1,
        y: 0,
        duration: 0.8,
        ease: "back.out(1.7)"
    }, '-=0.1')
    // Animate buttons
    .to('.crusades-btn-1', {
        opacity: 1,
        y: 0,
        duration: 0.5,
        ease: "back.out(1.7)"
    }, '-=0.2')
    .to('.crusades-btn-2', {
        opacity: 1,
        y: 0,
        duration: 0.5,
        ease: "back.out(1.7)"
    }, '-=0.3');

         // Mission Trips section glass overlay animation
     // Set initial state for the glass overlay (larger)
     gsap.set('.mission-trips-glass-overlay', {
         scale: 1.3,
         opacity: 0.8
     });

     // ScrollTrigger animation for Mission Trips glass overlay
     gsap.to('.mission-trips-glass-overlay', {
         scale: 1,
         opacity: 1,
         duration: 1.2,
         ease: "power2.out",
         scrollTrigger: {
             trigger: '.mission-trips-section',
             start: 'top 75%',
             end: 'center 50%',
             toggleActions: 'play none none reverse',
             scrub: 1 // Smooth scrubbing effect
         }
     });

     // MyStreamSpace Banner section glass overlay animation
     // Set initial state for the glass overlay (smaller and fade in)
     gsap.set('.mystreamspace-glass-overlay', {
         scale: 0.8,
         opacity: 0,
         y: 50
     });

     // ScrollTrigger animation for MyStreamSpace Banner glass overlay
     gsap.to('.mystreamspace-glass-overlay', {
         scale: 1,
         opacity: 1,
         y: 0,
         duration: 1.5,
         ease: "power3.out",
         scrollTrigger: {
             trigger: '.mystreamspace-banner-section',
             start: 'top 80%',
             end: 'center 60%',
             toggleActions: 'play none none reverse',
             scrub: 0.5 // Smooth scrubbing effect
         }
     });

     // Add subtle hover animations for interactive elements
     gsap.utils.toArray('.hero-btn-1, .hero-btn-2, .crusades-btn-1, .crusades-btn-2').forEach(button => {
         const hover = gsap.to(button, {
             scale: 1.05,
             duration: 0.3,
             ease: "power2.out",
             paused: true
         });

         button.addEventListener('mouseenter', () => hover.play());
         button.addEventListener('mouseleave', () => hover.reverse());
     });




});
</script>

<!-- Stripe and Donation Scripts -->
<script src="https://js.stripe.com/v3/"></script>
<script src="assets/js/donation.js"></script>

<?php include 'includes/footer.php'; ?>