<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

$chatFile = __DIR__ . '/chat-messages.json';

// Initialize chat file if it doesn't exist
if (!file_exists($chatFile)) {
    file_put_contents($chatFile, json_encode(['messages' => []]));
}

// Handle different request methods
$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'GET') {
    // Get messages
    $data = json_decode(file_get_contents($chatFile), true);
    $messages = $data['messages'] ?? [];
    
    // Clean old messages (older than 2 hours)
    $cutoff = time() - (2 * 60 * 60);
    $messages = array_filter($messages, function($msg) use ($cutoff) {
        return ($msg['timestamp'] ?? 0) > $cutoff;
    });
    
    // Keep only last 50 messages
    if (count($messages) > 50) {
        $messages = array_slice($messages, -50);
    }
    
    // Save cleaned messages back
    file_put_contents($chatFile, json_encode(['messages' => array_values($messages)], JSON_PRETTY_PRINT));
    
    echo json_encode(['success' => true, 'messages' => array_values($messages)]);
    
} elseif ($method === 'POST') {
    // Add new message
    $input = json_decode(file_get_contents('php://input'), true);
    
    $name = trim($input['name'] ?? '');
    $message = trim($input['message'] ?? '');
    
    if (empty($name) || empty($message)) {
        echo json_encode(['success' => false, 'error' => 'Name and message are required']);
        exit;
    }
    
    // Sanitize input
    $name = htmlspecialchars($name, ENT_QUOTES, 'UTF-8');
    $message = htmlspecialchars($message, ENT_QUOTES, 'UTF-8');
    
    // Load existing messages
    $data = json_decode(file_get_contents($chatFile), true);
    $messages = $data['messages'] ?? [];
    
    // Create new message
    $newMessage = [
        'id' => time() . '_' . uniqid(),
        'name' => $name,
        'message' => $message,
        'timestamp' => time(),
        'datetime' => date('Y-m-d H:i:s')
    ];
    
    // Add to messages array
    $messages[] = $newMessage;
    
    // Keep only last 50 messages
    if (count($messages) > 50) {
        $messages = array_slice($messages, -50);
    }
    
    // Save to file
    $result = file_put_contents($chatFile, json_encode(['messages' => array_values($messages)], JSON_PRETTY_PRINT));
    
    if ($result !== false) {
        echo json_encode(['success' => true, 'message' => $newMessage]);
    } else {
        echo json_encode(['success' => false, 'error' => 'Failed to save message']);
    }
    
} else {
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
}
?>