<?php
require_once '../includes/config.php';
require_once '../includes/languages.php';
require_once '../includes/TranslationService.php';

// Get translation service instance
$translationService = TranslationService::getInstance();
$currentLanguage = Language::getCurrentLanguage();

// Auto-translate static text content with caching
function autoTranslate($text) {
    global $translationService, $currentLanguage;
    if ($currentLanguage === 'en') {
        return $text;
    }
    return $translationService->translateText($text, 'en', $currentLanguage) ?? $text;
}

// Define static texts for translation
$texts = [
    'live_stream' => 'Live Stream',
    'join_live_broadcast' => 'Join our live broadcast and engage with the global community.',
    'live_now' => 'Live Now',
    'live_chat' => 'Live Chat',
    'type_message' => 'Type your message...',
    'chat_messages_appear' => 'Chat messages will appear here',
    'give_now' => 'Give Now',
    'bank_details' => 'Bank Details',
    'share_feedback' => 'Share Feedback',
    'stream_offline' => 'Stream Offline',
    'stream_offline_message' => 'The live stream is currently offline. Please check back later or contact support if the issue persists.',
    'account_name' => 'Account Name',
    'bank_name' => 'Bank Name',
    'account_number' => 'Account Number',
    'sort_code' => 'Sort code',
    'iban' => 'Iban',
    'routing_number' => 'Routing number',
    'swift_code' => 'Swift code',
    'branch_code' => 'Branch code',
    'send_transfer_details' => 'Please send a copy of your transfer details to',
    'nigeria' => 'Nigeria',
    'united_kingdom' => 'United Kingdom',
    'united_states' => 'United States',
    'south_africa' => 'South Africa',
    'site_title' => 'Rhapsody Crusades',
    'footer_contact' => 'Contact Us',
    'contact_email' => 'Email',
    'uk' => 'UK',
    'usa' => 'USA',
    'free_phone' => 'Free Phone',
    'footer_copyright' => '© 2025 Rhapsody of Realities. All rights reserved.',
    'enter_name_to_chat' => 'Enter your name to join the live chat',
    'enter_your_name' => 'Enter your name',
    'join_chat' => 'Join Chat',
    'chatting_as' => 'Chatting as',
    'change_name' => 'Change Name',
    'name_required' => 'Please enter your name to join the chat',
    'message_sent' => 'Message sent!',
    'failed_to_send' => 'Failed to send message. Please try again.',
    'just_now' => 'Just now',
    'minutes_ago' => 'minutes ago',
    'hours_ago' => 'hours ago',
    'payment_method' => 'Choose Payment Method',
    'card_payment' => 'Card Payment',
    'espees' => 'ESPEES',
    'donation_amount' => 'Donation Amount',
    'enter_amount' => 'Enter amount',
    'pay_now' => 'Pay Now',
    'espees_payment_code' => 'ESPEES Payment Code',
    'use_code_espees' => 'Use this code for your ESPEES payment.',
    'first_name' => 'First Name',
    'last_name' => 'Last Name',
    'email_address' => 'Email Address',
    'support_ministry' => 'Support our live crusade ministry',
    'clear_saved_name' => 'Clear your saved name? You will need to enter it again next time.',
    'clear' => 'Clear',
    'fill_donor_info' => 'Please fill in all donor information fields.',
    'enter_valid_email' => 'Please enter a valid email address.',
    'thank_you_donation' => 'Thank you for your donation! Your payment has been processed successfully.',
    'payment_setup_failed' => 'Payment setup failed',
    'payment_setup_failed_retry' => 'Payment setup failed. Please try again.'
];

// Auto-translate all texts
$translatedTexts = [];
foreach ($texts as $key => $englishText) {
    $translatedTexts[$key] = autoTranslate($englishText);
}
?>
<!DOCTYPE html>
<html lang="<?php echo Language::getCurrentLanguage(); ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $translatedTexts['live_stream']; ?> - <?php echo $translatedTexts['site_title']; ?></title>

    <!-- Favicon -->
    <link rel="icon" type="image/webp" href="../assets/images/favicon.webp">
    <link rel="shortcut icon" type="image/webp" href="../assets/images/favicon.webp">
    <link rel="apple-touch-icon" type="image/webp" href="../assets/images/favicon.webp">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Oswald:wght@200;300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#2563eb',
                        primaryDark: '#1d4ed8',
                        accent: '#fbbf24',
                        gold: '#d97706',
                        secondary: '#ffffff',
                    },
                    fontFamily: {
                        'sans': ['Oswald', 'system-ui', 'sans-serif'],
                        'display': ['Oswald', 'serif'],
                    }
                }
            }
        }
    </script>
</head>
<body class="min-h-screen flex flex-col">

<!-- Page Loader -->
<div id="page-loader" class="fixed inset-0 bg-white flex items-center justify-center z-50">
    <div class="w-12 h-12 border-4 border-gray-200 border-t-primary rounded-full animate-spin"></div>
</div>

<!-- Header -->
<header class="fixed top-0 left-0 right-0 bg-white border-b border-gray-100 z-50">
    <div class="max-w-6xl mx-auto px-4 sm:px-6">
        <div class="flex justify-between items-center py-4 sm:py-6">
            <!-- Logo -->
            <div class="flex-shrink-0">
                <a href="../index.php" class="flex items-center">
                    <img class="h-8 sm:h-10 w-auto" src="../assets/images/logo.webp" alt="<?php echo $translatedTexts['site_title']; ?>">
                </a>
            </div>
            
            <!-- Language Selector -->
            <div class="relative">
                <button type="button" id="language-menu-button" aria-expanded="false" aria-haspopup="true" 
                        class="inline-flex items-center px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium text-gray-600 hover:text-primary transition-colors duration-200 focus:outline-none rounded-lg sm:rounded-none">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="mr-2"><?php echo Language::getAvailableLanguages()[Language::getCurrentLanguage()]; ?></span>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div id="language-menu" role="menu" aria-orientation="vertical" aria-labelledby="language-menu-button" tabindex="-1" 
                     class="hidden origin-top-right absolute right-0 mt-2 w-48 bg-white border border-gray-200 focus:outline-none z-10 max-h-64 overflow-y-auto shadow-lg rounded-lg sm:rounded-none">
                    <?php 
                    // Get current URL parameters and preserve them
                    $currentParams = $_GET;
                    foreach (Language::getAvailableLanguages() as $code => $name): 
                        $currentParams['lang'] = $code;
                        $langUrl = '?' . http_build_query($currentParams);
                    ?>
                    <a href="<?php echo $langUrl; ?>" role="menuitem" 
                       class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors duration-200">
                        <img class="h-4 w-4 mr-3" src="../assets/images/flags/<?php echo $code; ?>.svg" alt="<?php echo $name; ?>">
                        <span><?php echo $name; ?></span>
                        <?php if (Language::getCurrentLanguage() === $code): ?>
                        <svg class="w-4 h-4 ml-auto text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <?php endif; ?>
                    </a>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</header>

<!-- Main Content (with top padding to compensate for fixed header) -->
<main class="pt-20 sm:pt-24 flex-1">

<!-- Live Stream Section -->
<section class="bg-white py-6 sm:py-8 md:py-10 lg:py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6">
        <!-- Page Title -->
        <div class="text-center mb-6 sm:mb-8 md:mb-10 lg:mb-12">
            <h1 class="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 sm:mb-4" style="font-family: 'Oswald', sans-serif;"><?php echo $translatedTexts['live_stream']; ?></h1>
            <div class="w-12 sm:w-16 h-px bg-gradient-to-r from-primary via-accent to-gold mx-auto"></div>
        </div>
        
        <!-- Video and Chat Container -->
        <div class="bg-white border border-gray-200 rounded-lg sm:rounded-none shadow-lg">
            <!-- Mobile Layout: Video on top, chat on bottom -->
            <div class="block lg:hidden">
                <!-- Video Container -->
                <div class="relative bg-black aspect-video rounded-t-lg sm:rounded-t-none">
                    <div class="absolute top-2 left-2 z-10 flex items-center gap-2 bg-primary text-white px-2 py-1 text-xs font-bold rounded-lg sm:rounded-none" style="font-family: 'Oswald', sans-serif;">
                        <span class="relative flex h-1.5 w-1.5">
                            <span class="animate-ping absolute inline-flex h-full w-full bg-white opacity-75 rounded-full"></span>
                            <span class="relative inline-flex h-1.5 w-1.5 bg-white rounded-full"></span>
                        </span>
                        <span class="font-semibold">LIVE</span>
                    </div>
                    
                    <video
                        id="livestream-player-mobile"
                        class="w-full h-full"
                        controls
                        playsinline
                    >
                        <source src="https://2nbyjxnbl53k-hls-live.5centscdn.com/RTV/59a49be6dc0f146c57cd9ee54da323b1.sdp/playlist.m3u8" type="application/x-mpegURL">
                        Your browser does not support the video tag.
                    </video>
                </div>
                
                <!-- Give Button -->
                <div class="bg-white border-t border-gray-200 p-3">
                    <button
                        type="button"
                        id="openGiveModalBtn-mobile"
                        class="w-full bg-primary text-white px-4 py-2.5 hover:bg-primaryDark transition-all duration-300 transform hover:scale-105 font-bold flex items-center justify-center gap-2 text-sm rounded-lg sm:rounded-none shadow-md hover:shadow-lg" style="font-family: 'Oswald', sans-serif;"
                    >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>Give Now</span>
                    </button>
                </div>
                
                <!-- Live Chat for Mobile -->
                <div class="flex flex-col h-[300px] border-t border-gray-200">
                    <!-- Chat Header -->
                    <div class="bg-gray-50 p-3 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-2">
                                <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                </svg>
                                <h3 class="font-bold text-gray-900 text-sm" style="font-family: 'Oswald', sans-serif;"><?php echo $translatedTexts['live_chat']; ?></h3>
                            </div>
                            <div class="flex items-center gap-1">
                                <span class="relative flex h-1.5 w-1.5">
                                    <span class="animate-ping absolute inline-flex h-full w-full bg-primary opacity-75 rounded-full"></span>
                                    <span class="relative inline-flex h-1.5 w-1.5 bg-primary rounded-full"></span>
                                </span>
                                <span class="text-xs text-primary font-medium">Live</span>
                            </div>
                        </div>
                    </div>

                    <!-- Chat Messages Mobile -->
                    <div id="chat-messages-mobile" class="flex-1 overflow-y-auto p-3 space-y-2 bg-gray-50 text-sm">
                        <!-- Welcome message when no messages -->
                    </div>

                    <!-- Name Input Form Mobile -->
                    <div id="name-form-mobile" class="p-3 border-t border-gray-200 bg-white">
                        <div class="text-center mb-3">
                            <p class="text-xs text-gray-600"><?php echo $translatedTexts['enter_name_to_chat']; ?></p>
                        </div>
                        <div class="flex flex-col sm:flex-row gap-2">
                            <input
                                type="text"
                                id="name-input-mobile"
                                class="flex-1 px-3 py-2 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all text-sm rounded-lg sm:rounded-none min-w-0"
                                placeholder="<?php echo $translatedTexts['enter_your_name']; ?>"
                                maxlength="50"
                            >
                            <button
                                type="button"
                                id="join-chat-btn-mobile"
                                class="bg-primary text-white px-4 py-2 hover:bg-primaryDark transition-all duration-300 transform hover:scale-105 font-bold text-sm rounded-lg sm:rounded-none flex-shrink-0 shadow-md hover:shadow-lg" style="font-family: 'Oswald', sans-serif;"
                            >
                                <?php echo $translatedTexts['join_chat']; ?>
                            </button>
                        </div>
                    </div>

                    <!-- Chat Input Form Mobile -->
                    <form id="message-form-mobile" class="p-3 border-t border-gray-200 bg-white hidden">
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center gap-1">
                                <span class="text-xs text-gray-600"><?php echo $translatedTexts['chatting_as']; ?>:</span>
                                <span id="chat-username-mobile" class="text-xs font-medium text-primary truncate max-w-[120px]"></span>
                            </div>
                            <div class="flex gap-1">
                                <button type="button" id="change-name-btn-mobile" class="text-xs text-gray-500 hover:text-primary whitespace-nowrap">
                                    <?php echo $translatedTexts['change_name']; ?>
                                </button>
                                <button type="button" id="clear-name-btn-mobile" class="text-xs text-red-500 hover:text-red-700 whitespace-nowrap" title="Clear saved name">
                                    Clear
                                </button>
                            </div>
                        </div>
                        <div class="flex items-center gap-2">
                            <input
                                type="text"
                                id="message-input-mobile"
                                class="flex-1 px-3 py-2 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all text-sm rounded-lg sm:rounded-none min-w-0"
                                placeholder="<?php echo $translatedTexts['type_message']; ?>"
                                maxlength="500"
                            >
                            <button
                                type="submit"
                                class="bg-primary text-white p-2.5 hover:bg-primaryDark transition-all duration-300 transform hover:scale-105 rounded-lg sm:rounded-none flex-shrink-0 shadow-md hover:shadow-lg"
                            >
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                </svg>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Desktop Layout: Video left, chat right -->
            <div class="hidden lg:grid lg:grid-cols-3">
                <!-- Video and Give Button Container -->
                <div class="lg:col-span-2">
                    <!-- Video Container -->
                    <div class="relative bg-black aspect-video rounded-tl-lg sm:rounded-tl-none">
                        <div class="absolute top-4 left-4 z-10 flex items-center gap-2 bg-primary text-white px-3 py-1 text-sm font-bold rounded-lg sm:rounded-none" style="font-family: 'Oswald', sans-serif;">
                            <span class="relative flex h-2 w-2">
                                <span class="animate-ping absolute inline-flex h-full w-full bg-white opacity-75 rounded-full"></span>
                                <span class="relative inline-flex h-2 w-2 bg-white rounded-full"></span>
                            </span>
                            <span class="font-semibold">LIVE</span>
                        </div>
                        
                        <video
                            id="livestream-player"
                            class="w-full h-full"
                            controls
                            playsinline
                        >
                            <source src="https://2nbyjxnbl53k-hls-live.5centscdn.com/RTV/59a49be6dc0f146c57cd9ee54da323b1.sdp/playlist.m3u8" type="application/x-mpegURL">
                            Your browser does not support the video tag.
                        </video>
                    </div>
                    
                    <!-- Give Button under video -->
                    <div class="bg-white border-t border-gray-200 p-4">
                        <button
                            type="button"
                            id="openGiveModalBtn"
                            class="w-full bg-primary text-white px-6 py-3 hover:bg-primaryDark transition-all duration-300 transform hover:scale-105 font-bold flex items-center justify-center gap-3 rounded-lg sm:rounded-none shadow-md hover:shadow-lg" style="font-family: 'Oswald', sans-serif;"
                        >
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span>Give Now</span>
                        </button>
                    </div>
                </div>

                <!-- Live Chat Container Desktop -->
                <div class="flex flex-col h-[500px] rounded-tr-lg sm:rounded-tr-none rounded-br-lg sm:rounded-br-none">
                    <!-- Chat Header -->
                    <div class="bg-gray-50 p-3 lg:p-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-2">
                                <svg class="w-4 h-4 lg:w-5 lg:h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                </svg>
                                <h3 class="font-bold text-gray-900 text-sm lg:text-base" style="font-family: 'Oswald', sans-serif;"><?php echo $translatedTexts['live_chat']; ?></h3>
                            </div>
                            <div class="flex items-center gap-1">
                                <span class="relative flex h-1.5 w-1.5 lg:h-2 lg:w-2">
                                    <span class="animate-ping absolute inline-flex h-full w-full bg-primary opacity-75 rounded-full"></span>
                                    <span class="relative inline-flex h-1.5 w-1.5 lg:h-2 lg:w-2 bg-primary rounded-full"></span>
                                </span>
                                <span class="text-xs text-primary font-medium">Live</span>
                            </div>
                        </div>
                    </div>

                    <!-- Chat Messages -->
                    <div id="chat-messages" class="flex-1 overflow-y-auto p-3 lg:p-4 space-y-2 lg:space-y-3 bg-gray-50 text-sm lg:text-base">
                        <!-- Welcome message when no messages -->
                    </div>

                    <!-- Name Input Form (shown initially) -->
                    <div id="name-form" class="p-3 lg:p-4 border-t border-gray-200 bg-white">
                        <div class="text-center mb-3">
                            <p class="text-xs lg:text-sm text-gray-600"><?php echo $translatedTexts['enter_name_to_chat']; ?></p>
                        </div>
                        <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-2">
                            <input
                                type="text"
                                id="name-input"
                                class="flex-1 px-3 py-2 lg:px-4 lg:py-2 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all text-sm lg:text-base rounded-lg sm:rounded-none"
                                placeholder="<?php echo $translatedTexts['enter_your_name']; ?>"
                                maxlength="50"
                            >
                            <button
                                type="button"
                                id="join-chat-btn"
                                class="bg-primary text-white px-4 py-2 lg:px-6 lg:py-2 hover:bg-primaryDark transition-all duration-300 transform hover:scale-105 font-bold text-sm lg:text-base rounded-lg sm:rounded-none whitespace-nowrap shadow-md hover:shadow-lg" style="font-family: 'Oswald', sans-serif;"
                            >
                                <?php echo $translatedTexts['join_chat']; ?>
                            </button>
                        </div>
                    </div>

                    <!-- Chat Input Form (shown after name is entered) -->
                    <form id="message-form" class="p-3 lg:p-4 border-t border-gray-200 bg-white hidden">
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center gap-1 lg:gap-2">
                                <span class="text-xs lg:text-sm text-gray-600"><?php echo $translatedTexts['chatting_as']; ?>:</span>
                                <span id="chat-username" class="text-xs lg:text-sm font-medium text-primary truncate max-w-[120px] lg:max-w-none"></span>
                            </div>
                            <div class="flex gap-1 lg:gap-2">
                                <button type="button" id="change-name-btn" class="text-xs text-gray-500 hover:text-primary whitespace-nowrap">
                                    <?php echo $translatedTexts['change_name']; ?>
                                </button>
                                <button type="button" id="clear-name-btn" class="text-xs text-red-500 hover:text-red-700 whitespace-nowrap" title="Clear saved name">
                                    Clear
                                </button>
                            </div>
                        </div>
                        <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-2">
                            <input
                                type="text"
                                id="message-input"
                                class="flex-1 px-3 py-2 lg:px-4 lg:py-2 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all text-sm lg:text-base rounded-lg sm:rounded-none"
                                placeholder="<?php echo $translatedTexts['type_message']; ?>"
                                maxlength="500"
                            >
                            <button
                                type="submit"
                                class="bg-primary text-white p-2 lg:p-2.5 hover:bg-primaryDark transition-all duration-300 transform hover:scale-105 rounded-lg sm:rounded-none flex-shrink-0 shadow-md hover:shadow-lg"
                            >
                                <svg class="w-4 h-4 lg:w-5 lg:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                </svg>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

</main>

<!-- Give Modal -->
<div id="giveModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="absolute inset-0 bg-black/50 backdrop-blur-sm"></div>
    <div class="relative w-full h-full flex items-center justify-center p-4">
        <div class="relative bg-white max-w-md w-full mx-4 shadow-xl rounded-lg sm:rounded-none">
            <!-- Close Button -->
            <button
                id="closeGiveModalBtn"
                class="absolute top-4 right-4 text-gray-400 hover:text-primary transition-colors p-2 hover:bg-gray-100 z-10 rounded-lg sm:rounded-none"
            >
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>

            <!-- Modal Content -->
            <div class="p-8">
                <!-- Header -->
                <div class="text-center mb-6">
                    <div class="w-16 h-16 bg-primary mx-auto mb-4 flex items-center justify-center rounded-lg sm:rounded-none shadow-lg">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl sm:text-2xl font-bold text-gray-900 mb-2" style="font-family: 'Oswald', sans-serif;"><?php echo $translatedTexts['give_now']; ?></h3>
                    <p class="text-sm sm:text-base text-gray-600"><?php echo $translatedTexts['support_ministry']; ?></p>
                </div>

                <!-- Donor Info Form (only for card payments) -->
                <form id="donor-info-form" class="space-y-4 mb-6 hidden">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="donor_first_name" class="block text-xs sm:text-sm font-bold text-gray-700 mb-1" style="font-family: 'Oswald', sans-serif;"><?php echo $translatedTexts['first_name']; ?> *</label>
                            <input type="text" id="donor_first_name" name="donor_first_name" required
                                   class="w-full px-3 py-2 border border-gray-300 focus:border-primary focus:outline-none transition-all rounded-lg sm:rounded-none text-sm">
                        </div>
                        <div>
                            <label for="donor_last_name" class="block text-xs sm:text-sm font-bold text-gray-700 mb-1" style="font-family: 'Oswald', sans-serif;"><?php echo $translatedTexts['last_name']; ?> *</label>
                            <input type="text" id="donor_last_name" name="donor_last_name" required
                                   class="w-full px-3 py-2 border border-gray-300 focus:border-primary focus:outline-none transition-all rounded-lg sm:rounded-none text-sm">
                        </div>
                    </div>
                    <div>
                        <label for="donor_email" class="block text-xs sm:text-sm font-bold text-gray-700 mb-1" style="font-family: 'Oswald', sans-serif;"><?php echo $translatedTexts['email_address']; ?> *</label>
                        <input type="email" id="donor_email" name="donor_email" required
                               class="w-full px-3 py-2 border border-gray-300 focus:border-primary focus:outline-none transition-all rounded-lg sm:rounded-none text-sm">
                    </div>
                </form>

                <!-- Payment Method Selection -->
                <div class="space-y-4 mb-6">
                    <h4 class="text-base sm:text-lg font-bold text-gray-900" style="font-family: 'Oswald', sans-serif;"><?php echo $translatedTexts['payment_method']; ?></h4>
                    <div class="space-y-3">
                        <label class="flex items-center p-3 border border-gray-200 hover:border-primary/50 transition-colors cursor-pointer rounded-lg sm:rounded-none">
                            <input type="radio" name="payment_method" value="card" class="w-4 h-4 text-primary border-gray-300 focus:ring-primary">
                            <div class="ml-3 flex items-center gap-2">
                                <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
                                </svg>
                                <span class="text-gray-900 text-sm sm:text-base font-medium"><?php echo $translatedTexts['card_payment']; ?></span>
                            </div>
                        </label>
                        <label class="flex items-center p-3 border border-gray-200 hover:border-primary/50 transition-colors cursor-pointer rounded-lg sm:rounded-none">
                            <input type="radio" name="payment_method" value="espees" class="w-4 h-4 text-primary border-gray-300 focus:ring-primary">
                            <div class="ml-3 flex items-center gap-2">
                                <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <span class="text-gray-900 text-sm sm:text-base font-medium"><?php echo $translatedTexts['espees']; ?></span>
                            </div>
                        </label>
                    </div>
                </div>

                <!-- Amount Section (for card payment) -->
                <div id="amount-section" class="hidden mb-6">
                    <label for="donation_amount" class="block text-xs sm:text-sm font-bold text-gray-700 mb-2" style="font-family: 'Oswald', sans-serif;"><?php echo $translatedTexts['donation_amount']; ?></label>
                    <div class="relative">
                        <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                        <input type="number" id="donation_amount" name="donation_amount" min="1" step="0.01"
                               class="w-full pl-8 pr-4 py-3 border border-gray-300 focus:border-primary focus:outline-none transition-all rounded-lg sm:rounded-none text-sm"
                               placeholder="<?php echo $translatedTexts['enter_amount']; ?>">
                    </div>
                    
                    <!-- Pay Now Button -->
                    <div id="pay-now-section" class="hidden mt-4">
                        <button type="button" id="pay-now-btn" 
                                class="w-full bg-primary text-white py-3 px-4 hover:bg-primaryDark transition-all duration-300 transform hover:scale-105 font-bold rounded-lg sm:rounded-none shadow-md hover:shadow-lg" style="font-family: 'Oswald', sans-serif;">
                            <?php echo $translatedTexts['pay_now']; ?>
                        </button>
                    </div>
                </div>

                <!-- ESPEES Section -->
                <div id="espees-section" class="hidden mb-6">
                    <div class="bg-blue-50 border border-primary p-6 text-center rounded-lg sm:rounded-none">
                        <h4 class="text-base sm:text-lg font-bold text-primary mb-3" style="font-family: 'Oswald', sans-serif;"><?php echo $translatedTexts['espees_payment_code']; ?></h4>
                        <div class="bg-white border border-primary p-4 rounded-lg sm:rounded-none">
                            <span class="text-xl sm:text-2xl font-bold text-primary tracking-wider" style="font-family: 'Oswald', sans-serif;">RORETC</span>
                        </div>
                        <p class="text-xs sm:text-sm text-primary mt-3">
                            <?php echo $translatedTexts['use_code_espees']; ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Footer -->
<footer class="bg-white border-t border-gray-200 py-16 mt-auto">
    <div class="max-w-6xl mx-auto px-6">
        <div class="text-center space-y-12">
            <!-- Contact Call-to-Action -->
            <div class="bg-gradient-to-r from-primary to-primaryDark rounded-lg sm:rounded-none p-6 sm:p-8 md:p-12 text-white relative overflow-hidden">
                <!-- Decorative background elements - scaled for mobile -->
                <div class="absolute top-0 right-0 w-16 h-16 sm:w-32 sm:h-32 bg-accent bg-opacity-10 rounded-full -mr-8 sm:-mr-16 -mt-8 sm:-mt-16"></div>
                <div class="absolute bottom-0 left-0 w-12 h-12 sm:w-24 sm:h-24 bg-gold bg-opacity-10 rounded-full -ml-6 sm:-ml-12 -mb-6 sm:-mb-12"></div>
                
                <div class="relative z-10 text-center space-y-4 sm:space-y-6">
                    <h3 class="text-2xl sm:text-3xl md:text-4xl font-bold"><?php 
                        $currentLanguage = Language::getCurrentLanguage();
                        if ($currentLanguage === 'en') {
                            echo 'Ready to Join Us?';
                        } else {
                            $translationService = TranslationService::getInstance();
                            echo $translationService->translateText('Ready to Join Us?', 'en', $currentLanguage) ?? 'Ready to Join Us?';
                        }
                    ?></h3>
                    
                    <p class="text-base sm:text-lg md:text-xl text-blue-100 max-w-2xl mx-auto leading-relaxed px-2 sm:px-0"><?php 
                        if ($currentLanguage === 'en') {
                            echo 'Connect with us today and be part of something extraordinary. Whether you have questions, want to participate, or need assistance, we\'re here for you.';
                        } else {
                            echo $translationService->translateText('Connect with us today and be part of something extraordinary. Whether you have questions, want to participate, or need assistance, we\'re here for you.', 'en', $currentLanguage) ?? 'Connect with us today and be part of something extraordinary. Whether you have questions, want to participate, or need assistance, we\'re here for you.';
                        }
                    ?></p>
                    
                    <!-- Contact Options -->
                    <div class="flex flex-col sm:flex-row gap-4 sm:gap-6 md:gap-8 items-center justify-center">
                    <!-- Email -->
                        <a href="mailto:<EMAIL>" class="w-full sm:w-auto flex items-center justify-center bg-white bg-opacity-10 hover:bg-opacity-20 backdrop-blur-sm px-4 sm:px-6 py-4 rounded-lg sm:rounded-none transition-all duration-200 group min-h-[48px]">
                            <svg class="w-5 h-5 mr-3 text-accent group-hover:text-gold transition-colors duration-200 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            <span class="font-medium text-sm sm:text-base break-all sm:break-normal"><EMAIL></span>
                        </a>
                        
                        <!-- Phone -->
                        <a href="tel:+14696561284" class="w-full sm:w-auto flex items-center justify-center bg-accent hover:bg-gold text-gray-900 px-4 sm:px-6 py-4 rounded-lg sm:rounded-none transition-all duration-200 font-medium shadow-lg hover:shadow-xl group min-h-[48px]">
                            <svg class="w-5 h-5 mr-3 group-hover:rotate-12 transition-transform duration-200 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                            <span class="text-sm sm:text-base">+****************</span>
                        </a>
                        </div>

                    <p class="text-blue-200 text-sm sm:text-base px-2 sm:px-0"><?php 
                        if ($currentLanguage === 'en') {
                            echo 'We\'re excited to hear from you and answer any questions you may have!';
                        } else {
                            echo $translationService->translateText('We\'re excited to hear from you and answer any questions you may have!', 'en', $currentLanguage) ?? 'We\'re excited to hear from you and answer any questions you may have!';
                        }
                    ?></p>
                </div>
            </div>

            <!-- Copyright -->
            <div class="pt-8 border-t border-gray-100">
                <p class="text-sm text-gray-500"><?php echo $translationService->translateText('© 2025 Rhapsody of Realities. All rights reserved.', 'en', $currentLanguage) ?? '© 2025 Rhapsody of Realities. All rights reserved.'; ?></p>
            </div>
        </div>
    </div>
</footer>

<!-- Scroll to top button -->
<button id="scroll-to-top" class="fixed bottom-6 right-6 h-12 w-12 bg-white border border-gray-200 text-gray-400 hover:text-primary hover:border-primary flex items-center justify-center opacity-0 invisible transition-all duration-300 focus:outline-none z-50 shadow-lg hover:shadow-xl rounded-lg sm:rounded-none">
    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
    </svg>
</button>

<!-- HLS.js for HLS streaming support -->
<script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>

<!-- Stripe.js -->
<script src="https://js.stripe.com/v3/"></script>

<script>
// Language selector functionality
document.addEventListener('DOMContentLoaded', function() {
    const button = document.getElementById('language-menu-button');
    const menu = document.getElementById('language-menu');
    
    // Explicitly set initial state
    menu.style.display = 'none';
    button.setAttribute('aria-expanded', 'false');

    // Toggle menu
    button.addEventListener('click', function(e) {
        e.stopPropagation();
        const isHidden = menu.style.display === 'none';
        menu.style.display = isHidden ? 'block' : 'none';
        button.setAttribute('aria-expanded', isHidden ? 'true' : 'false');
    });

    // Close menu when clicking outside
    document.addEventListener('click', function(event) {
        if (menu.style.display === 'block' && !menu.contains(event.target)) {
            menu.style.display = 'none';
            button.setAttribute('aria-expanded', 'false');
        }
    });
});

// Page loader
window.addEventListener('load', function() {
    document.getElementById('page-loader').style.display = 'none';
    document.body.classList.add('loaded');
});

document.addEventListener('DOMContentLoaded', function() {
    // Give Modal functionality
    const giveModal = document.getElementById('giveModal');
    const openGiveBtn = document.getElementById('openGiveModalBtn');
    const openGiveBtnMobile = document.getElementById('openGiveModalBtn-mobile');
    const closeGiveBtn = document.getElementById('closeGiveModalBtn');
    const paymentMethodRadios = document.querySelectorAll('input[name="payment_method"]');
    const amountSection = document.getElementById('amount-section');
    const espeesSection = document.getElementById('espees-section');
    const donationAmount = document.getElementById('donation_amount');
    const payNowSection = document.getElementById('pay-now-section');
    const payNowBtn = document.getElementById('pay-now-btn');
    
    let paymentCompleted = false;
    let stripe = null;
    let elements = null;

    // Function to open give modal
    function openGiveModal() {
        giveModal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    }

    // Open give modal - Desktop
    if (openGiveBtn) {
        openGiveBtn.addEventListener('click', openGiveModal);
    }

    // Open give modal - Mobile
    if (openGiveBtnMobile) {
        openGiveBtnMobile.addEventListener('click', openGiveModal);
    }

    // Close give modal
    closeGiveBtn.addEventListener('click', function() {
        giveModal.classList.add('hidden');
        document.body.style.overflow = '';
        resetModal();
    });

    // Close modal when clicking outside
    giveModal.addEventListener('click', function(e) {
        if (e.target === giveModal) {
            closeGiveBtn.click();
        }
    });
    
    // Handle payment method selection
    paymentMethodRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            const donorForm = document.getElementById('donor-info-form');
            amountSection.classList.add('hidden');
            espeesSection.classList.add('hidden');
            payNowSection.classList.add('hidden');
            paymentCompleted = false;
            
            if (this.value === 'card') {
                donorForm.classList.remove('hidden');
                amountSection.classList.remove('hidden');
                donationAmount.setAttribute('required', 'required');
                document.getElementById('donor_first_name').focus();
            } else if (this.value === 'espees') {
                donorForm.classList.add('hidden');
                espeesSection.classList.remove('hidden');
                donationAmount.removeAttribute('required');
                paymentCompleted = true;
            }
        });
    });
    
    // Handle amount input - show Pay Now button when valid amount is entered
    donationAmount.addEventListener('input', function() {
        const selectedPaymentMethod = document.querySelector('input[name="payment_method"]:checked');
        
        if (selectedPaymentMethod && selectedPaymentMethod.value === 'card') {
            const amount = parseFloat(this.value);
            
            if (amount && amount > 0) {
                payNowSection.classList.remove('hidden');
                payNowBtn.textContent = `Pay $${amount.toFixed(2)} Now`;
            } else {
                payNowSection.classList.add('hidden');
            }
        }
    });
    
    // Handle Pay Now button click
    payNowBtn.addEventListener('click', function() {
        const amount = parseFloat(donationAmount.value);
        const firstName = document.getElementById('donor_first_name').value.trim();
        const lastName = document.getElementById('donor_last_name').value.trim();
        const email = document.getElementById('donor_email').value.trim();
        
        if (!firstName || !lastName || !email) {
            alert('<?php echo $translatedTexts['fill_donor_info']; ?>');
            return;
        }
        
        if (!email.includes('@')) {
            alert('<?php echo $translatedTexts['enter_valid_email']; ?>');
            return;
        }
        
        // Validate amount
        if (!amount || isNaN(amount) || amount <= 0) {
            alert('Please enter a valid donation amount greater than $0.');
            donationAmount.focus();
            return;
        }
        
        if (amount < 1) {
            alert('Minimum donation amount is $1.');
            donationAmount.focus();
            return;
        }
        
        if (!paymentCompleted) {
            processStripePayment(amount, firstName + ' ' + lastName, email);
        }
    });
    
    function resetModal() {
        // Reset form
        const donorForm = document.getElementById('donor-info-form');
        donorForm.reset();
        donorForm.classList.add('hidden');
        paymentMethodRadios.forEach(radio => radio.checked = false);
        amountSection.classList.add('hidden');
        espeesSection.classList.add('hidden');
        payNowSection.classList.add('hidden');
        paymentCompleted = false;
    }
    
    function processStripePayment(amount, donorName, donorEmail) {
        // Show payment processing modal
        const paymentModal = document.createElement('div');
        paymentModal.className = 'fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50';
        paymentModal.innerHTML = `
            <div class="bg-white max-w-md w-full mx-6 p-8 shadow-lg">
                <div class="text-center">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Initializing Payment</h3>
                    <div class="mb-6">
                        <div class="animate-spin h-12 w-12 border-b-2 border-red-600 mx-auto"></div>
                    </div>
                    <p class="text-gray-600">Amount: $${amount.toFixed(2)}</p>
                    <p class="text-sm text-gray-500 mt-2">Setting up secure payment...</p>
                </div>
            </div>
        `;
        
        document.body.appendChild(paymentModal);
        
        // Create payment intent
        fetch('../process-donation.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                amount: amount,
                donor_name: donorName,
                donor_email: donorEmail
            })
        })
        .then(response => response.json())
        .then(data => {
            document.body.removeChild(paymentModal);
            
            if (data.success) {
                // Initialize Stripe
                stripe = Stripe(data.publishable_key);
                elements = stripe.elements();
                
                // Create payment modal with card input
                const stripeModal = document.createElement('div');
                stripeModal.className = 'fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50';
                stripeModal.innerHTML = `
                    <div class="bg-white max-w-md w-full mx-6 p-8 shadow-lg">
                        <div class="text-center mb-6">
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">Complete Payment</h3>
                            <p class="text-gray-600">Amount: $${amount.toFixed(2)}</p>
                        </div>
                        
                        <form id="payment-form">
                            <div id="card-element" class="p-3 border border-gray-300 mb-4">
                                <!-- Stripe card element will be inserted here -->
                            </div>
                            <div id="card-errors" role="alert" class="text-red-600 text-sm mb-4"></div>
                            
                            <div class="flex space-x-3">
                                <button type="submit" id="submit-payment" 
                                        class="flex-1 bg-red-600 text-white py-2 px-4 hover:bg-red-700 transition-colors">
                                    Pay $${amount.toFixed(2)}
                                </button>
                                <button type="button" id="cancel-payment" 
                                        class="flex-1 bg-gray-300 text-gray-700 py-2 px-4 hover:bg-gray-400 transition-colors">
                                    Cancel
                                </button>
                            </div>
                        </form>
                    </div>
                `;
                
                document.body.appendChild(stripeModal);
                
                // Create card element
                const cardElement = elements.create('card');
                cardElement.mount('#card-element');
                
                // Handle form submission
                const paymentForm = document.getElementById('payment-form');
                paymentForm.addEventListener('submit', async (event) => {
                    event.preventDefault();
                    
                    const submitButton = document.getElementById('submit-payment');
                    submitButton.disabled = true;
                    submitButton.textContent = 'Processing...';
                    
                    const {error, paymentIntent} = await stripe.confirmCardPayment(data.client_secret, {
                        payment_method: {
                            card: cardElement,
                            billing_details: {
                                name: donorName,
                                email: donorEmail
                            }
                        }
                    });
                    
                    if (error) {
                        document.getElementById('card-errors').textContent = error.message;
                        submitButton.disabled = false;
                        submitButton.textContent = 'Pay $' + amount.toFixed(2);
                    } else {
                        // Payment successful
                        paymentCompleted = true;
                        
                        // Remove modal
                        document.body.removeChild(stripeModal);
                        
                        // Show success message and close give modal
                        alert('<?php echo $translatedTexts['thank_you_donation']; ?>');
                        closeGiveBtn.click();
                    }
                });
                
                // Handle cancel button
                document.getElementById('cancel-payment').addEventListener('click', function() {
                    document.body.removeChild(stripeModal);
                });
            } else {
                alert('<?php echo $translatedTexts['payment_setup_failed']; ?>: ' + data.error);
            }
        })
        .catch(error => {
            document.body.removeChild(paymentModal);
            console.error('Error:', error);
            alert('<?php echo $translatedTexts['payment_setup_failed_retry']; ?>');
        });
    }

    // Video player setup
    const video = document.getElementById('livestream-player');
    const streamUrl = 'https://2nbyjxnbl53k-hls-live.5centscdn.com/RTV/59a49be6dc0f146c57cd9ee54da323b1.sdp/playlist.m3u8';

    if (Hls.isSupported()) {
        const hls = new Hls({
            maxBufferLength: 30,
            maxMaxBufferLength: 600,
            manifestLoadingTimeOut: 10000,
            manifestLoadingMaxRetry: 3,
            levelLoadingTimeOut: 10000,
            levelLoadingMaxRetry: 3
        });

        hls.loadSource(streamUrl);
        hls.attachMedia(video);

        hls.on(Hls.Events.MANIFEST_PARSED, function() {
            // Don't auto-play, let user click play button
            console.log('Video stream loaded and ready to play');
        });

        // Error handling
        hls.on(Hls.Events.ERROR, function(event, data) {
            if (data.fatal) {
                switch(data.type) {
                    case Hls.ErrorTypes.NETWORK_ERROR:
                        console.log("Network error, trying to recover...");
                        hls.startLoad();
                        break;
                    case Hls.ErrorTypes.MEDIA_ERROR:
                        console.log("Media error, trying to recover...");
                        hls.recoverMediaError();
                        break;
                    default:
                        console.error("Unrecoverable error");
                        showOfflineMessage();
                        break;
                }
            }
        });
    }
    // For Safari (native HLS support)
    else if (video.canPlayType('application/vnd.apple.mpegurl')) {
        video.src = streamUrl;
        video.addEventListener('loadedmetadata', function() {
            // Don't auto-play, let user click play button
            console.log('Video metadata loaded and ready to play');
        });
    }

    function showOfflineMessage() {
        const offlineMessage = document.createElement('div');
        offlineMessage.className = 'absolute inset-0 flex items-center justify-center bg-black/90 rounded-lg';
        offlineMessage.innerHTML = `
            <div class="text-center text-white p-8 max-w-md mx-auto">
                <div class="w-20 h-20 mx-auto mb-6 rounded-full bg-primary/20 flex items-center justify-center">
                    <svg class="w-10 h-10 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                    </svg>
                </div>
                <h3 class="text-2xl font-medium mb-4 text-primary"><?php echo $translatedTexts['stream_offline']; ?></h3>
                <p class="text-white/80"><?php echo $translatedTexts['stream_offline_message']; ?></p>
            </div>
        `;
        video.parentNode.appendChild(offlineMessage);
    }

    // Simple Local Storage Chat System
    const messageForm = document.getElementById('message-form');
    const messageInput = document.getElementById('message-input');
    const chatMessages = document.getElementById('chat-messages');
    const nameForm = document.getElementById('name-form');
    const nameInput = document.getElementById('name-input');
    const joinChatBtn = document.getElementById('join-chat-btn');
    const chatUsername = document.getElementById('chat-username');
    const changeNameBtn = document.getElementById('change-name-btn');
    const clearNameBtn = document.getElementById('clear-name-btn');
    
    // Mobile chat elements
    const messageFormMobile = document.getElementById('message-form-mobile');
    const messageInputMobile = document.getElementById('message-input-mobile');
    const chatMessagesMobile = document.getElementById('chat-messages-mobile');
    const nameFormMobile = document.getElementById('name-form-mobile');
    const nameInputMobile = document.getElementById('name-input-mobile');
    const joinChatBtnMobile = document.getElementById('join-chat-btn-mobile');
    const chatUsernameMobile = document.getElementById('chat-username-mobile');
    const changeNameBtnMobile = document.getElementById('change-name-btn-mobile');
    const clearNameBtnMobile = document.getElementById('clear-name-btn-mobile');
    
    let currentUsername = '';
    let chatMessagesArray = [];
    let chatPollInterval;
    
    // Initialize chat
    loadStoredUsername();
    loadMessages();
    
    // Load stored username
    function loadStoredUsername() {
        try {
            const storedName = localStorage.getItem('livechat_username');
            if (storedName && storedName.trim()) {
                currentUsername = storedName.trim();
                nameInput.value = currentUsername;
                // Auto-join chat if name is stored
                joinChat();
            }
        } catch (error) {
            console.error('Error loading stored username:', error);
        }
    }
    
    // Save username to localStorage
    function saveUsername(username) {
        try {
            localStorage.setItem('livechat_username', username);
        } catch (error) {
            console.error('Error saving username:', error);
        }
    }
    
    // Join chat function
    function joinChat() {
        if (currentUsername) {
            // Update both desktop and mobile elements
            if (chatUsername) chatUsername.textContent = currentUsername;
            if (chatUsernameMobile) chatUsernameMobile.textContent = currentUsername;
            
            // Hide name forms and show message forms
            if (nameForm) nameForm.classList.add('hidden');
            if (nameFormMobile) nameFormMobile.classList.add('hidden');
            if (messageForm) messageForm.classList.remove('hidden');
            if (messageFormMobile) messageFormMobile.classList.remove('hidden');
            
            // Focus the appropriate input
            if (messageInput) messageInput.focus();
            else if (messageInputMobile) messageInputMobile.focus();
            
            startChatPolling();
        }
    }
    
    // Function to handle join chat
    function handleJoinChat(nameInput) {
        const name = nameInput.value.trim();
        if (name) {
            currentUsername = name;
            saveUsername(name); // Save to localStorage
            joinChat();
        } else {
            alert('<?php echo $translatedTexts['name_required']; ?>');
        }
    }

    // Name form functionality - Desktop
    if (joinChatBtn && nameInput) {
        joinChatBtn.addEventListener('click', function() {
            handleJoinChat(nameInput);
        });
        
        nameInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                handleJoinChat(nameInput);
            }
        });
    }
    
    // Name form functionality - Mobile
    if (joinChatBtnMobile && nameInputMobile) {
        joinChatBtnMobile.addEventListener('click', function() {
            handleJoinChat(nameInputMobile);
        });
        
        nameInputMobile.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                handleJoinChat(nameInputMobile);
            }
        });
    }
    
    // Function to handle change name
    function handleChangeName() {
        if (messageForm) messageForm.classList.add('hidden');
        if (messageFormMobile) messageFormMobile.classList.add('hidden');
        if (nameForm) nameForm.classList.remove('hidden');
        if (nameFormMobile) nameFormMobile.classList.remove('hidden');
        
        if (nameInput) {
            nameInput.value = currentUsername;
            nameInput.focus();
            nameInput.select();
        }
        if (nameInputMobile) {
            nameInputMobile.value = currentUsername;
            nameInputMobile.focus();
            nameInputMobile.select();
        }
        stopChatPolling();
    }

    // Function to handle clear name
    function handleClearName() {
        if (confirm('<?php echo $translatedTexts['clear_saved_name']; ?>')) {
            try {
                localStorage.removeItem('livechat_username');
                currentUsername = '';
                if (nameInput) nameInput.value = '';
                if (nameInputMobile) nameInputMobile.value = '';
                if (messageForm) messageForm.classList.add('hidden');
                if (messageFormMobile) messageFormMobile.classList.add('hidden');
                if (nameForm) nameForm.classList.remove('hidden');
                if (nameFormMobile) nameFormMobile.classList.remove('hidden');
                if (nameInput) nameInput.focus();
                else if (nameInputMobile) nameInputMobile.focus();
                stopChatPolling();
            } catch (error) {
                console.error('Error clearing username:', error);
            }
        }
    }

    // Function to handle send message
    function handleSendMessage(messageInput) {
        const message = messageInput.value.trim();
        if (message && currentUsername) {
            sendMessage(message);
        }
    }

    // Change name functionality - Desktop
    if (changeNameBtn) {
        changeNameBtn.addEventListener('click', handleChangeName);
    }
    
    // Change name functionality - Mobile
    if (changeNameBtnMobile) {
        changeNameBtnMobile.addEventListener('click', handleChangeName);
    }
    
    // Clear name functionality - Desktop
    if (clearNameBtn) {
        clearNameBtn.addEventListener('click', handleClearName);
    }
    
    // Clear name functionality - Mobile
    if (clearNameBtnMobile) {
        clearNameBtnMobile.addEventListener('click', handleClearName);
    }
    
    // Message form functionality - Desktop
    if (messageForm && messageInput) {
        messageForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleSendMessage(messageInput);
        });
    }
    
    // Message form functionality - Mobile
    if (messageFormMobile && messageInputMobile) {
        messageFormMobile.addEventListener('submit', function(e) {
            e.preventDefault();
            handleSendMessage(messageInputMobile);
        });
    }
    
    // Load messages from server
    function loadMessages() {
        fetch('chat-api.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    chatMessagesArray = data.messages || [];
                    displayMessages(chatMessagesArray);
                } else {
                    console.error('Error loading messages:', data.error);
                    showWelcomeMessage();
                }
            })
            .catch(error => {
                console.error('Error fetching messages:', error);
                showWelcomeMessage();
            });
    }
    
    // Send message to server
    function sendMessage(message) {
        const messageData = {
            name: currentUsername,
            message: message
        };
        
        fetch('chat-api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(messageData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Clear inputs
                if (messageInput) messageInput.value = '';
                if (messageInputMobile) messageInputMobile.value = '';
                
                // Reload messages to show all messages including the new one
                loadMessages();
                
                console.log('Message sent successfully:', data.message);
            } else {
                console.error('Error sending message:', data.error);
                alert('Failed to send message. Please try again.');
            }
        })
        .catch(error => {
            console.error('Error sending message:', error);
            alert('Failed to send message. Please check your connection.');
        });
    }
    
    // Start polling for new messages from server
    function startChatPolling() {
        chatPollInterval = setInterval(checkForNewMessages, 2000); // Poll every 2 seconds
    }
    
    // Stop polling
    function stopChatPolling() {
        if (chatPollInterval) {
            clearInterval(chatPollInterval);
        }
    }
    
    // Check for new messages from server
    function checkForNewMessages() {
        fetch('chat-api.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const newMessages = data.messages || [];
                    if (newMessages.length !== chatMessagesArray.length || 
                        (newMessages.length > 0 && chatMessagesArray.length > 0 && 
                         newMessages[newMessages.length - 1].id !== chatMessagesArray[chatMessagesArray.length - 1].id)) {
                        chatMessagesArray = newMessages;
                        displayMessages(chatMessagesArray);
                    }
                }
            })
            .catch(error => {
                console.error('Error checking for new messages:', error);
            });
    }
    
    // Display messages in chat
    function displayMessages(messages, append = false) {
        // Clear both containers if not appending
        if (!append) {
            if (chatMessages) chatMessages.innerHTML = '';
            if (chatMessagesMobile) chatMessagesMobile.innerHTML = '';
        }
        
        if (messages.length === 0) {
            showWelcomeMessage();
            return;
        }
        
        messages.forEach(message => {
            const messageElement = createMessageElement(message);
            const messageElementMobile = createMessageElement(message);
            
            if (chatMessages) chatMessages.appendChild(messageElement);
            if (chatMessagesMobile) chatMessagesMobile.appendChild(messageElementMobile);
        });
        
        // Scroll to bottom for both containers
        if (chatMessages) chatMessages.scrollTop = chatMessages.scrollHeight;
        if (chatMessagesMobile) chatMessagesMobile.scrollTop = chatMessagesMobile.scrollHeight;
    }
    
    // Create message element
    function createMessageElement(message) {
        console.log('Creating message element for:', message);
        
        // Validate message object
        if (!message || typeof message !== 'object') {
            console.error('Invalid message object:', message);
            return document.createElement('div');
        }
        
        const messageDiv = document.createElement('div');
        messageDiv.className = 'flex flex-col space-y-1 lg:space-y-2 p-2 lg:p-3 bg-white border border-gray-200 hover:border-primary/20 transition-colors rounded lg:rounded-none';
        
        const name = message.name || 'Unknown';
        const text = message.message || '';
        const timestamp = message.timestamp || Date.now();
        
        const timeAgo = getTimeAgo(timestamp);
        
        messageDiv.innerHTML = `
            <div class="flex items-center justify-between">
                <span class="font-medium text-xs lg:text-sm text-gray-900 truncate max-w-[100px] lg:max-w-none">${escapeHtml(name)}</span>
                <span class="text-xs text-gray-500 ml-2">${timeAgo}</span>
            </div>
            <p class="text-xs lg:text-sm text-gray-700 break-words leading-relaxed">${escapeHtml(text)}</p>
        `;
        
        return messageDiv;
    }
    
    // Show welcome message when no messages
    function showWelcomeMessage() {
        const welcomeHTML = `
            <div class="flex items-center justify-center h-full">
                <div class="text-center text-gray-500">
                    <svg class="w-10 h-10 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                    <p><?php echo $translatedTexts['chat_messages_appear']; ?></p>
                </div>
            </div>
        `;
        
        if (chatMessages) chatMessages.innerHTML = welcomeHTML;
        if (chatMessagesMobile) chatMessagesMobile.innerHTML = welcomeHTML;
    }
    
    // Get time ago string
    function getTimeAgo(timestamp) {
        const now = Math.floor(Date.now() / 1000);
        const diff = now - timestamp;
        
        if (diff < 60) {
            return '<?php echo $translatedTexts['just_now']; ?>';
        } else if (diff < 3600) {
            const minutes = Math.floor(diff / 60);
            return `${minutes} <?php echo $translatedTexts['minutes_ago']; ?>`;
        } else {
            const hours = Math.floor(diff / 3600);
            return `${hours} <?php echo $translatedTexts['hours_ago']; ?>`;
        }
    }
    
    // Escape HTML to prevent XSS
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    // Clean up polling when page is unloaded
    window.addEventListener('beforeunload', function() {
        stopChatPolling();
    });
});

// Scroll to top functionality
const scrollButton = document.getElementById('scroll-to-top');

window.addEventListener('scroll', function() {
    if (window.pageYOffset > 100) {
        scrollButton.classList.remove('opacity-0', 'invisible');
        scrollButton.classList.add('opacity-100', 'visible');
    } else {
        scrollButton.classList.remove('opacity-100', 'visible');
        scrollButton.classList.add('opacity-0', 'invisible');
    }
});

scrollButton.addEventListener('click', function() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
});
</script>

<!-- Custom Mobile Responsive CSS -->
<style>
/* Ensure proper mobile viewport and prevent overflow */
@media (max-width: 1023px) {
    /* Ensure no horizontal overflow */
    body {
        overflow-x: hidden;
    }
    
    /* Mobile container adjustments */
    .max-w-7xl {
        max-width: 100%;
    }
    
    /* Ensure video container is responsive */
    .aspect-video {
        position: relative;
        width: 100%;
    }
    
    /* Mobile chat container improvements */
    #chat-messages-mobile,
    #chat-messages {
        /* Better scrolling on mobile */
        -webkit-overflow-scrolling: touch;
        /* Ensure messages don't break layout */
        word-wrap: break-word;
        overflow-wrap: break-word;
    }
    
    /* Prevent input overflow */
    input[type="text"] {
        min-width: 0;
        width: 100%;
    }
    
    /* Ensure buttons don't overflow */
    button {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    /* Chat message improvements */
    .chat-message {
        word-break: break-word;
        hyphens: auto;
    }
    
    /* Ensure proper flex behavior */
    .flex-1 {
        min-width: 0;
        flex: 1 1 0%;
    }
    
    .flex-shrink-0 {
        flex-shrink: 0;
    }
}

/* Very small screens (phones in portrait) */
@media (max-width: 640px) {
    /* Reduce padding on very small screens */
    .px-4 {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    /* Ensure chat inputs stack properly */
    .flex-col {
        width: 100%;
    }
    
    /* Better button sizing on small screens */
    button {
        min-height: 44px; /* iOS minimum touch target */
    }
    
    /* Ensure proper text sizing */
    .text-xs {
        font-size: 0.75rem;
        line-height: 1rem;
    }
}

/* Ensure inputs and buttons work well together */
.flex.gap-2 {
    gap: 0.5rem;
}

.flex.gap-2 > input {
    flex: 1;
    min-width: 0;
}

.flex.gap-2 > button {
    flex-shrink: 0;
}

/* Safe area adjustments for modern mobile devices */
@supports (padding: max(0px)) {
    .px-4 {
        padding-left: max(1rem, env(safe-area-inset-left));
        padding-right: max(1rem, env(safe-area-inset-right));
    }
}
</style>

</body>
</html>