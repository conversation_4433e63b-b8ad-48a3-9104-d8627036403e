<?php
// <PERSON>ript to merge networks_data.json into notc/data.json

echo "Starting data merge process...\n";

// Read the original data.json
$originalDataPath = 'notc/data.json';
$originalData = json_decode(file_get_contents($originalDataPath), true);

if (!$originalData) {
    die("Error: Could not read original data.json\n");
}

echo "Original data loaded. Found " . count($originalData['registrations']) . " existing registrations.\n";

// Read the networks data
$networksDataPath = 'networks_data.json';
$networksData = json_decode(file_get_contents($networksDataPath), true);

if (!$networksData) {
    die("Error: Could not read networks_data.json\n");
}

echo "Networks data loaded. Found " . count($networksData['registrations']) . " network registrations to add.\n";

// Merge the registrations arrays
$originalData['registrations'] = array_merge($originalData['registrations'], $networksData['registrations']);

echo "Data merged. Total registrations: " . count($originalData['registrations']) . "\n";

// Write back to data.json
$result = file_put_contents($originalDataPath, json_encode($originalData, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));

if ($result === false) {
    die("Error: Could not write merged data back to data.json\n");
}

echo "Successfully merged networks data into data.json\n";
echo "Backup was created as: notc/data_backup_" . date('Y-m-d_H-i-s') . ".json\n";
?>