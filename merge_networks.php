<?php
/**
 * Merge networks data with existing NOTC data.json
 */

function mergeNetworksWithNOTC($notcFile, $networksFile, $outputFile) {
    // Read existing NOTC data
    $notcData = json_decode(file_get_contents($notcFile), true);
    
    // Read networks data
    $networksData = json_decode(file_get_contents($networksFile), true);
    
    // Merge registrations
    $mergedRegistrations = array_merge($notcData['registrations'], $networksData['registrations']);
    
    // Update counts
    $totalRegistrations = count($mergedRegistrations);
    $churchRegistrations = 0;
    $networkRegistrations = 0;
    
    foreach ($mergedRegistrations as $registration) {
        if ($registration['type'] === 'church') {
            $churchRegistrations++;
        } elseif ($registration['type'] === 'network') {
            $networkRegistrations++;
        }
    }
    
    // Create merged data structure
    $mergedData = [
        'registrations' => $mergedRegistrations,
        'total_registrations' => $totalRegistrations,
        'church_registrations' => $churchRegistrations,
        'network_registrations' => $networkRegistrations,
        'last_updated' => date('c')
    ];
    
    // Save merged data
    $jsonOutput = json_encode($mergedData, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
    file_put_contents($outputFile, $jsonOutput);
    
    return $mergedData;
}

// Usage
if (php_sapi_name() === 'cli') {
    $notcFile = $argv[1] ?? 'notc/data.json';
    $networksFile = $argv[2] ?? 'networks_data.json';
    $outputFile = $argv[3] ?? 'merged_data.json';
    
    echo "Merging NOTC data ($notcFile) with networks data ($networksFile)...\n";
    
    $mergedData = mergeNetworksWithNOTC($notcFile, $networksFile, $outputFile);
    
    echo "Merge complete! Output saved to $outputFile\n";
    echo "Total registrations: " . $mergedData['total_registrations'] . "\n";
    echo "Church registrations: " . $mergedData['church_registrations'] . "\n";
    echo "Network registrations: " . $mergedData['network_registrations'] . "\n";
}
?>