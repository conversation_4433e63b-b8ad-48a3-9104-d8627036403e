<?php

// Read the existing NOTC data
$notcDataFile = '/Applications/XAMPP/xamppfiles/htdocs/crusades/notc/data.json';
$notcContent = file_get_contents($notcDataFile);
$notcData = json_decode($notcContent, true);

// Read the networks data
$networksDataFile = '/Applications/XAMPP/xamppfiles/htdocs/crusades/networks_data.json';
$networksContent = file_get_contents($networksDataFile);
$networksData = json_decode($networksContent, true);

// Check if data was loaded successfully
if (!$notcData || !$networksData) {
    die("Error: Could not load data files properly.\n");
}

// Backup the original notc/data.json file
$backupFile = '/Applications/XAMPP/xamppfiles/htdocs/crusades/notc/data_backup_' . date('Y-m-d_H-i-s') . '.json';
copy($notcDataFile, $backupFile);
echo "Created backup of original notc/data.json: " . basename($backupFile) . "\n";

// Count existing registrations
$existingCount = count($notcData['registrations']);
$networksCount = count($networksData['registrations']);

echo "Existing church registrations: " . $existingCount . "\n";
echo "Network registrations to add: " . $networksCount . "\n";

// Merge the networks data into the existing NOTC data
// Add all network registrations to the existing registrations array
foreach ($networksData['registrations'] as $networkRegistration) {
    $notcData['registrations'][] = $networkRegistration;
}

// Count final registrations
$finalCount = count($notcData['registrations']);
echo "Total registrations after merge: " . $finalCount . "\n";

// Write the merged data back to the notc/data.json file
$success = file_put_contents($notcDataFile, json_encode($notcData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

if ($success) {
    echo "\nMerge completed successfully!\n";
    echo "Networks data has been added to notc/data.json\n";
    echo "Original data preserved: " . $existingCount . " church registrations\n";
    echo "Added: " . $networksCount . " network registrations\n";
    echo "Total: " . $finalCount . " registrations\n";
    echo "Backup created: " . $backupFile . "\n";
} else {
    echo "\nError: Failed to write merged data to file.\n";
    echo "Original file is unchanged.\n";
}

// Verify the merge by checking some data integrity
echo "\nVerifying merge integrity...\n";

// Reload the merged file to verify
$verifyContent = file_get_contents($notcDataFile);
$verifyData = json_decode($verifyContent, true);

if ($verifyData && count($verifyData['registrations']) === $finalCount) {
    echo "✓ Merge verification successful\n";
    
    // Count different types
    $churchCount = 0;
    $networkCount = 0;
    
    foreach ($verifyData['registrations'] as $registration) {
        if ($registration['type'] === 'church') {
            $churchCount++;
        } elseif ($registration['type'] === 'network') {
            $networkCount++;
        }
    }
    
    echo "✓ Churches: " . $churchCount . "\n";
    echo "✓ Networks: " . $networkCount . "\n";
} else {
    echo "✗ Merge verification failed\n";
}

?>