<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Load countries data
$countriesFile = __DIR__ . '/../data/countries.json';
$countries = [];

if (file_exists($countriesFile)) {
    $countriesData = json_decode(file_get_contents($countriesFile), true);
    $countries = $countriesData['countries'] ?? [];
} else {
    echo json_encode(['error' => 'Countries data not found']);
    exit;
}

$action = $_GET['action'] ?? '';
$query = $_GET['q'] ?? '';
$country = $_GET['country'] ?? '';
$location = $_GET['location'] ?? '';

switch ($action) {
    case 'countries':
        handleCountriesRequest($countries, $query);
        break;
    
    case 'cities':
        handleCitiesRequest($countries, $country, $query);
        break;
    
    case 'coordinates':
        handleCoordinatesRequest($countries, $location);
        break;
    
    case 'search':
        handleSearchRequest($countries, $query);
        break;
    
    default:
        echo json_encode(['error' => 'Invalid action. Use: countries, cities, coordinates, or search']);
        break;
}

function handleCountriesRequest($countries, $query = '') {
    $results = [];
    
    foreach ($countries as $country) {
        // If query is provided, filter countries
        if (!empty($query)) {
            $query = strtolower($query);
            if (strpos(strtolower($country['name']), $query) === false && 
                strpos(strtolower($country['code']), $query) === false) {
                continue;
            }
        }
        
        $results[] = [
            'code' => $country['code'],
            'name' => $country['name'],
            'continent' => $country['continent'],
            'timezone' => $country['timezone'],
            'coordinates' => $country['coordinates'],
            'city_count' => count($country['major_cities'])
        ];
    }
    
    // Sort by name
    usort($results, function($a, $b) {
        return strcmp($a['name'], $b['name']);
    });
    
    echo json_encode([
        'action' => 'countries',
        'query' => $query,
        'results' => $results,
        'count' => count($results)
    ], JSON_PRETTY_PRINT);
}

function handleCitiesRequest($countries, $countryCode, $query = '') {
    $results = [];
    $countryFound = false;
    
    foreach ($countries as $country) {
        if (strtolower($country['code']) === strtolower($countryCode) || 
            strtolower($country['name']) === strtolower($countryCode)) {
            
            $countryFound = true;
            
            foreach ($country['major_cities'] as $city) {
                // If query is provided, filter cities
                if (!empty($query)) {
                    $query = strtolower($query);
                    if (strpos(strtolower($city['name']), $query) === false) {
                        continue;
                    }
                }
                
                $results[] = [
                    'name' => $city['name'],
                    'country' => $country['name'],
                    'country_code' => $country['code'],
                    'continent' => $country['continent'],
                    'coordinates' => $city['coordinates'],
                    'timezone' => $country['timezone']
                ];
            }
            break;
        }
    }
    
    if (!$countryFound) {
        echo json_encode(['error' => 'Country not found: ' . $countryCode]);
        return;
    }
    
    // Sort by city name
    usort($results, function($a, $b) {
        return strcmp($a['name'], $b['name']);
    });
    
    echo json_encode([
        'action' => 'cities',
        'country' => $countryCode,
        'query' => $query,
        'results' => $results,
        'count' => count($results)
    ], JSON_PRETTY_PRINT);
}

function handleCoordinatesRequest($countries, $location) {
    if (empty($location)) {
        echo json_encode(['error' => 'Location parameter required']);
        return;
    }
    
    $locationParts = array_map('trim', explode(',', $location));
    $cityName = $locationParts[0] ?? '';
    $countryName = $locationParts[1] ?? '';
    
    $results = [];
    
    foreach ($countries as $country) {
        // Check if country matches
        $countryMatches = empty($countryName) || 
                         strtolower($country['name']) === strtolower($countryName) ||
                         strtolower($country['code']) === strtolower($countryName);
        
        if ($countryMatches) {
            // Search in cities
            foreach ($country['major_cities'] as $city) {
                if (strtolower($city['name']) === strtolower($cityName)) {
                    $results[] = [
                        'type' => 'city',
                        'name' => $city['name'],
                        'country' => $country['name'],
                        'country_code' => $country['code'],
                        'continent' => $country['continent'],
                        'coordinates' => $city['coordinates'],
                        'timezone' => $country['timezone'],
                        'confidence' => 'high'
                    ];
                }
            }
            
            // If no city found but country matches, return country coordinates
            if (empty($results) && !empty($countryName) && 
                (strtolower($country['name']) === strtolower($countryName) ||
                 strtolower($country['code']) === strtolower($countryName))) {
                
                $results[] = [
                    'type' => 'country',
                    'name' => $country['name'],
                    'country' => $country['name'],
                    'country_code' => $country['code'],
                    'continent' => $country['continent'],
                    'coordinates' => $country['coordinates'],
                    'timezone' => $country['timezone'],
                    'confidence' => 'medium'
                ];
            }
        }
    }
    
    // If no exact match, try partial matching
    if (empty($results)) {
        foreach ($countries as $country) {
            foreach ($country['major_cities'] as $city) {
                if (stripos($city['name'], $cityName) !== false) {
                    $results[] = [
                        'type' => 'city',
                        'name' => $city['name'],
                        'country' => $country['name'],
                        'country_code' => $country['code'],
                        'continent' => $country['continent'],
                        'coordinates' => $city['coordinates'],
                        'timezone' => $country['timezone'],
                        'confidence' => 'low'
                    ];
                }
            }
        }
    }
    
    echo json_encode([
        'action' => 'coordinates',
        'location' => $location,
        'results' => $results,
        'count' => count($results)
    ], JSON_PRETTY_PRINT);
}

function handleSearchRequest($countries, $query) {
    if (empty($query)) {
        echo json_encode(['error' => 'Query parameter required']);
        return;
    }
    
    $query = strtolower($query);
    $results = [];
    
    foreach ($countries as $country) {
        // Search countries
        if (stripos($country['name'], $query) !== false || 
            stripos($country['code'], $query) !== false) {
            
            $results[] = [
                'type' => 'country',
                'name' => $country['name'],
                'country' => $country['name'],
                'country_code' => $country['code'],
                'continent' => $country['continent'],
                'coordinates' => $country['coordinates'],
                'timezone' => $country['timezone'],
                'match_type' => 'country'
            ];
        }
        
        // Search cities
        foreach ($country['major_cities'] as $city) {
            if (stripos($city['name'], $query) !== false) {
                $results[] = [
                    'type' => 'city',
                    'name' => $city['name'],
                    'country' => $country['name'],
                    'country_code' => $country['code'],
                    'continent' => $country['continent'],
                    'coordinates' => $city['coordinates'],
                    'timezone' => $country['timezone'],
                    'match_type' => 'city'
                ];
            }
        }
    }
    
    // Sort by relevance (exact matches first, then partial matches)
    usort($results, function($a, $b) use ($query) {
        $aExact = (strtolower($a['name']) === $query) ? 1 : 0;
        $bExact = (strtolower($b['name']) === $query) ? 1 : 0;
        
        if ($aExact !== $bExact) {
            return $bExact - $aExact; // Exact matches first
        }
        
        return strcmp($a['name'], $b['name']); // Alphabetical order
    });
    
    // Limit to top 10 results
    $results = array_slice($results, 0, 10);
    
    echo json_encode([
        'action' => 'search',
        'query' => $query,
        'results' => $results,
        'count' => count($results)
    ], JSON_PRETTY_PRINT);
}
?> 