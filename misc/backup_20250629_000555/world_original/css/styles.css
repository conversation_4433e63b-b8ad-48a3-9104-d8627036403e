/* Main stylesheet for continent application */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #667eea 100%);
    height: 100vh;
    overflow: hidden;
}

/* Glide carousel styles */
.glide {
    height: 100vh;
    width: 100vw;
}

.glide__track {
    height: 100%;
}

.glide__slides {
    height: 100%;
}

.glide__slide {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100vh;
    position: relative;
}

/* Continent card styles */
.continent-card {
    width: 92vw;
    height: 85vh;
    max-width: 1400px;
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    padding: 30px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.continent-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(255, 255, 255, 0.3) 50%, 
        transparent 100%);
}

/* Title styles */
.continent-title {
    font-size: 3rem;
    font-weight: 600;
    color: white;
    text-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    letter-spacing: 1px;
    margin-bottom: 25px;
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 1s ease forwards;
    text-align: center;
    background: linear-gradient(135deg, #fff 0%, #e0e7ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Map container styles */
.continent-map {
    width: 100%;
    height: calc(100% - 100px);
    flex: 1;
    opacity: 0;
    transform: scale(0.9);
    animation: mapFadeIn 1.2s ease forwards 0.4s;
    border-radius: 16px;
    overflow: hidden;
    position: relative;
}

.map-container {
    width: 100%;
    height: 100%;
    position: relative;
    border-radius: 16px;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Loading indicator */
.loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 10px;
    z-index: 1000;
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Leaflet map specific styles */
.leaflet-container {
    background: transparent !important;
    border-radius: 16px !important;
}

.leaflet-control-container {
    display: none !important;
}

.leaflet-tile-pane {
    border-radius: 16px !important;
    overflow: hidden !important;
}

/* Navigation arrows */
.glide__arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 64px;
    height: 64px;
    color: white;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
    z-index: 1000;
}

.glide__arrow:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-50%) scale(1.05);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.glide__arrow:active {
    transform: translateY(-50%) scale(0.95);
}

.glide__arrow--left {
    left: 30px;
}

.glide__arrow--right {
    right: 30px;
}

/* Continent info */
.continent-info {
    position: absolute;
    bottom: 20px;
    left: 30px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    opacity: 0;
    animation: fadeInUp 1s ease forwards 0.8s;
}

/* Slide indicators */
.slide-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 8px;
    z-index: 100;
}

.slide-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    cursor: pointer;
}

.slide-dot.active {
    background: rgba(255, 255, 255, 0.8);
    transform: scale(1.2);
}

/* Animations */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes mapFadeIn {
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Responsive design */
@media (max-width: 1024px) {
    .continent-title {
        font-size: 2.5rem;
    }
    
    .continent-card {
        width: 95vw;
        height: 90vh;
        padding: 25px;
    }
}

@media (max-width: 768px) {
    .continent-title {
        font-size: 2rem;
        margin-bottom: 20px;
    }
    
    .continent-card {
        width: 96vw;
        height: 92vh;
        padding: 20px;
        border-radius: 20px;
    }

    .glide__arrow {
        width: 50px;
        height: 50px;
        font-size: 16px;
    }

    .glide__arrow--left {
        left: 15px;
    }

    .glide__arrow--right {
        right: 15px;
    }

    .continent-info {
        font-size: 0.8rem;
        left: 20px;
        bottom: 15px;
    }
}

@media (max-width: 480px) {
    .continent-title {
        font-size: 1.8rem;
    }
    
    .continent-card {
        padding: 15px;
    }

    .glide__arrow {
        width: 44px;
        height: 44px;
    }
}