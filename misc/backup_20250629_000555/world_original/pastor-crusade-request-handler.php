<?php
require_once '../includes/config.php';

// Set content type for JSON response
header('Content-Type: application/json');

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    
    // Sanitize and validate input
    $designation = $_POST['designation'] ?? '';
    $first_name = trim($_POST['first_name'] ?? '');
    $last_name = trim($_POST['last_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $kingschat_username = trim($_POST['kingschat_username'] ?? '');
    $zone = $_POST['zone'] ?? '';
    $group = trim($_POST['group'] ?? '');
    $church = trim($_POST['church'] ?? '');
    
    // Crusade information
    $crusade_title = trim($_POST['crusade_title'] ?? '');
    $country = $_POST['country'] ?? '';
    $city = trim($_POST['city'] ?? '');
    $venue = trim($_POST['venue'] ?? '');
    $preferred_date = $_POST['preferred_date'] ?? '';
    $preferred_time = $_POST['preferred_time'] ?? '';
    $expected_attendance = $_POST['expected_attendance'] ?? '';
    
    // Additional information
    $additional_comments = trim($_POST['additional_comments'] ?? '');
    
    // Validate required fields
    if (empty($designation) || empty($first_name) || empty($last_name) || empty($email) || 
        empty($phone) || empty($kingschat_username) || empty($zone) || empty($group) || empty($church) ||
        empty($crusade_title) || empty($country) || empty($city) || empty($venue) || 
        empty($preferred_date) || empty($preferred_time) || empty($expected_attendance)) {
        throw new Exception('Please fill in all required fields.');
    }
    
    // Validate email
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Please enter a valid email address.');
    }
    
    // Validate date
    if (!DateTime::createFromFormat('Y-m-d', $preferred_date)) {
        throw new Exception('Please enter a valid date.');
    }
    
    // Check if date is in the future
    if (strtotime($preferred_date) < strtotime('today')) {
        throw new Exception('Preferred date must be in the future.');
    }
    
    // Validate time
    if (!DateTime::createFromFormat('H:i', $preferred_time)) {
        throw new Exception('Please enter a valid time.');
    }
    
    // Prepare request data
    $request_data = [
        'id' => uniqid('crusade_request_', true),
        
        // Personal Information
        'designation' => $designation,
        'first_name' => $first_name,
        'last_name' => $last_name,
        'email' => $email,
        'phone' => $phone,
        'kingschat_username' => $kingschat_username,
        
        // Church Information
        'zone' => $zone,
        'group' => $group,
        'church' => $church,
        
        // Crusade Information
        'crusade_title' => $crusade_title,
        'country' => $country,
        'city' => $city,
        'venue' => $venue,
        'preferred_date' => $preferred_date,
        'preferred_time' => $preferred_time,
        'expected_attendance' => $expected_attendance,
        
        // Additional Information  
        'additional_comments' => $additional_comments,
        
        'created_at' => date('Y-m-d H:i:s'),
        'status' => 'pending'
    ];
    
    // Define the JSON file path
    $json_file = __DIR__ . '/../data/pastor_crusade_requests.json';
    
    // Create data directory if it doesn't exist
    $data_dir = dirname($json_file);
    if (!is_dir($data_dir)) {
        mkdir($data_dir, 0755, true);
    }
    
    // Read existing data or create empty array
    $existing_data = [];
    if (file_exists($json_file)) {
        $json_content = file_get_contents($json_file);
        $existing_data = json_decode($json_content, true) ?: [];
    }
    
    // Check if email already exists
    foreach ($existing_data as $existing_request) {
        if ($existing_request['email'] === $email) {
            throw new Exception('A crusade request with this email address already exists.');
        }
    }
    
    // Add new request to existing data
    $existing_data[] = $request_data;
    
    // Save back to JSON file
    $json_result = file_put_contents($json_file, json_encode($existing_data, JSON_PRETTY_PRINT));
    
    if ($json_result === false) {
        throw new Exception('Failed to save crusade request. Please try again.');
    }
    
    $request_id = $request_data['id'];
    
    // Send notification email (optional - you can configure this later)
    $notification_email = '<EMAIL>'; // Use your admin email
    $subject = 'New Crusade Request from Pastor';
    $message = "
        A new crusade request has been submitted by a pastor.
        
        PASTOR INFORMATION:
        Designation: {$designation}
        Name: {$first_name} {$last_name}
        Email: {$email}
        Phone: {$phone}
        KingsChat Username: {$kingschat_username}
        
        CHURCH INFORMATION:
        Zone: {$zone}
        Group: {$group}
        Church: {$church}
        
        PROPOSED CRUSADE DETAILS:
        Title: {$crusade_title}
        Country: {$country}
        City: {$city}
        Venue: {$venue}
        Preferred Date: {$preferred_date}
        Preferred Time: {$preferred_time}
        Expected Attendance: {$expected_attendance}
        
        ADDITIONAL INFORMATION:
        Additional Comments: {$additional_comments}
        
        View full details in the data file.
        Request ID: {$request_id}
    ";
    
    // Send confirmation email to pastor
    $pastor_subject = 'Crusade Request Received - Thank You!';
    $pastor_message = "
        Dear {$designation} {$first_name} {$last_name},
        
        Thank you for your interest in hosting a crusade! We have received your request for '{$crusade_title}' in {$city}, {$country}.
        
        Your request details:
        - Proposed Date: {$preferred_date} at {$preferred_time}
        - Expected Attendance: {$expected_attendance}
        - Venue: {$venue}
        
        Our team will review your request and contact you within 5-7 business days. We appreciate your heart for ministry and spreading the Gospel.
        
        God bless you!
        
        The Rhapsody Crusades Team
        Request ID: {$request_id}
    ";
    
    // Uncomment the following lines if you want to send email notifications
    // mail($notification_email, $subject, $message);
    // mail($email, $pastor_subject, $pastor_message);
    
    // Log the request for debugging
    error_log("New crusade request: ID {$request_id}, Pastor: {$first_name} {$last_name}, Email: {$email}, Crusade: {$crusade_title}");
    
    echo json_encode([
        'success' => true,
        'message' => 'Crusade request submitted successfully',
        'request_id' => $request_id
    ]);
    
} catch (Exception $e) {
    error_log("Crusade request error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?> 