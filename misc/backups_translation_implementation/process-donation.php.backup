<?php
require_once 'vendor/autoload.php';

// Stripe configuration
$stripe_secret_key = '********************************';
$stripe_publishable_key = 'pk_live_IZSllnNdyEGuoTWhLv5lAp7h';

\Stripe\Stripe::setApiKey($stripe_secret_key);

header('Content-Type: application/json');

try {
    // Get the JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    $amount = $input['amount'] ?? 0;
    $donor_name = $input['donor_name'] ?? '';
    $donor_email = $input['donor_email'] ?? '';
    
    // Validate input
    if ($amount < 1) {
        throw new Exception('Invalid donation amount');
    }
    
    if (empty($donor_name) || empty($donor_email)) {
        throw new Exception('Donor name and email are required');
    }
    
    // Create payment intent
    $payment_intent = \Stripe\PaymentIntent::create([
        'amount' => $amount * 100, // Convert to cents
        'currency' => 'usd',
        'metadata' => [
            'donor_name' => $donor_name,
            'donor_email' => $donor_email,
            'purpose' => 'Rhapsody End-Time Crusades Donation'
        ],
        'receipt_email' => $donor_email,
        'description' => 'Donation for Rhapsody End-Time Crusades'
    ]);
    
    echo json_encode([
        'success' => true,
        'client_secret' => $payment_intent->client_secret,
        'publishable_key' => $stripe_publishable_key
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>