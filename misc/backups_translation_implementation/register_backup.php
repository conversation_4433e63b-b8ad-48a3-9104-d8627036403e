<?php
require_once 'includes/config.php';
require_once 'includes/languages.php';
require_once 'includes/db.php';

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Get crusade slug from URL
$crusadeSlug = $_GET['event'] ?? '';

// Read crusades data
$jsonFile = __DIR__ . '/data/crusades.json';
$crusades = json_decode(file_get_contents($jsonFile), true);

// Find the crusade details
$crusadeDetails = null;
foreach ($crusades['crusades'] as $crusade) {
    $registerLink = $crusade['register_link'];

    // Handle different register link formats
    if (strpos($registerLink, 'register.php?event=') === 0) {
        // New format: register.php?event=slug
        $linkSlug = substr($registerLink, strlen('register.php?event='));
        $linkSlug = urldecode($linkSlug); // Decode URL encoding
    } else {
        // Old format: slug.php
        $linkSlug = basename($registerLink, '.php');
    }

    if ($linkSlug === $crusadeSlug) {
        $crusadeDetails = $crusade;
        break;
    }
}

// If no valid crusade found, redirect to home
if (!$crusadeDetails) {
    header('Location: index.php');
    exit();
}

$success = false;
$errors = [];

// Check for session errors and form data
if (isset($_SESSION['form_errors'])) {
    $errors = $_SESSION['form_errors'];
    unset($_SESSION['form_errors']);
}

if (isset($_SESSION['form_data'])) {
    $_POST = $_SESSION['form_data'];
    unset($_SESSION['form_data']);
}

include 'includes/header.php';
?>

<!-- Event Banner -->
<section class="py-8 sm:py-12 lg:py-16 bg-gray-50">
    <div class="max-w-6xl mx-auto px-4 sm:px-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center">
            <div class="space-y-4 sm:space-y-6">
                <h1 class="text-2xl sm:text-3xl lg:text-4xl font-light text-gray-900 leading-tight"><?php echo htmlspecialchars($crusadeDetails['title']); ?></h1>
                <p class="text-base sm:text-lg lg:text-xl text-gray-600 leading-relaxed"><?php echo htmlspecialchars($crusadeDetails['description']); ?></p>
                <div class="flex flex-col space-y-2">
                    <p class="text-sm sm:text-base text-gray-700">
                        <span class="font-medium"><?php echo __('event_date'); ?>:</span> 
                        <?php echo date('F j, Y', strtotime($crusadeDetails['date'])); ?> at <?php echo date('g:i A', strtotime($crusadeDetails['time'])); ?>
                    </p>
                    <p class="text-sm sm:text-base text-gray-700">
                        <span class="font-medium"><?php echo __('event_location'); ?>:</span>
                        <?php echo htmlspecialchars($crusadeDetails['venue']); ?>, <?php echo htmlspecialchars($crusadeDetails['address']); ?>
                    </p>
                </div>
            </div>
            <div class="aspect-[4/3] overflow-hidden rounded-lg">
                <img src="<?php echo htmlspecialchars($crusadeDetails['image']); ?>" 
                     alt="<?php echo htmlspecialchars($crusadeDetails['title']); ?>" 
                     class="w-full h-full object-cover">
            </div>
        </div>
    </div>
</section>

<!-- Registration Form Section -->
<section class="py-12 sm:py-16 lg:py-24 bg-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 register-form-container">
        <div class="text-center mb-8 sm:mb-12 lg:mb-16">
            <h2 class="text-2xl sm:text-3xl lg:text-4xl font-light text-gray-900 mb-4 sm:mb-6"><?php echo __('registration_form'); ?></h2>
            <div class="w-16 h-px bg-primary mx-auto mb-4 sm:mb-6"></div>
            <p class="text-base sm:text-lg lg:text-xl text-gray-600 leading-relaxed px-4"><?php echo __('register_welcome'); ?></p>
        </div>

        <?php if (!empty($errors)): ?>
        <div class="bg-red-50 border border-red-200 text-red-800 px-6 py-4 mb-8" role="alert">
            <p class="font-medium mb-2"><?php echo __('correct_errors'); ?></p>
            <ul class="list-disc list-inside text-sm space-y-1">
                <?php foreach ($errors as $error): ?>
                <li><?php echo htmlspecialchars($error); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php endif; ?>

        <form action="register-handler.php" method="post" class="bg-gray-50 border border-gray-200 p-6 sm:p-8 lg:p-12 rounded-lg">
            <!-- Hidden fields for form processing -->
            <input type="hidden" name="form_source" value="register.php">
            <input type="hidden" name="crusade_slug" value="<?php echo htmlspecialchars($crusadeSlug); ?>">
            <input type="hidden" name="crusade_id" value="<?php echo htmlspecialchars($crusadeDetails['id']); ?>">
            <input type="hidden" name="crusade_title" value="<?php echo htmlspecialchars($crusadeDetails['title']); ?>">
            
            <!-- Personal Details -->
            <div class="mb-8 sm:mb-10 lg:mb-12">
                <h3 class="text-xl sm:text-2xl font-light text-gray-900 mb-6 sm:mb-8"><?php echo __('personal_details'); ?></h3>

                <div class="mb-4 sm:mb-6">
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('title'); ?></label>
                    <select id="title" name="title" class="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200 rounded-md">
                        <option value="Mr."><?php echo __('mr'); ?></option>
                        <option value="Mrs."><?php echo __('mrs'); ?></option>
                        <option value="Ms."><?php echo __('ms'); ?></option>
                        <option value="Dr."><?php echo __('dr'); ?></option>
                        <option value="Pastor"><?php echo __('pastor'); ?></option>
                        <option value="Other"><?php echo __('other'); ?></option>
                    </select>
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 mb-4 sm:mb-6">
                    <div>
                        <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('first_name'); ?> *</label>
                        <input type="text" id="first_name" name="first_name" value="<?php echo isset($_POST['first_name']) ? htmlspecialchars($_POST['first_name']) : ''; ?>" class="form-input w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200 rounded-md" required>
                    </div>

                    <div>
                        <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('last_name'); ?> *</label>
                        <input type="text" id="last_name" name="last_name" value="<?php echo isset($_POST['last_name']) ? htmlspecialchars($_POST['last_name']) : ''; ?>" class="form-input w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200 rounded-md" required>
                    </div>
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 mb-4 sm:mb-6">
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('email'); ?> *</label>
                        <input type="email" id="email" name="email" value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" class="form-input w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200 rounded-md" required>
                    </div>

                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('phone'); ?> *</label>
                        <input type="tel" id="phone" name="phone" value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>" class="form-input w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200 rounded-md" required>
                    </div>
                </div>

                <div class="mb-4 sm:mb-6">
                    <label for="address" class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('address'); ?></label>
                    <textarea id="address" name="address" rows="3" class="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200 rounded-md"><?php echo isset($_POST['address']) ? htmlspecialchars($_POST['address']) : ''; ?></textarea>
                </div>
            </div>

            <!-- Invitation Details -->
            <div class="mb-8 sm:mb-10 lg:mb-12">
                <h3 class="text-xl sm:text-2xl font-light text-gray-900 mb-6 sm:mb-8"><?php echo __('invitation_details'); ?></h3>

                <div class="mb-4 sm:mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3 sm:mb-4"><?php echo __('how_did_you_hear'); ?></label>
                    <div class="space-y-2 sm:space-y-3">
                        <div class="flex items-center">
                            <input type="radio" id="source_social" name="source" value="Social Media" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="source_social" class="ml-3 text-gray-700"><?php echo __('social_media'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="source_friend" name="source" value="Friend/Family" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="source_friend" class="ml-3 text-gray-700"><?php echo __('friend_family'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="source_church" name="source" value="Church/Pastor" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="source_church" class="ml-3 text-gray-700"><?php echo __('church_pastor'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="source_online" name="source" value="Online Search" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="source_online" class="ml-3 text-gray-700"><?php echo __('online_search'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="source_other" name="source" value="Other" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="source_other" class="ml-3 text-gray-700"><?php echo __('other'); ?></label>
                            <input type="text" name="source_other_text" class="ml-2 px-2 sm:px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-600 text-sm">
                        </div>
                    </div>
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('interested_in_attending'); ?></label>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <input type="radio" id="attending_yes" name="attending" value="Yes" <?php echo (isset($_POST['attending']) && $_POST['attending'] === 'Yes') ? 'checked' : ''; ?> <?php echo !isset($_POST['attending']) ? 'checked' : ''; ?> class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="attending_yes" class="ml-3 text-gray-700"><?php echo __('yes'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="attending_no" name="attending" value="No" <?php echo (isset($_POST['attending']) && $_POST['attending'] === 'No') ? 'checked' : ''; ?> class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="attending_no" class="ml-3 text-gray-700"><?php echo __('no'); ?></label>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 mb-4 sm:mb-6">
                    <div>
                        <label for="coming_with" class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('how_many_coming'); ?></label>
                        <input type="number" id="coming_with" name="coming_with" min="0" value="<?php echo isset($_POST['coming_with']) ? htmlspecialchars($_POST['coming_with']) : '0'; ?>" class="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200 rounded-md">
                    </div>

                    <div>
                        <label for="inviting" class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('how_many_inviting'); ?></label>
                        <input type="number" id="inviting" name="inviting" min="0" value="<?php echo isset($_POST['inviting']) ? htmlspecialchars($_POST['inviting']) : '0'; ?>" class="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200 rounded-md">
                    </div>
                </div>
            </div>

            <!-- Prayer and Healing -->
            <div class="mb-8 sm:mb-10 lg:mb-12">
                <h3 class="text-xl sm:text-2xl font-light text-gray-900 mb-6 sm:mb-8"><?php echo __('prayer_healing'); ?></h3>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('require_healing'); ?></label>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <input type="radio" id="healing_yes" name="healing" value="Yes" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="healing_yes" class="ml-3 text-gray-700"><?php echo __('yes'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="healing_no" name="healing" value="No" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="healing_no" class="ml-3 text-gray-700"><?php echo __('no'); ?></label>
                        </div>
                    </div>
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('specific_prayer'); ?></label>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <input type="radio" id="prayer_yes" name="prayer" value="Yes" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="prayer_yes" class="ml-3 text-gray-700"><?php echo __('yes'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="prayer_no" name="prayer" value="No" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="prayer_no" class="ml-3 text-gray-700"><?php echo __('no'); ?></label>
                        </div>
                    </div>
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('prayer_request_type'); ?></label>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <input type="checkbox" id="prayer_healing" name="prayer_type[]" value="Healing" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="prayer_healing" class="ml-3 text-gray-700"><?php echo __('healing_self_loved'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="prayer_financial" name="prayer_type[]" value="Financial" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="prayer_financial" class="ml-3 text-gray-700"><?php echo __('financial_prosperity'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="prayer_spiritual" name="prayer_type[]" value="Spiritual" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="prayer_spiritual" class="ml-3 text-gray-700"><?php echo __('spiritual_growth'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="prayer_relationship" name="prayer_type[]" value="Relationship" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="prayer_relationship" class="ml-3 text-gray-700"><?php echo __('relationship_restoration'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="prayer_salvation" name="prayer_type[]" value="Salvation" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="prayer_salvation" class="ml-3 text-gray-700"><?php echo __('salvation'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="prayer_other" name="prayer_type[]" value="Other" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="prayer_other" class="ml-3 text-gray-700"><?php echo __('other'); ?></label>
                            <input type="text" name="prayer_other_text" class="ml-2 px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-600">
                        </div>
                    </div>
                </div>

                <div class="mb-6">
                    <label for="prayer_details" class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('prayer_details'); ?></label>
                    <textarea id="prayer_details" name="prayer_details" rows="3" class="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200 rounded-md"><?php echo isset($_POST['prayer_details']) ? htmlspecialchars($_POST['prayer_details']) : ''; ?></textarea>
                </div>
            </div>

            <!-- Rhapsody of Realities -->
            <div class="mb-8 sm:mb-10 lg:mb-12">
                <h3 class="text-xl sm:text-2xl font-light text-gray-900 mb-6 sm:mb-8"><?php echo __('rhapsody_section'); ?></h3>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('interested_in_copy'); ?></label>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <input type="radio" id="rhapsody_interested_yes" name="rhapsody_interested" value="Yes" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="rhapsody_interested_yes" class="ml-3 text-gray-700"><?php echo __('yes'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="rhapsody_interested_no" name="rhapsody_interested" value="No" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="rhapsody_interested_no" class="ml-3 text-gray-700"><?php echo __('no'); ?></label>
                        </div>
                    </div>
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('interested_in_buying'); ?></label>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <input type="radio" id="buying_yes" name="buying" value="Yes" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="buying_yes" class="ml-3 text-gray-700"><?php echo __('yes'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="buying_no" name="buying" value="No" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="buying_no" class="ml-3 text-gray-700"><?php echo __('no'); ?></label>
                        </div>
                    </div>
                </div>

                <div class="mb-6">
                    <label for="copies" class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('how_many_copies'); ?></label>
                    <input type="number" id="copies" name="copies" min="0" value="<?php echo isset($_POST['copies']) ? htmlspecialchars($_POST['copies']) : '0'; ?>" class="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200 rounded-md">
                </div>
            </div>

            <!-- Additional Information -->
            <div class="mb-8 sm:mb-10 lg:mb-12">
                <h3 class="text-xl sm:text-2xl font-light text-gray-900 mb-6 sm:mb-8"><?php echo __('additional_info'); ?></h3>

                <div class="mb-6">
                    <label for="church" class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('church_affiliation'); ?></label>
                    <input type="text" id="church" name="church" value="<?php echo isset($_POST['church']) ? htmlspecialchars($_POST['church']) : ''; ?>" class="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200 rounded-md">
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('attended_before'); ?></label>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <input type="radio" id="attended_yes" name="attended" value="Yes" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="attended_yes" class="ml-3 text-gray-700"><?php echo __('yes'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="attended_no" name="attended" value="No" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="attended_no" class="ml-3 text-gray-700"><?php echo __('no'); ?></label>
                        </div>
                    </div>
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('receive_updates'); ?></label>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <input type="radio" id="updates_yes" name="updates" value="Yes" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="updates_yes" class="ml-3 text-gray-700"><?php echo __('yes'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="updates_no" name="updates" value="No" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="updates_no" class="ml-3 text-gray-700"><?php echo __('no'); ?></label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Confirmation -->
            <div class="mb-8 sm:mb-10 lg:mb-12">
                <h3 class="text-xl sm:text-2xl font-light text-gray-900 mb-6 sm:mb-8"><?php echo __('confirmation'); ?></h3>

                <div class="flex items-start">
                    <input type="checkbox" id="confirm" name="confirm" value="1" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 mt-1" required>
                    <label for="confirm" class="ml-3 text-gray-700 leading-relaxed"><?php echo __('confirm_text'); ?></label>
                </div>
            </div>

            <div class="text-center">
                <button type="submit" class="inline-flex items-center justify-center w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-4 bg-primary text-white font-medium hover:bg-red-700 transition-colors duration-200 rounded-lg text-base sm:text-lg">
                    <?php echo __('submit_registration'); ?>
                    <svg class="ml-2 w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </button>
            </div>
        </form>
    </div>
</section>

<style>
/* Safari mobile input fixes */
input[type="text"],
input[type="email"], 
input[type="tel"],
input[type="number"],
select,
textarea {
    -webkit-appearance: none;
    -webkit-border-radius: 0;
    border-radius: 6px;
    box-sizing: border-box;
    width: 100%;
    max-width: 100%;
}

/* Prevent zoom on focus in Safari iOS */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
    select,
    textarea,
    input[type="text"],
    input[type="email"],
    input[type="tel"],
    input[type="number"] {
        font-size: 16px;
    }
}

/* Fix Safari input width calculation */
.form-input {
    width: 100% !important;
    min-width: 0;
    box-sizing: border-box;
}

/* Ensure container doesn't overflow */
.register-form-container {
    overflow-x: hidden;
    width: 100%;
}

/* Fix grid items on mobile Safari */
@media (max-width: 640px) {
    .grid > div {
        width: 100%;
        min-width: 0;
    }
    
    /* Force container widths on small screens */
    .register-form-container,
    .register-form-container * {
        max-width: 100%;
        box-sizing: border-box;
    }
}

/* Additional Safari iOS fixes */
@supports (-webkit-touch-callout: none) {
    .form-input {
        -webkit-appearance: none;
        background-clip: padding-box;
    }
    
    /* Fix for Safari input field zoom */
    input, select, textarea {
        font-size: 16px !important;
        transform: scale(1);
    }
}
</style>

<?php include 'includes/footer.php'; ?>
