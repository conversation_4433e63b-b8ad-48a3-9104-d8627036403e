# Force HTTPS (if available)
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteCond %{HTTP_HOST} !^localhost
    RewriteCond %{HTTP_HOST} !^127\.0\.0\.1
    RewriteRule (.*) https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</IfModule>

# Enable PHP short tags
<IfModule mod_php7.c>
    php_value short_open_tag 1
</IfModule>

# Prevent directory listing
Options -Indexes

# Set default character set
AddDefaultCharset UTF-8

# Set default language if none is specified
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Don't apply to existing files or directories
    RewriteCond %{REQUEST_FILENAME} -f [OR]
    RewriteCond %{REQUEST_FILENAME} -d
    RewriteRule ^ - [L]
    
    # Don't rewrite if lang parameter already exists
    RewriteCond %{QUERY_STRING} (?:^|&)lang= [NC]
    RewriteRule ^ - [L]
    
    # Don't rewrite for specific files that handle language detection
    RewriteRule ^(assets|includes|admin|test-language\.php|download_flags\.php) - [L]
    
    # Append lang parameter based on cookie if it exists
    RewriteCond %{HTTP_COOKIE} lang=([^;]+) [NC]
    RewriteRule ^(.*)$ $1?lang=%1 [QSA,L]
</IfModule>

# Compress text files for faster transmission
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/x-javascript application/json application/xml
</IfModule>

# Enable browser caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType text/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# Set security headers
<IfModule mod_headers.c>
    Header always set X-XSS-Protection "1; mode=block"
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# PHP settings
<IfModule mod_php7.c>
    # Set max upload size
    php_value upload_max_filesize 10M
    php_value post_max_size 20M
    php_value max_execution_time 300
    php_value max_input_time 300
    
    # Set error reporting for production
    php_flag display_errors Off
    php_flag log_errors On
    php_value error_log /path/to/php-errors.log
</IfModule> 