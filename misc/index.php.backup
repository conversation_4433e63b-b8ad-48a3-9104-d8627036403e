<?php
require_once 'includes/config.php';
require_once 'includes/languages.php';
require_once 'includes/TranslationService.php';
include 'includes/header.php';
?>

<!-- Hero Section -->
<div class="bg-white min-h-screen flex items-center relative overflow-hidden">
    <main class="w-full">
        <div class="max-w-6xl mx-auto px-6 py-20">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                <!-- Left Content -->
                <div class="space-y-8">
                    <div class="hero-title-container">
                        <h1 class="text-5xl lg:text-6xl font-light leading-tight text-gray-900">
                            <span class="text-primary"><?php echo __('hero_title_rhapsody'); ?> <span id="typewriter-text" class="font-display font-semibold bg-gradient-to-r from-red-600 via-red-500 to-orange-500 bg-clip-text text-transparent drop-shadow-sm uppercase"><?php echo __('typewriter_crusades'); ?><span id="cursor" class="animate-pulse text-red-500 font-bold">|</span></span></span>
                        </h1>
                    </div>

                    <p class="text-xl text-gray-600 leading-relaxed font-light">
                        <?php echo __('hero_description'); ?>
                    </p>

                    <div class="pt-4">
                        <a href="#highlights" class="inline-flex items-center text-primary font-medium hover:text-red-700 transition-colors duration-200">
                            <?php echo __('view_highlights'); ?>
                            <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Right Image -->
                <div class="flex justify-center lg:justify-end">
                    <div class="w-full max-w-md">
                        <img
                            class="w-full h-auto"
                            src="assets/images/hero.png"
                            alt="<?php echo __('hero_alt_text'); ?>"
                            loading="lazy"
                        >
                    </div>
                </div>

            </div>
        </div>
    </main>
</div>

<!-- Scroll Indicator -->
<div class="hidden md:block absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10">
    <div class="flex flex-col items-center space-y-3 group cursor-pointer" onclick="document.getElementById('about').scrollIntoView({behavior: 'smooth'})">
        <span class="text-xs font-medium text-gray-400 uppercase tracking-wider group-hover:text-primary transition-colors duration-300"><?php echo __('scroll_text'); ?></span>
        <div class="w-px h-8 bg-gray-300 group-hover:bg-primary transition-colors duration-300"></div>
        <div class="animate-bounce">
            <svg class="w-4 h-4 text-gray-400 group-hover:text-primary transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
            </svg>
        </div>
    </div>
</div>

<!-- Events Section -->
<section id="about" class="py-24 bg-gray-50">
    <div class="max-w-6xl mx-auto px-6">
        <div class="text-center mb-20">
            <h2 class="text-4xl font-light text-gray-900 mb-4"><?php echo __('upcoming_crusades'); ?></h2>
            <div class="w-16 h-px bg-primary mx-auto"></div>
        </div>

        <div class="relative">
            <?php
            // Load crusades from JSON file
            $crusadesJson = file_get_contents(__DIR__ . '/data/crusades.json');
            $crusadesData = json_decode($crusadesJson, true);
            $currentDate = new DateTime();
            
            // Sort crusades: future events first, then past events
            usort($crusadesData['crusades'], function($a, $b) use ($currentDate) {
                $dateA = new DateTime($a['date'] . ' ' . $a['time']);
                $dateB = new DateTime($b['date'] . ' ' . $b['time']);
                
                $isPastA = $dateA < $currentDate;
                $isPastB = $dateB < $currentDate;
                
                // If one is past and one is future, future comes first
                if ($isPastA && !$isPastB) return 1;
                if (!$isPastA && $isPastB) return -1;
                
                // If both are future or both are past, sort by date (newest first for future, newest first for past too)
                return $dateB <=> $dateA;
            });
            
            // Get translation service instance
            $translationService = TranslationService::getInstance();
            $currentLanguage = Language::getCurrentLanguage();
            ?>
            <!-- Carousel container -->
            <div id="crusadesCarousel" class="overflow-hidden">
                <div id="crusadesSlider" class="flex transition-transform duration-500 ease-in-out">
                    <?php foreach ($crusadesData['crusades'] as $crusade): 
                        // Translate crusade content for current language
                        $translatedCrusade = $translationService->translateCrusade($crusade, $currentLanguage);
                        
                        $eventDate = new DateTime($crusade['date'] . ' ' . $crusade['time']);
                        $isPastEvent = $eventDate < $currentDate;
                        $eventClass = $isPastEvent ? 'opacity-60' : '';
                    ?>
                    <div class="w-full flex-shrink-0 px-4 sm:px-6">
                        <div class="bg-white border border-gray-200 overflow-hidden <?php echo $eventClass; ?>">
                            <div class="relative flex flex-col lg:flex-row">
                                <!-- Past Event Badge -->
                                <?php if ($isPastEvent): ?>
                                <div class="absolute top-4 right-4 bg-red-600 text-white text-xs font-bold px-3 py-1 rounded-full z-10 shadow-md">
                                    Past Event
                                </div>
                                <?php endif; ?>
                                <!-- Image Section -->
                                <div class="w-full lg:w-1/2 relative">
                                    <div class="aspect-[16/9] sm:aspect-[4/3] lg:aspect-auto lg:h-full overflow-hidden">
                                        <img class="w-full h-full object-cover" src="<?php echo htmlspecialchars($crusade['image']); ?>" alt="<?php echo htmlspecialchars($translatedCrusade['title']); ?>">
                                    </div>
                                </div>

                                <!-- Content Section -->
                                <div class="w-full lg:w-1/2 p-6 sm:p-8 lg:p-12 flex flex-col justify-center">
                                    <div class="space-y-6 lg:space-y-8">
                                        <h3 class="text-2xl sm:text-3xl lg:text-4xl font-light text-gray-900 leading-tight"><?php echo htmlspecialchars($translatedCrusade['title']); ?></h3>
                                        <p class="text-base sm:text-lg text-gray-600 leading-relaxed"><?php echo htmlspecialchars($translatedCrusade['description']); ?></p>

                                        <!-- Event Details -->
                                        <div class="space-y-3 sm:space-y-4">
                                            <div class="flex items-start sm:items-center">
                                                <svg class="w-5 h-5 text-primary mr-3 mt-0.5 sm:mt-0 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                </svg>
                                                <div class="flex flex-col sm:flex-row sm:items-center">
                                                    <span class="text-sm font-medium text-gray-500 sm:w-20 sm:mr-2"><?php echo __('event_date'); ?>:</span>
                                                    <span class="text-sm sm:text-base text-gray-900 font-medium">
                                                        <?php echo $eventDate->format('F j, Y'); ?> at <?php echo $eventDate->format('g:i A'); ?>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="flex items-start sm:items-center">
                                                <svg class="w-5 h-5 text-primary mr-3 mt-0.5 sm:mt-0 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                </svg>
                                                <div class="flex flex-col">
                                                    <span class="text-sm font-medium text-gray-500"><?php echo __('venue'); ?></span>
                                                    <span class="text-sm sm:text-base text-gray-900 font-medium">
                                                        <?php echo htmlspecialchars($translatedCrusade['venue']); ?><br>
                                                        <?php echo htmlspecialchars($crusade['address']); ?>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>

                                        <?php if (!$isPastEvent): ?>
                                        <div class="pt-2 flex flex-col sm:flex-row gap-4">
                                            <a href="<?php echo htmlspecialchars($crusade['register_link']); ?>" class="inline-flex items-center text-primary font-medium hover:text-red-700 transition-colors duration-200">
                                                <?php echo __('register_now'); ?>
                                                <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                                </svg>
                                            </a>
                                            <a href="pastor-enrollment.php?crusade_id=<?php echo $crusade['id']; ?>&espees_code=RORETC" class="inline-flex items-center px-6 py-3 bg-primary text-white font-medium hover:bg-red-700 transition-colors duration-200 shadow-md hover:shadow-lg">
                                                Join Us
                                            </a>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Navigation Buttons -->
            <button id="prevButton" class="absolute top-1/2 -left-2 sm:-left-8 lg:-left-16 transform -translate-y-1/2 w-10 h-10 sm:w-12 sm:h-12 bg-white border border-gray-200 text-gray-400 hover:text-primary hover:border-primary transition-colors duration-200 focus:outline-none z-10 shadow-sm">
                <svg class="w-4 h-4 sm:w-5 sm:h-5 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <button id="nextButton" class="absolute top-1/2 -right-2 sm:-right-8 lg:-right-16 transform -translate-y-1/2 w-10 h-10 sm:w-12 sm:h-12 bg-white border border-gray-200 text-gray-400 hover:text-primary hover:border-primary transition-colors duration-200 focus:outline-none z-10 shadow-sm">
                <svg class="w-4 h-4 sm:w-5 sm:h-5 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </button>

            <!-- Carousel Indicators -->
            <div id="dotsContainer" class="flex justify-center mt-16 space-x-4">
                <?php foreach ($crusadesData['crusades'] as $index => $crusade): ?>
                <button
                    onclick="goToSlide(<?php echo $index; ?>)"
                    aria-label="Go to slide <?php echo $index + 1; ?>"
                    data-index="<?php echo $index; ?>"
                    class="w-3 h-3 bg-gray-300 hover:bg-primary focus:outline-none transition-colors duration-200 <?php echo $index === 0 ? 'bg-primary' : ''; ?>"
                ></button>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</section>

<!-- Give Section -->
<section id="give" class="py-24 bg-white">
    <div class="max-w-6xl mx-auto px-6">
        <div class="text-center mb-20">
            <h2 class="text-4xl font-light text-gray-900 mb-4"><?php echo __('give_title'); ?></h2>
            <p class="text-xl text-gray-600 mb-6"><?php echo __('give_subtitle'); ?></p>
            <div class="w-16 h-px bg-primary mx-auto"></div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <!-- Left Content -->
            <div class="space-y-8">
                <h3 class="text-3xl font-light text-gray-900"><?php echo __('give_join_us'); ?></h3>
                <p class="text-gray-600 leading-relaxed">
                    <?php echo __('give_description'); ?>
                </p>

                <div class="space-y-4">
                    <div class="flex items-center text-gray-600">
                        <svg class="w-5 h-5 text-primary mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <?php echo __('give_sponsor_crusades'); ?>
                    </div>
                    <div class="flex items-center text-gray-600">
                        <svg class="w-5 h-5 text-primary mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <?php echo __('give_fund_outreach'); ?>
                    </div>
                    <div class="flex items-center text-gray-600">
                        <svg class="w-5 h-5 text-primary mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <?php echo __('give_enable_distribution'); ?>
                    </div>
                </div>
            </div>

            <!-- Right Payment Form -->
            <div class="bg-gray-50 p-8 border border-gray-200">
                <h4 class="text-2xl font-light text-gray-900 mb-6"><?php echo __('give_select_amount'); ?></h4>

                <!-- Payment Method Selection -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">Choose Payment Method</label>
                    <div class="grid grid-cols-2 gap-3">
                        <label class="flex flex-col items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:border-primary transition-colors duration-200 payment-method-option">
                            <input type="radio" name="payment_method" value="card_payment" checked class="sr-only">
                            <svg class="w-8 h-8 mb-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                            </svg>
                            <span class="text-sm font-medium text-gray-900">Card Payment</span>
                        </label>
                        <label class="flex flex-col items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:border-primary transition-colors duration-200 payment-method-option">
                            <input type="radio" name="payment_method" value="espees" class="sr-only">
                            <img src="https://web.espees.org/espeesCoinOption5.png" alt="ESPEES" class="w-8 h-8 mb-2">
                            <span class="text-sm font-medium text-gray-900">ESPEES</span>
                        </label>
                    </div>
                </div>

                <!-- Card Payment Section (shown by default) -->
                <div id="card-payment-section">
                    <!-- Preset Amounts -->
                    <div class="grid grid-cols-3 gap-3 mb-6">
                        <button onclick="selectAmount(100)" class="amount-btn p-4 border border-gray-200 text-center hover:border-primary hover:bg-primary hover:text-white transition-all duration-200">
                            <div class="text-lg font-medium">$100</div>
                        </button>
                        <button onclick="selectAmount(500)" class="amount-btn p-4 border border-gray-200 text-center hover:border-primary hover:bg-primary hover:text-white transition-all duration-200">
                            <div class="text-lg font-medium">$500</div>
                        </button>
                        <button onclick="selectAmount(1000)" class="amount-btn p-4 border border-gray-200 text-center hover:border-primary hover:bg-primary hover:text-white transition-all duration-200">
                            <div class="text-lg font-medium">$1000</div>
                        </button>
                    </div>

                    <!-- Custom Amount -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('give_custom_amount'); ?></label>
                        <div class="relative">
                            <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                            <input type="number" id="customAmount" placeholder="<?php echo __('give_enter_amount'); ?>"
                                   class="w-full pl-8 pr-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200"
                                   min="1" step="0.01">
                        </div>
                    </div>
                </div>

                <!-- Donor Information (shown only for card payment) -->
                <div id="donor-info-section" class="space-y-4 mb-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('give_full_name'); ?></label>
                        <input type="text" id="donorName"
                               class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200"
                               placeholder="<?php echo __('give_enter_full_name'); ?>">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('give_email_address'); ?></label>
                        <input type="email" id="donorEmail"
                               class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200"
                               placeholder="<?php echo __('give_enter_email'); ?>">
                    </div>
                </div>

                <!-- Card Payment Button -->
                <button id="donate-button" onclick="processDonation()"
                        class="w-full py-4 bg-primary text-white font-medium hover:bg-red-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                    <?php echo __('give_donate_now'); ?>
                </button>

                <!-- ESPEES Code Display (hidden by default) -->
                <div id="espees-donation-section" class="hidden">
                    <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                        <h4 class="text-lg font-medium text-red-900 mb-3">ESPEES Payment Code</h4>
                        <div class="bg-white border border-red-300 rounded-lg p-4 text-center">
                            <span class="text-2xl font-bold text-red-700 tracking-wider">RORETC</span>
                        </div>
                        <p class="text-sm text-red-700 mt-3">
                            Use this code for your ESPEES payment. No verification required.
                        </p>
                    </div>
                </div>

                <!-- Security Notice -->
                <div id="security-notice" class="mt-4 text-center">
                    <p class="text-xs text-gray-500">
                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                        <?php echo __('give_secure_payment'); ?>
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
// Load highlights from JSON file
$highlightsJson = file_get_contents(__DIR__ . '/data/highlights.json');
$highlightsData = json_decode($highlightsJson, true);

// Check if there are any highlights to display
$hasHighlights = !empty($highlightsData['highlights']);
?>

<?php if ($hasHighlights): ?>
<!-- Highlights Section -->
<section id="highlights" class="py-24 bg-gray-50">
    <div class="max-w-6xl mx-auto px-6">
        <div class="text-center mb-20">
            <h2 class="text-4xl font-light text-gray-900 mb-4">R.E.C.C.O.R.D.s Highlights</h2>
            <p class="text-xl text-gray-600 mb-6"><?php echo __('highlights_subtitle'); ?></p>
            <div class="w-16 h-px bg-primary mx-auto"></div>
        </div>

        <div class="relative">
            <?php
            // Get translation service instance
            $translationService = TranslationService::getInstance();
            $currentLanguage = Language::getCurrentLanguage();
            ?>
            <!-- Highlights Carousel -->
            <div id="highlightsCarousel" class="overflow-hidden relative">
                <div id="highlightsSlider" class="flex transition-transform duration-500 ease-in-out">
                    <?php foreach ($highlightsData['highlights'] as $highlight): 
                        // Auto-translate content if enabled and not in English
                        $translatedTitle = $highlight['title'];
                        $translatedDescription = $highlight['description'];
                        $translatedCategory = $highlight['category'];
                        
                        if (isset($highlight['auto_translate']) && $highlight['auto_translate'] && $currentLanguage !== 'en') {
                            // Here you would implement your API translation service
                            // For now, we'll use the original content as fallback
                            $translatedTitle = $translationService->translateText($highlight['title'], 'en', $currentLanguage) ?? $highlight['title'];
                            $translatedDescription = $translationService->translateText($highlight['description'], 'en', $currentLanguage) ?? $highlight['description'];
                            $translatedCategory = $translationService->translateText($highlight['category'], 'en', $currentLanguage) ?? $highlight['category'];
                        }
                            
                        // Set badge color classes
                        $badgeColorClasses = [
                            'red' => 'bg-red-600',
                            'green' => 'bg-green-600',
                            'blue' => 'bg-blue-600',
                            'yellow' => 'bg-yellow-600',
                            'purple' => 'bg-purple-600'
                        ];
                        $badgeClass = isset($badgeColorClasses[$highlight['badge_color']]) ? 
                            $badgeColorClasses[$highlight['badge_color']] : 'bg-red-600';
                            
                        // Set category color classes
                        $categoryColorClasses = [
                            'red' => 'bg-red-100 text-red-800',
                            'green' => 'bg-green-100 text-green-800',
                            'blue' => 'bg-blue-100 text-blue-800',
                            'yellow' => 'bg-yellow-100 text-yellow-800',
                            'purple' => 'bg-purple-100 text-purple-800'
                        ];
                        $categoryClass = isset($categoryColorClasses[$highlight['badge_color']]) ? 
                            $categoryColorClasses[$highlight['badge_color']] : 'bg-red-100 text-red-800';
                            
                        // Format date
                        $highlightDate = new DateTime($highlight['date']);
                    ?>
                    <div class="w-full flex-shrink-0 px-4 sm:px-6">
                        <div class="bg-white border border-gray-200 overflow-hidden">
                            <div class="relative flex flex-col lg:flex-row">
                                <!-- Badge -->
                                <div class="absolute top-4 right-4 <?php echo $badgeClass; ?> text-white text-xs font-bold px-3 py-1 rounded-full z-10 shadow-md">
                                    <?php echo htmlspecialchars($highlight['badge']); ?>
                                </div>
                                
                                <!-- Image Section -->
                                <div class="w-full lg:w-1/2 relative">
                                    <div class="aspect-[16/9] sm:aspect-[4/3] lg:aspect-auto lg:h-full overflow-hidden">
                                        <?php 
                                        $hasVideos = !empty($highlight['videos']) && count($highlight['videos']) > 0;
                                        $hasImages = !empty($highlight['images']) && count($highlight['images']) > 0;
                                        $primaryImage = $hasImages ? $highlight['images'][0] : 'assets/images/default-highlight.jpg';
                                        $mediaCount = count($highlight['images'] ?? []) + count($highlight['videos'] ?? []);
                                        ?>
                                        <img class="w-full h-full object-cover" 
                                             src="<?php echo htmlspecialchars($primaryImage); ?>" 
                                             alt="<?php echo htmlspecialchars($translatedTitle); ?>">
                                    </div>
                                </div>

                                <!-- Content Section -->
                                <div class="w-full lg:w-1/2 p-6 sm:p-8 lg:p-12 flex flex-col justify-center">
                                    <div class="space-y-6 lg:space-y-8">
                                        <h3 class="text-2xl sm:text-3xl lg:text-4xl font-light text-gray-900 leading-tight"><?php echo htmlspecialchars($translatedTitle); ?></h3>
                                        <p class="text-base sm:text-lg text-gray-600 leading-relaxed"><?php echo htmlspecialchars($translatedDescription); ?></p>

                                        <!-- Event Details -->
                                        <div class="space-y-3 sm:space-y-4">
                                            <div class="flex items-start sm:items-center">
                                                <svg class="w-5 h-5 text-primary mr-3 mt-0.5 sm:mt-0 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                </svg>
                                                <div class="flex flex-col sm:flex-row sm:items-center">
                                                    <span class="text-sm font-medium text-gray-500 sm:w-20 sm:mr-2"><?php echo __('event_date'); ?>:</span>
                                                    <span class="text-sm sm:text-base text-gray-900 font-medium">
                                                        <?php echo $highlightDate->format('F j, Y'); ?>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="flex items-start sm:items-center">
                                                <svg class="w-5 h-5 text-primary mr-3 mt-0.5 sm:mt-0 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                </svg>
                                                <div class="flex flex-col">
                                                    <span class="text-sm font-medium text-gray-500"><?php echo __('venue'); ?></span>
                                                    <span class="text-sm sm:text-base text-gray-900 font-medium">
                                                        <?php echo htmlspecialchars($highlight['location']); ?>
                                                    </span>
                                                </div>
                                            </div>
                                            <?php if ($translatedCategory): ?>
                                            <div class="flex items-start sm:items-center">
                                                <svg class="w-5 h-5 text-primary mr-3 mt-0.5 sm:mt-0 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                                </svg>
                                                <div class="flex flex-col">
                                                    <span class="text-sm font-medium text-gray-500">Category:</span>
                                                    <span class="text-sm sm:text-base text-gray-900 font-medium">
                                                        <?php echo htmlspecialchars($translatedCategory); ?>
                                                    </span>
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                            <?php if ($hasImages || $hasVideos): ?>
                                            <div class="flex items-start sm:items-center">
                                                <svg class="w-5 h-5 text-primary mr-3 mt-0.5 sm:mt-0 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                                </svg>
                                                <div class="flex flex-col">
                                                    <span class="text-sm font-medium text-gray-500">Media:</span>
                                                    <span class="text-sm sm:text-base text-gray-900 font-medium">
                                                        <?php if ($hasImages): ?>
                                                            <?php echo count($highlight['images']); ?> Photo<?php echo count($highlight['images']) > 1 ? 's' : ''; ?>
                                                        <?php endif; ?>
                                                        <?php if ($hasImages && $hasVideos): ?>, <?php endif; ?>
                                                        <?php if ($hasVideos): ?>
                                                            <?php echo count($highlight['videos']); ?> Video<?php echo count($highlight['videos']) > 1 ? 's' : ''; ?>
                                                        <?php endif; ?>
                                                    </span>
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                        </div>

                                        <!-- CTA Button -->
                                        <div class="pt-4">
                                            <a href="highlight-details.php?id=<?php echo $highlight['id']; ?>" 
                                               class="inline-flex items-center text-primary font-medium hover:text-red-700 transition-colors duration-200">
                                                Read Full Report
                                                <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                                </svg>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Navigation Buttons -->
            <button id="highlightsPrevBtn" class="absolute top-1/2 -left-2 sm:-left-8 lg:-left-16 transform -translate-y-1/2 w-10 h-10 sm:w-12 sm:h-12 bg-white border border-gray-200 text-gray-400 hover:text-primary hover:border-primary transition-colors duration-200 focus:outline-none z-10 shadow-sm">
                <svg class="w-4 h-4 sm:w-5 sm:h-5 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <button id="highlightsNextBtn" class="absolute top-1/2 -right-2 sm:-right-8 lg:-right-16 transform -translate-y-1/2 w-10 h-10 sm:w-12 sm:h-12 bg-white border border-gray-200 text-gray-400 hover:text-primary hover:border-primary transition-colors duration-200 focus:outline-none z-10 shadow-sm">
                <svg class="w-4 h-4 sm:w-5 sm:h-5 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </button>

            <!-- Carousel Indicators -->
            <div id="highlightsDotsContainer" class="flex justify-center mt-16 space-x-4">
                <?php foreach ($highlightsData['highlights'] as $index => $highlight): ?>
                <button
                    onclick="goToHighlightSlide(<?php echo $index; ?>)"
                    aria-label="Go to highlight slide <?php echo $index + 1; ?>"
                    data-index="<?php echo $index; ?>"
                    class="w-3 h-3 bg-gray-300 hover:bg-primary focus:outline-none transition-colors duration-200 <?php echo $index === 0 ? 'bg-primary' : ''; ?>"
                ></button>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<?php
// Load media from JSON file
$mediaJsonFile = __DIR__ . '/data/media.json';
$hasMediaData = false;
$mediaData = ['media' => []];

if (file_exists($mediaJsonFile)) {
    $mediaJson = file_get_contents($mediaJsonFile);
    $mediaData = json_decode($mediaJson, true);
    $hasMediaData = !empty($mediaData['media']);
}

if ($hasMediaData) {
    // Sort media by date_added (newest first) and get only first 2
    usort($mediaData['media'], function($a, $b) {
        return strtotime($b['date_added']) - strtotime($a['date_added']);
    });
    $recentMedia = array_slice($mediaData['media'], 0, 2);
?>
<!-- Media Section -->
<section id="media" class="py-24 bg-white">
    <div class="max-w-6xl mx-auto px-6">
        <div class="text-center mb-20">
            <h2 class="text-4xl font-light text-gray-900 mb-4">Media</h2>
            <p class="text-xl text-gray-600 mb-6">Latest videos from our events</p>
            <div class="w-16 h-px bg-primary mx-auto"></div>
        </div>

        <div class="relative">
            <!-- Media Carousel -->
            <div id="mediaCarousel" class="overflow-hidden">
                <div id="mediaSlider" class="flex transition-transform duration-500 ease-in-out">
                    <?php foreach ($recentMedia as $media): ?>
                    <div class="w-full flex-shrink-0 px-4 sm:px-6">
                        <div class="bg-white border border-gray-200 overflow-hidden">
                            <div class="relative flex flex-col lg:flex-row">
                                <!-- Video Section -->
                                <div class="w-full lg:w-1/2 relative">
                                    <div class="aspect-video overflow-hidden bg-gray-100 relative group cursor-pointer" onclick="openVideoModal('<?php echo htmlspecialchars($media['video_url']); ?>', '<?php echo htmlspecialchars($media['title']); ?>')">
                                        <?php if (!empty($media['thumbnail'])): ?>
                                            <img src="<?php echo htmlspecialchars($media['thumbnail']); ?>" alt="<?php echo htmlspecialchars($media['title']); ?>" class="w-full h-full object-cover">
                                        <?php else: ?>
                                            <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                                                <svg class="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                                </svg>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <!-- Play Button Overlay -->
                                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                                            <div class="w-16 h-16 bg-primary bg-opacity-90 rounded-full flex items-center justify-center transition-all duration-300">
                                                <svg class="w-8 h-8 text-white ml-1" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M8 5v14l11-7z"/>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Content Section -->
                                <div class="w-full lg:w-1/2 p-6 sm:p-8 lg:p-12 flex flex-col justify-center">
                                    <div class="space-y-6 lg:space-y-8">
                                        <h3 class="text-2xl sm:text-3xl lg:text-4xl font-light text-gray-900 leading-tight">
                                            <?php echo htmlspecialchars($media['title']); ?>
                                        </h3>
                                        <p class="text-base sm:text-lg text-gray-600 leading-relaxed">
                                            <?php echo htmlspecialchars($media['description']); ?>
                                        </p>

                                        <!-- Media Details -->
                                        <div class="space-y-3 sm:space-y-4">
                                            <div class="flex items-start sm:items-center">
                                                <svg class="w-5 h-5 text-primary mr-3 mt-0.5 sm:mt-0 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                </svg>
                                                <div class="flex flex-col sm:flex-row sm:items-center">
                                                    <span class="text-sm font-medium text-gray-500 sm:w-20 sm:mr-2">Date:</span>
                                                    <span class="text-sm sm:text-base text-gray-900 font-medium">
                                                        <?php echo date('F j, Y', strtotime($media['date_added'])); ?>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="flex items-start sm:items-center">
                                                <svg class="w-5 h-5 text-primary mr-3 mt-0.5 sm:mt-0 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                                </svg>
                                                <div class="flex flex-col">
                                                    <span class="text-sm font-medium text-gray-500">Category:</span>
                                                    <span class="text-sm sm:text-base text-gray-900 font-medium capitalize">
                                                        <?php echo htmlspecialchars($media['category']); ?>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <?php if (count($recentMedia) > 1): ?>
            <!-- Navigation Buttons -->
            <button id="mediaPrevBtn" class="absolute top-1/2 -left-2 sm:-left-8 lg:-left-16 transform -translate-y-1/2 w-10 h-10 sm:w-12 sm:h-12 bg-white border border-gray-200 text-gray-400 hover:text-primary hover:border-primary transition-colors duration-200 focus:outline-none z-10 shadow-sm">
                <svg class="w-4 h-4 sm:w-5 sm:h-5 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <button id="mediaNextBtn" class="absolute top-1/2 -right-2 sm:-right-8 lg:-right-16 transform -translate-y-1/2 w-10 h-10 sm:w-12 sm:h-12 bg-white border border-gray-200 text-gray-400 hover:text-primary hover:border-primary transition-colors duration-200 focus:outline-none z-10 shadow-sm">
                <svg class="w-4 h-4 sm:w-5 sm:h-5 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </button>

            <!-- Carousel Indicators -->
            <div id="mediaDotsContainer" class="flex justify-center mt-16 space-x-4">
                <?php foreach ($recentMedia as $index => $media): ?>
                <button
                    onclick="goToMediaSlide(<?php echo $index; ?>)"
                    aria-label="Go to media slide <?php echo $index + 1; ?>"
                    data-index="<?php echo $index; ?>"
                    class="w-3 h-3 bg-gray-300 hover:bg-primary focus:outline-none transition-colors duration-200 <?php echo $index === 0 ? 'bg-primary' : ''; ?>"></button>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>

        <!-- View More Button -->
        <div class="text-center mt-16">
            <a href="media.php" class="inline-flex items-center px-6 py-3 bg-primary text-white font-medium hover:bg-red-700 transition-colors duration-200 shadow-md hover:shadow-lg">
                View More Videos
                <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                </svg>
            </a>
        </div>

    </div>
</section>
<?php } ?>

<!-- Products Section -->
<section id="products" class="py-24 bg-white">
    <div class="max-w-6xl mx-auto px-6">
        <div class="text-center mb-20">
            <h2 class="text-4xl font-light text-gray-900 mb-4"><?php echo __('our_products'); ?></h2>
            <div class="w-16 h-px bg-primary mx-auto"></div>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            <!-- Rhapsody Daily Card -->
            <div class="group">
                <div class="aspect-[4/3] overflow-hidden mb-6">
                    <img class="w-full h-full object-cover" src="assets/images/rhapsody.png" alt="<?php echo __('rhapsody_daily'); ?>">
                </div>
                <div class="space-y-4">
                    <h3 class="text-xl font-light text-gray-900"><?php echo __('rhapsody_daily'); ?></h3>
                    <p class="text-gray-600 text-sm leading-relaxed"><?php echo __('rhapsody_desc'); ?></p>
                    <a href="https://rhapsodyofrealities.org/" target="_blank" class="inline-flex items-center text-primary font-medium hover:text-red-700 transition-colors duration-200">
                        <?php echo __('learn_more_btn'); ?>
                        <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </a>
                </div>
            </div>

            <!-- Rhapsody TV Card -->
            <div class="group">
                <div class="aspect-[4/3] overflow-hidden mb-6">
                    <img class="w-full h-full object-cover" src="assets/images/rtv.jpg" alt="<?php echo __('rhapsody_tv_title'); ?>">
                </div>
                <div class="space-y-4">
                    <h3 class="text-xl font-light text-gray-900"><?php echo __('rhapsody_tv_title'); ?></h3>
                    <p class="text-gray-600 text-sm leading-relaxed"><?php echo __('rhapsody_tv_desc'); ?></p>
                    <a href="https://rhapsodytv.live/" target="_blank" class="inline-flex items-center text-primary font-medium hover:text-red-700 transition-colors duration-200">
                        <?php echo __('learn_more_btn'); ?>
                        <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </a>
                </div>
            </div>

            <!-- Reach Out World Card -->
            <div class="group">
                <div class="aspect-[4/3] overflow-hidden mb-6">
                    <img class="w-full h-full object-cover" src="assets/images/reachout.jpg" alt="<?php echo __('reachout_title'); ?>">
                </div>
                <div class="space-y-4">
                    <h3 class="text-xl font-light text-gray-900"><?php echo __('reachout_title'); ?></h3>
                    <p class="text-gray-600 text-sm leading-relaxed"><?php echo __('reachout_desc'); ?></p>
                    <a href="https://reachoutworld.org/" target="_blank" class="inline-flex items-center text-primary font-medium hover:text-red-700 transition-colors duration-200">
                        <?php echo __('learn_more_btn'); ?>
                        <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </a>
                </div>
            </div>

            <!-- Tap2Read Card -->
            <div class="group">
                <div class="aspect-[4/3] overflow-hidden mb-6">
                    <img class="w-full h-full object-cover" src="https://rhapsodyofrealities.b-cdn.net/rhapsodyofrealities.org/card_images/2024/TAP2READAPP%20(1).jpg" alt="<?php echo __('tap2read_title'); ?>">
                </div>
                <div class="space-y-4">
                    <h3 class="text-xl font-light text-gray-900"><?php echo __('tap2read_title'); ?></h3>
                    <p class="text-gray-600 text-sm leading-relaxed"><?php echo __('tap2read_desc'); ?></p>
                    <a href="https://shop.rhapsodytap2read.com/" target="_blank" class="inline-flex items-center text-primary font-medium hover:text-red-700 transition-colors duration-200">
                        <?php echo __('order_now'); ?>
                        <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- App Download Section -->
<section class="py-24 bg-gray-50">
    <div class="max-w-6xl mx-auto px-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div class="order-2 lg:order-1 space-y-8">
                <h2 class="text-4xl font-light text-gray-900"><?php echo __('download_app_title'); ?></h2>
                <p class="text-gray-600 leading-relaxed"><?php echo __('download_app_desc'); ?></p>
                <div>
                    <button
                        onclick="openDownloadModal()"
                        class="inline-flex items-center text-primary font-medium hover:text-red-700 transition-colors duration-200"
                    >
                        <?php echo __('download_app_btn'); ?>
                        <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <div class="order-1 lg:order-2">
                <div class="aspect-[4/3] overflow-hidden">
                    <img class="w-full h-full object-cover" src="https://lwappstore.com/developers/uploads/1604670119.jpg" alt="Download Rhapsody of Realities App">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Download Modal -->
<div id="downloadModal" class="fixed inset-0 z-50 hidden items-center justify-center bg-black bg-opacity-30">
    <div class="bg-white max-w-lg w-full mx-6 border border-gray-200">
        <div class="p-12">
            <!-- Header -->
            <div class="flex justify-between items-start mb-8">
                <h3 class="text-2xl font-light text-gray-900"><?php echo __('download_app_modal_title'); ?></h3>
                <button onclick="closeDownloadModal()" class="text-gray-400 hover:text-gray-600 focus:outline-none transition-colors duration-200">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Download Options -->
            <div class="space-y-6">
                <!-- LoveWorld App Store -->
                <div class="group">
                    <a href="https://web.lwappstore.com/share/appId-32181354074e5cf63319371178894acd" target="_blank"
                       class="block p-6 border border-gray-200 hover:border-primary transition-colors duration-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-lg font-medium text-gray-900 mb-2"><?php echo __('download_loveworld'); ?></h4>
                                <p class="text-sm text-gray-600">Official LoveWorld App Store</p>
                            </div>
                            <svg class="w-5 h-5 text-gray-400 group-hover:text-primary transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                            </svg>
                        </div>
                    </a>
                </div>

                <!-- Google Play Store -->
                <div class="group">
                    <a href="https://play.google.com/store/apps/details?id=com.rhapsodyreader&pli=1" target="_blank"
                       class="block p-6 border border-gray-200 hover:border-primary transition-colors duration-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-lg font-medium text-gray-900 mb-2"><?php echo __('download_playstore'); ?></h4>
                                <p class="text-sm text-gray-600">Available on Google Play</p>
                            </div>
                            <svg class="w-5 h-5 text-gray-400 group-hover:text-primary transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                            </svg>
                        </div>
                    </a>
                </div>

                <!-- App Store (if available) -->
                <div class="group">
                    <a href="https://apps.apple.com/app/rhapsody-of-realities/id1114966583" target="_blank"
                       class="block p-6 border border-gray-200 hover:border-primary transition-colors duration-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-lg font-medium text-gray-900 mb-2">Download from App Store</h4>
                                <p class="text-sm text-gray-600">Available on iOS App Store</p>
                            </div>
                            <svg class="w-5 h-5 text-gray-400 group-hover:text-primary transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                            </svg>
                        </div>
                    </a>
                </div>
            </div>

            <!-- Footer Note -->
            <div class="mt-8 pt-6 border-t border-gray-100">
                <p class="text-sm text-gray-500 text-center">Choose your preferred platform to download the app</p>
            </div>
        </div>
    </div>
</div>

<style>
/* Custom Animations for Tailwind */
@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-left {
    animation: fadeInLeft 0.8s ease-out forwards;
}

.animate-fade-in-right {
    animation: fadeInRight 0.8s ease-out forwards;
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
}

/* Default state for scroll animations */
.scroll-element, .scroll-element-left, .scroll-element-right, .scroll-card {
    opacity: 0;
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.scroll-element.visible {
    opacity: 1;
    transform: translateY(0);
}

.scroll-element-left.visible {
    opacity: 1;
    transform: translateX(0);
}

.scroll-element-right.visible {
    opacity: 1;
    transform: translateX(0);
}

.scroll-card.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Initial positions */
.scroll-element {
    transform: translateY(30px);
}

.scroll-element-left {
    transform: translateX(-30px);
}

.scroll-element-right {
    transform: translateX(30px);
}

.scroll-card {
    transform: translateY(30px);
}
</style>

<script>
function openDownloadModal() {
    document.getElementById('downloadModal').style.display = 'flex';
    document.body.style.overflow = 'hidden';
}

function closeDownloadModal() {
    document.getElementById('downloadModal').style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Close modal when clicking outside
document.getElementById('downloadModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDownloadModal();
    }
});

// Scroll animation function
function initScrollAnimations() {
    const scrollElements = document.querySelectorAll('.scroll-element, .scroll-element-left, .scroll-element-right');
    const scrollCards = document.querySelectorAll('.scroll-card');

    // Check if element is in viewport
    function isElementInViewport(el) {
        const rect = el.getBoundingClientRect();
        return (
            rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.8
        );
    }

    // Handle scroll animation
    function handleScrollAnimation() {
        // Animate sections
        scrollElements.forEach(element => {
            if (isElementInViewport(element) && !element.classList.contains('visible')) {
                element.classList.add('visible');
            }
        });

        // Animate cards with delay
        scrollCards.forEach(card => {
            if (isElementInViewport(card) && !card.classList.contains('visible')) {
                const delay = card.getAttribute('data-delay') || 0;
                setTimeout(() => {
                    card.classList.add('visible');
                }, delay);
            }
        });
    }

    // Run on scroll
    window.addEventListener('scroll', handleScrollAnimation);

    // Run on load
    handleScrollAnimation();
}

// Smooth scroll for anchors
document.addEventListener('DOMContentLoaded', function() {
    const learnMoreBtn = document.querySelector('a[href="#highlights"]');

    if (learnMoreBtn) {
        learnMoreBtn.addEventListener('click', function(e) {
            e.preventDefault();

            const targetSection = document.getElementById('highlights');
            if (targetSection) {
                // Get the header height to adjust scroll position
                const headerHeight = document.querySelector('header').offsetHeight;

                // Calculate the top position of the target section
                const targetPosition = targetSection.getBoundingClientRect().top + window.pageYOffset - headerHeight;

                // Smooth scroll to the target
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    }
});

// Enhanced carousel functionality
function goToSlide(index) {
    const slider = document.getElementById('crusadesSlider');
    const dots = document.querySelectorAll('#dotsContainer button');

    if (slider) {
        slider.style.transform = `translateX(-${index * 100}%)`;

        // Update active dot
        dots.forEach((dot, i) => {
            if (i === index) {
                dot.classList.add('bg-primary');
                dot.classList.remove('bg-gray-300');
            } else {
                dot.classList.remove('bg-primary');
                dot.classList.add('bg-gray-300');
            }
        });
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const prevButton = document.getElementById('prevButton');
    const nextButton = document.getElementById('nextButton');
    const slider = document.getElementById('crusadesSlider');
    const dots = document.querySelectorAll('#dotsContainer button');

    if (prevButton && nextButton && slider) {
        let currentIndex = 0;
        const slides = slider.children.length;

        // Initialize first dot as active
        if (dots.length > 0) {
            dots[0].classList.add('bg-primary');
            dots[0].classList.remove('bg-gray-300');
        }

        prevButton.addEventListener('click', function() {
            currentIndex = (currentIndex - 1 + slides) % slides;
            goToSlide(currentIndex);
        });

        nextButton.addEventListener('click', function() {
            currentIndex = (currentIndex + 1) % slides;
            goToSlide(currentIndex);
        });

        // Auto-advance carousel every 5 seconds
        setInterval(function() {
            currentIndex = (currentIndex + 1) % slides;
            goToSlide(currentIndex);
        }, 5000);
    }

    // Initialize scroll animations
    initScrollAnimations();
    
    // Initialize highlights carousel
    initHighlightsCarousel();
});

// Typewriter Animation
const words = [
    '<?php echo __('typewriter_crusades'); ?>',
    '<?php echo __('typewriter_conferences'); ?>',
    '<?php echo __('typewriter_outreaches'); ?>',
    '<?php echo __('typewriter_rallies'); ?>',
    '<?php echo __('typewriter_distributions'); ?>'
];
let currentWordIndex = 0;
let currentCharIndex = 0;
let isDeleting = false;
let typeSpeed = 150;
let deleteSpeed = 100;
let pauseTime = 2000;

function typeWriter() {
    const typewriterElement = document.getElementById('typewriter-text');
    const cursorElement = document.getElementById('cursor');
    const currentWord = words[currentWordIndex];

    if (isDeleting) {
        // Deleting characters
        const displayText = currentWord.substring(0, currentCharIndex - 1);
        typewriterElement.innerHTML = displayText + '<span id="cursor" class="animate-pulse text-red-500 font-bold">|</span>';
        currentCharIndex--;

        if (currentCharIndex === 0) {
            isDeleting = false;
            currentWordIndex = (currentWordIndex + 1) % words.length;
            // Add scaling animation when starting new word
            typewriterElement.classList.add('text-changing');
            setTimeout(() => {
                typewriterElement.classList.remove('text-changing');
            }, 300);
            setTimeout(typeWriter, 500); // Pause before typing next word
            return;
        }
        setTimeout(typeWriter, deleteSpeed);
    } else {
        // Typing characters
        const displayText = currentWord.substring(0, currentCharIndex + 1);
        typewriterElement.innerHTML = displayText + '<span id="cursor" class="animate-pulse text-red-500 font-bold">|</span>';
        currentCharIndex++;

        if (currentCharIndex === currentWord.length) {
            isDeleting = true;
            // Add scaling animation when word is complete
            typewriterElement.classList.add('text-changing');
            setTimeout(() => {
                typewriterElement.classList.remove('text-changing');
            }, 300);
            setTimeout(typeWriter, pauseTime); // Pause when word is complete
            return;
        }
        setTimeout(typeWriter, typeSpeed);
    }
}

// Highlights carousel functionality
function goToHighlightSlide(index) {
    const slider = document.getElementById('highlightsSlider');
    const dots = document.querySelectorAll('#highlightsDotsContainer button');

    if (slider) {
        slider.style.transform = `translateX(-${index * 100}%)`;

        // Update active dot
        dots.forEach((dot, i) => {
            if (i === index) {
                dot.classList.add('bg-primary');
                dot.classList.remove('bg-gray-300');
            } else {
                dot.classList.remove('bg-primary');
                dot.classList.add('bg-gray-300');
            }
        });
    }
}

function initHighlightsCarousel() {
    const prevButton = document.getElementById('highlightsPrevBtn');
    const nextButton = document.getElementById('highlightsNextBtn');
    const slider = document.getElementById('highlightsSlider');
    const dots = document.querySelectorAll('#highlightsDotsContainer button');

    if (prevButton && nextButton && slider) {
        let currentIndex = 0;
        let autoAdvanceInterval;
        const slides = slider.children.length;

        // Initialize first dot as active
        if (dots.length > 0) {
            dots[0].classList.add('bg-primary');
            dots[0].classList.remove('bg-gray-300');
        }

        // Function to start auto-advance
        function startAutoAdvance() {
            if (autoAdvanceInterval) clearInterval(autoAdvanceInterval);
            autoAdvanceInterval = setInterval(function() {
                // Check if any video is playing before advancing
                if (!isAnyVideoPlaying()) {
                    currentIndex = (currentIndex + 1) % slides;
                    goToHighlightSlide(currentIndex);
                }
            }, 20000);
        }

        // Function to stop auto-advance
        function stopAutoAdvance() {
            if (autoAdvanceInterval) {
                clearInterval(autoAdvanceInterval);
                autoAdvanceInterval = null;
            }
        }

        prevButton.addEventListener('click', function() {
            currentIndex = (currentIndex - 1 + slides) % slides;
            goToHighlightSlide(currentIndex);
        });

        nextButton.addEventListener('click', function() {
            currentIndex = (currentIndex + 1) % slides;
            goToHighlightSlide(currentIndex);
        });

        // Start auto-advance initially
        startAutoAdvance();
        
        // Add keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') {
                currentIndex = (currentIndex - 1 + slides) % slides;
                goToHighlightSlide(currentIndex);
            } else if (e.key === 'ArrowRight') {
                currentIndex = (currentIndex + 1) % slides;
                goToHighlightSlide(currentIndex);
            }
        });

        // Add video event listeners to pause/resume carousel
        const videos = document.querySelectorAll('.highlight-video');
        videos.forEach(video => {
            video.addEventListener('play', stopAutoAdvance);
            video.addEventListener('pause', startAutoAdvance);
            video.addEventListener('ended', startAutoAdvance);
            
            // Add error handling for video loading
            video.addEventListener('error', function(e) {
                console.error('Video loading error:', e);
                console.error('Video source:', video.src || video.querySelector('source')?.src);
            });
            
            video.addEventListener('loadstart', function() {
                console.log('Video loading started:', video.src || video.querySelector('source')?.src);
            });
            
            video.addEventListener('canplay', function() {
                console.log('Video can play:', video.src || video.querySelector('source')?.src);
            });
        });
    }
}

// Function to check if any video is currently playing
function isAnyVideoPlaying() {
    const videos = document.querySelectorAll('.highlight-video');
    return Array.from(videos).some(video => !video.paused);
}

// Function to handle mixed media navigation in highlights
function showHighlightMedia(highlightId, mediaIndex) {
    const slider = document.querySelector(`[data-highlight-id="${highlightId}"]`);
    if (!slider) return;
    
    const mediaItems = slider.querySelectorAll('.media-item');
    const dots = slider.querySelectorAll('button');
    
    // Hide all media items
    mediaItems.forEach((item, index) => {
        if (index === mediaIndex) {
            item.classList.remove('hidden');
            item.classList.add('active');
        } else {
            item.classList.add('hidden');
            item.classList.remove('active');
            
            // Pause any videos that are now hidden
            const video = item.querySelector('video');
            if (video) {
                video.pause();
            }
        }
    });
    
    // Update dots
    dots.forEach((dot, index) => {
        if (index === mediaIndex) {
            dot.classList.remove('bg-opacity-50');
            dot.classList.add('bg-opacity-100');
        } else {
            dot.classList.remove('bg-opacity-100');
            dot.classList.add('bg-opacity-50');
        }
    });
}

// Function to change media with direction
function changeHighlightMedia(highlightId, direction) {
    const slider = document.querySelector(`[data-highlight-id="${highlightId}"]`);
    if (!slider) return;
    
    const mediaCount = parseInt(slider.getAttribute('data-media-count'));
    const currentActive = slider.querySelector('.media-item.active');
    const currentIndex = Array.from(slider.querySelectorAll('.media-item')).indexOf(currentActive);
    
    let newIndex = currentIndex + direction;
    if (newIndex < 0) {
        newIndex = mediaCount - 1;
    } else if (newIndex >= mediaCount) {
        newIndex = 0;
    }
    
    showHighlightMedia(highlightId, newIndex);
}

// Legacy function for backward compatibility
function showHighlightImage(highlightId, imageIndex) {
    showHighlightMedia(highlightId, imageIndex);
}

// Auto-cycle through media in highlights with multiple items
function initHighlightMediaCycling() {
    const sliders = document.querySelectorAll('.highlight-media-slider');
    
    sliders.forEach(slider => {
        const mediaItems = slider.querySelectorAll('.media-item');
        const mediaCount = parseInt(slider.getAttribute('data-media-count'));
        
        if (mediaCount <= 1) return;
        
        const highlightId = slider.getAttribute('data-highlight-id');
        let currentIndex = 0;
        let cyclingInterval;
        
        function startCycling() {
            if (cyclingInterval) clearInterval(cyclingInterval);
            cyclingInterval = setInterval(() => {
                // Only cycle if no video is currently playing in this slider
                const activeVideo = slider.querySelector('.media-item.active video');
                if (!activeVideo || activeVideo.paused) {
                    currentIndex = (currentIndex + 1) % mediaCount;
                    showHighlightMedia(highlightId, currentIndex);
                }
            }, 6000); // Change media every 6 seconds
        }
        
        function stopCycling() {
            if (cyclingInterval) {
                clearInterval(cyclingInterval);
                cyclingInterval = null;
            }
        }
        
        // Start cycling
        startCycling();
        
        // Add event listeners to pause cycling when videos play
        const videos = slider.querySelectorAll('video');
        videos.forEach(video => {
            video.addEventListener('play', stopCycling);
            video.addEventListener('pause', startCycling);
            video.addEventListener('ended', startCycling);
        });
    });
}

// Handle payment method selection for donations
document.addEventListener('DOMContentLoaded', function() {
    const paymentMethodRadios = document.querySelectorAll('input[name="payment_method"]');
    const paymentMethodOptions = document.querySelectorAll('.payment-method-option');
    const cardPaymentSection = document.getElementById('card-payment-section');
    const donorInfoSection = document.getElementById('donor-info-section');
    const donateButton = document.getElementById('donate-button');
    const espeesSection = document.getElementById('espees-donation-section');
    const securityNotice = document.getElementById('security-notice');

    // Update visual appearance of payment method selection
    function updatePaymentMethodUI() {
        paymentMethodOptions.forEach(option => {
            const radio = option.querySelector('input[type="radio"]');
            if (radio.checked) {
                option.classList.add('border-primary', 'bg-red-50');
                option.classList.remove('border-gray-200');
            } else {
                option.classList.remove('border-primary', 'bg-red-50');
                option.classList.add('border-gray-200');
            }
        });
    }

    paymentMethodRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            updatePaymentMethodUI();
            
            if (this.value === 'card_payment') {
                cardPaymentSection.classList.remove('hidden');
                donorInfoSection.classList.remove('hidden');
                donateButton.classList.remove('hidden');
                espeesSection.classList.add('hidden');
                securityNotice.classList.remove('hidden');
            } else if (this.value === 'espees') {
                cardPaymentSection.classList.add('hidden');
                donorInfoSection.classList.add('hidden');
                donateButton.classList.add('hidden');
                espeesSection.classList.remove('hidden');
                securityNotice.classList.add('hidden');
            }
        });
    });

    // Initialize the UI
    updatePaymentMethodUI();

    setTimeout(typeWriter, 1000); // Start after 1 second
    initHighlightMediaCycling(); // Initialize media cycling
    initMediaCarousel(); // Initialize media carousel
});

// Media carousel functionality
function goToMediaSlide(index) {
    const slider = document.getElementById('mediaSlider');
    const dots = document.querySelectorAll('#mediaDotsContainer button');

    if (slider) {
        slider.style.transform = `translateX(-${index * 100}%)`;

        // Update active dot
        dots.forEach((dot, i) => {
            if (i === index) {
                dot.classList.add('bg-primary');
                dot.classList.remove('bg-gray-300');
            } else {
                dot.classList.remove('bg-primary');
                dot.classList.add('bg-gray-300');
            }
        });
    }
}

function initMediaCarousel() {
    const prevButton = document.getElementById('mediaPrevBtn');
    const nextButton = document.getElementById('mediaNextBtn');
    const slider = document.getElementById('mediaSlider');
    const dots = document.querySelectorAll('#mediaDotsContainer button');

    if (prevButton && nextButton && slider) {
        let currentIndex = 0;
        const slides = slider.children.length;

        // Initialize first dot as active
        if (dots.length > 0) {
            dots[0].classList.add('bg-primary');
            dots[0].classList.remove('bg-gray-300');
        }

        prevButton.addEventListener('click', function() {
            currentIndex = (currentIndex - 1 + slides) % slides;
            goToMediaSlide(currentIndex);
        });

        nextButton.addEventListener('click', function() {
            currentIndex = (currentIndex + 1) % slides;
            goToMediaSlide(currentIndex);
        });

        // Auto-advance carousel every 10 seconds
        setInterval(function() {
            currentIndex = (currentIndex + 1) % slides;
            goToMediaSlide(currentIndex);
        }, 10000);
    }
}
</script>

<style>
/* Enhanced typewriter animation styles */
#typewriter-text {
    position: relative;
    display: inline-block;
    animation: textGlow 3s ease-in-out infinite alternate;
    white-space: nowrap;
    min-width: 250px;
}

#cursor {
    animation: cursorBlink 1s infinite;
}

@keyframes textGlow {
    0% {
        text-shadow: 0 0 5px rgba(220, 38, 38, 0.3);
    }
    100% {
        text-shadow: 0 0 20px rgba(220, 38, 38, 0.6), 0 0 30px rgba(220, 38, 38, 0.4);
    }
}

@keyframes cursorBlink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0;
    }
}

/* Add a subtle scale animation when text changes */
.text-changing {
    animation: textScale 0.3s ease-in-out;
}

@keyframes textScale {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Mobile-specific fixes for typewriter animation */
@media (max-width: 768px) {
    #typewriter-text {
        min-width: 200px;
    }
    
    .hero-title-container {
        position: relative;
        min-height: 3.5rem;
        overflow: visible;
    }
}
</style>

<!-- SweetAlert2 and Animate.css -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css">
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- Make translations available to JavaScript -->
<script>
window.translations = {
    payment_complete_giving: '<?php echo addslashes(__('payment_complete_giving')); ?>',
    payment_giving_amount: '<?php echo addslashes(__('payment_giving_amount')); ?>',
    payment_donor: '<?php echo addslashes(__('payment_donor')); ?>',
    payment_complete_giving_btn: '<?php echo addslashes(__('payment_complete_giving_btn')); ?>',
    payment_processing: '<?php echo addslashes(__('payment_processing')); ?>',
    payment_secure_stripe: '<?php echo addslashes(__('payment_secure_stripe')); ?>',
    success_thank_you: '<?php echo addslashes(__('success_thank_you')); ?>',
    success_message: '<?php echo addslashes(__('success_message')); ?>',
    success_continue: '<?php echo addslashes(__('success_continue')); ?>'
};
</script>

<!-- Video Modal -->
<div id="videoModal" class="fixed inset-0 z-50 hidden items-center justify-center bg-black bg-opacity-30">
    <div class="bg-white max-w-4xl w-full mx-6 border border-gray-200">
        <div class="flex justify-between items-start p-12">
            <h3 id="modalTitle" class="text-2xl font-light text-gray-900"></h3>
            <button onclick="closeVideoModal()" class="text-gray-400 hover:text-gray-600 focus:outline-none transition-colors duration-200">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <div class="aspect-video px-12 pb-12">
            <iframe id="modalVideo" class="w-full h-full" src="" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen playsinline webkit-playsinline></iframe>
        </div>
    </div>
</div>

<script>
function openVideoModal(videoUrl, title) {
    document.getElementById('modalTitle').textContent = title;
    
    // Add autoplay parameter for mobile fullscreen
    let finalUrl = videoUrl;
    if (videoUrl.includes('youtube.com') || videoUrl.includes('youtu.be')) {
        finalUrl += (videoUrl.includes('?') ? '&' : '?') + 'autoplay=1&playsinline=0';
    }
    
    document.getElementById('modalVideo').src = finalUrl;
    document.getElementById('videoModal').style.display = 'flex';
    document.body.style.overflow = 'hidden';
    
    // Force fullscreen on mobile devices
    if (window.innerWidth <= 768) {
        setTimeout(() => {
            const iframe = document.getElementById('modalVideo');
            if (iframe.requestFullscreen) {
                iframe.requestFullscreen();
            } else if (iframe.webkitRequestFullscreen) {
                iframe.webkitRequestFullscreen();
            } else if (iframe.mozRequestFullScreen) {
                iframe.mozRequestFullScreen();
            } else if (iframe.msRequestFullscreen) {
                iframe.msRequestFullscreen();
            }
        }, 500);
    }
}

function closeVideoModal() {
    document.getElementById('modalVideo').src = '';
    document.getElementById('videoModal').style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Close modal when clicking outside
document.getElementById('videoModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeVideoModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeVideoModal();
    }
});
</script>

<!-- Stripe and Donation Scripts -->
<script src="https://js.stripe.com/v3/"></script>
<script src="assets/js/donation.js"></script>

<?php include 'includes/footer.php'; ?>