<?php
require_once 'includes/config.php';
require_once 'includes/languages.php';
require_once 'includes/db.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$success = false;
$errors = [];

// Check for session errors and form data
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if there are any errors in the session
if (isset($_SESSION['form_errors'])) {
    $errors = $_SESSION['form_errors'];
    unset($_SESSION['form_errors']);
}

// Check if there is form data in the session
if (isset($_SESSION['form_data'])) {
    $_POST = $_SESSION['form_data'];
    unset($_SESSION['form_data']);
}

// Redirect to unified registration system
header('Location: register.php?event=la-gloria-de-su-presencia');
exit;
?>

<?php include 'includes/header.php'; ?>

<!-- Event Banner -->
<section class="py-16 bg-gray-50">
    <div class="max-w-6xl mx-auto px-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div class="space-y-6">
                <h1 class="text-4xl font-light text-gray-900">La Gloria de Su Presencia</h1>
                <p class="text-xl text-gray-600 leading-relaxed">Join us for this transformative spiritual gathering</p>
            </div>
            <div class="aspect-[4/3] overflow-hidden">
                <img src="assets/images/gloria.jpg" alt="La Gloria de Su Presencia" class="w-full h-full object-cover">
            </div>
        </div>
    </div>
</section>

<!-- Registration Form Section -->
<section class="py-24 bg-white">
    <div class="max-w-4xl mx-auto px-6">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-light text-gray-900 mb-6">Registration Form</h2>
            <div class="w-16 h-px bg-primary mx-auto mb-6"></div>
            <p class="text-xl text-gray-600 leading-relaxed">Join us for a life-changing experience at Manga de cóleo Angelo Hernández, Sta Teresa del tuy on April 18, 2025.</p>
        </div>

        <?php if ($success): ?>
        <div class="bg-green-50 border border-green-200 text-green-800 px-6 py-4 mb-8" role="alert">
            <p class="font-medium"><?php echo __('registration_success'); ?></p>
            <p class="text-sm mt-1"><?php echo __('check_email'); ?></p>
        </div>
        <?php endif; ?>

        <?php if (!empty($errors)): ?>
        <div class="bg-red-50 border border-red-200 text-red-800 px-6 py-4 mb-8" role="alert">
            <p class="font-medium mb-2">Please correct the following errors:</p>
            <ul class="list-disc list-inside text-sm space-y-1">
                <?php foreach ($errors as $error): ?>
                <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php endif; ?>

        <form action="register-handler.php" method="post" class="bg-gray-50 border border-gray-200 p-12">
            <!-- Hidden fields for form processing -->
            <input type="hidden" name="form_source" value="la-gloria-de-su-presencia.php">
            <input type="hidden" name="crusade_slug" value="la-gloria-de-su-presencia">
            <!-- Personal Details -->
            <div class="mb-12">
                <h3 class="text-2xl font-light text-gray-900 mb-8"><?php echo __('personal_details'); ?></h3>

                <div class="mb-6">
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('title'); ?></label>
                    <select id="title" name="title" class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200">
                        <option value="Mr."><?php echo __('mr'); ?></option>
                        <option value="Mrs."><?php echo __('mrs'); ?></option>
                        <option value="Ms."><?php echo __('ms'); ?></option>
                        <option value="Dr."><?php echo __('dr'); ?></option>
                        <option value="Pastor"><?php echo __('pastor'); ?></option>
                        <option value="Other"><?php echo __('other'); ?></option>
                    </select>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('first_name'); ?> *</label>
                        <input type="text" id="first_name" name="first_name" value="<?php echo isset($_POST['first_name']) ? htmlspecialchars($_POST['first_name']) : ''; ?>" class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200" required>
                    </div>

                    <div>
                        <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('last_name'); ?> *</label>
                        <input type="text" id="last_name" name="last_name" value="<?php echo isset($_POST['last_name']) ? htmlspecialchars($_POST['last_name']) : ''; ?>" class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200" required>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('email'); ?> *</label>
                        <input type="email" id="email" name="email" value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200" required>
                    </div>

                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('phone'); ?> *</label>
                        <input type="tel" id="phone" name="phone" value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>" class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200" required>
                    </div>
                </div>

                <div class="mb-6">
                    <label for="address" class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('address'); ?></label>
                    <textarea id="address" name="address" rows="3" class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200"><?php echo isset($_POST['address']) ? htmlspecialchars($_POST['address']) : ''; ?></textarea>
                </div>
            </div>

            <!-- Invitation Details -->
            <div class="mb-12">
                <h3 class="text-2xl font-light text-gray-900 mb-8"><?php echo __('invitation_details'); ?></h3>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-4"><?php echo __('how_did_you_hear'); ?></label>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <input type="radio" id="source_social" name="source" value="Social Media" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="source_social" class="ml-3 text-gray-700"><?php echo __('social_media'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="source_friend" name="source" value="Friend/Family" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="source_friend" class="ml-3 text-gray-700"><?php echo __('friend_family'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="source_church" name="source" value="Church/Pastor" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="source_church" class="ml-3 text-gray-700"><?php echo __('church_pastor'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="source_online" name="source" value="Online Search" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="source_online" class="ml-3 text-gray-700"><?php echo __('online_search'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="source_other" name="source" value="Other" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="source_other" class="ml-3 text-gray-700"><?php echo __('other'); ?></label>
                            <input type="text" name="source_other_text" class="ml-3 px-4 py-2 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200">
                        </div>
                    </div>
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-4"><?php echo __('interested_in_attending'); ?></label>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <input type="radio" id="attending_yes" name="attending" value="Yes" <?php echo (isset($_POST['attending']) && $_POST['attending'] === 'Yes') ? 'checked' : ''; ?> <?php echo !isset($_POST['attending']) ? 'checked' : ''; ?> class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="attending_yes" class="ml-3 text-gray-700"><?php echo __('yes'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="attending_no" name="attending" value="No" <?php echo (isset($_POST['attending']) && $_POST['attending'] === 'No') ? 'checked' : ''; ?> class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="attending_no" class="ml-3 text-gray-700"><?php echo __('no'); ?></label>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label for="coming_with" class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('how_many_coming'); ?></label>
                        <input type="number" id="coming_with" name="coming_with" min="0" value="<?php echo isset($_POST['coming_with']) ? htmlspecialchars($_POST['coming_with']) : '0'; ?>" class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200">
                    </div>

                    <div>
                        <label for="inviting" class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('how_many_inviting'); ?></label>
                        <input type="number" id="inviting" name="inviting" min="0" value="<?php echo isset($_POST['inviting']) ? htmlspecialchars($_POST['inviting']) : '0'; ?>" class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200">
                    </div>
                </div>


            </div>

            <!-- Prayer and Healing -->
            <div class="mb-12">
                <h3 class="text-2xl font-light text-gray-900 mb-8"><?php echo __('prayer_healing'); ?></h3>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-4"><?php echo __('require_healing'); ?></label>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <input type="radio" id="healing_yes" name="healing" value="Yes" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="healing_yes" class="ml-3 text-gray-700"><?php echo __('yes'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="healing_no" name="healing" value="No" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="healing_no" class="ml-3 text-gray-700"><?php echo __('no'); ?></label>
                        </div>
                    </div>
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-4"><?php echo __('specific_prayer'); ?></label>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <input type="radio" id="prayer_yes" name="prayer" value="Yes" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="prayer_yes" class="ml-3 text-gray-700"><?php echo __('yes'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="prayer_no" name="prayer" value="No" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="prayer_no" class="ml-3 text-gray-700"><?php echo __('no'); ?></label>
                        </div>
                    </div>
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-4"><?php echo __('prayer_request_type'); ?></label>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <input type="checkbox" id="prayer_healing" name="prayer_type[]" value="Healing" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="prayer_healing" class="ml-3 text-gray-700"><?php echo __('healing_self_loved'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="prayer_financial" name="prayer_type[]" value="Financial" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="prayer_financial" class="ml-3 text-gray-700"><?php echo __('financial_prosperity'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="prayer_spiritual" name="prayer_type[]" value="Spiritual" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="prayer_spiritual" class="ml-3 text-gray-700"><?php echo __('spiritual_growth'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="prayer_relationship" name="prayer_type[]" value="Relationship" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="prayer_relationship" class="ml-3 text-gray-700"><?php echo __('relationship_restoration'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="prayer_salvation" name="prayer_type[]" value="Salvation" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="prayer_salvation" class="ml-3 text-gray-700"><?php echo __('salvation'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="prayer_other" name="prayer_type[]" value="Other" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="prayer_other" class="ml-3 text-gray-700"><?php echo __('other'); ?></label>
                            <input type="text" name="prayer_other_text" class="ml-3 px-4 py-2 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200">
                        </div>
                    </div>
                </div>

                <div class="mb-6">
                    <label for="prayer_details" class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('prayer_details'); ?></label>
                    <textarea id="prayer_details" name="prayer_details" rows="4" class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200"><?php echo isset($_POST['prayer_details']) ? htmlspecialchars($_POST['prayer_details']) : ''; ?></textarea>
                </div>
            </div>

            <!-- Rhapsody of Realities -->
            <div class="mb-12">
                <h3 class="text-2xl font-light text-gray-900 mb-8"><?php echo __('rhapsody_section'); ?></h3>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-4"><?php echo __('interested_in_copy'); ?></label>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <input type="radio" id="copy_yes" name="copy" value="Yes" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="copy_yes" class="ml-3 text-gray-700"><?php echo __('yes'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="copy_no" name="copy" value="No" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="copy_no" class="ml-3 text-gray-700"><?php echo __('no'); ?></label>
                        </div>
                    </div>
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-4"><?php echo __('interested_in_buying'); ?></label>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <input type="radio" id="buying_yes" name="buying" value="Yes" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="buying_yes" class="ml-3 text-gray-700"><?php echo __('yes'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="buying_no" name="buying" value="No" class="h-4 w-4 text-primary focus:ring-primary">
                            <label for="buying_no" class="ml-2 text-gray-700"><?php echo __('no'); ?></label>
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <label for="copies" class="block text-gray-700 font-bold mb-2"><?php echo __('how_many_copies'); ?></label>
                    <input type="number" id="copies" name="copies" min="0" value="<?php echo isset($_POST['copies']) ? htmlspecialchars($_POST['copies']) : '0'; ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                </div>
            </div>

            <!-- Additional Information -->
            <div class="mb-12">
                <h3 class="text-2xl font-light text-gray-900 mb-8"><?php echo __('additional_info'); ?></h3>

                <div class="mb-6">
                    <label for="church" class="block text-sm font-medium text-gray-700 mb-2"><?php echo __('church_affiliation'); ?></label>
                    <input type="text" id="church" name="church" value="<?php echo isset($_POST['church']) ? htmlspecialchars($_POST['church']) : ''; ?>" class="w-full px-4 py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200">
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-4"><?php echo __('attended_before'); ?></label>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <input type="radio" id="attended_yes" name="attended" value="Yes" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="attended_yes" class="ml-3 text-gray-700"><?php echo __('yes'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="attended_no" name="attended" value="No" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="attended_no" class="ml-3 text-gray-700"><?php echo __('no'); ?></label>
                        </div>
                    </div>
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-4"><?php echo __('receive_updates'); ?></label>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <input type="radio" id="updates_yes" name="updates" value="Yes" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="updates_yes" class="ml-3 text-gray-700"><?php echo __('yes'); ?></label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="updates_no" name="updates" value="No" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                            <label for="updates_no" class="ml-3 text-gray-700"><?php echo __('no'); ?></label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Confirmation -->
            <div class="mb-12">
                <h3 class="text-2xl font-light text-gray-900 mb-8"><?php echo __('confirmation'); ?></h3>

                <div class="flex items-start">
                    <input type="checkbox" id="confirm" name="confirm" value="1" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 mt-1" required>
                    <label for="confirm" class="ml-3 text-gray-700 leading-relaxed"><?php echo __('confirm_text'); ?></label>
                </div>
            </div>

            <div class="text-center">
                <button type="submit" class="inline-flex items-center text-primary font-medium hover:text-red-700 transition-colors duration-200">
                    <?php echo __('submit'); ?>
                    <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </button>
            </div>
        </form>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
