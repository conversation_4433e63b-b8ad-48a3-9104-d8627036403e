<?php
// Debug version of media.php to identify the hanging issue
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "Starting media.php debug...\n";

try {
    echo "Loading config.php...\n";
    require_once 'includes/config.php';
    echo "Config loaded successfully\n";
    
    echo "Loading languages.php...\n";
    require_once 'includes/languages.php';
    echo "Languages loaded successfully\n";
    
    echo "Loading TranslationService.php...\n";
    require_once 'includes/TranslationService.php';
    echo "TranslationService loaded successfully\n";
    
    echo "Loading header.php...\n";
    include 'includes/header.php';
    echo "Header loaded successfully\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}

echo "Debug complete\n";
?>