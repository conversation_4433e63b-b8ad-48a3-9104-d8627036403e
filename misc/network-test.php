<?php
// Test network connectivity and API calls
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "Testing network connectivity...\n";

// Test the ipinfo.io API call that might be causing issues
$testIP = '*******'; // Google DNS
echo "Testing ipinfo.io API with IP: $testIP\n";

$start = microtime(true);
try {
    $context = stream_context_create([
        'http' => [
            'timeout' => 5, // 5 second timeout
            'method' => 'GET',
            'user_agent' => 'Mozilla/5.0 (compatible; Test)',
        ]
    ]);
    
    $details = @file_get_contents("https://ipinfo.io/{$testIP}/json", false, $context);
    $end = microtime(true);
    
    if ($details) {
        $details = json_decode($details, true);
        echo "API call successful in " . ($end - $start) . " seconds\n";
        echo "Response: " . print_r($details, true) . "\n";
    } else {
        echo "API call failed after " . ($end - $start) . " seconds\n";
    }
    
} catch (Exception $e) {
    $end = microtime(true);
    echo "Exception after " . ($end - $start) . " seconds: " . $e->getMessage() . "\n";
}

echo "Network test complete\n";
?>