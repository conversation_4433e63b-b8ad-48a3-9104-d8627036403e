<?php
require_once 'includes/config.php';
require_once 'includes/languages.php';
require_once 'includes/db.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Load crusades data
$crusadesJson = file_get_contents(__DIR__ . '/data/crusades.json');
$crusadesData = json_decode($crusadesJson, true);

// Get the event slug from the URL
$eventSlug = $_GET['event'] ?? '';
$crusade = null;

// Find the requested crusade
if ($eventSlug) {
    foreach ($crusadesData['crusades'] as $c) {
        $slug = pathinfo($c['register_link'], PATHINFO_FILENAME);
        if ($slug === $eventSlug) {
            $crusade = $c;
            break;
        }
    }
}

// If no valid crusade found, redirect to home
if (!$crusade) {
    header('Location: index.php');
    exit;
}

// Check if event is in the past
$eventDate = new DateTime($crusade['date'] . ' ' . $crusade['time']);
$isPastEvent = $eventDate < new DateTime();

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Include the registration handler
    require_once 'register-handler.php';
    exit;
}

// Include header
include 'includes/header.php';
?>

<section class="py-24 bg-white">
    <div class="max-w-4xl mx-auto px-6">
        <?php if (isset($_SESSION['form_errors'])): ?>
            <div class="mb-6 p-4 bg-red-100 border-l-4 border-red-500 text-red-700">
                <p class="font-bold">Error</p>
                <ul class="list-disc list-inside">
                    <?php foreach ($_SESSION['form_errors'] as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php unset($_SESSION['form_errors']); ?>
        <?php endif; ?>

        <div class="text-center mb-16">
            <h2 class="text-4xl font-light text-gray-900 mb-6">Registration Form</h2>
            <div class="w-16 h-px bg-primary mx-auto mb-6"></div>
            <p class="text-xl text-gray-600 leading-relaxed">
                Join us for <?php echo htmlspecialchars($crusade['title']); ?> on 
                <?php echo $eventDate->format('F j, Y'); ?> at <?php echo $eventDate->format('g:i A'); ?>
            </p>
            <?php if ($isPastEvent): ?>
                <div class="mt-4 text-red-600 font-medium">
                    This event has already occurred. Registration is closed.
                </div>
            <?php endif; ?>
        </div>

        <?php if (!$isPastEvent): ?>
        <form action="register.php?event=<?php echo urlencode($eventSlug); ?>" method="post" class="space-y-6">
            <input type="hidden" name="crusade_slug" value="<?php echo htmlspecialchars($eventSlug); ?>">
            
            <!-- Personal Information -->
            <div class="bg-gray-50 p-6 rounded-lg">
                <h3 class="text-xl font-light text-gray-900 mb-6">Personal Information</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="first_name" class="block text-sm font-medium text-gray-700 mb-1">First Name *</label>
                        <input type="text" id="first_name" name="first_name" required
                               class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                               value="<?php echo htmlspecialchars($_SESSION['form_data']['first_name'] ?? ''); ?>">
                    </div>
                    
                    <div>
                        <label for="last_name" class="block text-sm font-medium text-gray-700 mb-1">Last Name *</label>
                        <input type="text" id="last_name" name="last_name" required
                               class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                               value="<?php echo htmlspecialchars($_SESSION['form_data']['last_name'] ?? ''); ?>">
                    </div>
                    
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email *</label>
                        <input type="email" id="email" name="email" required
                               class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                               value="<?php echo htmlspecialchars($_SESSION['form_data']['email'] ?? ''); ?>">
                    </div>
                    
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number *</label>
                        <input type="tel" id="phone" name="phone" required
                               class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                               value="<?php echo htmlspecialchars($_SESSION['form_data']['phone'] ?? ''); ?>">
                    </div>
                </div>
            </div>

            <!-- Additional Fields -->
            <div class="bg-gray-50 p-6 rounded-lg">
                <h3 class="text-xl font-light text-gray-900 mb-6">Additional Information</h3>
                
                <div class="space-y-4">
                    <div>
                        <label for="address" class="block text-sm font-medium text-gray-700 mb-1">Address</label>
                        <input type="text" id="address" name="address"
                               class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                               value="<?php echo htmlspecialchars($_SESSION['form_data']['address'] ?? ''); ?>">
                    </div>
                    
                    <div>
                        <label for="city" class="block text-sm font-medium text-gray-700 mb-1">City</label>
                        <input type="text" id="city" name="city"
                               class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                               value="<?php echo htmlspecialchars($_SESSION['form_data']['city'] ?? ''); ?>">
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="state" class="block text-sm font-medium text-gray-700 mb-1">State/Province</label>
                            <input type="text" id="state" name="state"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                                   value="<?php echo htmlspecialchars($_SESSION['form_data']['state'] ?? ''); ?>">
                        </div>
                        
                        <div>
                            <label for="postal_code" class="block text-sm font-medium text-gray-700 mb-1">Postal Code</label>
                            <input type="text" id="postal_code" name="postal_code"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                                   value="<?php echo htmlspecialchars($_SESSION['form_data']['postal_code'] ?? ''); ?>">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Prayer Request -->
            <div class="bg-gray-50 p-6 rounded-lg">
                <h3 class="text-xl font-light text-gray-900 mb-6">Prayer Request (Optional)</h3>
                
                <div class="space-y-4">
                    <div>
                        <label for="prayer_request" class="block text-sm font-medium text-gray-700 mb-1">Your Prayer Request</label>
                        <textarea id="prayer_request" name="prayer_request" rows="4"
                                 class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"><?php echo htmlspecialchars($_SESSION['form_data']['prayer_request'] ?? ''); ?></textarea>
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-center">
                <button type="submit" class="px-8 py-3 bg-primary text-white font-medium rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
                    Register Now
                </button>
            </div>
        </form>
        <?php endif; ?>
    </div>
</section>

<?php 
// Clear form data from session
if (isset($_SESSION['form_data'])) {
    unset($_SESSION['form_data']);
}
include 'includes/footer.php'; 
?>
