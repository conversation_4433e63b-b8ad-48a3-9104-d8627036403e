<?php
/**
 * Test page for language detection functionality
 */

require_once 'includes/config.php';
require_once 'includes/languages.php';
require_once 'includes/db.php';
require_once 'includes/header.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Clear language session if requested
if (isset($_GET['clear'])) {
    unset($_SESSION['lang']);
    header('Location: test-language.php');
    exit;
}

// Function to test IP-based language detection
function testIpDetection($ip) {
    // Using ipinfo.io free tier
    $details = @file_get_contents("https://ipinfo.io/{$ip}/json");
    if ($details) {
        $details = json_decode($details, true);
        return $details;
    }
    return false;
}

// Get client IP
$clientIp = $_SERVER['REMOTE_ADDR'];

// Test detection with some sample IPs if we're on localhost
$testResults = [];
if (in_array($clientIp, ['127.0.0.1', '::1']) || strpos($clientIp, '192.168.') === 0) {
    // Sample IPs from different countries
    $testIps = [
        'US' => '*******',        // Google DNS (US)
        'GB' => '*************',  // BBC (UK)
        'FR' => '***********',    // France
        'ES' => '*************',  // Spain
        'CN' => '**************', // China
        'BR' => '**********',     // Brazil
        'RU' => '************',   // Russia
        'AE' => '***********'     // UAE
    ];
    
    foreach ($testIps as $country => $ip) {
        $result = testIpDetection($ip);
        if ($result) {
            $testResults[$country] = $result;
        }
    }
}
?>

<div class="bg-white shadow-md rounded-lg p-6 my-8">
    <h1 class="text-2xl font-bold mb-6 text-gray-800">Language Detection Test</h1>
    
    <div class="bg-blue-50 p-4 rounded-md mb-6">
        <h2 class="text-lg font-semibold mb-3 text-blue-800">Current Language Settings</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <p class="mb-2"><strong>Current Language:</strong> <?php echo Language::getCurrentLanguage(); ?> (<?php echo Language::getAvailableLanguages()[Language::getCurrentLanguage()]; ?>)</p>
                <p class="mb-2"><strong>Default Language:</strong> <?php echo DEFAULT_LANGUAGE; ?></p>
                <p class="mb-2"><strong>Session Language:</strong> <?php echo isset($_SESSION['lang']) ? $_SESSION['lang'] : 'Not set'; ?></p>
                <p class="mb-2"><strong>GET Parameter:</strong> <?php echo isset($_GET['lang']) ? $_GET['lang'] : 'Not set'; ?></p>
                <p class="mb-2"><strong>Browser Language:</strong> <?php echo isset($_SERVER['HTTP_ACCEPT_LANGUAGE']) ? $_SERVER['HTTP_ACCEPT_LANGUAGE'] : 'Not available'; ?></p>
            </div>
            <div>
                <p class="mb-2"><strong>CloudFlare Country:</strong> <?php echo isset($_SERVER['HTTP_CF_IPCOUNTRY']) ? $_SERVER['HTTP_CF_IPCOUNTRY'] : 'Not available'; ?></p>
                <p class="mb-2"><strong>IP Address:</strong> <?php echo $clientIp; ?></p>
                <p class="mb-2"><strong>User Agent:</strong> <?php echo $_SERVER['HTTP_USER_AGENT']; ?></p>
            </div>
        </div>
        
        <div class="mt-4">
            <a href="test-language.php?clear=1" class="bg-red-600 text-white py-2 px-4 rounded hover:bg-red-700 transition-colors">Clear Language Session</a>
        </div>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
            <h2 class="text-lg font-semibold mb-3 text-gray-800">Available Languages</h2>
            <div class="bg-gray-50 p-4 rounded-md">
                <div class="space-y-2">
                    <?php foreach (Language::getAvailableLanguages() as $code => $name): ?>
                    <div class="flex items-center">
                        <img src="assets/images/flags/<?php echo $code; ?>.svg" alt="<?php echo $name; ?>" class="w-5 h-5 mr-3 rounded-sm">
                        <span><?php echo $name; ?></span>
                        <span class="text-gray-500 text-sm ml-2">(<?php echo $code; ?>)</span>
                        <a href="?lang=<?php echo $code; ?>" class="ml-auto bg-gray-200 hover:bg-gray-300 text-gray-800 px-2 py-1 rounded text-xs transition-colors">Switch</a>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        
        <?php if (!empty($testResults)): ?>
        <div>
            <h2 class="text-lg font-semibold mb-3 text-gray-800">Sample IP Detection Tests</h2>
            <div class="bg-gray-50 p-4 rounded-md">
                <div class="space-y-4">
                    <?php foreach ($testResults as $country => $result): ?>
                    <div class="border-b border-gray-200 pb-3 last:border-0 last:pb-0">
                        <h3 class="font-medium"><?php echo $country; ?> (<?php echo $result['ip']; ?>)</h3>
                        <p><strong>Detected Country:</strong> <?php echo $result['country']; ?></p>
                        <p><strong>Detected Region:</strong> <?php echo $result['region']; ?></p>
                        <p><strong>Detected City:</strong> <?php echo $result['city']; ?></p>
                        <p><strong>Would Set Language:</strong> 
                            <?php 
                            $countryMap = Language::getCountryLanguageMap();
                            echo isset($countryMap[$result['country']]) ? $countryMap[$result['country']] : 'none'; 
                            ?>
                        </p>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?> 