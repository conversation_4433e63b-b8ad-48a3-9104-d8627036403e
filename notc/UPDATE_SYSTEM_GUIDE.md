# Update System Guide

## Overview
The update system allows zones and networks to update their registration data through secure, unique links generated from the admin dashboard.

## How It Works

### 1. Generate Update Links (Admin)
- Go to the admin dashboard (`admin-dashboard.php`)
- Find the registration you want to create an update link for
- Click the "Update Link" button in the Actions column
- A modal will appear with the unique update link
- Copy the link and share it with the organization

### 2. Organization Updates Their Data
- The organization clicks on their unique update link
- They are taken to their update dashboard (`update-dashboard.php`)
- They can see their current registration details
- They click "Update Registration" to modify their data
- They are redirected to the appropriate registration form (church or network)
- The form is pre-populated with their existing data
- After submitting, they are redirected back to their update dashboard with a success message

### 3. Admin Sees Updated Data
- Updated registrations are immediately reflected in the admin dashboard
- The system tracks when registrations were last updated
- Update history is maintained for audit purposes

## Security Features

### Token-Based Authentication
- Each update link contains a unique 64-character token
- Tokens are tied to specific registrations
- Tokens expire after 1 year
- Tokens can be deactivated if compromised

### Access Control
- Only the organization with the correct token can access their data
- Tokens are validated on every access
- Invalid or expired tokens show an error message

### Audit Trail
- System tracks when tokens are generated
- System tracks when tokens are used
- System tracks when registrations are updated
- All activities are logged for security purposes

## File Structure

```
notc/
├── includes/
│   └── UpdateTokenManager.php     # Core token management class
├── data/
│   └── update_tokens.json         # Token storage (secure this file!)
├── update-dashboard.php           # Organization's update dashboard
├── generate-update-link.php       # AJAX endpoint for generating links
├── register-church.php            # Church registration form (supports updates)
├── register-network.php           # Network registration form (supports updates)
├── register-church-handler.php    # Handles church updates
└── register-network-handler.php   # Handles network updates
```

## API Endpoints

### Generate Update Link
**POST** `generate-update-link.php`

Parameters:
- `registration_id`: The ID of the registration
- `registration_type`: Either "church" or "network"

Response:
```json
{
  "success": true,
  "token": "64-character-token",
  "update_link": "https://domain.com/notc/update-dashboard.php?token=...",
  "is_new": false
}
```

### Update Dashboard
**GET** `update-dashboard.php?token=TOKEN`

Shows the organization's current data and provides update options.

## Database Schema

### update_tokens.json
```json
{
  "tokens": {
    "token_string": {
      "token": "64-character-token",
      "registration_id": "church_4",
      "registration_type": "church",
      "created_at": "2025-01-08 12:00:00",
      "expires_at": "2026-01-08 12:00:00",
      "used_count": 3,
      "last_used": "2025-01-08 15:30:00",
      "is_active": true
    }
  },
  "created_at": "2025-01-08 10:00:00",
  "last_updated": "2025-01-08 15:30:00"
}
```

## Maintenance

### Generate Links for Existing Registrations
Run `generate-all-update-links.php` to create update links for all existing registrations:

```bash
php notc/generate-all-update-links.php
```

### Clean Up Expired Tokens
The system automatically handles expired tokens, but you can manually clean them up:

```php
$tokenManager = new UpdateTokenManager();
$cleaned = $tokenManager->cleanupExpiredTokens();
echo "Cleaned up $cleaned expired tokens";
```

### File Permissions
Ensure the web server can write to the data directory:

```bash
chmod 777 notc/data
chmod 666 notc/data/update_tokens.json
```

## Troubleshooting

### "Invalid or expired update link" Error
- Check if the token exists in `update_tokens.json`
- Verify the token hasn't expired
- Ensure the registration ID still exists in the main data file
- Check file permissions on the data directory

### "Failed to generate update token" Error
- Check file permissions on `notc/data/` directory
- Ensure `update_tokens.json` is writable by the web server
- Check PHP error logs for detailed error messages

### Update Form Not Pre-populated
- Verify the token is valid
- Check that the registration data exists
- Ensure the helper functions are working in the registration forms

## Best Practices

1. **Secure the Data Directory**: Ensure `notc/data/` is not publicly accessible via web
2. **Regular Backups**: Backup `update_tokens.json` regularly
3. **Monitor Usage**: Check token usage statistics periodically
4. **Rotate Tokens**: Consider regenerating tokens periodically for high-security environments
5. **Audit Logs**: Review update logs regularly for suspicious activity

## Support

For technical support or questions about the update system, contact the system administrator.