<?php 
require_once '../includes/config.php';
require_once '../includes/languages.php';
require_once '../includes/TranslationService.php';

// Initialize translation service
$translationService = TranslationService::getInstance();
$currentLanguage = Language::getCurrentLanguage();

// Load zones from JSON file
$zonesFile = __DIR__ . '/../data/zones.json';
$zones = [];
if (file_exists($zonesFile)) {
    $zonesData = json_decode(file_get_contents($zonesFile), true);
    $zones = $zonesData['zones'] ?? [];
}

// Load registrations data to get counts and available networks
$dataFile = __DIR__ . '/data.json';
$registrationsData = [];
if (file_exists($dataFile)) {
    $jsonData = file_get_contents($dataFile);
    $data = json_decode($jsonData, true);
    $registrationsData = $data['registrations'] ?? [];
}

// Count registrations per zone
$zoneCounts = [];
$networkCounts = [];
$availableNetworks = [];

foreach ($registrationsData as $registration) {
    // Count zones
    if (isset($registration['zone']) && !empty($registration['zone'])) {
        $zone = $registration['zone'];
        $zoneCounts[$zone] = ($zoneCounts[$zone] ?? 0) + 1;
    }
    
    // Count networks and collect unique networks
    if ($registration['type'] === 'network' && isset($registration['network_type']) && !empty($registration['network_type'])) {
        $network = $registration['network_type'];
        $networkCounts[$network] = ($networkCounts[$network] ?? 0) + 1;
        if (!in_array($network, $availableNetworks)) {
            $availableNetworks[] = $network;
        }
    }
}

// Sort networks alphabetically
sort($availableNetworks);

include 'includes/header.php'; 
?>

<!-- Fixed Video Background -->
<div class="fixed inset-0 w-full h-full z-0">
    <video 
        autoplay 
        muted 
        loop 
        playsinline 
        class="absolute inset-0 w-full h-full object-cover"
        poster="https://rorcloud.org/serve.php?id=269&name=Night_of_thousand_crusades.jpg">
        <source src="https://rorcloud.org/serve.php?id=279&name=output(compress-video-online.com).mp4" type="video/mp4">
        Your browser does not support the video tag.
    </video>
    <!-- Dark Overlay for better text readability -->
    <div class="absolute inset-0 bg-black bg-opacity-80"></div>
    <!-- Additional gradient overlay for better text contrast -->
    <div class="absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-black/50"></div>
</div>

<!-- Admin Backend Links Content -->
<section class="min-h-screen text-white relative z-10 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl sm:text-5xl md:text-6xl font-bold mb-6 drop-shadow-lg">
                <?php 
                if ($currentLanguage === 'en') {
                    echo 'Admin Backend Links';
                } else {
                    echo $translationService->translateText('Admin Backend Links', 'en', $currentLanguage) ?? 'Admin Backend Links';
                }
                ?>
            </h1>
            <p class="text-xl md:text-2xl drop-shadow-lg text-gray-100 max-w-3xl mx-auto">
                <?php 
                if ($currentLanguage === 'en') {
                    echo 'Access zone and network dashboards for detailed analytics and management';
                } else {
                    echo $translationService->translateText('Access zone and network dashboards for detailed analytics and management', 'en', $currentLanguage) ?? 'Access zone and network dashboards for detailed analytics and management';
                }
                ?>
            </p>
            <div class="w-20 h-1 bg-accent mx-auto mt-6"></div>
        </div>

        <!-- Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
            <div class="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-xl text-center">
                <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                </div>
                <p class="text-3xl font-bold text-white"><?php echo count($zones); ?></p>
                <p class="text-gray-300">Total Zones</p>
            </div>

            <div class="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-xl text-center">
                <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <p class="text-3xl font-bold text-white"><?php echo count($availableNetworks); ?></p>
                <p class="text-gray-300">Active Networks</p>
            </div>

            <div class="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-xl text-center">
                <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <p class="text-3xl font-bold text-white"><?php echo count(array_filter($zoneCounts)); ?></p>
                <p class="text-gray-300">Zones with Data</p>
            </div>

            <div class="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-xl text-center">
                <div class="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <p class="text-3xl font-bold text-white"><?php echo count($registrationsData); ?></p>
                <p class="text-gray-300">Total Registrations</p>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Zone Dashboards -->
            <div class="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-xl">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-2xl font-bold text-white flex items-center">
                        <svg class="w-6 h-6 mr-3 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        Zone Dashboards
                    </h3>
                    <div class="text-sm text-gray-300">
                        <?php echo count($zones); ?> zones available
                    </div>
                </div>

                <!-- Search Box for Zones -->
                <div class="mb-4">
                    <div class="relative">
                        <input type="text" 
                               id="zoneSearch" 
                               placeholder="Search zones..." 
                               class="w-full px-4 py-3 pl-10 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-300 focus:border-blue-400 focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 transition-all duration-300">
                        <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                </div>

                <div id="zonesList" class="space-y-2 max-h-96 overflow-y-auto">
                    <?php 
                    // Sort zones: ones with registrations first, then alphabetically
                    $sortedZones = array_filter($zones, function($zone) {
                        return $zone !== 'Not Applicable';
                    });
                    
                    usort($sortedZones, function($a, $b) use ($zoneCounts) {
                        $countA = $zoneCounts[$a] ?? 0;
                        $countB = $zoneCounts[$b] ?? 0;
                        
                        // If both have registrations or both don't, sort alphabetically
                        if (($countA > 0 && $countB > 0) || ($countA == 0 && $countB == 0)) {
                            return strcmp($a, $b);
                        }
                        
                        // Zones with registrations come first
                        return $countB > 0 ? 1 : -1;
                    });
                    
                    foreach ($sortedZones as $zone): 
                        $count = $zoneCounts[$zone] ?? 0;
                        $encodedZone = urlencode($zone);
                    ?>
                        <a href="zone-dashboard.php?zone=<?php echo $encodedZone; ?>" 
                           class="zone-item flex items-center justify-between p-4 bg-white/5 hover:bg-blue-500/20 rounded-xl transition-all duration-300 group border border-transparent hover:border-blue-400/30"
                           data-zone="<?php echo strtolower($zone); ?>">
                            <div class="flex items-center">
                                <div class="w-3 h-3 rounded-full <?php echo $count > 0 ? 'bg-green-400' : 'bg-gray-400'; ?> mr-3"></div>
                                <span class="text-white font-medium group-hover:text-blue-200"><?php echo htmlspecialchars($zone); ?></span>
                                <?php if ($count > 0): ?>
                                    <span class="ml-2 px-2 py-1 bg-green-500/20 text-green-300 text-xs rounded-full">Active</span>
                                <?php endif; ?>
                            </div>
                            <div class="flex items-center">
                                <span class="text-sm text-gray-300 mr-3">
                                    <?php echo $count; ?> registration<?php echo $count !== 1 ? 's' : ''; ?>
                                </span>
                                <svg class="w-5 h-5 text-gray-400 group-hover:text-blue-300 transform group-hover:translate-x-1 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </div>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Network Dashboards -->
            <div class="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-xl">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-2xl font-bold text-white flex items-center">
                        <svg class="w-6 h-6 mr-3 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                        Network Dashboards
                    </h3>
                    <div class="text-sm text-gray-300">
                        <?php echo count($availableNetworks); ?> networks active
                    </div>
                </div>

                <!-- Search Box for Networks -->
                <div class="mb-4">
                    <div class="relative">
                        <input type="text" 
                               id="networkSearch" 
                               placeholder="Search networks..." 
                               class="w-full px-4 py-3 pl-10 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-300 focus:border-green-400 focus:ring-2 focus:ring-green-400 focus:ring-opacity-50 transition-all duration-300">
                        <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                </div>

                <div id="networksList" class="space-y-2 max-h-96 overflow-y-auto">
                    <?php if (empty($availableNetworks)): ?>
                        <div class="text-center py-8">
                            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                            <p class="text-gray-300">No network registrations yet</p>
                        </div>
                    <?php else: ?>
                        <?php 
                        // Sort networks by registration count (descending), then alphabetically
                        usort($availableNetworks, function($a, $b) use ($networkCounts) {
                            $countA = $networkCounts[$a] ?? 0;
                            $countB = $networkCounts[$b] ?? 0;
                            
                            // If counts are equal, sort alphabetically
                            if ($countA == $countB) {
                                return strcmp($a, $b);
                            }
                            
                            // Higher counts first
                            return $countB - $countA;
                        });
                        
                        foreach ($availableNetworks as $network): 
                            $count = $networkCounts[$network] ?? 0;
                            $encodedNetwork = urlencode($network);
                        ?>
                            <a href="zone-dashboard.php?network=<?php echo $encodedNetwork; ?>" 
                               class="network-item flex items-center justify-between p-4 bg-white/5 hover:bg-green-500/20 rounded-xl transition-all duration-300 group border border-transparent hover:border-green-400/30"
                               data-network="<?php echo strtolower($network); ?>">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 rounded-full bg-green-400 mr-3"></div>
                                    <span class="text-white font-medium group-hover:text-green-200"><?php echo htmlspecialchars($network); ?></span>
                                    <span class="ml-2 px-2 py-1 bg-green-500/20 text-green-300 text-xs rounded-full">Active</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="text-sm text-gray-300 mr-3">
                                        <?php echo $count; ?> registration<?php echo $count !== 1 ? 's' : ''; ?>
                                    </span>
                                    <svg class="w-5 h-5 text-gray-400 group-hover:text-green-300 transform group-hover:translate-x-1 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </div>
                            </a>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Additional Admin Links -->
        <div class="mt-12 bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-xl">
            <h3 class="text-2xl font-bold text-white mb-6 flex items-center">
                <svg class="w-6 h-6 mr-3 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                Additional Admin Tools
            </h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <a href="admin-dashboard.php" 
                   class="flex items-center p-4 bg-white/5 hover:bg-accent/20 rounded-xl transition-all duration-300 group border border-transparent hover:border-accent/30">
                    <svg class="w-8 h-8 text-accent mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <span class="text-white font-medium group-hover:text-accent">Main Admin Dashboard</span>
                </a>

                <a href="public-dashboard.php" 
                   class="flex items-center p-4 bg-white/5 hover:bg-blue-500/20 rounded-xl transition-all duration-300 group border border-transparent hover:border-blue-400/30">
                    <svg class="w-8 h-8 text-blue-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="text-white font-medium group-hover:text-blue-200">Public Dashboard</span>
                </a>
            </div>
        </div>

        <!-- Back to Main -->
        <div class="text-center mt-12">
            <a href="../index.php" 
               class="inline-flex items-center px-6 py-3 bg-accent hover:bg-yellow-300 text-gray-900 font-semibold rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Main Site
            </a>
        </div>
    </div>
</section>

<!-- JavaScript for Search Functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Zone search functionality
    const zoneSearch = document.getElementById('zoneSearch');
    const zoneItems = document.querySelectorAll('.zone-item');
    
    zoneSearch.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        
        zoneItems.forEach(item => {
            const zoneName = item.dataset.zone;
            if (zoneName.includes(searchTerm)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });
    });
    
    // Network search functionality
    const networkSearch = document.getElementById('networkSearch');
    const networkItems = document.querySelectorAll('.network-item');
    
    networkSearch.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        
        networkItems.forEach(item => {
            const networkName = item.dataset.network;
            if (networkName.includes(searchTerm)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>