<?php 
// Add cache control headers
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");
include 'includes/header.php'; 
?>

<!-- Admin Dashboard Section -->
<section class="min-h-screen bg-gray-50 relative z-10 py-12 sm:py-20 pt-16 sm:pt-20 md:pt-24">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-8 sm:mb-12">
            <h1 class="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 sm:mb-6 text-gray-900">
                Global Registration Dashboard
            </h1>
            <p class="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto">
                Manage and view all church and network registrations
            </p>
            <div class="w-20 h-1 bg-primary mx-auto mt-4 sm:mt-6"></div>
        </div>

        <!-- Statistics Cards -->
        <div class="statistics-grid grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4 mb-8">
            <div class="stat-card bg-white p-4 rounded-lg shadow">
                <div class="stat-content">
                    <div class="stat-text text-gray-600">Total Number of Crusades</div>
                    <div class="stat-number text-2xl font-bold" id="totalCrusades">0</div>
                </div>
            </div>
            <div class="stat-card bg-white p-4 rounded-lg shadow">
                <div class="stat-content">
                    <div class="stat-text text-gray-600">Countries Coverage</div>
                    <div class="stat-number text-2xl font-bold" id="countriesProgress">0 of 0</div>
                    <div class="stat-subtext text-sm text-gray-500" id="countriesPercentage">0%</div>
                </div>
            </div>
            <div class="stat-card bg-white p-4 rounded-lg shadow">
                <div class="stat-content">
                    <div class="stat-text text-gray-600">Cities Coverage</div>
                    <div class="stat-number text-2xl font-bold" id="citiesProgress">0 of 1000</div>
                    <div class="stat-subtext text-sm text-gray-500" id="citiesPercentage">0%</div>
                </div>
            </div>
            <div class="stat-card bg-white p-4 rounded-lg shadow">
                <div class="stat-content">
                    <div class="stat-text text-gray-600">Total Registrations</div>
                    <div class="stat-number text-2xl font-bold" id="totalRegistrations">0</div>
                </div>
            </div>
            <div class="stat-card bg-white p-4 rounded-lg shadow">
                <div class="stat-content">
                    <div class="stat-text text-gray-600">Church Registrations</div>
                    <div class="stat-number text-2xl font-bold" id="churchRegistrations">0</div>
                </div>
            </div>
            <div class="stat-card bg-white p-4 rounded-lg shadow">
                <div class="stat-content">
                    <div class="stat-text text-gray-600">Network Registrations</div>
                    <div class="stat-number text-2xl font-bold" id="networkRegistrations">0</div>
                </div>
            </div>
        </div>



        <!-- Continental Coverage Analysis -->
        <div id="coverage-analysis" class="mb-8">
            <!-- Continental analysis will be loaded here -->
        </div>

        <!-- Search Section -->
        <div class="search-section bg-white rounded-lg shadow-md mb-6 p-4 sm:p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Search</h3>
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
                <input type="text" id="global-search" placeholder="Search registrations by name, email, phone, zone, country, city..." 
                       class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                       autocomplete="off">
                
                <!-- Search Suggestions Dropdown -->
                <div id="search-suggestions" class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto hidden">
                    <!-- Suggestions will be populated here -->
                </div>
            </div>
            <p class="mt-2 text-sm text-gray-500">Search across all registration data including names, contact information, zones, countries, and cities.</p>
        </div>

        <!-- Filters Section -->
        <div class="filter-section bg-white rounded-lg shadow-md mb-6 p-4 sm:p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Filters</h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
                <!-- Registration Type Filter -->
                <div>
                    <label for="filter-type" class="block text-sm font-medium text-gray-700 mb-1">Registration Type</label>
                    <select id="filter-type" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        <option value="">All Types</option>
                        <option value="church">Churches</option>
                        <option value="network">Networks</option>
                    </select>
                </div>

                <!-- Church Type Filter -->
                <div>
                    <label for="filter-church-type" class="block text-sm font-medium text-gray-700 mb-1">Church/Group Type</label>
                    <select id="filter-church-type" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        <option value="">All Church Types</option>
                        <option value="zone">Zones</option>
                        <option value="group">Groups</option>
                        <option value="church">Individual Churches</option>
                    </select>
                </div>

                <!-- Zones Filter -->
                <div>
                    <label for="filter-zone" class="block text-sm font-medium text-gray-700 mb-1">Zone</label>
                    <select id="filter-zone" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        <option value="">All Zones</option>
                        <!-- Zone options will be populated dynamically -->
                    </select>
                </div>

                <!-- Date Range Filter -->
                <div>
                    <label for="filter-date-range" class="block text-sm font-medium text-gray-700 mb-1">Registration Period</label>
                    <select id="filter-date-range" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        <option value="">All Time</option>
                        <option value="today">Today</option>
                        <option value="yesterday">Yesterday</option>
                        <option value="this-week">This Week</option>
                        <option value="last-week">Last Week</option>
                        <option value="this-month">This Month</option>
                        <option value="last-month">Last Month</option>
                        <option value="this-year">This Year</option>
                        <option value="custom">Custom Range</option>
                    </select>
                </div>

                <!-- Custom Date Range (initially hidden) -->
                <div id="custom-date-container" class="col-span-1 sm:col-span-2 lg:col-span-3 xl:col-span-5" style="display: none;">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Custom Date Range</label>
                    <div class="flex space-x-2">
                        <input type="date" id="filter-date-from" class="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        <input type="date" id="filter-date-to" class="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-wrap gap-2 mt-4">
                <button id="apply-filters" class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Apply Filters
                </button>
                <button id="clear-filters" class="px-4 py-2 bg-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                    Clear Filters
                </button>
                <button id="export-filtered" class="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                    Export Filtered Data
                </button>
            </div>

            <!-- Filter Summary -->
            <div id="filter-summary" class="mt-3 text-sm text-gray-600" style="display: none;">
                <!-- Filter summary will be displayed here -->
            </div>
        </div>

        <!-- Data Table Section -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="px-4 sm:px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-900">All Registrations</h3>
                    <div id="results-count" class="text-sm text-gray-500">
                        <!-- Results count will be displayed here -->
                    </div>
                </div>
            </div>
            <div class="p-4 sm:p-6">
                <div class="overflow-x-auto -mx-4 sm:mx-0">
                    <table id="registrations-table" class="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name/Network</th>
                                <th class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact Person</th>
                                <th class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Countries</th>
                                <th class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Target Cities</th>
                                <th class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Crusades</th>
                                <th class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Registered</th>
                                <th class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- Data will be loaded here -->
                    </tbody>
                </table>
                </div>
            </div>
            
            <!-- Admin Backend Links -->
            <div class="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
                <div class="text-center">
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Zone & Network Management</h3>
                    <p class="text-sm text-gray-600 mb-4">
                        Access detailed dashboards for individual zones and networks
                    </p>
                    <a href="admin-backend-links" 
                       class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        View Zone & Network Dashboards
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Dependencies -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.tailwindcss.min.js"></script>

<!-- Tailwind CSS -->
<script src="https://cdn.tailwindcss.com"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.tailwindcss.min.css">

<style>
/* Body and general font styling */
body {
    font-family: 'Oswald', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
}

/* Apply Oswald font to all text elements */
h1, h2, h3, h4, h5, h6, p, div, span, button, input, select, textarea, th, td, .stat-text, .stat-number {
    font-family: 'Oswald', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
}

/* DataTable wrapper styling */
.dataTables_wrapper {
    @apply font-sans;
}

/* Search input styling */
.dataTables_filter input {
    @apply px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm;
}

/* Length select styling */
.dataTables_length select {
    @apply mt-1 block pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md mx-2;
}

/* Pagination styling */
.dataTables_paginate {
    @apply mt-4 flex items-center justify-end;
}

.dataTables_paginate .paginate_button {
    @apply px-3 py-1 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 mx-1 rounded;
}

.dataTables_paginate .paginate_button.current {
    @apply bg-blue-50 border-blue-500 text-blue-600 hover:bg-blue-100;
}

.dataTables_paginate .paginate_button.disabled {
    @apply opacity-50 cursor-not-allowed;
}

/* Info text styling */
.dataTables_info {
    @apply text-sm text-gray-700 py-2;
}

/* Table styling */
#registrations-table {
    @apply w-full;
}

#registrations-table thead th {
    @apply bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

#registrations-table tbody td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

#registrations-table tbody tr {
    @apply hover:bg-gray-50 transition-colors;
}

/* Processing indicator */
.dataTables_processing {
    @apply absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 px-4 py-2 bg-white shadow-lg rounded-md text-gray-700;
}

/* Mobile-first DataTable styles */
@media (max-width: 640px) {
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_paginate {
        width: 100%;
        margin-bottom: 0.5rem;
        text-align: center;
    }

    .dataTables_wrapper .dataTables_filter input {
        width: 100%;
        margin-left: 0;
        margin-top: 0.5rem;
    }

    .dataTables_wrapper .dataTables_length select {
        width: auto;
        margin: 0.5rem auto;
    }

    .dataTables_wrapper .dataTables_paginate {
        display: flex;
        justify-content: center;
        margin-top: 1rem;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.5rem;
        min-width: 2rem;
    }
}

/* Responsive table styles */
@media (max-width: 640px) {
    #registrations-table {
        display: block;
        width: 100%;
    }

    #registrations-table thead {
        display: none;
    }

    #registrations-table tbody tr {
        display: block;
        margin-bottom: 1rem;
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        padding: 1rem;
    }

    #registrations-table tbody td {
        display: block;
        text-align: left;
        padding: 0.5rem 0;
        border: none;
    }

    #registrations-table tbody td:before {
        content: attr(data-label);
        font-weight: 500;
        color: #6b7280;
        display: block;
        margin-bottom: 0.25rem;
    }

    #registrations-table tbody td:not(:last-child) {
        border-bottom: 1px solid #f3f4f6;
        margin-bottom: 0.5rem;
    }
}

/* Touch-friendly styles for mobile */
@media (max-width: 640px) {
    .paginate_button,
    button,
    select,
    input {
        min-height: 2.5rem !important;
        touch-action: manipulation;
    }

    /* Increase spacing for touch targets */
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 8px 12px;
        margin: 0 2px;
    }

    /* Make dropdowns more touch-friendly */
    select {
        padding: 8px 24px 8px 8px !important;
    }
}

/* Filter section responsive styles */
@media (max-width: 640px) {
    .grid.grid-cols-1.sm\\:grid-cols-2.lg\\:grid-cols-3.xl\\:grid-cols-5 {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .flex.flex-wrap.gap-2 {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .flex.flex-wrap.gap-2 button {
        width: 100%;
        justify-content: center;
    }
}

@media (min-width: 640px) and (max-width: 1024px) {
    .grid.grid-cols-1.sm\\:grid-cols-2.lg\\:grid-cols-3.xl\\:grid-cols-5 {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) and (max-width: 1280px) {
    .grid.grid-cols-1.sm\\:grid-cols-2.lg\\:grid-cols-3.xl\\:grid-cols-5 {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Search section styling */
.search-section input {
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.search-section input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Search suggestions styling */
#search-suggestions {
    border-top: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.suggestion-item {
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid #f3f4f6;
    transition: background-color 0.15s ease-in-out;
}

.suggestion-item:hover,
.suggestion-item.highlighted {
    background-color: #f8fafc;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-category {
    font-size: 0.75rem;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.25rem;
}

.suggestion-text {
    color: #1f2937;
    font-weight: 500;
}

.suggestion-subtext {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 0.125rem;
}

.suggestion-highlight {
    background-color: #fef3c7;
    font-weight: 600;
}

/* Filter input styling */
.filter-section select,
.filter-section input {
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.filter-section select:focus,
.filter-section input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Custom date container animation */
#custom-date-container {
    transition: all 0.3s ease-in-out;
    overflow: hidden;
}

/* Filter summary styling */
#filter-summary {
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    padding: 0.75rem;
    transition: all 0.3s ease-in-out;
}

/* Results count styling */
#results-count {
    font-weight: 500;
    color: #6b7280;
}

/* Statistics Cards Responsive Layout */
.statistics-grid {
    display: grid;
    gap: 1rem;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
}

@media (max-width: 640px) {
    .statistics-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
}

@media (min-width: 640px) and (max-width: 768px) {
    .statistics-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
}

@media (min-width: 768px) and (max-width: 1024px) {
    .statistics-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.25rem;
    }
}

@media (min-width: 1024px) and (max-width: 1280px) {
    .statistics-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 1.5rem;
    }
}

@media (min-width: 1280px) {
    .statistics-grid {
        grid-template-columns: repeat(6, 1fr);
        gap: 1.5rem;
    }
}

/* Ensure statistics cards don't overflow */
.stat-card {
    min-width: 0;
    width: 100%;
    box-sizing: border-box;
    overflow: hidden;
    }

.stat-card .stat-content {
    min-width: 0;
    overflow: hidden;
}

.stat-card .stat-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    }

.stat-card .stat-number {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.stat-card .stat-subtext {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    margin-top: 2px;
}
</style>

<script>
// Add timestamp to URLs to prevent caching
function getURL(url) {
    return `${url}?_=${new Date().getTime()}`;
}

let registrationsTable; // Global variable to hold the DataTable instance
let allRegistrations = []; // Store all registration data for filtering
let allCountries = []; // Store all countries data
let currentFilters = {}; // Store current filter values

function initializeDashboard() {
    console.log('Initializing dashboard...');
    
    // Load data with cache busting
    Promise.all([
        fetch(getURL('../data/countriesbkd.json'))
            .then(response => {
                console.log('Countries response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Countries data loaded:', data);
                return data;
            }),
        fetch(getURL('data.json'))
            .then(response => {
                console.log('Registrations response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Registrations data loaded:', data);
                return data;
            })
    ])
    .then(([countriesResponse, registrationData]) => {
        console.log('All data loaded successfully');
        console.log('Countries:', countriesResponse);
        console.log('Registrations:', registrationData);
        
        // Store data globally for filtering
        allCountries = countriesResponse.countries || [];
        allRegistrations = registrationData.registrations || [];
        
        // Initialize filter event listeners
        initializeFilters();
        
        updateDashboard(countriesResponse, registrationData);
    })
    .catch(error => {
        console.error('Error loading data:', error);
        console.error('Error details:', error.message);
        showError('Failed to load data. Please try refreshing the page.');
    });
}

// Initialize filter event listeners
function initializeFilters() {
    console.log('Initializing filters...');
    
    // Populate zones dropdown
    populateZonesDropdown();
    
    // Initialize search suggestions
    initializeSearchSuggestions();
    
    // Global search functionality
    document.getElementById('global-search').addEventListener('input', function() {
        showSearchSuggestions(this.value);
        clearTimeout(filterTimeout);
        filterTimeout = setTimeout(applyFilters, 300);
    });
    
    // Show/hide custom date range
    document.getElementById('filter-date-range').addEventListener('change', function() {
        const customContainer = document.getElementById('custom-date-container');
        if (this.value === 'custom') {
            customContainer.style.display = 'block';
        } else {
            customContainer.style.display = 'none';
        }
    });
    
    // Apply filters button
    document.getElementById('apply-filters').addEventListener('click', applyFilters);
    
    // Clear filters button
    document.getElementById('clear-filters').addEventListener('click', clearFilters);
    
    // Export filtered data button
    document.getElementById('export-filtered').addEventListener('click', exportFilteredData);
    
    // Auto-apply filters when dropdowns change (optional)
    ['filter-type', 'filter-church-type', 'filter-zone', 'filter-date-range'].forEach(id => {
        document.getElementById(id).addEventListener('change', autoApplyFilters);
    });
    
    // Auto-apply filters for custom date inputs
    ['filter-date-from', 'filter-date-to'].forEach(id => {
        document.getElementById(id).addEventListener('change', autoApplyFilters);
    });
}

// Auto-apply filters with a small delay
let filterTimeout;
function autoApplyFilters() {
    clearTimeout(filterTimeout);
    filterTimeout = setTimeout(applyFilters, 300);
}

// Populate zones dropdown with unique zones from registration data
function populateZonesDropdown() {
    const zonesSet = new Set();
    
    allRegistrations.forEach(registration => {
        if (registration.zone && registration.zone.trim()) {
            zonesSet.add(registration.zone.trim());
        }
    });
    
    const zonesArray = Array.from(zonesSet).sort();
    const zoneSelect = document.getElementById('filter-zone');
    
    // Clear existing options except the first one
    while (zoneSelect.children.length > 1) {
        zoneSelect.removeChild(zoneSelect.lastChild);
    }
    
    // Add zone options
    zonesArray.forEach(zone => {
        const option = document.createElement('option');
        option.value = zone;
        option.textContent = zone;
        zoneSelect.appendChild(option);
    });
    
    console.log('Populated zones dropdown with', zonesArray.length, 'zones');
}

// Initialize search suggestions functionality
function initializeSearchSuggestions() {
    const searchInput = document.getElementById('global-search');
    const suggestionsDiv = document.getElementById('search-suggestions');
    let currentHighlightIndex = -1;
    
    // Handle keyboard navigation
    searchInput.addEventListener('keydown', function(e) {
        const suggestions = suggestionsDiv.querySelectorAll('.suggestion-item');
        
        if (e.key === 'ArrowDown') {
            e.preventDefault();
            currentHighlightIndex = Math.min(currentHighlightIndex + 1, suggestions.length - 1);
            updateHighlight(suggestions);
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            currentHighlightIndex = Math.max(currentHighlightIndex - 1, -1);
            updateHighlight(suggestions);
        } else if (e.key === 'Enter') {
            e.preventDefault();
            if (currentHighlightIndex >= 0 && suggestions[currentHighlightIndex]) {
                selectSuggestion(suggestions[currentHighlightIndex].dataset.value);
            }
        } else if (e.key === 'Escape') {
            hideSuggestions();
        }
    });
    
    // Hide suggestions when clicking outside
    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !suggestionsDiv.contains(e.target)) {
            hideSuggestions();
        }
    });
    
    // Show suggestions when input is focused and has value
    searchInput.addEventListener('focus', function() {
        if (this.value.trim()) {
            showSearchSuggestions(this.value);
        }
    });
    
    function updateHighlight(suggestions) {
        suggestions.forEach((item, index) => {
            if (index === currentHighlightIndex) {
                item.classList.add('highlighted');
            } else {
                item.classList.remove('highlighted');
            }
        });
    }
    
    function selectSuggestion(value) {
        searchInput.value = value;
        hideSuggestions();
        applyFilters();
    }
    
    function hideSuggestions() {
        suggestionsDiv.classList.add('hidden');
        currentHighlightIndex = -1;
    }
    
    // Store reference for global access
    window.hideSuggestions = hideSuggestions;
    window.selectSuggestion = selectSuggestion;
}

// Show search suggestions based on input
function showSearchSuggestions(query) {
    const suggestionsDiv = document.getElementById('search-suggestions');
    
    if (!query || query.trim().length < 2) {
        suggestionsDiv.classList.add('hidden');
        return;
    }
    
    const searchTerm = query.toLowerCase().trim();
    const suggestions = generateSearchSuggestions(searchTerm);
    
    if (suggestions.length === 0) {
        suggestionsDiv.classList.add('hidden');
        return;
    }
    
    // Build suggestions HTML
    let suggestionsHTML = '';
    let currentCategory = '';
    
    suggestions.forEach(suggestion => {
        if (suggestion.category !== currentCategory) {
            currentCategory = suggestion.category;
            suggestionsHTML += `<div class="suggestion-category">${currentCategory}</div>`;
        }
        
        const highlightedText = highlightSearchTerm(suggestion.text, searchTerm);
        const subtextHTML = suggestion.subtext ? `<div class="suggestion-subtext">${suggestion.subtext}</div>` : '';
        
        suggestionsHTML += `
            <div class="suggestion-item" data-value="${suggestion.value}" onclick="selectSuggestion('${suggestion.value.replace(/'/g, "\\'")}')">
                <div class="suggestion-text">${highlightedText}</div>
                ${subtextHTML}
            </div>
        `;
    });
    
    suggestionsDiv.innerHTML = suggestionsHTML;
    suggestionsDiv.classList.remove('hidden');
}

// Generate search suggestions based on query
function generateSearchSuggestions(searchTerm) {
    const suggestions = [];
    const maxSuggestions = 10;
    const seenValues = new Set();
    
    // Helper function to add suggestion if not already seen
    function addSuggestion(category, text, value, subtext = '') {
        if (seenValues.has(value) || suggestions.length >= maxSuggestions) return;
        seenValues.add(value);
        suggestions.push({ category, text, value, subtext });
    }
    
    allRegistrations.forEach(registration => {
        if (suggestions.length >= maxSuggestions) return;
        
        // Names
        if (registration.display_name && registration.display_name.toLowerCase().includes(searchTerm)) {
            addSuggestion('Names', registration.display_name, registration.display_name, 
                `${registration.type === 'church' ? 'Church' : 'Network'} • ${registration.zone || 'No zone'}`);
        }
        
        if (registration.full_name && registration.full_name.toLowerCase().includes(searchTerm)) {
            addSuggestion('Contact Persons', registration.full_name, registration.full_name,
                `${registration.display_name || 'Unknown organization'}`);
        }
        
        // Zones
        if (registration.zone && registration.zone.toLowerCase().includes(searchTerm)) {
            addSuggestion('Zones', registration.zone, registration.zone,
                `${registration.type === 'church' ? 'Church' : 'Network'} zone`);
        }
        
        // Countries
        if (registration.selected_countries) {
            const countries = registration.selected_countries.split(',');
            countries.forEach(country => {
                const countryName = country.trim();
                if (countryName.toLowerCase().includes(searchTerm)) {
                    addSuggestion('Countries', countryName, countryName,
                        `Target country for ${registration.display_name || 'registration'}`);
                }
            });
        }
        
        // Cities
        if (registration.selected_cities_data && Array.isArray(registration.selected_cities_data)) {
            registration.selected_cities_data.forEach(city => {
                if (city.name && city.name.toLowerCase().includes(searchTerm)) {
                    addSuggestion('Cities', city.name, city.name,
                        `${city.country} • Target city for ${registration.display_name || 'registration'}`);
                }
            });
        }
        
        // Email addresses
        if (registration.email && registration.email.toLowerCase().includes(searchTerm)) {
            addSuggestion('Email Addresses', registration.email, registration.email,
                `Contact for ${registration.display_name || 'registration'}`);
        }
    });
    
    // Sort suggestions by category and relevance
    suggestions.sort((a, b) => {
        const categoryOrder = ['Names', 'Contact Persons', 'Zones', 'Countries', 'Cities', 'Email Addresses'];
        const aCategoryIndex = categoryOrder.indexOf(a.category);
        const bCategoryIndex = categoryOrder.indexOf(b.category);
        
        if (aCategoryIndex !== bCategoryIndex) {
            return aCategoryIndex - bCategoryIndex;
        }
        
        // Within same category, sort by how early the search term appears
        const aIndex = a.text.toLowerCase().indexOf(searchTerm);
        const bIndex = b.text.toLowerCase().indexOf(searchTerm);
        return aIndex - bIndex;
    });
    
    return suggestions;
}

// Highlight search term in suggestion text
function highlightSearchTerm(text, searchTerm) {
    const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    return text.replace(regex, '<span class="suggestion-highlight">$1</span>');
}

// Apply current filters
function applyFilters() {
    console.log('Applying filters...');
    
    // Get filter values
    currentFilters = {
        search: document.getElementById('global-search').value,
        type: document.getElementById('filter-type').value,
        churchType: document.getElementById('filter-church-type').value,
        zone: document.getElementById('filter-zone').value,
        dateRange: document.getElementById('filter-date-range').value,
        dateFrom: document.getElementById('filter-date-from').value,
        dateTo: document.getElementById('filter-date-to').value
    };
    
    // Filter registrations
    const filteredRegistrations = filterRegistrations(allRegistrations, currentFilters);
    
    // Update dashboard with filtered data
    updateDashboard({ countries: allCountries }, { registrations: filteredRegistrations });
    
    // Update filter summary
    updateFilterSummary(currentFilters, filteredRegistrations.length);
    
    console.log('Filters applied:', currentFilters);
    console.log('Filtered results:', filteredRegistrations.length);
}

// Clear all filters
function clearFilters() {
    console.log('Clearing filters...');
    
    // Reset all filter inputs
    document.getElementById('global-search').value = '';
    document.getElementById('filter-type').value = '';
    document.getElementById('filter-church-type').value = '';
    document.getElementById('filter-zone').value = '';
    document.getElementById('filter-date-range').value = '';
    document.getElementById('filter-date-from').value = '';
    document.getElementById('filter-date-to').value = '';
    document.getElementById('custom-date-container').style.display = 'none';
    
    // Hide search suggestions
    if (window.hideSuggestions) {
        window.hideSuggestions();
    }
    
    // Clear current filters
    currentFilters = {};
    
    // Update dashboard with all data
    updateDashboard({ countries: allCountries }, { registrations: allRegistrations });
    
    // Hide filter summary
    document.getElementById('filter-summary').style.display = 'none';
    
    console.log('Filters cleared');
}

// Filter registrations based on current filter criteria
function filterRegistrations(registrations, filters) {
    return registrations.filter(registration => {
        // Global search filter
        if (filters.search && filters.search.trim()) {
            const searchTerm = filters.search.toLowerCase().trim();
            const searchableFields = [
                registration.display_name || '',
                registration.first_name || '',
                registration.last_name || '',
                registration.full_name || '',
                registration.email || '',
                registration.phone || '',
                registration.full_phone || '',
                registration.zone || '',
                registration.church_group_name || '',
                registration.church_name || '',
                registration.organization_name || '',
                registration.network_name || '',
                registration.selected_countries || '',
                registration.countries_display || '',
                registration.cities_display || '',
                registration.additional_comments || ''
            ];
            
            // Also search in cities data
            if (registration.selected_cities_data && Array.isArray(registration.selected_cities_data)) {
                registration.selected_cities_data.forEach(city => {
                    searchableFields.push(city.name || '');
                    searchableFields.push(city.country || '');
                });
            }
            
            const searchableText = searchableFields.join(' ').toLowerCase();
            if (!searchableText.includes(searchTerm)) {
                return false;
            }
        }
        
        // Filter by registration type
        if (filters.type && registration.type !== filters.type) {
            return false;
        }
        
        // Filter by church type
        if (filters.churchType && registration.type === 'church') {
            if (registration.church_type !== filters.churchType) {
                return false;
            }
        }
        
        // Filter by zone
        if (filters.zone && filters.zone.trim()) {
            if (!registration.zone || registration.zone.trim() !== filters.zone.trim()) {
                return false;
            }
        }
        
        // Filter by date range
        if (filters.dateRange || (filters.dateFrom && filters.dateTo)) {
            const regDate = new Date(registration.registration_date);
            const now = new Date();
            
            if (filters.dateRange === 'custom') {
                if (filters.dateFrom && filters.dateTo) {
                    const fromDate = new Date(filters.dateFrom);
                    const toDate = new Date(filters.dateTo + 'T23:59:59'); // End of day
                    if (regDate < fromDate || regDate > toDate) {
                        return false;
                    }
                }
            } else if (filters.dateRange) {
                const { start, end } = getDateRange(filters.dateRange, now);
                if (regDate < start || regDate > end) {
                    return false;
                }
            }
        }
        
        return true;
    });
}

// Get date range for predefined periods
function getDateRange(period, now) {
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);
    
    switch (period) {
        case 'today':
            return { start: today, end: tomorrow };
        
        case 'yesterday':
            const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
            return { start: yesterday, end: today };
        
        case 'this-week':
            const startOfWeek = new Date(today);
            startOfWeek.setDate(today.getDate() - today.getDay());
            return { start: startOfWeek, end: tomorrow };
        
        case 'last-week':
            const lastWeekStart = new Date(today);
            lastWeekStart.setDate(today.getDate() - today.getDay() - 7);
            const lastWeekEnd = new Date(lastWeekStart);
            lastWeekEnd.setDate(lastWeekStart.getDate() + 7);
            return { start: lastWeekStart, end: lastWeekEnd };
        
        case 'this-month':
            const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            return { start: startOfMonth, end: tomorrow };
        
        case 'last-month':
            const lastMonthStart = new Date(today.getFullYear(), today.getMonth() - 1, 1);
            const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 1);
            return { start: lastMonthStart, end: lastMonthEnd };
        
        case 'this-year':
            const startOfYear = new Date(today.getFullYear(), 0, 1);
            return { start: startOfYear, end: tomorrow };
        
        default:
            return { start: new Date(0), end: new Date() };
    }
}

// Update filter summary
function updateFilterSummary(filters, resultCount) {
    const summaryDiv = document.getElementById('filter-summary');
    const activeParts = [];
    
    if (filters.search && filters.search.trim()) {
        activeParts.push(`Search: "${filters.search.trim()}"`);
    }
    
    if (filters.type) {
        activeParts.push(`Type: ${filters.type}`);
    }
    
    if (filters.churchType) {
        activeParts.push(`Church Type: ${filters.churchType}`);
    }
    
    if (filters.zone && filters.zone.trim()) {
        activeParts.push(`Zone: ${filters.zone.trim()}`);
    }
    
    if (filters.dateRange) {
        if (filters.dateRange === 'custom' && filters.dateFrom && filters.dateTo) {
            activeParts.push(`Date: ${filters.dateFrom} to ${filters.dateTo}`);
        } else {
            activeParts.push(`Date: ${filters.dateRange.replace('-', ' ')}`);
        }
    }
    
    if (activeParts.length > 0) {
        summaryDiv.innerHTML = `<strong>Active Filters:</strong> ${activeParts.join(' | ')} | <strong>Results:</strong> ${resultCount} registration${resultCount !== 1 ? 's' : ''}`;
        summaryDiv.style.display = 'block';
    } else {
        summaryDiv.style.display = 'none';
    }
    
    // Update results count
    const resultsCount = document.getElementById('results-count');
    if (resultsCount) {
        resultsCount.textContent = `Showing ${resultCount} of ${allRegistrations.length} registrations`;
    }
}

// Export filtered data
function exportFilteredData() {
    console.log('Exporting filtered data...');
    
    const filteredRegistrations = filterRegistrations(allRegistrations, currentFilters);
    
    if (filteredRegistrations.length === 0) {
        showError('No data to export with current filters');
        return;
    }
    
    // Create CSV content
    const headers = [
        'Registration ID', 'Type', 'Name/Organization', 'Contact Person', 'Email', 'Phone', 
        'Church/Network Type', 'Zone/Group', 'Countries', 'Target Cities', 'Crusade Types', 
        'Number of Crusades', 'Expected Attendance', 'Registration Date', 'Additional Comments'
    ];
    
    const csvContent = [
        headers.join(','),
        ...filteredRegistrations.map(reg => [
            reg.id || '',
            reg.type || '',
            reg.display_name || '',
            reg.full_name || '',
            reg.email || '',
            reg.phone_display || '',
            reg.church_type || reg.network_type || reg.organisation_type || '',
            reg.zone || reg.church_group_name || '',
            reg.countries_display || '',
            reg.cities_display || '',
            reg.crusade_types_display || '',
            reg.number_of_crusades || '',
            reg.expected_attendance || '',
            reg.registration_date_formatted || '',
            (reg.additional_comments || '').replace(/,/g, ';').replace(/\n/g, ' ')
        ].map(field => `"${field}"`).join(','))
    ].join('\n');
    
    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `registrations_filtered_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    console.log('Export completed');
}

// Function to show loading state
function showLoading() {
    const loadingModal = document.createElement('div');
    loadingModal.id = 'loadingModal';
    loadingModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    loadingModal.innerHTML = `
        <div class="bg-white rounded-lg p-6 flex items-center space-x-4">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-accent"></div>
            <p class="text-gray-700">Loading...</p>
        </div>
    `;
    document.body.appendChild(loadingModal);
}

// Function to hide loading state
function hideLoading() {
    const loadingModal = document.getElementById('loadingModal');
    if (loadingModal) {
        loadingModal.remove();
    }
}

// Function to show error modal
function showError(message) {
    const errorModal = document.createElement('div');
    errorModal.id = 'errorModal';
    errorModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    errorModal.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-red-600">Error</h3>
                <button onclick="closeError()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <p class="text-gray-600">${message}</p>
            <div class="mt-4 flex justify-end">
                <button onclick="closeError()" class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300">Close</button>
            </div>
        </div>
    `;
    document.body.appendChild(errorModal);
}

// Function to close error modal
function closeError() {
    const errorModal = document.getElementById('errorModal');
    if (errorModal) {
        errorModal.remove();
    }
}

function updateDashboard(countriesResponse, registrationData) {
    console.log('Updating dashboard with data:', { countriesResponse, registrationData });
    const countries = countriesResponse.countries || [];
    const registrations = registrationData.registrations || [];
    
    console.log('Processed data:', {
        countriesCount: countries.length,
        registrationsCount: registrations.length
    });
    
    // Update zones dropdown if we have new data
    if (registrations.length > 0) {
        populateZonesDropdown();
    }
    
    // Update statistics
    updateStatistics(countries, registrations);
    
    // Update country analysis
    updateCountryAnalysis(countries, registrations);
    
    // Initialize or refresh DataTable
    initializeDataTable(countries, registrations);
}

async function updateStatistics(countries, registrations) {
    // Update filtered statistics
    document.getElementById('totalRegistrations').textContent = registrations.length;
    document.getElementById('churchRegistrations').textContent = registrations.filter(reg => reg.type === 'church').length;
    document.getElementById('networkRegistrations').textContent = registrations.filter(reg => reg.type === 'network' || reg.type === 'organisation').length;
    
    // Calculate total number of crusades
    const totalCrusades = registrations.reduce((total, reg) => {
        const crusadeCount = parseInt(reg.number_of_crusades) || 0;
        return total + crusadeCount;
    }, 0);
    document.getElementById('totalCrusades').textContent = totalCrusades.toLocaleString();
    
    // Calculate countries coverage
    try {
        const countriesResponse = await fetch('../data/countriesbkd.json');
        const countriesData = await countriesResponse.json();
        const totalCountries = countriesData.countries.length;
        
        // Calculate registered countries from registration data
        const registeredCountriesSet = new Set();
        registrations.forEach(reg => {
            if (reg.selected_countries) {
                const selectedCountries = reg.selected_countries.split(',').map(c => c.trim().toLowerCase());
                selectedCountries.forEach(country => registeredCountriesSet.add(country));
            }
        });
        
        const registeredCountriesCount = registeredCountriesSet.size;
        const countriesPercentage = totalCountries > 0 ? Math.round((registeredCountriesCount / totalCountries) * 100) : 0;
        
        document.getElementById('countriesPercentage').textContent = `${countriesPercentage}%`;
        document.getElementById('countriesProgress').textContent = `${registeredCountriesCount} of ${totalCountries}`;
    } catch (error) {
        console.error('Error fetching countries:', error);
        document.getElementById('countriesPercentage').textContent = '0%';
        document.getElementById('countriesProgress').textContent = '0 of 0';
    }

    // Calculate cities coverage (target: 1000 cities)
    const registeredCitiesSet = new Set();
    registrations.forEach(reg => {
        if (reg.selected_cities_data && Array.isArray(reg.selected_cities_data)) {
            reg.selected_cities_data.forEach(city => {
                if (city.name && city.country) {
                    registeredCitiesSet.add(`${city.name}_${city.country}`);
                }
            });
        }
    });
    
    const totalCitiesTarget = 1000;
    const registeredCitiesCount = registeredCitiesSet.size;
    const citiesPercentage = Math.round((registeredCitiesCount / totalCitiesTarget) * 100);
    
    document.getElementById('citiesPercentage').textContent = `${citiesPercentage}%`;
    document.getElementById('citiesProgress').textContent = `${registeredCitiesCount} of ${totalCitiesTarget}`;
}

function updateCountryAnalysis(countries, registrations) {
    const registeredCountries = new Set();
    registrations.forEach(reg => {
        if (reg.selected_countries) {
            const selectedCountries = reg.selected_countries.split(',').map(c => c.trim().toLowerCase());
            selectedCountries.forEach(country => registeredCountries.add(country));
        }
    });

    const continentOrder = ['Africa', 'Asia', 'Europe', 'North America', 'South America', 'Oceania'];
    const coverageAnalysis = document.getElementById('coverage-analysis');
    
    let analysisHTML = `
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">
    `;
    
    // Group countries by continent
    const continentStats = {};
    countries.forEach(country => {
        if (!continentStats[country.continent]) {
            continentStats[country.continent] = {
                total: 0,
                registered: 0,
                remaining: 0,
                countries: []
            };
        }
        continentStats[country.continent].total++;
        const countryKey = country.name.toLowerCase().replace(/\s+/g, '-');
        if (registeredCountries.has(countryKey)) {
            continentStats[country.continent].registered++;
        } else {
            continentStats[country.continent].remaining++;
        }
        continentStats[country.continent].countries.push({
            ...country,
            key: countryKey,
            isRegistered: registeredCountries.has(countryKey)
        });
    });
    
    continentOrder.forEach(continent => {
        const stats = continentStats[continent];
        if (!stats) return;
        
        const coverage = (stats.registered / stats.total * 100).toFixed(1);
        
        analysisHTML += `
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="p-4 border-b border-gray-200">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="text-lg font-semibold text-gray-900">${continent}</h3>
                        <span class="px-2 py-1 text-sm rounded-full ${coverage >= 50 ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'}">
                            ${coverage}% Coverage
                        </span>
                    </div>
                    <div class="grid grid-cols-3 gap-4 text-sm">
                        <div>
                            <div class="text-gray-600">Total</div>
                            <div class="font-semibold">${stats.total}</div>
                        </div>
                        <div>
                            <div class="text-gray-600">Registered</div>
                            <div class="font-semibold text-green-600">${stats.registered}</div>
                        </div>
                        <div>
                            <div class="text-gray-600">Remaining</div>
                            <div class="font-semibold text-orange-600">${stats.remaining}</div>
                        </div>
                    </div>
                </div>
                <div class="p-4 bg-gray-50 max-h-64 overflow-y-auto">
                    <div class="grid grid-cols-2 gap-2">
                        ${stats.countries.map(country => `
                            <div class="flex items-center py-1 px-2 rounded ${country.isRegistered ? 'bg-green-50 text-green-800' : 'bg-orange-50 text-orange-800'} text-xs">
                                <img src="../assets/images/flags/${country.code.toLowerCase()}.svg" 
                                     alt="${country.name}" 
                                     class="w-4 h-3 object-cover rounded mr-2"
                                     onerror="this.style.display='none'">
                                <span class="truncate">${country.name}</span>
                                ${country.isRegistered ? '<span class="ml-1">✓</span>' : ''}
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
    });
    
    analysisHTML += '</div>';
    coverageAnalysis.innerHTML = analysisHTML;
}

function initializeDataTable(countries, registrations) {
    // Destroy existing DataTable if it exists
    if ($.fn.DataTable.isDataTable('#registrations-table')) {
        $('#registrations-table').DataTable().destroy();
        $('#registrations-table tbody').empty();
    }

    // Initialize new DataTable with mobile optimizations
    registrationsTable = $('#registrations-table').DataTable({
        data: registrations,
        responsive: true,
        columns: [
            {
                data: 'display_name',
                title: 'Name/Network',
                className: 'font-medium',
                render: function(data, type, row) {
                    if (type === 'display' && window.innerWidth < 640) {
                        const type_label = row.church_type || row.network_type || 'N/A';
                        return `
                            <div class="mb-2">
                                <div class="font-semibold text-gray-900">${data}</div>
                                <div class="text-sm text-gray-500">${type_label}</div>
                            </div>
                        `;
                    }
                    const type_label = row.church_type || row.network_type || 'N/A';
                    return `
                        <div class="flex flex-col">
                            <span class="text-gray-900">${data}</span>
                            <span class="text-sm text-gray-500">${type_label}</span>
                        </div>
                    `;
                }
            },
            {
                data: 'full_name',
                title: 'Contact Person',
                className: 'text-sm',
                render: function(data, type, row) {
                    const contactName = data || `${row.first_name || ''} ${row.last_name || ''}`.trim() || 'N/A';
                    if (type === 'display' && window.innerWidth < 640) {
                        return `
                            <div class="mb-2">
                                <div class="font-medium text-gray-900">${contactName}</div>
                                <div class="text-sm text-gray-500">Contact available in details</div>
                            </div>
                        `;
                    }
                    return `
                        <div class="flex flex-col">
                            <span class="text-gray-900 font-medium">${contactName}</span>
                            <span class="text-sm text-gray-500">Contact available in details</span>
                        </div>
                    `;
                }
            },
            {
                data: 'selected_countries',
                title: 'Countries',
                className: 'text-sm',
                render: function(data, type, row) {
                    if (!data) return '<span class="text-gray-500">None selected</span>';
                    const countryList = data.split(',').map(c => c.trim());
                    const countries_to_show = countryList.slice(0, 3); // Show first 3 countries
                    const remainingCount = countryList.length - 3;
                    
                    const countryHtml = countries_to_show.map(countryKey => {
                        const country = countries.find(c => 
                            c.name.toLowerCase().replace(/\s+/g, '-') === countryKey.toLowerCase() ||
                            c.name.toLowerCase() === countryKey.toLowerCase()
                        );
                        if (!country) return `
                            <span class="inline-flex items-center px-2 py-1 bg-gray-50 text-gray-700 text-xs rounded mb-1 mr-1">
                                ${countryKey}
                            </span>
                        `;
                        return `
                            <span class="inline-flex items-center px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded mb-1 mr-1">
                                <img src="../assets/images/flags/${country.code.toLowerCase()}.svg" 
                                     alt="${country.name}" 
                                     class="w-4 h-3 object-cover rounded mr-1"
                                     onerror="this.style.display='none'">
                                ${country.name}
                            </span>
                        `;
                    }).join('');

                    let additionalText = '';
                    if (remainingCount > 0) {
                        additionalText = `<span class="text-xs text-gray-500">+${remainingCount} more</span>`;
                    }

                    if (type === 'display' && window.innerWidth < 640) {
                        return `
                            <div class="mb-2">
                                <div class="flex flex-wrap gap-1">
                                    ${countryHtml}
                                </div>
                                ${additionalText}
                            </div>
                        `;
                    }
                    return `
                        <div class="flex flex-col">
                        <div class="flex flex-wrap gap-1">
                            ${countryHtml}
                            </div>
                            ${additionalText}
                        </div>
                    `;
                }
            },
            {
                data: 'selected_cities_data',
                title: 'Target Cities',
                className: 'text-sm',
                render: function(data, type, row) {
                    if (!data || !Array.isArray(data) || data.length === 0) {
                        return '<span class="text-gray-500">None selected</span>';
                    }
                    
                    const cities = data.slice(0, 3); // Show first 3 cities
                    const remainingCount = data.length - 3;
                    
                    const citiesHtml = cities.map(city => `
                        <span class="inline-flex items-center px-2 py-1 bg-cyan-50 text-cyan-700 text-xs rounded mb-1 mr-1">
                            ${city.name} <span class="text-cyan-500 ml-1">(${city.country})</span>
                        </span>
                    `).join('');

                    let additionalText = '';
                    if (remainingCount > 0) {
                        additionalText = `<span class="text-xs text-gray-500">+${remainingCount} more</span>`;
                    }

                    if (type === 'display' && window.innerWidth < 640) {
                        return `
                            <div class="mb-2">
                                <div class="flex flex-wrap gap-1">
                                    ${citiesHtml}
                                </div>
                                ${additionalText}
                            </div>
                        `;
                    }
                    return `
                        <div class="flex flex-col">
                            <div class="flex flex-wrap gap-1">
                                ${citiesHtml}
                            </div>
                            ${additionalText}
                        </div>
                    `;
                }
            },
            {
                data: 'number_of_crusades',
                title: 'Crusades',
                className: 'text-sm text-center',
                render: function(data, type, row) {
                    const crusadeCount = parseInt(data) || 0;
                    if (type === 'display' && window.innerWidth < 640) {
                        return `
                            <div class="mb-2 text-center">
                                <div class="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded-full">
                                    ${crusadeCount.toLocaleString()}
                                </div>
                            </div>
                        `;
                    }
                    return `
                        <div class="flex justify-center">
                            <span class="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded-full">
                                ${crusadeCount.toLocaleString()}
                            </span>
                        </div>
                    `;
                }
            },
            {
                data: 'registration_date',
                title: 'Registered',
                className: 'text-sm',
                render: function(data, type, row) {
                    const date = new Date(data);
                    if (type === 'display' && window.innerWidth < 640) {
                        return `
                            <div class="mb-2">
                                <div class="font-medium">${date.toLocaleDateString()}</div>
                                <div class="text-gray-500">${date.toLocaleTimeString()}</div>
                            </div>
                        `;
                    }
                    return `
                        <div class="flex flex-col">
                            <span class="text-gray-900">${date.toLocaleDateString()}</span>
                            <span class="text-gray-500">${date.toLocaleTimeString()}</span>
                        </div>
                    `;
                }
            },
            {
                data: null,
                title: 'Actions',
                className: 'text-right',
                orderable: false,
                render: function(data, type, row) {
                    if (type === 'display' && window.innerWidth < 640) {
                        return `
                            <div class="space-y-2">
                                <button onclick="viewDetails('${row.id}')"
                                        class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                    </svg>
                                    View Details
                                </button>
                                <button onclick="generateUpdateLink('${row.id}', '${row.registration_type}')"
                                        class="w-full inline-flex items-center justify-center px-4 py-2 border border-blue-300 shadow-sm text-sm font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
                                    </svg>
                                    Update Link
                                </button>
                            </div>
                        `;
                    }
                    return `
                        <div class="flex space-x-2">
                            <button onclick="viewDetails('${row.id}')"
                                    class="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                </svg>
                                View Details
                            </button>
                            <button onclick="generateUpdateLink('${row.id}', '${row.registration_type}')"
                                    class="inline-flex items-center px-3 py-1.5 border border-blue-300 shadow-sm text-sm font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
                                </svg>
                                Update Link
                            </button>
                        </div>
                    `;
                }
            }
        ],
        order: [[5, 'desc']], // Sort by registration date by default
        pageLength: 10,
        dom: '<"flex flex-col sm:flex-row justify-between items-center mb-4"lf>rt<"flex flex-col sm:flex-row justify-between items-center mt-4"ip>',
            language: {
            search: "",
            searchPlaceholder: "Search registrations...",
            lengthMenu: "Show _MENU_ entries",
            info: "Showing _START_ to _END_ of _TOTAL_ entries",
            paginate: {
                first: "First",
                last: "Last",
                next: "Next",
                previous: "Previous"
            }
        },
        drawCallback: function() {
            // Add hover effects to rows
            $(this.api().table().container()).find('tbody tr')
                .hover(
                    function() { $(this).addClass('bg-gray-50'); },
                    function() { $(this).removeClass('bg-gray-50'); }
                );

            // Add data-label attributes for mobile view
            if (window.innerWidth < 640) {
                $(this.api().table().container()).find('tbody td').each(function() {
                    const colIdx = $(this).index();
                    const title = $(this).closest('table').find('thead th').eq(colIdx).text();
                    $(this).attr('data-label', title);
                });
            }
        }
    });
}

// Function to view registration details
async function viewDetails(id) {
    try {
        showLoading();
        
        // Fetch the data from data.json in the same directory
        const response = await fetch(getURL('data.json'));
        if (!response.ok) {
            throw new Error(`Failed to fetch data: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        const registration = data.registrations.find(r => r.id === id);
        
        if (!registration) {
            throw new Error('Registration not found');
        }
        
        // Create modal
        const modal = document.createElement('div');
        modal.id = 'detailsModal';
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 overflow-y-auto';
        modal.innerHTML = `
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto relative">
                <div class="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex justify-between items-center">
                    <h2 class="text-xl font-semibold text-gray-900">Registration Details</h2>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
            </button>
                </div>
                <div class="p-6">
                ${generateModalContent(registration)}
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    document.addEventListener('keydown', handleEscapeKey);

        // Add click outside to close
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeModal();
            }
        });
        
    } catch (error) {
        console.error('Error viewing details:', error);
        showError(error.message || 'Failed to load registration details');
    } finally {
        hideLoading();
    }
}

function generateModalContent(registration) {
    let details = `
        <div class="space-y-4 max-h-96 overflow-y-auto">
            <h3 class="text-lg font-semibold">Registration Details</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div><strong>Type:</strong> ${getRegistrationType(registration)}</div>
                <div><strong>Registration Date:</strong> ${new Date(registration.registration_date).toLocaleString()}</div>
                <div><strong>Status:</strong> <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Approved</span></div>
            </div>
            
            <div class="border-t pt-4">
                <h4 class="font-semibold mb-2">Contact Information</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div><strong>Name:</strong> ${registration.first_name} ${registration.last_name}</div>
                    <div><strong>Email:</strong> ${registration.email}</div>
                    <div><strong>Phone:</strong> ${registration.full_phone || registration.phone || 'N/A'}</div>
                    <div><strong>Country Code:</strong> ${registration.phone_country_code || 'N/A'}</div>
                </div>
            </div>
    `;
    
    if (registration.type === 'church') {
        details += `
            <div class="border-t pt-4">
                <h4 class="font-semibold mb-2">Church/Group/Zone Information</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div><strong>Registration Type:</strong> ${registration.church_type ? registration.church_type.charAt(0).toUpperCase() + registration.church_type.slice(1) : 'N/A'}</div>
                    ${registration.zone ? `<div><strong>Zone:</strong> ${registration.zone}</div>` : ''}
                    ${registration.church_group_name ? `<div><strong>Group Name:</strong> ${registration.church_group_name}</div>` : ''}
                    ${registration.church_name ? `<div><strong>Church Name:</strong> ${registration.church_name}</div>` : ''}
                    ${registration.organization_name ? `<div><strong>Organization Name:</strong> ${registration.organization_name}</div>` : ''}
                </div>
            </div>
        `;
    } else if (registration.type === 'network' || registration.type === 'organisation') {
        details += `
            <div class="border-t pt-4">
                <h4 class="font-semibold mb-2">Network Information</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div><strong>Network Type:</strong> ${registration.network_type || 'N/A'}</div>
                    ${registration.other_network_type ? `<div><strong>Other Network:</strong> ${registration.other_network_type}</div>` : ''}
                    ${registration.network_website ? `<div><strong>Website:</strong> <a href="${registration.network_website}" target="_blank" class="text-blue-600">${registration.network_website}</a></div>` : ''}
                </div>
            </div>
        `;
    }

    // Helper function to get formatted registration type
    function getRegistrationType(reg) {
        if (reg.type === 'church') {
            if (reg.church_type) {
                const type = reg.church_type.charAt(0).toUpperCase() + reg.church_type.slice(1);
                return `Church (${type})`;
            }
            return 'Church';
        } else if (reg.type === 'network' || reg.type === 'organisation') {
            if (reg.network_type) {
                return `Network (${reg.network_type})`;
            }
            return 'Network';
        }
        return reg.type.charAt(0).toUpperCase() + reg.type.slice(1);
    }
    
    // Add countries information section
    details += `
            <div class="border-t pt-4">
            <h4 class="font-semibold mb-2">Target Countries</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div><strong>Countries Count:</strong> ${registration.countries_count || (registration.selected_countries ? registration.selected_countries.split(',').filter(c => c.trim()).length : 0)}</div>
                <div><strong>Countries Display:</strong> ${registration.countries_display || 'N/A'}</div>
            </div>
            <div class="mt-2">
                <strong>Selected Countries:</strong>
                <div class="flex flex-wrap gap-2 mt-2">
                    ${registration.selected_countries ? registration.selected_countries.split(',').map(countryKey => {
                        const trimmedKey = countryKey.trim();
                        return `
                            <span class="inline-flex items-center px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded border border-blue-200">
                                ${trimmedKey}
                            </span>
                        `;
                    }).join('') : '<span class="text-gray-500">None selected</span>'}
                </div>
            </div>
        </div>
    `;
    
    // Add cities information if available
    if (registration.selected_cities_data && Array.isArray(registration.selected_cities_data) && registration.selected_cities_data.length > 0) {
        details += `
            <div class="border-t pt-4">
                <h4 class="font-semibold mb-2">Target Cities</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div><strong>Cities Count:</strong> ${registration.cities_count || registration.selected_cities_data.length}</div>
                    <div><strong>Cities Display:</strong> ${registration.cities_display || 'N/A'}</div>
                </div>
                <div class="mt-2">
                    <strong>Selected Cities:</strong>
                    <div class="flex flex-wrap gap-2 mt-2">
                        ${registration.selected_cities_data.map(city => `
                            <span class="inline-flex items-center px-2 py-1 bg-cyan-50 text-cyan-700 text-xs rounded border border-cyan-200">
                                ${city.name} <span class="text-cyan-500 ml-1">(${city.country})</span>
                            </span>
                        `).join('')}
                    </div>
                </div>
                ${registration.cities_by_country ? `
                    <div class="mt-3">
                        <strong>Cities by Country:</strong>
                        ${Object.entries(registration.cities_by_country).map(([country, cities]) => `
                            <div class="text-sm mt-1 ml-4">
                                <strong>${country}:</strong> ${cities.join(', ')}
                            </div>
                        `).join('')}
                    </div>
                ` : ''}
            </div>
        `;
    }
    
    details += `
        <div class="border-t pt-4">
            <h4 class="font-semibold mb-2">Crusade Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div><strong>Number of Crusades:</strong> ${registration.number_of_crusades || 'N/A'}</div>
                    <div><strong>Expected Attendance:</strong> ${registration.expected_attendance || 'N/A'}</div>
                </div>
                <div class="mt-2">
                    <strong>Crusade Types:</strong>
                    <div class="text-sm mt-1">${Array.isArray(registration.crusade_types) ? registration.crusade_types.join(', ') : (registration.crusade_types_string || registration.crusade_types || 'N/A')}</div>
                    ${registration.other_crusade_types ? `<div class="text-sm mt-1"><strong>Other Crusade Types:</strong> ${registration.other_crusade_types}</div>` : ''}
                </div>
            </div>
    `;
            
    details += `
            ${registration.additional_comments ? `
            <div class="border-t pt-4">
                <h4 class="font-semibold mb-2">Additional Comments</h4>
                <div class="text-sm bg-gray-50 p-3 rounded">${registration.additional_comments}</div>
            </div>` : ''}
        </div>
    `;
    return details;
}

// Global function to close modal
function closeModal() {
    const modal = document.getElementById('detailsModal');
    if (modal) {
        modal.remove();
        document.removeEventListener('keydown', handleEscapeKey);
    }
}

// Handle escape key
function handleEscapeKey(e) {
    if (e.key === 'Escape') {
        closeModal();
    }
}

// Generate update link for registration
function generateUpdateLink(registrationId, registrationType) {
    // Show loading state
    const button = event.target.closest('button');
    const originalHTML = button.innerHTML;
    button.disabled = true;
    button.innerHTML = `
        <svg class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Generating...
    `;
    
    // Make AJAX request to generate update link
    fetch('generate-update-link.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `registration_id=${encodeURIComponent(registrationId)}&registration_type=${encodeURIComponent(registrationType)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success modal with the update link
            showUpdateLinkModal(data.update_link, data.is_new);
        } else {
            throw new Error(data.error || 'Failed to generate update link');
        }
    })
    .catch(error => {
        console.error('Error generating update link:', error);
        alert('Failed to generate update link: ' + error.message);
    })
    .finally(() => {
        // Restore button state
        button.disabled = false;
        button.innerHTML = originalHTML;
    });
}

// Show update link modal
function showUpdateLinkModal(updateLink, isNew) {
    const modal = document.createElement('div');
    modal.id = 'updateLinkModal';
    modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
    modal.innerHTML = `
        <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-lg shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">
                        ${isNew ? 'Update Link Generated' : 'Existing Update Link'}
                    </h3>
                    <button onclick="closeUpdateLinkModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                
                <div class="mb-4">
                    <p class="text-sm text-gray-600 mb-3">
                        ${isNew ? 'A new update link has been generated.' : 'An existing update link was found.'} 
                        Share this link with the organization to allow them to update their registration:
                    </p>
                    
                    <div class="bg-gray-50 border rounded-lg p-3 mb-3">
                        <div class="flex items-center justify-between">
                            <input type="text" id="updateLinkInput" value="${updateLink}" 
                                   class="flex-1 bg-transparent text-sm text-gray-800 mr-2" readonly>
                            <button onclick="copyUpdateLink()" 
                                    class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                Copy
                            </button>
                        </div>
                    </div>
                    
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <div class="text-sm">
                                <p class="font-medium text-yellow-800">Security Notice</p>
                                <p class="text-yellow-700 mt-1">This link is unique and should only be shared with the authorized organization. It allows them to update their registration data.</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end">
                    <button onclick="closeUpdateLinkModal()" 
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500">
                        Close
                    </button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Add escape key listener
    document.addEventListener('keydown', handleUpdateLinkEscapeKey);
}

// Close update link modal
function closeUpdateLinkModal() {
    const modal = document.getElementById('updateLinkModal');
    if (modal) {
        modal.remove();
        document.removeEventListener('keydown', handleUpdateLinkEscapeKey);
    }
}

// Handle escape key for update link modal
function handleUpdateLinkEscapeKey(e) {
    if (e.key === 'Escape') {
        closeUpdateLinkModal();
    }
}

// Copy update link to clipboard
function copyUpdateLink() {
    const input = document.getElementById('updateLinkInput');
    input.select();
    input.setSelectionRange(0, 99999); // For mobile devices
    
    try {
        document.execCommand('copy');
        
        // Show success feedback
        const button = event.target;
        const originalText = button.textContent;
        button.textContent = 'Copied!';
        button.classList.add('bg-green-600', 'hover:bg-green-700');
        button.classList.remove('bg-blue-600', 'hover:bg-blue-700');
        
        setTimeout(() => {
            button.textContent = originalText;
            button.classList.remove('bg-green-600', 'hover:bg-green-700');
            button.classList.add('bg-blue-600', 'hover:bg-blue-700');
        }, 2000);
        
    } catch (err) {
        console.error('Failed to copy text: ', err);
        alert('Failed to copy link. Please copy it manually.');
    }
}

// Add auto-refresh functionality
function setupAutoRefresh() {
    // Refresh data every 2 minutes
    setInterval(() => {
        console.log('Auto-refreshing dashboard data...');
        initializeDashboard();
    }, 120000);
}

// Initialize dashboard and setup auto-refresh when page loads
window.addEventListener('load', function() {
    initializeDashboard();
    setupAutoRefresh();
});
</script>

<?php include 'includes/footer.php'; ?>