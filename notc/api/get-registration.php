<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// Get registration ID from request
$registrationId = $_GET['id'] ?? $_POST['id'] ?? '';

if (empty($registrationId)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Registration ID is required']);
    exit;
}

// Load registrations data
$dataFile = __DIR__ . '/../data.json';
if (!file_exists($dataFile)) {
    http_response_code(404);
    echo json_encode(['success' => false, 'error' => 'Data file not found']);
    exit;
}

$jsonData = file_get_contents($dataFile);
$data = json_decode($jsonData, true);

if (!$data || !isset($data['registrations'])) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Invalid data format']);
    exit;
}

// Find the registration by ID
$registration = null;
foreach ($data['registrations'] as $reg) {
    if (isset($reg['id']) && $reg['id'] === $registrationId) {
        $registration = $reg;
        break;
    }
}

if (!$registration) {
    http_response_code(404);
    echo json_encode(['success' => false, 'error' => 'Registration not found']);
    exit;
}

// Return the registration data
echo json_encode([
    'success' => true,
    'registration' => $registration
]);
?>