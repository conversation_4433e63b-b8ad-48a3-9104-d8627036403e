<?php
// Debug script to test generate-update-link.php
echo "=== Debug Generate Update Link ===\n";

// Check if files exist
echo "1. Checking file existence:\n";
echo "   - UpdateTokenManager.php: " . (file_exists('includes/UpdateTokenManager.php') ? "✓" : "✗") . "\n";
echo "   - data.json: " . (file_exists('data.json') ? "✓" : "✗") . "\n";
echo "   - data/update_tokens.json: " . (file_exists('data/update_tokens.json') ? "✓" : "✗") . "\n";

// Test UpdateTokenManager
echo "\n2. Testing UpdateTokenManager:\n";
try {
    require_once 'includes/UpdateTokenManager.php';
    $tokenManager = new UpdateTokenManager();
    echo "   - UpdateTokenManager created: ✓\n";
    
    // Test token generation
    $token = $tokenManager->generateUpdateToken('church_4', 'church');
    if ($token) {
        echo "   - Token generated: ✓ ($token)\n";
    } else {
        echo "   - Token generation failed: ✗\n";
    }
    
} catch (Exception $e) {
    echo "   - Error: " . $e->getMessage() . "\n";
}

// Test the actual generate-update-link.php
echo "\n3. Testing generate-update-link.php:\n";

// Simulate POST request
$_SERVER['REQUEST_METHOD'] = 'POST';
$_POST['registration_id'] = 'church_4';
$_POST['registration_type'] = 'church';
$_SERVER['HTTP_HOST'] = 'localhost';
$_SERVER['REQUEST_URI'] = '/crusades/notc/debug-generate-link.php';

echo "   - Simulating POST request with:\n";
echo "     registration_id: " . $_POST['registration_id'] . "\n";
echo "     registration_type: " . $_POST['registration_type'] . "\n";

// Capture any errors
ob_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    // Include the generate-update-link.php content directly
    require_once 'includes/UpdateTokenManager.php';

    // Get the registration ID and type from POST data
    $registrationId = $_POST['registration_id'] ?? '';
    $registrationType = $_POST['registration_type'] ?? '';

    if (empty($registrationId) || empty($registrationType)) {
        throw new Exception('Missing required parameters');
    }

    // Check if required directories exist
    $dataDir = __DIR__ . '/data';
    if (!is_dir($dataDir)) {
        if (!mkdir($dataDir, 0755, true)) {
            throw new Exception('Failed to create data directory');
        }
    }

    // Check if registrations file exists
    $regsFile = __DIR__ . '/data.json';
    if (!file_exists($regsFile)) {
        throw new Exception('Registrations file not found: ' . $regsFile);
    }

    $tokenManager = new UpdateTokenManager();

    // Check if a token already exists for this registration
    $existingToken = $tokenManager->getTokenForRegistration($registrationId);

    if ($existingToken) {
        echo "   - Found existing token: $existingToken\n";
        $updateLink = 'https://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/update-dashboard.php?token=' . $existingToken;
        $result = [
            'success' => true,
            'token' => $existingToken,
            'update_link' => $updateLink,
            'is_new' => false
        ];
    } else {
        // Generate new token
        $token = $tokenManager->generateUpdateToken($registrationId, $registrationType);

        if ($token) {
            echo "   - Generated new token: $token\n";
            $updateLink = 'https://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/update-dashboard.php?token=' . $token;
            $result = [
                'success' => true,
                'token' => $token,
                'update_link' => $updateLink,
                'is_new' => true
            ];
        } else {
            throw new Exception('Failed to generate update token');
        }
    }

    echo "   - Success: ✓\n";
    echo "   - Result: " . json_encode($result, JSON_PRETTY_PRINT) . "\n";

} catch (Exception $e) {
    echo "   - Error: " . $e->getMessage() . "\n";
    echo "   - Stack trace:\n" . $e->getTraceAsString() . "\n";
}

$output = ob_get_clean();
if (!empty($output)) {
    echo "\n4. Captured output/errors:\n";
    echo $output . "\n";
}

echo "\n=== End Debug ===\n";
?>