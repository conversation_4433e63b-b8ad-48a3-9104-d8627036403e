<?php
/**
 * Delete Network Handler
 * Handles deletion of network registrations from the admin backend
 */

// Set content type to JSON
header('Content-Type: application/json');

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    // Check if request method is POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST requests are allowed');
    }
    
    // Check if network_type is provided
    if (!isset($_POST['network_type']) || empty($_POST['network_type'])) {
        throw new Exception('Network type is required');
    }
    
    $networkType = $_POST['network_type'];
    
    // Load the data file
    $dataFile = __DIR__ . '/data.json';
    if (!file_exists($dataFile)) {
        throw new Exception('Data file not found');
    }
    
    // Read current data
    $jsonData = file_get_contents($dataFile);
    if ($jsonData === false) {
        throw new Exception('Failed to read data file');
    }
    
    $data = json_decode($jsonData, true);
    if ($data === null) {
        throw new Exception('Invalid JSON data');
    }
    
    if (!isset($data['registrations']) || !is_array($data['registrations'])) {
        throw new Exception('Invalid data structure');
    }
    
    // Filter out network registrations of the specified type
    $originalCount = count($data['registrations']);
    $filteredRegistrations = [];
    $deletedCount = 0;
    
    foreach ($data['registrations'] as $registration) {
        // Check if this is a network registration of the specified type
        if (isset($registration['type']) && 
            $registration['type'] === 'network' && 
            isset($registration['network_type']) && 
            $registration['network_type'] === $networkType) {
            // This registration should be deleted
            $deletedCount++;
        } else {
            // Keep this registration
            $filteredRegistrations[] = $registration;
        }
    }
    
    // Update the data structure
    $data['registrations'] = $filteredRegistrations;
    
    // Create backup of original file
    $backupFile = $dataFile . '.backup.' . date('Y-m-d_H-i-s');
    if (!copy($dataFile, $backupFile)) {
        error_log("Warning: Failed to create backup file: $backupFile");
    }
    
    // Write the updated data back to the file
    $newJsonData = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
    if ($newJsonData === false) {
        throw new Exception('Failed to encode JSON data');
    }
    
    if (file_put_contents($dataFile, $newJsonData) === false) {
        throw new Exception('Failed to write updated data to file');
    }
    
    // Log the deletion
    error_log("Network deletion: Deleted $deletedCount registrations for network type '$networkType'");
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => "Successfully deleted $deletedCount registration(s) for network '$networkType'",
        'deleted_count' => $deletedCount,
        'network_type' => $networkType
    ]);
    
} catch (Exception $e) {
    // Log the error
    error_log("Network deletion error: " . $e->getMessage());
    
    // Return error response
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
