<?php
/**
 * <PERSON>ript to generate update links for all existing registrations
 * This should be run once to create update links for existing registrations
 */

require_once 'includes/UpdateTokenManager.php';

// Load existing registrations
$registrationsFile = __DIR__ . '/data.json';
if (!file_exists($registrationsFile)) {
    die("Registrations file not found: $registrationsFile\n");
}

$data = json_decode(file_get_contents($registrationsFile), true);
if (!$data || !isset($data['registrations'])) {
    die("Invalid registrations data\n");
}

$tokenManager = new UpdateTokenManager();
$generated = 0;
$existing = 0;

echo "Generating update links for existing registrations...\n\n";

foreach ($data['registrations'] as $registration) {
    $registrationId = $registration['id'];
    $registrationType = $registration['registration_type'] ?? $registration['type'];
    
    // Check if token already exists
    $existingToken = $tokenManager->getTokenForRegistration($registrationId);
    
    if ($existingToken) {
        $existing++;
        echo "EXISTING: {$registration['display_name']} ({$registrationType}) - Token already exists\n";
    } else {
        // Generate new token
        $token = $tokenManager->generateUpdateToken($registrationId, $registrationType);
        
        if ($token) {
            $generated++;
            $updateLink = 'https://' . ($_SERVER['HTTP_HOST'] ?? 'your-domain.com') . '/notc/update-dashboard.php?token=' . $token;
            echo "GENERATED: {$registration['display_name']} ({$registrationType})\n";
            echo "  Link: $updateLink\n";
        } else {
            echo "ERROR: Failed to generate token for {$registration['display_name']}\n";
        }
    }
}

echo "\n=== SUMMARY ===\n";
echo "Generated: $generated new tokens\n";
echo "Existing: $existing tokens already existed\n";
echo "Total: " . ($generated + $existing) . " registrations processed\n";

if ($generated > 0) {
    echo "\nUpdate links have been generated. You can now use the admin dashboard to view and share these links.\n";
}
?>