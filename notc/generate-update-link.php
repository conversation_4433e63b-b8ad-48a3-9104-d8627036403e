<?php
header('Content-Type: application/json');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method not allowed');
    }

    $registrationId = $_POST['registration_id'] ?? '';
    $registrationType = $_POST['registration_type'] ?? '';

    if (empty($registrationId) || empty($registrationType)) {
        throw new Exception('Missing parameters: ID=' . $registrationId . ', Type=' . $registrationType);
    }

    // Include the UpdateTokenManager
    require_once __DIR__ . '/includes/UpdateTokenManager.php';
    
    $tokenManager = new UpdateTokenManager();
    
    // Check if a token already exists for this registration
    $existingToken = $tokenManager->getTokenForRegistration($registrationId);
    
    if ($existingToken) {
        // Return existing token
        $updateLink = 'https://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/update-dashboard.php?token=' . $existingToken;
        echo json_encode([
            'success' => true,
            'token' => $existingToken,
            'update_link' => $updateLink,
            'is_new' => false
        ]);
    } else {
        // Generate new token
        $token = $tokenManager->generateUpdateToken($registrationId, $registrationType);
        
        if ($token) {
            $updateLink = 'https://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/update-dashboard.php?token=' . $token;
            echo json_encode([
                'success' => true,
                'token' => $token,
                'update_link' => $updateLink,
                'is_new' => true
            ]);
        } else {
            throw new Exception('Failed to generate update token');
        }
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to generate update link: ' . $e->getMessage()]);
}
?>