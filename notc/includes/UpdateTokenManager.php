<?php

class UpdateTokenManager {
    private $tokensFile;
    private $registrationsFile;
    
    public function __construct() {
        $this->tokensFile = __DIR__ . '/../data/update_tokens.json';
        $this->registrationsFile = __DIR__ . '/../data.json'; // Use the data.json file in notc directory
        $this->ensureTokensFileExists();
    }
    
    private function ensureTokensFileExists() {
        if (!file_exists($this->tokensFile)) {
            // Ensure directory exists
            $dir = dirname($this->tokensFile);
            if (!is_dir($dir)) {
                if (!mkdir($dir, 0755, true)) {
                    throw new Exception('Failed to create tokens directory: ' . $dir);
                }
            }
            
            $initialData = [
                'tokens' => [],
                'created_at' => date('Y-m-d H:i:s'),
                'last_updated' => date('Y-m-d H:i:s')
            ];
            
            if (file_put_contents($this->tokensFile, json_encode($initialData, JSON_PRETTY_PRINT)) === false) {
                throw new Exception('Failed to create tokens file: ' . $this->tokensFile);
            }
        }
    }
    
    private function loadTokens() {
        if (!file_exists($this->tokensFile)) {
            return ['tokens' => []];
        }
        
        $content = file_get_contents($this->tokensFile);
        $data = json_decode($content, true);
        return $data ?: ['tokens' => []];
    }
    
    private function saveTokens($data) {
        $data['last_updated'] = date('Y-m-d H:i:s');
        $result = file_put_contents($this->tokensFile, json_encode($data, JSON_PRETTY_PRINT));
        
        if ($result === false) {
            throw new Exception('Failed to save tokens to file: ' . $this->tokensFile);
        }
        
        return true;
    }
    
    public function generateUpdateToken($registrationId, $registrationType) {
        try {
            $token = bin2hex(random_bytes(32)); // 64 character token
            $tokenData = [
                'token' => $token,
                'registration_id' => $registrationId,
                'registration_type' => $registrationType,
                'created_at' => date('Y-m-d H:i:s'),
                'expires_at' => date('Y-m-d H:i:s', strtotime('+1 year')), // Token expires in 1 year
                'used_count' => 0,
                'last_used' => null,
                'is_active' => true
            ];
            
            $data = $this->loadTokens();
            $data['tokens'][$token] = $tokenData;
            
            $this->saveTokens($data);
            return $token;
            
        } catch (Exception $e) {
            error_log('Error generating update token: ' . $e->getMessage());
            return false;
        }
    }
    
    public function validateToken($token) {
        $data = $this->loadTokens();
        
        if (!isset($data['tokens'][$token])) {
            return false;
        }
        
        $tokenData = $data['tokens'][$token];
        
        // Check if token is active
        if (!$tokenData['is_active']) {
            return false;
        }
        
        // Check if token has expired
        if (strtotime($tokenData['expires_at']) < time()) {
            return false;
        }
        
        return $tokenData;
    }
    
    public function useToken($token) {
        $data = $this->loadTokens();
        
        if (!isset($data['tokens'][$token])) {
            return false;
        }
        
        // Update usage statistics
        $data['tokens'][$token]['used_count']++;
        $data['tokens'][$token]['last_used'] = date('Y-m-d H:i:s');
        
        return $this->saveTokens($data);
    }
    
    public function getRegistrationByToken($token) {
        $tokenData = $this->validateToken($token);
        if (!$tokenData) {
            return false;
        }
        
        // Load registration data
        if (!file_exists($this->registrationsFile)) {
            return false;
        }
        
        $content = file_get_contents($this->registrationsFile);
        $registrationData = json_decode($content, true);
        
        if (!$registrationData || !isset($registrationData['registrations'])) {
            return false;
        }
        
        // Find the registration
        foreach ($registrationData['registrations'] as $registration) {
            if ($registration['id'] === $tokenData['registration_id']) {
                return $registration;
            }
        }
        
        return false;
    }
    
    public function updateRegistration($token, $updatedData) {
        $tokenData = $this->validateToken($token);
        if (!$tokenData) {
            return false;
        }
        
        // Load registration data
        if (!file_exists($this->registrationsFile)) {
            return false;
        }
        
        $content = file_get_contents($this->registrationsFile);
        $registrationData = json_decode($content, true);
        
        if (!$registrationData || !isset($registrationData['registrations'])) {
            return false;
        }
        
        // Find and update the registration
        $updated = false;
        foreach ($registrationData['registrations'] as &$registration) {
            if ($registration['id'] === $tokenData['registration_id']) {
                // Merge updated data with existing registration
                $registration = array_merge($registration, $updatedData);
                $registration['updated_at'] = date('Y-m-d H:i:s');
                $registration['last_update_source'] = 'update_link';
                $updated = true;
                break;
            }
        }
        
        if ($updated) {
            // Save updated registration data
            $success = file_put_contents($this->registrationsFile, json_encode($registrationData, JSON_PRETTY_PRINT)) !== false;
            
            if ($success) {
                // Mark token as used
                $this->useToken($token);
            }
            
            return $success;
        }
        
        return false;
    }
    
    public function getTokenForRegistration($registrationId) {
        $data = $this->loadTokens();
        
        foreach ($data['tokens'] as $token => $tokenData) {
            if ($tokenData['registration_id'] === $registrationId && $tokenData['is_active']) {
                return $token;
            }
        }
        
        return false;
    }
    
    public function deactivateToken($token) {
        $data = $this->loadTokens();
        
        if (isset($data['tokens'][$token])) {
            $data['tokens'][$token]['is_active'] = false;
            $data['tokens'][$token]['deactivated_at'] = date('Y-m-d H:i:s');
            return $this->saveTokens($data);
        }
        
        return false;
    }
    
    public function getTokenStats($token) {
        $data = $this->loadTokens();
        
        if (isset($data['tokens'][$token])) {
            return $data['tokens'][$token];
        }
        
        return false;
    }
    
    public function getAllTokens() {
        $data = $this->loadTokens();
        return $data['tokens'];
    }
    
    public function cleanupExpiredTokens() {
        $data = $this->loadTokens();
        $currentTime = time();
        $cleaned = 0;
        
        foreach ($data['tokens'] as $token => $tokenData) {
            if (strtotime($tokenData['expires_at']) < $currentTime) {
                unset($data['tokens'][$token]);
                $cleaned++;
            }
        }
        
        if ($cleaned > 0) {
            $this->saveTokens($data);
        }
        
        return $cleaned;
    }
}

?>