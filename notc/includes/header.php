<?php
require_once '../includes/config.php';
require_once '../includes/languages.php';
require_once '../includes/TranslationService.php';

// Set global variable for subdirectory detection
$GLOBALS['in_subdirectory'] = true;

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Initialize translation service
$translationService = TranslationService::getInstance();
$currentLanguage = Language::getCurrentLanguage();
?>
<!DOCTYPE html>
<html lang="<?php echo Language::getCurrentLanguage(); ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title><?php echo __('site_title'); ?> - Global</title>
    
    <!-- Page Loader Styles -->
    <style>
        /* Payment method selection styles */
        .payment-method-option {
            transition: all 0.2s ease;
            border: 1px solid #e5e7eb;
            cursor: pointer;
        }
        .payment-method-option:hover {
            border-color: #3b82f6;
        }
        .payment-method-option.border-primary {
            border-color: #3b82f6;
            background-color: rgba(59, 130, 246, 0.05);
        }
        .payment-method-option input[type="radio"]:checked + div {
            border-color: #3b82f6;
            background-color: rgba(59, 130, 246, 0.05);
        }
        
        #page-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.5s ease, visibility 0.5s ease;
        }

        #page-loader.hidden {
            opacity: 0;
            visibility: hidden;
            pointer-events: none;
        }

        /* Force hide loader with !important if needed */
        #page-loader.force-hide {
            display: none !important;
        }

        .loader-circle {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f4f6;
            border-top: 4px solid #2563eb;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Hide main content until loaded */
        body:not(.loaded) main,
        body:not(.loaded) header,
        body:not(.loaded) footer,
        body:not(.loaded) section {
            opacity: 0;
        }

        /* Scroll Snap Styles */
        .scroll-container {
            scroll-snap-type: y mandatory;
            overflow-y: scroll;
            overflow-x: hidden;
            height: 100vh;
            width: 100vw;
        }

        .scroll-section {
            scroll-snap-align: start;
            height: 100vh;
            width: 100vw;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow-x: hidden;
        }

        /* Prevent horizontal overflow */
        html, body {
            overflow-x: hidden;
            width: 100%;
            max-width: 100vw;
        }

        /* Enhanced mobile touch targets */
        .touch-target {
            min-height: 44px;
            min-width: 44px;
            touch-action: manipulation;
        }

        /* iOS Safari input fix */
        input[type="text"],
        input[type="email"], 
        input[type="tel"],
        input[type="number"],
        select,
        textarea {
            -webkit-appearance: none;
            border-radius: 0;
            transform: translateZ(0);
            -webkit-transform: translateZ(0);
        }

        /* Prevent iOS zoom on input focus */
        @media screen and (max-width: 768px) {
            input[type="text"],
            input[type="email"], 
            input[type="tel"],
            input[type="number"],
            select,
            textarea {
                font-size: 16px !important;
            }
        }

    </style>

    <!-- Favicon -->
    <link rel="icon" type="image/webp" href="../assets/images/favicon.webp">
    <link rel="shortcut icon" type="image/webp" href="../assets/images/favicon.webp">
    <link rel="apple-touch-icon" type="image/webp" href="../assets/images/favicon.webp">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Oswald:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- GSAP and ScrollTrigger -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Cross-Platform Utilities -->
    <script>
        // Cross-platform compatibility utilities
        window.AppUtils = {
            // Check if device supports touch
            isTouchDevice: function() {
                return 'ontouchstart' in window || navigator.maxTouchPoints > 0 || navigator.msMaxTouchPoints > 0;
            },
            
            // Get base URL for API calls
            getBaseUrl: function() {
                const protocol = window.location.protocol;
                const host = window.location.host;
                const path = window.location.pathname;
                const basePath = path.substring(0, path.lastIndexOf('/'));
                return protocol + '//' + host + basePath;
            },
            
            // Enhanced fetch with fallback for older browsers
            fetchWithFallback: function(url, options = {}) {
                if (typeof fetch !== 'undefined') {
                    return fetch(url, options);
                } else {
                    // XMLHttpRequest fallback for older browsers
                    return new Promise((resolve, reject) => {
                        const xhr = new XMLHttpRequest();
                        xhr.open(options.method || 'GET', url);
                        xhr.onload = () => {
                            if (xhr.status >= 200 && xhr.status < 300) {
                                resolve({
                                    ok: true,
                                    status: xhr.status,
                                    json: () => Promise.resolve(JSON.parse(xhr.responseText)),
                                    text: () => Promise.resolve(xhr.responseText)
                                });
                            } else {
                                reject(new Error(xhr.statusText));
                            }
                        };
                        xhr.onerror = () => reject(new Error('Network Error'));
                        if (options.headers) {
                            Object.keys(options.headers).forEach(key => {
                                xhr.setRequestHeader(key, options.headers[key]);
                            });
                        }
                        xhr.send(options.body);
                    });
                }
            },
            
            // Add touch event support
            addTouchSupport: function(element, callback) {
                if (this.isTouchDevice()) {
                    element.addEventListener('touchstart', callback, { passive: true });
                    element.addEventListener('touchend', function(e) {
                        e.preventDefault();
                        callback(e);
                    });
                }
                element.addEventListener('click', callback);
            },
            
            // Fix iOS Safari viewport issues
            fixiOSViewport: function() {
                if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
                    const viewportMeta = document.querySelector('meta[name="viewport"]');
                    if (viewportMeta) {
                        let content = viewportMeta.getAttribute('content');
                        if (!content.includes('viewport-fit=cover')) {
                            content += ', viewport-fit=cover';
                            viewportMeta.setAttribute('content', content);
                        }
                    }
                }
            }
        };
        
        // Initialize on DOM load
        document.addEventListener('DOMContentLoaded', function() {
            window.AppUtils.fixiOSViewport();
        });
    </script>
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    screens: {
                        'xs': '475px',  // Custom breakpoint for very small screens
                    },
                    colors: {
                        primary: '#2563eb', // Blue-600
                        primaryDark: '#1d4ed8', // Blue-700
                        primaryGradient: 'linear-gradient(to right, #2563eb, #1e40af)', // Blue-600 to Blue-800
                        secondary: '#ffffff', // White
                        accent: '#fbbf24', // Yellow-400
                        gold: '#d97706', // Amber-600
                        'blue-25': '#f0f9ff', // Very light blue
                    },
                    backgroundImage: {
                        'gradient-primary': 'linear-gradient(to right, var(--tw-gradient-stops))',
                    },
                    gradientColorStops: {
                        'primary-start': '#2563eb', // Blue-600
                        'primary-end': '#1e40af', // Blue-800
                    },
                    fontFamily: {
                        'sans': ['Oswald', 'system-ui', 'sans-serif'],
                        'display': ['Oswald', 'serif'],
                    },
                    animation: {
                        'fade-in-left': 'fadeInLeft 1s ease-out forwards',
                        'fade-in-right': 'fadeInRight 1s ease-out 0.2s forwards',
                        'fade-in-up': 'fadeInUp 0.8s ease-out forwards',
                        'gentle-float': 'gentleFloat 6s ease-in-out infinite',
                    },
                    boxShadow: {
                        'soft': '0 4px 20px rgba(0, 0, 0, 0.08)',
                        'soft-lg': '0 10px 40px rgba(0, 0, 0, 0.1)',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-white text-gray-800 font-sans scroll-container">
    <!-- Page Loader -->
    <div id="page-loader">
        <div class="loader-circle"></div>
    </div>

    <!-- Header (fixed) -->
    <header class="fixed top-0 left-0 right-0 bg-white border-b border-gray-100 z-50">
        <div class="max-w-6xl mx-auto px-4 sm:px-6">
            <div class="flex justify-between items-center py-3 sm:py-6">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="../notc" class="flex items-center">
                        <img class="h-12 sm:h-16 md:h-20 w-auto" src="../assets/images/logo.webp" alt="<?php echo __('site_title'); ?>">
                    </a>
                </div>
                
                <!-- Navigation -->
                <div class="flex items-center space-x-4">
                    <a href="../" class="text-primary hover:text-primaryDark transition-colors duration-200">
                        <?php 
                        if ($currentLanguage === 'en') {
                            echo 'Home';
                        } else {
                            echo $translationService->translateText('Home', 'en', $currentLanguage) ?? 'Home';
                        }
                        ?>
                    </a>
                    
                    <!-- Language Selector -->
                    <div class="relative">
                        <button type="button" id="language-menu-button" aria-expanded="false" aria-haspopup="true" 
                                class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-600 hover:text-primary transition-colors duration-200 focus:outline-none border border-transparent hover:border-accent rounded-md">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="mr-2"><?php 
                                $availableLanguages = Language::getAvailableLanguages();
                                $currentLang = Language::getCurrentLanguage();
                                echo isset($availableLanguages[$currentLang]) ? $availableLanguages[$currentLang] : strtoupper($currentLang);
                            ?></span>
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div id="language-menu" role="menu" aria-orientation="vertical" aria-labelledby="language-menu-button" tabindex="-1" 
                                 class="hidden origin-top-right absolute right-0 mt-2 w-48 bg-white border border-gray-200 focus:outline-none z-10 max-h-64 overflow-y-auto shadow-lg rounded-none">
                                <?php 
                                // Get current URL parameters and preserve them
                                $currentParams = $_GET;
                                foreach (Language::getAvailableLanguages() as $code => $name): 
                                    $currentParams['lang'] = $code;
                                    $langUrl = '?' . http_build_query($currentParams);
                                ?>
                                <a href="<?php echo $langUrl; ?>" role="menuitem" 
                                   class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors duration-200">
                                    <img class="h-4 w-4 mr-3" src="../assets/images/flags/<?php echo $code; ?>.svg" alt="<?php echo $name; ?>">
                                    <span><?php echo $name; ?></span>
                                    <?php if (Language::getCurrentLanguage() === $code): ?>
                                    <svg class="w-4 h-4 ml-auto text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <?php endif; ?>
                                </a>
                                <?php endforeach; ?>
                            </div>
                        </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="pt-16 sm:pt-20 md:pt-24">

    <!-- Language Dropdown and Page Loader JavaScript -->
    <script>
        // Language dropdown functionality
        document.addEventListener('DOMContentLoaded', function() {
            const languageButton = document.getElementById('language-menu-button');
            const languageMenu = document.getElementById('language-menu');
            
            if (languageButton && languageMenu) {
                languageButton.addEventListener('click', function() {
                    const isExpanded = languageButton.getAttribute('aria-expanded') === 'true';
                    languageButton.setAttribute('aria-expanded', !isExpanded);
                    languageMenu.classList.toggle('hidden');
                });
                
                // Close dropdown when clicking outside
                document.addEventListener('click', function(event) {
                    if (!languageButton.contains(event.target) && !languageMenu.contains(event.target)) {
                        languageButton.setAttribute('aria-expanded', 'false');
                        languageMenu.classList.add('hidden');
                    }
                });
            }
            
            // Page loader functionality
            const loader = document.getElementById('page-loader');
            
            // Hide loader after DOM is loaded
            function hideLoader() {
                if (loader) {
                    loader.classList.add('hidden');
                    document.body.classList.add('loaded');
                    console.log('Loader hidden');
                }
            }
            
            // Set a timer to hide the loader if it takes too long
            const loaderTimeout = setTimeout(() => {
                console.log('Loader timeout - forcing hide');
                hideLoader();
            }, 2000);
            
            // Hide loader when everything is ready
            if (document.readyState === 'complete') {
                hideLoader();
                clearTimeout(loaderTimeout);
            } else {
                window.addEventListener('load', () => {
                    hideLoader();
                    clearTimeout(loaderTimeout);
                });
            }
        });
    </script>