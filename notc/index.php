<?php 
require_once '../includes/config.php';
require_once '../includes/languages.php';
require_once '../includes/TranslationService.php';

// Initialize translation service
$translationService = TranslationService::getInstance();
$currentLanguage = Language::getCurrentLanguage();

include 'includes/header.php'; 
?>

<!-- Fixed Video Background -->
<div class="fixed inset-0 w-full h-full z-0">
    <video 
        autoplay 
        muted 
        loop 
        playsinline 
        class="absolute inset-0 w-full h-full object-cover"
        poster="https://rorcloud.org/serve.php?id=269&name=Night_of_thousand_crusades.jpg">
        <source src="https://rorcloud.org/serve.php?id=279&name=output(compress-video-online.com).mp4" type="video/mp4">
        Your browser does not support the video tag.
    </video>
    <!-- Dark Overlay for better text readability -->
    <div class="absolute inset-0 bg-black bg-opacity-70"></div>
    <!-- Additional gradient overlay for better text contrast -->
    <div class="absolute inset-0 bg-gradient-to-b from-black/20 via-transparent to-black/40"></div>
</div>

<!-- Scroll Snap Section 1 - Church/Group/Zone -->
<section class="scroll-section text-white relative z-10 overflow-x-hidden" id="section-1">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative w-full">
        <div class="flex flex-col items-center text-center w-full max-w-5xl mx-auto">
            <!-- Text Content -->
            <div class="w-full overflow-hidden">
                <h1 class="section-title text-3xl xs:text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl 2xl:text-9xl font-bold mb-4 sm:mb-6 lg:mb-8 drop-shadow-lg leading-tight sm:leading-none break-words">
                    <span class="block sm:inline">
                        <?php 
                        if ($currentLanguage === 'en') {
                            echo 'Church/Group/Zone';
                        } else {
                            echo $translationService->translateText('Church/Group/Zone', 'en', $currentLanguage) ?? 'Church/Group/Zone';
                        }
                        ?>
                    </span>
                    <span class="block">
                        <?php 
                        if ($currentLanguage === 'en') {
                            echo 'Registration';
                        } else {
                            echo $translationService->translateText('Registration', 'en', $currentLanguage) ?? 'Registration';
                        }
                        ?>
                    </span>
                </h1>
                <p class="section-description text-base xs:text-lg sm:text-xl md:text-2xl lg:text-2xl xl:text-3xl drop-shadow-lg leading-relaxed text-gray-100 opacity-0 max-w-4xl mx-auto break-words mb-8 sm:mb-10 lg:mb-12">
                    <?php 
                    if ($currentLanguage === 'en') {
                        echo 'Register your church, group, or zone to participate in the Night of Thousand Crusades.';
                    } else {
                        echo $translationService->translateText('Register your church, group, or zone to participate in the Night of Thousand Crusades.', 'en', $currentLanguage) ?? 'Register your church, group, or zone to participate in the Night of Thousand Crusades.';
                    }
                    ?>
                </p>
            </div>
            
            <!-- Button Below Text -->
            <div class="flex justify-center">
                <a href="register-church" class="section-button inline-block bg-accent text-gray-900 px-8 sm:px-10 md:px-12 lg:px-16 py-4 sm:py-5 md:py-6 font-bold hover:bg-yellow-300 transition-all duration-300 shadow-xl hover:shadow-2xl rounded-lg text-lg sm:text-xl md:text-2xl text-center transform opacity-0">
                    <span class="hidden sm:inline">
                        <?php 
                        if ($currentLanguage === 'en') {
                            echo 'Register Church/Group/Zone';
                        } else {
                            echo $translationService->translateText('Register Church/Group/Zone', 'en', $currentLanguage) ?? 'Register Church/Group/Zone';
                        }
                        ?>
                    </span>
                    <span class="sm:hidden">
                        <?php 
                        if ($currentLanguage === 'en') {
                            echo 'Register Church/Group';
                        } else {
                            echo $translationService->translateText('Register Church/Group', 'en', $currentLanguage) ?? 'Register Church/Group';
                        }
                        ?>
                    </span>
                </a>
            </div>
            
            <!-- Navigation Button to Section 2 -->
            <div class="flex justify-center mt-8 sm:mt-12">
                <button onclick="scrollToSection('section-2')" class="nav-button inline-flex items-center bg-white/10 backdrop-blur-md text-white px-6 py-3 rounded-full hover:bg-white/20 transition-all duration-300 shadow-lg hover:shadow-xl border border-white/20 hover:border-white/40 opacity-0">
                    <span class="mr-2">
                        <?php 
                        if ($currentLanguage === 'en') {
                            echo 'Network Registration';
                        } else {
                            echo $translationService->translateText('Network Registration', 'en', $currentLanguage) ?? 'Network Registration';
                        }
                        ?>
                    </span>
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>
</section>

<!-- Scroll Snap Section 2 - Network -->
<section class="scroll-section text-white relative z-10 overflow-x-hidden" id="section-2">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative w-full">
        <div class="flex flex-col items-center text-center w-full max-w-5xl mx-auto">
            <!-- Navigation Button to Section 1 -->
            <div class="flex justify-center mb-8 sm:mb-12">
                <button onclick="scrollToSection('section-1')" class="nav-button inline-flex items-center bg-white/10 backdrop-blur-md text-white px-6 py-3 rounded-full hover:bg-white/20 transition-all duration-300 shadow-lg hover:shadow-xl border border-white/20 hover:border-white/40 opacity-0">
                    <svg class="w-5 h-5 mr-2 rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                    <span>
                        <?php 
                        if ($currentLanguage === 'en') {
                            echo 'Church/Group/Zone Registration';
                        } else {
                            echo $translationService->translateText('Church/Group/Zone Registration', 'en', $currentLanguage) ?? 'Church/Group/Zone Registration';
                        }
                        ?>
                    </span>
                </button>
            </div>
            
            <!-- Text Content -->
            <div class="w-full overflow-hidden">
                <h1 class="section-title text-3xl xs:text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl 2xl:text-9xl font-bold mb-4 sm:mb-6 lg:mb-8 drop-shadow-lg leading-tight sm:leading-none break-words">
                    <span class="block sm:inline">
                        <?php 
                        if ($currentLanguage === 'en') {
                            echo 'Network';
                        } else {
                            echo $translationService->translateText('Network', 'en', $currentLanguage) ?? 'Network';
                        }
                        ?>
                    </span>
                    <span class="block">
                        <?php 
                        if ($currentLanguage === 'en') {
                            echo 'Registration';
                        } else {
                            echo $translationService->translateText('Registration', 'en', $currentLanguage) ?? 'Registration';
                        }
                        ?>
                    </span>
                </h1>
                <p class="section-description text-base xs:text-lg sm:text-xl md:text-2xl lg:text-2xl xl:text-3xl drop-shadow-lg leading-relaxed text-gray-100 opacity-0 max-w-4xl mx-auto break-words mb-8 sm:mb-10 lg:mb-12">
                    <?php 
                    if ($currentLanguage === 'en') {
                        echo 'Register your network or organization to coordinate large-scale crusade activities, reaching the world on a massive scale.';
                    } else {
                        echo $translationService->translateText('Register your network or organization to coordinate large-scale crusade activities, reaching the world on a massive scale.', 'en', $currentLanguage) ?? 'Register your network or organization to coordinate large-scale crusade activities, reaching the world on a massive scale.';
                    }
                    ?>
                </p>
            </div>
            
            <!-- Button Below Text -->
            <div class="flex justify-center">
                <a href="register-network" class="section-button inline-block bg-primary text-white px-8 sm:px-10 md:px-12 lg:px-16 py-4 sm:py-5 md:py-6 font-bold hover:bg-primaryDark transition-all duration-300 shadow-xl hover:shadow-2xl rounded-lg text-lg sm:text-xl md:text-2xl text-center transform opacity-0">
                    <span class="hidden sm:inline">
                        <?php 
                        if ($currentLanguage === 'en') {
                            echo 'Register Network';
                        } else {
                            echo $translationService->translateText('Register Network', 'en', $currentLanguage) ?? 'Register Network';
                        }
                        ?>
                    </span>
                    <span class="sm:hidden">
                        <?php 
                        if ($currentLanguage === 'en') {
                            echo 'Register Network';
                        } else {
                            echo $translationService->translateText('Register Network', 'en', $currentLanguage) ?? 'Register Network';
                        }
                        ?>
                    </span>
                </a>
            </div>
        </div>
    </div>
</section>

<!-- GSAP Animations Script -->
<script>
// Smooth scroll function for navigation buttons
function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        section.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
        
        // Wait for smooth scroll to complete, then refresh and check animations
        setTimeout(() => {
            if (!isMobile) {
                ScrollTrigger.refresh();
                
                // Manually trigger animations if they haven't started
                const targetSection = document.getElementById(sectionId);
                const elements = targetSection.querySelectorAll('.section-title, .section-description, .section-button, .nav-button');
                
                elements.forEach(element => {
                    const currentOpacity = gsap.getProperty(element, "opacity");
                    if (currentOpacity === 0 || currentOpacity === "0") {
                        // Element hasn't animated yet, force trigger
                        ScrollTrigger.refresh();
                    }
                });
            }
        }, 1000);
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Get screen size for responsive animations
    const isMobile = window.innerWidth < 640;
    const isTablet = window.innerWidth >= 640 && window.innerWidth < 1024;
    const isDesktop = window.innerWidth >= 1024;
    
    // For mobile, set all elements to be visible immediately
    if (isMobile) {
        document.querySelectorAll('.section-title, .section-description, .section-button, .nav-button').forEach(el => {
            el.style.opacity = '1';
            el.style.transform = 'none';
        });
        return; // Skip all GSAP animations for mobile
    }
    
    // Register GSAP ScrollTrigger plugin - only for non-mobile
    gsap.registerPlugin(ScrollTrigger);
    
    // Responsive animation values
    const getAnimationValues = () => ({
        titleY: isMobile ? 50 : isTablet ? 75 : 100,
        titleScale: isMobile ? 0.9 : 0.8,
        titleDuration: isMobile ? 0.8 : isTablet ? 1.0 : 1.2,
        descriptionY: isMobile ? 30 : 50,
        descriptionDuration: isMobile ? 0.6 : 0.8,
        buttonY: isMobile ? 30 : isTablet ? 40 : 50,
        buttonScale: isMobile ? 0.95 : 0.9,
        buttonDuration: isMobile ? 0.6 : 0.8,
        navButtonY: isMobile ? 20 : 30,
        navButtonDuration: isMobile ? 0.5 : 0.6,
        staggerDelay: isMobile ? 0.3 : 0.5
    });
    
    const animValues = getAnimationValues();
    
    // Animation for Section 1
    const section1Timeline = gsap.timeline({
        scrollTrigger: {
            trigger: "#section-1",
            start: "top 90%",
            end: "bottom 10%",
            toggleActions: "restart none restart reverse",
            refreshPriority: -1
        }
    });
    
    section1Timeline
        .fromTo("#section-1 .section-title", {
            opacity: 0,
            y: animValues.titleY,
            scale: animValues.titleScale
        }, {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: animValues.titleDuration,
            ease: "power3.out"
        })
        .fromTo("#section-1 .section-description", {
            opacity: 0,
            y: animValues.descriptionY
        }, {
            opacity: 1,
            y: 0,
            duration: animValues.descriptionDuration,
            ease: "power2.out"
        }, `-=${animValues.staggerDelay}`)
        .fromTo("#section-1 .section-button", {
            opacity: 0,
            y: animValues.buttonY,
            scale: animValues.buttonScale
        }, {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: animValues.buttonDuration,
            ease: "back.out(1.7)"
        }, "-=0.3")
        .fromTo("#section-1 .nav-button", {
            opacity: 0,
            y: animValues.navButtonY,
            scale: 0.9
        }, {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: animValues.navButtonDuration,
            ease: "power2.out"
        }, "-=0.2");
    
    // Animation for Section 2
    const section2Timeline = gsap.timeline({
        scrollTrigger: {
            trigger: "#section-2",
            start: "top 90%",
            end: "bottom 10%",
            toggleActions: "restart none restart reverse",
            refreshPriority: -1
        }
    });
    
    section2Timeline
        .fromTo("#section-2 .section-title", {
            opacity: 0,
            y: animValues.titleY,
            scale: animValues.titleScale
        }, {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: animValues.titleDuration,
            ease: "power3.out"
        })
        .fromTo("#section-2 .section-description", {
            opacity: 0,
            y: animValues.descriptionY
        }, {
            opacity: 1,
            y: 0,
            duration: animValues.descriptionDuration,
            ease: "power2.out"
        }, `-=${animValues.staggerDelay}`)
        .fromTo("#section-2 .nav-button", {
            opacity: 0,
            y: -animValues.navButtonY,
            scale: 0.9
        }, {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: animValues.navButtonDuration,
            ease: "power2.out"
        }, `-=${animValues.staggerDelay}`)
        .fromTo("#section-2 .section-button", {
            opacity: 0,
            y: animValues.buttonY,
            scale: animValues.buttonScale
        }, {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: animValues.buttonDuration,
            ease: "back.out(1.7)"
        }, "-=0.3");
    
    // Responsive button hover animations (desktop only)
    if (!isMobile) {
        const buttons = document.querySelectorAll('.section-button');
        buttons.forEach(button => {
            button.addEventListener('mouseenter', () => {
                gsap.to(button, {
                    scale: isTablet ? 1.03 : 1.05,
                    duration: 0.3,
                    ease: "power2.out"
                });
            });
            
            button.addEventListener('mouseleave', () => {
                gsap.to(button, {
                    scale: 1,
                    duration: 0.3,
                    ease: "power2.out"
                });
            });
        });
    }
    
    // Responsive ScrollTrigger refresh on resize
    let resizeTimer;
    window.addEventListener('resize', () => {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(() => {
            const newIsMobile = window.innerWidth < 640;
            
            // If switching between mobile and desktop, reload the page
            if (newIsMobile !== isMobile) {
                window.location.reload();
            } else if (!newIsMobile) {
                ScrollTrigger.refresh();
            }
        }, 250);
    });
});
</script>

<?php include 'includes/footer.php'; ?>