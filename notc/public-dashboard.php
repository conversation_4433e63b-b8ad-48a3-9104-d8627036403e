<?php
// Public Dashboard - Shows only statistics, no sensitive data
session_start();

// Set page title
$pageTitle = "Night of a Thousand Crusades - Live Dashboard";

// Include header
include 'includes/header.php';
?>

<div class="min-h-screen bg-gray-50 pt-16 sm:pt-20 md:pt-24">
    <!-- Dashboard Header Section -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Night of a Thousand Crusades</h1>
                    <p class="mt-2 text-gray-600">Live Dashboard - Real-time Statistics</p>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-500">Last Updated: <span id="lastUpdated">Loading...</span></p>
                    <p class="text-xs text-gray-400">Auto-refreshes every 2 minutes</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 mt-8">
        
        <!-- Statistics Cards -->
        <div class="statistics-grid grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4 mb-8">
            <div class="stat-card bg-white p-4 rounded-lg shadow">
                <div class="stat-content">
                    <div class="stat-text text-gray-600">Total Number of Crusades</div>
                    <div class="stat-number text-2xl font-bold" id="totalCrusades">0</div>
                </div>
            </div>
            <div class="stat-card bg-white p-4 rounded-lg shadow">
                <div class="stat-content">
                    <div class="stat-text text-gray-600">Countries Coverage</div>
                    <div class="stat-number text-2xl font-bold" id="countriesProgress">0 of 0</div>
                    <div class="stat-subtext text-sm text-gray-500" id="countriesPercentage">0%</div>
                </div>
            </div>
            <div class="stat-card bg-white p-4 rounded-lg shadow">
                <div class="stat-content">
                    <div class="stat-text text-gray-600">Cities Coverage</div>
                    <div class="stat-number text-2xl font-bold" id="citiesProgress">0 of 1000</div>
                    <div class="stat-subtext text-sm text-gray-500" id="citiesPercentage">0%</div>
                </div>
            </div>
            <div class="stat-card bg-white p-4 rounded-lg shadow">
                <div class="stat-content">
                    <div class="stat-text text-gray-600">Total Registrations</div>
                    <div class="stat-number text-2xl font-bold" id="totalRegistrations">0</div>
                </div>
            </div>
            <div class="stat-card bg-white p-4 rounded-lg shadow">
                <div class="stat-content">
                    <div class="stat-text text-gray-600">Church Registrations</div>
                    <div class="stat-number text-2xl font-bold" id="churchRegistrations">0</div>
                </div>
            </div>
            <div class="stat-card bg-white p-4 rounded-lg shadow">
                <div class="stat-content">
                    <div class="stat-text text-gray-600">Network Registrations</div>
                    <div class="stat-number text-2xl font-bold" id="networkRegistrations">0</div>
                </div>
            </div>
        </div>

        <!-- Coverage Map or Additional Info Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- Countries Overview -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Countries Coverage</h3>
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Registered Countries</span>
                        <span class="font-semibold" id="registeredCountriesCount">0</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Total Available Countries</span>
                        <span class="font-semibold" id="totalCountriesCount">0</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-3">
                        <div class="bg-blue-600 h-3 rounded-full transition-all duration-500" id="countriesProgressBar" style="width: 0%"></div>
                    </div>
                </div>
            </div>

            <!-- Cities Overview -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Cities Coverage</h3>
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Registered Cities</span>
                        <span class="font-semibold" id="registeredCitiesCount">0</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Target Cities</span>
                        <span class="font-semibold">1,000</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-3">
                        <div class="bg-green-600 h-3 rounded-full transition-all duration-500" id="citiesProgressBar" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Continental Coverage Analysis -->
        <div id="coverage-analysis" class="mb-8">
            <!-- Continental analysis will be loaded here -->
        </div>



    </main>
</div>

<style>
/* Body and general font styling */
body {
    font-family: 'Oswald', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
}

/* Apply Oswald font to all text elements */
h1, h2, h3, h4, h5, h6, p, div, span, button, input, select, textarea, th, td, .stat-text, .stat-number {
    font-family: 'Oswald', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
}

.statistics-grid {
    display: grid;
    gap: 1rem;
}

@media (min-width: 1280px) {
    .statistics-grid {
        grid-template-columns: repeat(6, 1fr);
    }
}

.stat-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.stat-content {
    text-align: center;
}

.stat-text {
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.stat-number {
    line-height: 1.2;
    margin-bottom: 0.25rem;
}

.stat-subtext {
    margin-top: 0.25rem;
}

/* Loading animation */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading .stat-number {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}
</style>

<script>
let countries = [];
let lastUpdateTime = null;

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    loadData();
    setupAutoRefresh();
    updateLastUpdatedTime();
});

// Load and update data
async function loadData() {
    try {
        // Add loading state
        document.body.classList.add('loading');
        
        // Load countries and registration data
        const [countriesResponse, registrationsResponse] = await Promise.all([
            fetch('../data/countriesbkd.json'),
            fetch('data.json')
        ]);
        
        countries = await countriesResponse.json();
        const data = await registrationsResponse.json();
        const registrations = data.registrations || [];
        
        // Update statistics
        await updateStatistics(countries, registrations);
        
        // Update country analysis
        updateCountryAnalysis(countries, registrations);
        
        // Remove loading state
        document.body.classList.remove('loading');
        
        // Update last updated time
        lastUpdateTime = new Date();
        updateLastUpdatedTime();
        
    } catch (error) {
        console.error('Error loading data:', error);
        document.body.classList.remove('loading');
    }
}

// Update statistics display
async function updateStatistics(countries, registrations) {
    // Update basic statistics
    document.getElementById('totalRegistrations').textContent = registrations.length.toLocaleString();
    document.getElementById('churchRegistrations').textContent = registrations.filter(reg => reg.type === 'church').length.toLocaleString();
    document.getElementById('networkRegistrations').textContent = registrations.filter(reg => reg.type === 'network' || reg.type === 'organisation').length.toLocaleString();
    
    // Calculate total number of crusades
    const totalCrusades = registrations.reduce((total, reg) => {
        const crusadeCount = parseInt(reg.number_of_crusades) || 0;
        return total + crusadeCount;
    }, 0);
    document.getElementById('totalCrusades').textContent = totalCrusades.toLocaleString();
    
    // Calculate countries coverage
    try {
        const countriesResponse = await fetch('../data/countriesbkd.json');
        const countriesData = await countriesResponse.json();
        const totalCountries = countriesData.countries.length;
        
        // Calculate registered countries from registration data
        const registeredCountriesSet = new Set();
        registrations.forEach(reg => {
            if (reg.selected_countries) {
                const selectedCountries = reg.selected_countries.split(',').map(c => c.trim().toLowerCase());
                selectedCountries.forEach(country => registeredCountriesSet.add(country));
            }
        });
        
        const registeredCountriesCount = registeredCountriesSet.size;
        const countriesPercentage = totalCountries > 0 ? Math.round((registeredCountriesCount / totalCountries) * 100) : 0;
        
        document.getElementById('countriesProgress').textContent = `${registeredCountriesCount} of ${totalCountries}`;
        document.getElementById('countriesPercentage').textContent = `${countriesPercentage}%`;
        document.getElementById('registeredCountriesCount').textContent = registeredCountriesCount.toLocaleString();
        document.getElementById('totalCountriesCount').textContent = totalCountries.toLocaleString();
        document.getElementById('countriesProgressBar').style.width = `${countriesPercentage}%`;
        
    } catch (error) {
        console.error('Error fetching countries:', error);
        document.getElementById('countriesProgress').textContent = '0 of 0';
        document.getElementById('countriesPercentage').textContent = '0%';
        document.getElementById('registeredCountriesCount').textContent = '0';
        document.getElementById('totalCountriesCount').textContent = '0';
        document.getElementById('countriesProgressBar').style.width = '0%';
    }

    // Calculate cities coverage (target: 1000 cities)
    const registeredCitiesSet = new Set();
    registrations.forEach(reg => {
        if (reg.selected_cities_data && Array.isArray(reg.selected_cities_data)) {
            reg.selected_cities_data.forEach(city => {
                if (city.name && city.country) {
                    registeredCitiesSet.add(`${city.name}_${city.country}`);
                }
            });
        }
    });
    
    const totalCitiesTarget = 1000;
    const registeredCitiesCount = registeredCitiesSet.size;
    const citiesPercentage = Math.round((registeredCitiesCount / totalCitiesTarget) * 100);
    
    document.getElementById('citiesProgress').textContent = `${registeredCitiesCount} of ${totalCitiesTarget}`;
    document.getElementById('citiesPercentage').textContent = `${citiesPercentage}%`;
    document.getElementById('registeredCitiesCount').textContent = registeredCitiesCount.toLocaleString();
    document.getElementById('citiesProgressBar').style.width = `${citiesPercentage}%`;
}

// Setup auto-refresh
function setupAutoRefresh() {
    // Refresh every 2 minutes (120000ms)
    setInterval(() => {
        loadData();
    }, 120000);
}

// Update last updated time display
function updateLastUpdatedTime() {
    if (lastUpdateTime) {
        document.getElementById('lastUpdated').textContent = lastUpdateTime.toLocaleTimeString();
    }
}

// Update time display every 30 seconds
setInterval(updateLastUpdatedTime, 30000);

function updateCountryAnalysis(countries, registrations) {
    const registeredCountries = new Set();
    registrations.forEach(reg => {
        if (reg.selected_countries) {
            const selectedCountries = reg.selected_countries.split(',').map(c => c.trim().toLowerCase());
            selectedCountries.forEach(country => registeredCountries.add(country));
        }
    });

    const continentOrder = ['Africa', 'Asia', 'Europe', 'North America', 'South America', 'Oceania'];
    const coverageAnalysis = document.getElementById('coverage-analysis');
    
    let analysisHTML = `
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">Continental Coverage Analysis</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    `;
    
    // Group countries by continent
    const continentStats = {};
    countries.countries.forEach(country => {
        if (!continentStats[country.continent]) {
            continentStats[country.continent] = {
                total: 0,
                registered: 0,
                remaining: 0,
                countries: []
            };
        }
        continentStats[country.continent].total++;
        const countryKey = country.name.toLowerCase().replace(/\s+/g, '-');
        if (registeredCountries.has(countryKey)) {
            continentStats[country.continent].registered++;
        } else {
            continentStats[country.continent].remaining++;
        }
        continentStats[country.continent].countries.push({
            ...country,
            key: countryKey,
            isRegistered: registeredCountries.has(countryKey)
        });
    });
    
    continentOrder.forEach(continent => {
        const stats = continentStats[continent];
        if (!stats) return;
        
        const coverage = (stats.registered / stats.total * 100).toFixed(1);
        
        analysisHTML += `
            <div class="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden">
                <div class="p-4 border-b border-gray-200">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="text-md font-semibold text-gray-900">${continent}</h4>
                        <span class="px-2 py-1 text-sm rounded-full ${coverage >= 50 ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'}">
                            ${coverage}% Coverage
                        </span>
                    </div>
                    <div class="grid grid-cols-3 gap-4 text-sm">
                        <div>
                            <div class="text-gray-600">Total</div>
                            <div class="font-semibold">${stats.total}</div>
                        </div>
                        <div>
                            <div class="text-gray-600">Registered</div>
                            <div class="font-semibold text-green-600">${stats.registered}</div>
                        </div>
                        <div>
                            <div class="text-gray-600">Remaining</div>
                            <div class="font-semibold text-orange-600">${stats.remaining}</div>
                        </div>
                    </div>
                </div>
                <div class="p-4 bg-white max-h-48 overflow-y-auto">
                    <div class="grid grid-cols-1 gap-1">
                        ${stats.countries.map(country => `
                            <div class="flex items-center py-1 px-2 rounded ${country.isRegistered ? 'bg-green-50 text-green-800' : 'bg-orange-50 text-orange-800'} text-xs">
                                <img src="../assets/images/flags/${country.code.toLowerCase()}.svg" 
                                     alt="${country.name}" 
                                     class="w-4 h-3 object-cover rounded mr-2"
                                     onerror="this.style.display='none'">
                                <span class="truncate flex-1">${country.name}</span>
                                ${country.isRegistered ? '<span class="ml-1">✓</span>' : ''}
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
    });
    
    analysisHTML += '</div></div>';
    coverageAnalysis.innerHTML = analysisHTML;
}
</script>

<?php include 'includes/footer.php'; ?>