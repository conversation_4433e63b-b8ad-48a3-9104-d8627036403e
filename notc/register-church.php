<?php 
require_once '../includes/config.php';
require_once '../includes/languages.php';
require_once '../includes/TranslationService.php';
require_once 'includes/UpdateTokenManager.php';

// Initialize translation service
$translationService = TranslationService::getInstance();
$currentLanguage = Language::getCurrentLanguage();

// Check if this is an update request
$isUpdate = false;
$updateToken = $_GET['token'] ?? '';
$existingRegistration = null;

if (!empty($updateToken)) {
    $tokenManager = new UpdateTokenManager();
    $existingRegistration = $tokenManager->getRegistrationByToken($updateToken);
    
    if ($existingRegistration && $existingRegistration['registration_type'] === 'church') {
        $isUpdate = true;
        // Mark token as used for statistics
        $tokenManager->useToken($updateToken);
    } else {
        // Invalid token or wrong registration type
        header('Location: update-dashboard.php?token=' . urlencode($updateToken));
        exit();
    }
}

// Helper function to get field value (existing data or empty)
function getFieldValue($fieldName, $existingData = null) {
    if ($existingData && isset($existingData[$fieldName])) {
        return htmlspecialchars($existingData[$fieldName]);
    }
    return '';
}

// Helper function to check if radio/checkbox should be selected
function isSelected($fieldName, $value, $existingData = null) {
    if ($existingData && isset($existingData[$fieldName])) {
        if (is_array($existingData[$fieldName])) {
            return in_array($value, $existingData[$fieldName]) ? 'checked' : '';
        }
        return $existingData[$fieldName] === $value ? 'checked' : '';
    }
    return '';
}

include 'includes/header.php'; 
?>

<!-- Fixed Video Background -->
<div class="fixed inset-0 w-full h-full z-0">
    <video 
        autoplay 
        muted 
        loop 
        playsinline 
        class="absolute inset-0 w-full h-full object-cover"
        poster="https://rorcloud.org/serve.php?id=269&name=Night_of_thousand_crusades.jpg">
        <source src="https://rorcloud.org/serve.php?id=279&name=output(compress-video-online.com).mp4" type="video/mp4">
        Your browser does not support the video tag.
    </video>
    <!-- Dark Overlay for better text readability -->
    <div class="absolute inset-0 bg-black bg-opacity-80"></div>
    <!-- Additional gradient overlay for better text contrast -->
    <div class="absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-black/50"></div>
</div>

<!-- Registration Form Section -->
<section class="min-h-screen text-white relative z-10 overflow-x-hidden py-20">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative w-full">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl sm:text-5xl md:text-6xl font-bold mb-6 drop-shadow-lg">
                <?php 
                if ($isUpdate) {
                    if ($currentLanguage === 'en') {
                        echo 'Update Church/Group/Zone Registration';
                    } else {
                        echo $translationService->translateText('Update Church/Group/Zone Registration', 'en', $currentLanguage) ?? 'Update Church/Group/Zone Registration';
                    }
                } else {
                    if ($currentLanguage === 'en') {
                        echo 'Church/Group/Zone Registration';
                    } else {
                        echo $translationService->translateText('Church/Group/Zone Registration', 'en', $currentLanguage) ?? 'Church/Group/Zone Registration';
                    }
                }
                ?>
            </h1>
            <?php if ($isUpdate): ?>
                <div class="bg-blue-500/20 border border-blue-400/30 rounded-lg p-4 mb-6 max-w-2xl mx-auto">
                    <div class="flex items-center justify-center mb-2">
                        <svg class="w-6 h-6 text-blue-300 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        <span class="text-blue-200 font-semibold">Update Mode</span>
                    </div>
                    <p class="text-blue-100 text-sm text-center">
                        You are updating the registration for: <strong><?php echo htmlspecialchars($existingRegistration['display_name'] ?? 'Your Organization'); ?></strong>
                    </p>
                </div>
            <?php endif; ?>
            <p class="text-xl md:text-2xl drop-shadow-lg text-gray-100 max-w-3xl mx-auto">
                <?php 
                if ($isUpdate) {
                    if ($currentLanguage === 'en') {
                        echo 'Update your church, group, or zone registration details';
                    } else {
                        echo $translationService->translateText('Update your church, group, or zone registration details', 'en', $currentLanguage) ?? 'Update your church, group, or zone registration details';
                    }
                } else {
                    if ($currentLanguage === 'en') {
                        echo 'Register your church, group, or zone to participate in the global crusade movement';
                    } else {
                        echo $translationService->translateText('Register your church, group, or zone to participate in the global crusade movement', 'en', $currentLanguage) ?? 'Register your church, group, or zone to participate in the global crusade movement';
                    }
                }
                ?>
            </p>
            <div class="w-20 h-1 bg-accent mx-auto mt-6"></div>
        </div>

        <!-- Registration Form -->
        <div class="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-8 shadow-2xl">
            <form id="crusadeRegistrationForm" method="POST" action="register-church-handler.php" class="space-y-8">
                <?php if ($isUpdate): ?>
                    <input type="hidden" name="update_token" value="<?php echo htmlspecialchars($updateToken); ?>">
                    <input type="hidden" name="is_update" value="1">
                <?php endif; ?>
                <!-- Registration Type Selection -->
                <div class="space-y-4">
                    <h3 class="text-xl font-semibold text-white mb-4 flex items-center">
                        <div class="w-8 h-8 bg-accent rounded-full flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-gray-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                        <?php 
                        if ($currentLanguage === 'en') {
                            echo 'Registering as';
                        } else {
                            echo $translationService->translateText('Registering as', 'en', $currentLanguage) ?? 'Registering as';
                        }
                        ?> <span class="text-accent">*</span>
                    </h3>
                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <label class="flex items-center justify-center p-4 bg-white/10 border-2 border-white/20 rounded-xl cursor-pointer hover:border-accent transition-all duration-300 hover:bg-white/20">
                            <input type="radio" name="church_type" value="zone" required class="sr-only" onchange="handleChurchTypeSelection()" <?php echo isSelected('church_type', 'zone', $existingRegistration); ?>>
                            <span class="text-white font-medium">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Zone';
                                } else {
                                    echo $translationService->translateText('Zone', 'en', $currentLanguage) ?? 'Zone';
                                }
                                ?>
                            </span>
                        </label>
                        <label class="flex items-center justify-center p-4 bg-white/10 border-2 border-white/20 rounded-xl cursor-pointer hover:border-accent transition-all duration-300 hover:bg-white/20">
                            <input type="radio" name="church_type" value="group" required class="sr-only" onchange="handleChurchTypeSelection()" <?php echo isSelected('church_type', 'group', $existingRegistration); ?>>
                            <span class="text-white font-medium">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Group';
                                } else {
                                    echo $translationService->translateText('Group', 'en', $currentLanguage) ?? 'Group';
                                }
                                ?>
                            </span>
                        </label>
                        <label class="flex items-center justify-center p-4 bg-white/10 border-2 border-white/20 rounded-xl cursor-pointer hover:border-accent transition-all duration-300 hover:bg-white/20">
                            <input type="radio" name="church_type" value="church" required class="sr-only" onchange="handleChurchTypeSelection()" <?php echo isSelected('church_type', 'church', $existingRegistration); ?>>
                            <span class="text-white font-medium">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Church';
                                } else {
                                    echo $translationService->translateText('Church', 'en', $currentLanguage) ?? 'Church';
                                }
                                ?>
                            </span>
                        </label>
                    </div>
                </div>

                <!-- Zone Selection -->
                <div id="zoneSelectionContainer" class="space-y-4">
                    <label for="zoneSelect" class="block text-lg font-medium text-white">
                        <?php 
                        if ($currentLanguage === 'en') {
                            echo 'Select Zone';
                        } else {
                            echo $translationService->translateText('Select Zone', 'en', $currentLanguage) ?? 'Select Zone';
                        }
                        ?> <span class="text-accent">*</span>
                    </label>
                    <select id="zoneSelect" name="zone" required 
                            class="w-full px-4 py-4 bg-white/10 border-2 border-white/20 rounded-xl text-white placeholder-gray-300 focus:border-accent focus:ring-2 focus:ring-accent focus:ring-opacity-50 transition-all duration-300 backdrop-blur-sm">
                        <option value="" class="text-gray-900">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Select your zone';
                            } else {
                                echo $translationService->translateText('Select your zone', 'en', $currentLanguage) ?? 'Select your zone';
                            }
                            ?>
                        </option>
                        <?php
                        // Load zones from JSON file
                        $zonesJson = file_get_contents(__DIR__ . '/../world/zones.json');
                        $zonesData = json_decode($zonesJson, true);
                        
                        if ($zonesData && isset($zonesData['zones'])) {
                            foreach ($zonesData['zones'] as $zone) {
                                echo '<option value="' . htmlspecialchars($zone) . '" class="text-gray-900">' . htmlspecialchars($zone) . '</option>';
                            }
                        } else {
                            // Fallback zones if JSON fails to load
                            $fallbackZones = [
                                'Lagos Zone 1', 'Lagos Zone 2', 'Lagos Zone 3', 'Lagos Zone 4', 'Lagos Zone 5',
                                'Abuja Zone', 'Port Harcourt Zone', 'Kano Zone', 'Ibadan Zone', 'Benin Zone',
                                'Warri Zone', 'Kaduna Zone', 'Jos Zone', 'Calabar Zone', 'Uyo Zone'
                            ];
                            foreach ($fallbackZones as $zone) {
                                echo '<option value="' . htmlspecialchars($zone) . '" class="text-gray-900">' . htmlspecialchars($zone) . '</option>';
                            }
                        }
                        ?>
                    </select>
                </div>

                <!-- Church Fields (initially hidden) -->
                <div id="churchFields" class="space-y-6 hidden">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Group Name for Church -->
                        <div class="md:col-span-2">
                            <label for="churchGroupName" class="block text-lg font-medium text-white mb-2">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Group Name';
                                } else {
                                    echo $translationService->translateText('Group Name', 'en', $currentLanguage) ?? 'Group Name';
                                }
                                ?> <span class="text-accent">*</span>
                            </label>
                            <input type="text" id="churchGroupName" name="church_group_name" 
                                   value="<?php echo getFieldValue('church_group_name', $existingRegistration); ?>"
                                   class="w-full px-4 py-4 bg-white/10 border-2 border-white/20 rounded-xl text-white placeholder-gray-300 focus:border-accent focus:ring-2 focus:ring-accent focus:ring-opacity-50 transition-all duration-300 backdrop-blur-sm"
                                   placeholder="<?php 
                                   if ($currentLanguage === 'en') {
                                       echo 'Enter group name';
                                   } else {
                                       echo $translationService->translateText('Enter group name', 'en', $currentLanguage) ?? 'Enter group name';
                                   }
                                   ?>">
                        </div>
                        <!-- Church Name -->
                        <div class="md:col-span-2">
                            <label for="churchName" class="block text-lg font-medium text-white mb-2">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Church Name';
                                } else {
                                    echo $translationService->translateText('Church Name', 'en', $currentLanguage) ?? 'Church Name';
                                }
                                ?> <span class="text-accent">*</span>
                            </label>
                            <input type="text" id="churchName" name="church_name" 
                                   value="<?php echo getFieldValue('church_name', $existingRegistration); ?>"
                                   class="w-full px-4 py-4 bg-white/10 border-2 border-white/20 rounded-xl text-white placeholder-gray-300 focus:border-accent focus:ring-2 focus:ring-accent focus:ring-opacity-50 transition-all duration-300 backdrop-blur-sm"
                                   placeholder="<?php 
                                   if ($currentLanguage === 'en') {
                                       echo 'Enter church name';
                                   } else {
                                       echo $translationService->translateText('Enter church name', 'en', $currentLanguage) ?? 'Enter church name';
                                   }
                                   ?>">
                        </div>
                    </div>
                </div>

                <!-- Group Name (for non-church types) -->
                <div id="organizationNameField" class="space-y-4">
                    <label for="organizationName" class="block text-lg font-medium text-white">
                        <?php 
                        if ($currentLanguage === 'en') {
                            echo 'Group Name';
                        } else {
                            echo $translationService->translateText('Group Name', 'en', $currentLanguage) ?? 'Group Name';
                        }
                        ?> <span class="text-accent">*</span>
                    </label>
                    <input type="text" id="organizationName" name="organization_name" required 
                           value="<?php echo getFieldValue('organization_name', $existingRegistration); ?>"
                           class="w-full px-4 py-4 bg-white/10 border-2 border-white/20 rounded-xl text-white placeholder-gray-300 focus:border-accent focus:ring-2 focus:ring-accent focus:ring-opacity-50 transition-all duration-300 backdrop-blur-sm"
                           placeholder="<?php 
                           if ($currentLanguage === 'en') {
                               echo 'Enter your group name';
                           } else {
                               echo $translationService->translateText('Enter your group name', 'en', $currentLanguage) ?? 'Enter your group name';
                           }
                           ?>">
                </div>

                <!-- Countries Guidance Section -->
                <div class="mb-8 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center justify-between">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Select Your Crusade Locations';
                            } else {
                                echo $translationService->translateText('Select Your Crusade Locations', 'en', $currentLanguage) ?? 'Select Your Crusade Locations';
                            }
                            ?>
                        </div>
                        <div class="text-sm text-blue-600">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Click to select countries';
                            } else {
                                echo $translationService->translateText('Click to select countries', 'en', $currentLanguage) ?? 'Click to select countries';
                            }
                            ?>
                        </div>
                        </h3>
                    
                    <p class="text-gray-700 mb-4">
                        <?php 
                        if ($currentLanguage === 'en') {
                            echo 'Select the countries where your church/group/zone will be conducting crusades. Countries in <span class="text-orange-600 font-medium">orange</span> have no registered crusades yet - consider including these in your plans.';
                        } else {
                            echo $translationService->translateText('Select the countries where your church/group/zone will be conducting crusades. Countries in <span class="text-orange-600 font-medium">orange</span> have no registered crusades yet - consider including these in your plans.', 'en', $currentLanguage) ?? 'Select the countries where your church/group/zone will be conducting crusades. Countries in <span class="text-orange-600 font-medium">orange</span> have no registered crusades yet - consider including these in your plans.';
                        }
                        ?>
                    </p>
                    
                    <div id="countriesGuide" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div class="text-center text-gray-500 py-8">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Loading country coverage information...';
                            } else {
                                echo $translationService->translateText('Loading country coverage information...', 'en', $currentLanguage) ?? 'Loading country coverage information...';
                            }
                            ?>
                        </div>
                    </div>
                    
                    <div class="mt-4 flex flex-wrap gap-4 text-sm">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-green-100 border border-green-300 rounded mr-2"></div>
                            <span class="text-gray-600">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Has registered crusades';
                                } else {
                                    echo $translationService->translateText('Has registered crusades', 'en', $currentLanguage) ?? 'Has registered crusades';
                                }
                                ?>
                            </span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-orange-100 border border-orange-300 rounded mr-2"></div>
                            <span class="text-gray-600">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'No registered crusades yet';
                                } else {
                                    echo $translationService->translateText('No registered crusades yet', 'en', $currentLanguage) ?? 'No registered crusades yet';
                                }
                                ?>
                            </span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-blue-100 border border-blue-300 rounded mr-2"></div>
                            <span class="text-gray-600">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Selected for your crusades';
                                } else {
                                    echo $translationService->translateText('Selected for your crusades', 'en', $currentLanguage) ?? 'Selected for your crusades';
                                }
                                ?>
                            </span>
                        </div>
                        </div>
                    </div>
                    
                <!-- Selected Countries Display -->
                <div class="mb-6">
                    <div class="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-t-lg p-3">
                        <label class="block text-lg font-semibold text-white mb-0">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Selected Countries/Regions';
                            } else {
                                echo $translationService->translateText('Selected Countries/Regions', 'en', $currentLanguage) ?? 'Selected Countries/Regions';
                            }
                            ?> <span class="text-red-200 font-bold">*</span>
                        </label>
                            </div>
                    <div id="selected-countries" class="min-h-[3rem] flex flex-wrap gap-2 p-3 bg-white border border-gray-300 border-t-0 rounded-b-lg">
                        <span id="country-placeholder" class="text-gray-500">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Click countries in the guide above to select them';
                            } else {
                                echo $translationService->translateText('Click countries in the guide above to select them', 'en', $currentLanguage) ?? 'Click countries in the guide above to select them';
                            }
                            ?>
                        </span>
                        </div>
                    <input type="hidden" name="selected_countries" id="selected_countries" required>
                    <p class="mt-1 text-sm text-gray-500">
                        <?php 
                        if ($currentLanguage === 'en') {
                            echo 'Selected countries where your church/group/zone plans to conduct crusades';
                        } else {
                            echo $translationService->translateText('Selected countries where your church/group/zone plans to conduct crusades', 'en', $currentLanguage) ?? 'Selected countries where your church/group/zone plans to conduct crusades';
                        }
                        ?>
                    </p>
                </div>

                <!-- Cities Selection -->
                <div class="mb-6">
                    <div class="bg-gradient-to-r from-green-600 to-emerald-600 rounded-t-lg p-3">
                        <label for="crusade_cities" class="block text-lg font-semibold text-white mb-0">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Target Cities';
                            } else {
                                echo $translationService->translateText('Target Cities', 'en', $currentLanguage) ?? 'Target Cities';
                            }
                            ?> <span class="text-red-200 font-bold">*</span>
                        </label>
                    </div>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <input type="text" 
                               id="crusade_cities" 
                               name="crusade_cities" 
                               class="w-full pl-10 pr-4 py-4 bg-white border border-gray-300 border-t-0 rounded-b-lg text-gray-900 placeholder-gray-500 focus:border-green-500 focus:ring-2 focus:ring-green-500 focus:ring-opacity-50 transition-all duration-300"
                               placeholder="<?php 
                               if ($currentLanguage === 'en') {
                                   echo 'Search for cities... (e.g., Lagos, Accra, Nairobi)';
                               } else {
                                   echo $translationService->translateText('Search for cities... (e.g., Lagos, Accra, Nairobi)', 'en', $currentLanguage) ?? 'Search for cities... (e.g., Lagos, Accra, Nairobi)';
                               }
                               ?>"
                               autocomplete="off">
                        
                        <!-- Cities Autocomplete Dropdown -->
                        <div id="cities-autocomplete" class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto hidden">
                            <div id="cities-suggestions" class="p-2">
                                <!-- Suggestions will be populated here -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- Selected Cities Display -->
                    <div id="selected-cities" class="min-h-[2rem] flex flex-wrap gap-2 p-3 bg-gray-50 border border-gray-300 rounded-b-lg mt-2">
                        <span id="cities-placeholder" class="text-gray-500">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Please select at least one city (required)';
                            } else {
                                echo $translationService->translateText('Please select at least one city (required)', 'en', $currentLanguage) ?? 'Please select at least one city (required)';
                            }
                            ?>
                        </span>
                    </div>
                    
                    <input type="hidden" name="selected_cities_data" id="selected_cities_data" required>
                    
                    <p class="mt-2 text-sm text-gray-600">
                        <?php 
                        if ($currentLanguage === 'en') {
                            echo 'Type to search for cities and select from the suggestions. You must select at least one city. Cities will be automatically matched to their countries for better planning.';
                        } else {
                            echo $translationService->translateText('Type to search for cities and select from the suggestions. You must select at least one city. Cities will be automatically matched to their countries for better planning.', 'en', $currentLanguage) ?? 'Type to search for cities and select from the suggestions. You must select at least one city. Cities will be automatically matched to their countries for better planning.';
                        }
                        ?>
                    </p>
                </div>

                <!-- Crusade Types Section -->
                <div class="space-y-4">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-accent rounded-full flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-gray-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2zm8 0h-2a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-white">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Crusade Type(s)';
                            } else {
                                echo $translationService->translateText('Crusade Type(s)', 'en', $currentLanguage) ?? 'Crusade Type(s)';
                            }
                            ?> <span class="text-accent">*</span>
                        </h3>
                    </div>
                    
                    <div class="relative">
                        <select id="churchCrusadeTypes" name="crusade_types[]" multiple required 
                                class="w-full px-4 py-3 bg-white/10 border-2 border-white/20 rounded-xl text-white focus:border-accent focus:ring-2 focus:ring-accent focus:ring-opacity-50 transition-all duration-300 backdrop-blur-sm"
                                style="display: none;">
                            <option value="mega">Mega Crusades (10,000+ people)</option>
                            <option value="tap2read">TAP2READ Crusades - promoting TAP2READ app with evangelism</option>
                            <option value="youths-aglow">Youths Aglow Crusades</option>
                            <option value="teevolution">Teevolution Crusades (Teens-focused)</option>
                            <option value="say-yes-to-kids">Say Yes To Kids Crusades</option>
                            <option value="nolb">No One Left Behind Crusades (for the visually/hearing impaired)</option>
                            <option value="leading-ladies">Leading Ladies Crusades</option>
                            <option value="mighty-men">Mighty Men Crusades</option>
                            <option value="professionals">Specialized Crusades to Professionals</option>
                            <option value="tv">TV Crusades</option>
                            <option value="social-media">Social Media Crusades</option>
                            <option value="online">Online Crusades - targeting digital audiences</option>
                            <option value="mystreamspace">MyStreamSpace Crusades</option>
                            <option value="mall">Mall Crusades</option>
                            <option value="school">School Crusades</option>
                            <option value="hospital">Hospital Crusades</option>
                            <option value="street">Street Crusades</option>
                            <option value="prison">Prison Crusades</option>
                            <option value="other">Other (please specify)</option>
                        </select>
                        
                        <!-- Custom Dropdown Interface -->
                        <div class="church-crusade-types-dropdown">
                            <div class="church-crusade-types-selected w-full px-4 py-3 bg-white/10 border-2 border-white/20 rounded-xl focus:border-accent focus:ring-2 focus:ring-accent focus:ring-opacity-50 transition-all duration-300 backdrop-blur-sm cursor-pointer min-h-[3rem] flex items-center justify-between">
                                <span class="church-crusade-types-placeholder text-gray-300">
                                    <?php 
                                    if ($currentLanguage === 'en') {
                                        echo 'Select crusade type(s) - You can select multiple';
                                    } else {
                                        echo $translationService->translateText("Select crusade type(s) - You can select multiple", "en", $currentLanguage) ?? "Select crusade type(s) - You can select multiple";
                                    }
                                    ?>
                                </span>
                                <svg class="w-5 h-5 text-gray-400 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                            <div class="church-crusade-types-options absolute z-50 w-full mt-1 bg-white/95 backdrop-blur-md border-2 border-white/20 rounded-xl shadow-lg max-h-60 overflow-y-auto hidden">
                                <div class="p-2">
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" value="mega" class="church-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">Mega Crusades (10,000+ people)</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" value="tap2read" class="church-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">TAP2READ Crusades - promoting TAP2READ app with evangelism</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" value="youths-aglow" class="church-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">Youths Aglow Crusades</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" value="teevolution" class="church-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">Teevolution Crusades (Teens-focused)</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" value="say-yes-to-kids" class="church-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">Say Yes To Kids Crusades</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" value="nolb" class="church-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">No One Left Behind Crusades (for the visually/hearing impaired)</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" value="leading-ladies" class="church-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">Leading Ladies Crusades</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" value="mighty-men" class="church-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">Mighty Men Crusades</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" value="professionals" class="church-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">Specialized Crusades to Professionals</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" value="tv" class="church-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">TV Crusades</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" value="social-media" class="church-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">Social Media Crusades</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" value="online" class="church-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">Online Crusades - targeting digital audiences</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" value="mystreamspace" class="church-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">MyStreamSpace Crusades</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" value="mall" class="church-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">Mall Crusades</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" value="school" class="church-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">School Crusades</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" value="hospital" class="church-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">Hospital Crusades</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" value="street" class="church-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">Street Crusades</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" value="prison" class="church-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">Prison Crusades</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" value="other" class="church-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">Other (please specify)</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Other Crusade Types Field -->
                    <div id="churchOtherCrusadeTypesField" class="space-y-4 hidden">
                        <label for="churchOtherCrusadeTypes" class="block text-lg font-medium text-white">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Please specify other crusade type(s)';
                            } else {
                                echo $translationService->translateText('Please specify other crusade type(s)', 'en', $currentLanguage) ?? 'Please specify other crusade type(s)';
                            }
                            ?> <span class="text-accent">*</span>
                        </label>
                        <input type="text" id="churchOtherCrusadeTypes" name="other_crusade_types" 
                               value="<?php echo getFieldValue('other_crusade_types', $existingRegistration); ?>"
                               class="w-full px-4 py-4 bg-white/10 border-2 border-white/20 rounded-xl text-white placeholder-gray-300 focus:border-accent focus:ring-2 focus:ring-accent focus:ring-opacity-50 transition-all duration-300 backdrop-blur-sm"
                               placeholder="<?php 
                               if ($currentLanguage === 'en') {
                                   echo 'Please describe your other crusade type(s)';
                               } else {
                                   echo $translationService->translateText('Please describe your other crusade type(s)', 'en', $currentLanguage) ?? 'Please describe your other crusade type(s)';
                               }
                               ?>">
                    </div>
                </div>

                <!-- Number of Crusades Section -->
                <div class="space-y-4">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-accent rounded-full flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-gray-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-white">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Number of Crusades';
                            } else {
                                echo $translationService->translateText('Number of Crusades', 'en', $currentLanguage) ?? 'Number of Crusades';
                            }
                            ?>
                        </h3>
                    </div>
                    
                    <label for="numberOfCrusades" class="block text-lg font-medium text-white">
                        <?php 
                        if ($currentLanguage === 'en') {
                            echo 'Number of Crusades';
                        } else {
                            echo $translationService->translateText('Number of Crusades', 'en', $currentLanguage) ?? 'Number of Crusades';
                        }
                        ?> <span class="text-accent">*</span>
                    </label>
                    <input type="number" id="numberOfCrusades" name="number_of_crusades" required min="1" max="1000"
                           value="<?php echo getFieldValue('number_of_crusades', $existingRegistration); ?>"
                           class="w-full px-4 py-4 bg-white/10 border-2 border-white/20 rounded-xl text-white placeholder-gray-300 focus:border-accent focus:ring-2 focus:ring-accent focus:ring-opacity-50 transition-all duration-300 backdrop-blur-sm"
                           placeholder="<?php 
                           if ($currentLanguage === 'en') {
                               echo 'Enter number of crusades';
                           } else {
                               echo $translationService->translateText('Enter number of crusades', 'en', $currentLanguage) ?? 'Enter number of crusades';
                           }
                           ?>">
                </div>

                <!-- Expected Attendance Section -->
                <div class="space-y-4">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-accent rounded-full flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-gray-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-white">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Expected Attendance';
                            } else {
                                echo $translationService->translateText('Expected Attendance', 'en', $currentLanguage) ?? 'Expected Attendance';
                            }
                            ?>
                        </h3>
                    </div>
                    
                    <label for="expectedAttendance" class="block text-lg font-medium text-white">
                        <?php 
                        if ($currentLanguage === 'en') {
                            echo 'Expected Total Attendance';
                        } else {
                            echo $translationService->translateText('Expected Total Attendance', 'en', $currentLanguage) ?? 'Expected Total Attendance';
                        }
                        ?> <span class="text-accent">*</span>
                    </label>
                    <select id="expectedAttendance" name="expected_attendance" required 
                            class="w-full px-4 py-4 bg-white/10 border-2 border-white/20 rounded-xl text-white focus:border-accent focus:ring-2 focus:ring-accent focus:ring-opacity-50 transition-all duration-300 backdrop-blur-sm">
                        <option value="" class="text-gray-900">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Select expected attendance';
                            } else {
                                echo $translationService->translateText('Select expected attendance', 'en', $currentLanguage) ?? 'Select expected attendance';
                            }
                            ?>
                        </option>
                        <option value="50-100" class="text-gray-900">50 - 100</option>
                        <option value="100-250" class="text-gray-900">100 - 250</option>
                        <option value="250-500" class="text-gray-900">250 - 500</option>
                        <option value="500-1000" class="text-gray-900">500 - 1,000</option>
                        <option value="1000-2500" class="text-gray-900">1,000 - 2,500</option>
                        <option value="2500-5000" class="text-gray-900">2,500 - 5,000</option>
                        <option value="5000-10000" class="text-gray-900">5,000 - 10,000</option>
                        <option value="10000+" class="text-gray-900">10,000+</option>
                        <option value="unsure" class="text-gray-900">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Unsure';
                            } else {
                                echo $translationService->translateText('Unsure', 'en', $currentLanguage) ?? 'Unsure';
                            }
                            ?>
                        </option>
                    </select>
                </div>

                <!-- Contact Information Section -->
                <div class="space-y-6">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-accent rounded-full flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-gray-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-white">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Contact Information';
                            } else {
                                echo $translationService->translateText('Contact Information', 'en', $currentLanguage) ?? 'Contact Information';
                            }
                            ?>
                        </h3>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <label for="contactFirstName" class="block text-lg font-medium text-white">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'First Name';
                                } else {
                                    echo $translationService->translateText('First Name', 'en', $currentLanguage) ?? 'First Name';
                                }
                                ?> <span class="text-accent">*</span>
                            </label>
                            <input type="text" id="contactFirstName" name="first_name" required 
                                   value="<?php echo getFieldValue('first_name', $existingRegistration); ?>"
                                   class="w-full px-4 py-4 bg-white/10 border-2 border-white/20 rounded-xl text-white placeholder-gray-300 focus:border-accent focus:ring-2 focus:ring-accent focus:ring-opacity-50 transition-all duration-300 backdrop-blur-sm"
                                   placeholder="<?php 
                                   if ($currentLanguage === 'en') {
                                       echo 'Enter your first name';
                                   } else {
                                       echo $translationService->translateText('Enter your first name', 'en', $currentLanguage) ?? 'Enter your first name';
                                   }
                                   ?>">
                        </div>
                        
                        <div class="space-y-4">
                            <label for="contactLastName" class="block text-lg font-medium text-white">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Last Name';
                                } else {
                                    echo $translationService->translateText('Last Name', 'en', $currentLanguage) ?? 'Last Name';
                                }
                                ?> <span class="text-accent">*</span>
                            </label>
                            <input type="text" id="contactLastName" name="last_name" required 
                                   value="<?php echo getFieldValue('last_name', $existingRegistration); ?>"
                                   class="w-full px-4 py-4 bg-white/10 border-2 border-white/20 rounded-xl text-white placeholder-gray-300 focus:border-accent focus:ring-2 focus:ring-accent focus:ring-opacity-50 transition-all duration-300 backdrop-blur-sm"
                                   placeholder="<?php 
                                   if ($currentLanguage === 'en') {
                                       echo 'Enter your last name';
                                   } else {
                                       echo $translationService->translateText('Enter your last name', 'en', $currentLanguage) ?? 'Enter your last name';
                                   }
                                   ?>">
                        </div>
                        
                        <div class="space-y-4">
                            <label for="contactEmail" class="block text-lg font-medium text-white">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Email Address';
                                } else {
                                    echo $translationService->translateText('Email Address', 'en', $currentLanguage) ?? 'Email Address';
                                }
                                ?> <span class="text-accent">*</span>
                            </label>
                            <input type="email" id="contactEmail" name="email" required 
                                   value="<?php echo getFieldValue('email', $existingRegistration); ?>"
                                   class="w-full px-4 py-4 bg-white/10 border-2 border-white/20 rounded-xl text-white placeholder-gray-300 focus:border-accent focus:ring-2 focus:ring-accent focus:ring-opacity-50 transition-all duration-300 backdrop-blur-sm"
                                   placeholder="<?php 
                                   if ($currentLanguage === 'en') {
                                       echo '<EMAIL>';
                                   } else {
                                       echo $translationService->translateText('<EMAIL>', 'en', $currentLanguage) ?? '<EMAIL>';
                                   }
                                   ?>">
                        </div>
                        
                        <div class="space-y-4">
                            <label for="contactPhone" class="block text-lg font-medium text-white">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Phone Number';
                                } else {
                                    echo $translationService->translateText('Phone Number', 'en', $currentLanguage) ?? 'Phone Number';
                                }
                                ?> <span class="text-accent">*</span>
                            </label>
                            <div class="flex">
                                <select id="phoneCountryCode" name="phone_country_code" required 
                                        class="px-3 py-4 bg-white/10 border-2 border-white/20 border-r-0 rounded-l-xl text-white focus:border-accent focus:ring-2 focus:ring-accent focus:ring-opacity-50 transition-all duration-300 backdrop-blur-sm">
                                    <option value="+1" class="text-gray-900">+1 (US/CA)</option>
                                    <option value="+7" class="text-gray-900">+7 (RU/KZ)</option>
                                    <option value="+20" class="text-gray-900">+20 (EG)</option>
                                    <option value="+27" class="text-gray-900">+27 (ZA)</option>
                                    <option value="+30" class="text-gray-900">+30 (GR)</option>
                                    <option value="+31" class="text-gray-900">+31 (NL)</option>
                                    <option value="+32" class="text-gray-900">+32 (BE)</option>
                                    <option value="+33" class="text-gray-900">+33 (FR)</option>
                                    <option value="+34" class="text-gray-900">+34 (ES)</option>
                                    <option value="+36" class="text-gray-900">+36 (HU)</option>
                                    <option value="+39" class="text-gray-900">+39 (IT)</option>
                                    <option value="+40" class="text-gray-900">+40 (RO)</option>
                                    <option value="+41" class="text-gray-900">+41 (CH)</option>
                                    <option value="+43" class="text-gray-900">+43 (AT)</option>
                                    <option value="+44" class="text-gray-900">+44 (GB)</option>
                                    <option value="+45" class="text-gray-900">+45 (DK)</option>
                                    <option value="+46" class="text-gray-900">+46 (SE)</option>
                                    <option value="+47" class="text-gray-900">+47 (NO)</option>
                                    <option value="+48" class="text-gray-900">+48 (PL)</option>
                                    <option value="+49" class="text-gray-900">+49 (DE)</option>
                                    <option value="+51" class="text-gray-900">+51 (PE)</option>
                                    <option value="+52" class="text-gray-900">+52 (MX)</option>
                                    <option value="+53" class="text-gray-900">+53 (CU)</option>
                                    <option value="+54" class="text-gray-900">+54 (AR)</option>
                                    <option value="+55" class="text-gray-900">+55 (BR)</option>
                                    <option value="+56" class="text-gray-900">+56 (CL)</option>
                                    <option value="+57" class="text-gray-900">+57 (CO)</option>
                                    <option value="+58" class="text-gray-900">+58 (VE)</option>
                                    <option value="+60" class="text-gray-900">+60 (MY)</option>
                                    <option value="+61" class="text-gray-900">+61 (AU)</option>
                                    <option value="+62" class="text-gray-900">+62 (ID)</option>
                                    <option value="+63" class="text-gray-900">+63 (PH)</option>
                                    <option value="+64" class="text-gray-900">+64 (NZ)</option>
                                    <option value="+65" class="text-gray-900">+65 (SG)</option>
                                    <option value="+66" class="text-gray-900">+66 (TH)</option>
                                    <option value="+81" class="text-gray-900">+81 (JP)</option>
                                    <option value="+82" class="text-gray-900">+82 (KR)</option>
                                    <option value="+84" class="text-gray-900">+84 (VN)</option>
                                    <option value="+86" class="text-gray-900">+86 (CN)</option>
                                    <option value="+90" class="text-gray-900">+90 (TR)</option>
                                    <option value="+91" class="text-gray-900">+91 (IN)</option>
                                    <option value="+92" class="text-gray-900">+92 (PK)</option>
                                    <option value="+93" class="text-gray-900">+93 (AF)</option>
                                    <option value="+94" class="text-gray-900">+94 (LK)</option>
                                    <option value="+95" class="text-gray-900">+95 (MM)</option>
                                    <option value="+98" class="text-gray-900">+98 (IR)</option>
                                    <option value="+212" class="text-gray-900">+212 (MA)</option>
                                    <option value="+213" class="text-gray-900">+213 (DZ)</option>
                                    <option value="+216" class="text-gray-900">+216 (TN)</option>
                                    <option value="+218" class="text-gray-900">+218 (LY)</option>
                                    <option value="+220" class="text-gray-900">+220 (GM)</option>
                                    <option value="+221" class="text-gray-900">+221 (SN)</option>
                                    <option value="+222" class="text-gray-900">+222 (MR)</option>
                                    <option value="+223" class="text-gray-900">+223 (ML)</option>
                                    <option value="+224" class="text-gray-900">+224 (GN)</option>
                                    <option value="+225" class="text-gray-900">+225 (CI)</option>
                                    <option value="+226" class="text-gray-900">+226 (BF)</option>
                                    <option value="+227" class="text-gray-900">+227 (NE)</option>
                                    <option value="+228" class="text-gray-900">+228 (TG)</option>
                                    <option value="+229" class="text-gray-900">+229 (BJ)</option>
                                    <option value="+230" class="text-gray-900">+230 (MU)</option>
                                    <option value="+231" class="text-gray-900">+231 (LR)</option>
                                    <option value="+232" class="text-gray-900">+232 (SL)</option>
                                    <option value="+233" class="text-gray-900">+233 (GH)</option>
                                    <option value="+234" class="text-gray-900">+234 (NG)</option>
                                    <option value="+235" class="text-gray-900">+235 (TD)</option>
                                    <option value="+236" class="text-gray-900">+236 (CF)</option>
                                    <option value="+237" class="text-gray-900">+237 (CM)</option>
                                    <option value="+238" class="text-gray-900">+238 (CV)</option>
                                    <option value="+239" class="text-gray-900">+239 (ST)</option>
                                    <option value="+240" class="text-gray-900">+240 (GQ)</option>
                                    <option value="+241" class="text-gray-900">+241 (GA)</option>
                                    <option value="+242" class="text-gray-900">+242 (CG)</option>
                                    <option value="+243" class="text-gray-900">+243 (CD)</option>
                                    <option value="+244" class="text-gray-900">+244 (AO)</option>
                                    <option value="+245" class="text-gray-900">+245 (GW)</option>
                                    <option value="+246" class="text-gray-900">+246 (IO)</option>
                                    <option value="+247" class="text-gray-900">+247 (AC)</option>
                                    <option value="+248" class="text-gray-900">+248 (SC)</option>
                                    <option value="+249" class="text-gray-900">+249 (SD)</option>
                                    <option value="+250" class="text-gray-900">+250 (RW)</option>
                                    <option value="+251" class="text-gray-900">+251 (ET)</option>
                                    <option value="+252" class="text-gray-900">+252 (SO)</option>
                                    <option value="+253" class="text-gray-900">+253 (DJ)</option>
                                    <option value="+254" class="text-gray-900">+254 (KE)</option>
                                    <option value="+255" class="text-gray-900">+255 (TZ)</option>
                                    <option value="+256" class="text-gray-900">+256 (UG)</option>
                                    <option value="+257" class="text-gray-900">+257 (BI)</option>
                                    <option value="+258" class="text-gray-900">+258 (MZ)</option>
                                    <option value="+260" class="text-gray-900">+260 (ZM)</option>
                                    <option value="+261" class="text-gray-900">+261 (MG)</option>
                                    <option value="+262" class="text-gray-900">+262 (RE)</option>
                                    <option value="+263" class="text-gray-900">+263 (ZW)</option>
                                    <option value="+264" class="text-gray-900">+264 (NA)</option>
                                    <option value="+265" class="text-gray-900">+265 (MW)</option>
                                    <option value="+266" class="text-gray-900">+266 (LS)</option>
                                    <option value="+267" class="text-gray-900">+267 (BW)</option>
                                    <option value="+268" class="text-gray-900">+268 (SZ)</option>
                                    <option value="+269" class="text-gray-900">+269 (KM)</option>
                                    <option value="+290" class="text-gray-900">+290 (SH)</option>
                                    <option value="+291" class="text-gray-900">+291 (ER)</option>
                                    <option value="+297" class="text-gray-900">+297 (AW)</option>
                                    <option value="+298" class="text-gray-900">+298 (FO)</option>
                                    <option value="+299" class="text-gray-900">+299 (GL)</option>
                                    <option value="+350" class="text-gray-900">+350 (GI)</option>
                                    <option value="+351" class="text-gray-900">+351 (PT)</option>
                                    <option value="+352" class="text-gray-900">+352 (LU)</option>
                                    <option value="+353" class="text-gray-900">+353 (IE)</option>
                                    <option value="+354" class="text-gray-900">+354 (IS)</option>
                                    <option value="+355" class="text-gray-900">+355 (AL)</option>
                                    <option value="+356" class="text-gray-900">+356 (MT)</option>
                                    <option value="+357" class="text-gray-900">+357 (CY)</option>
                                    <option value="+358" class="text-gray-900">+358 (FI)</option>
                                    <option value="+359" class="text-gray-900">+359 (BG)</option>
                                    <option value="+370" class="text-gray-900">+370 (LT)</option>
                                    <option value="+371" class="text-gray-900">+371 (LV)</option>
                                    <option value="+372" class="text-gray-900">+372 (EE)</option>
                                    <option value="+373" class="text-gray-900">+373 (MD)</option>
                                    <option value="+374" class="text-gray-900">+374 (AM)</option>
                                    <option value="+375" class="text-gray-900">+375 (BY)</option>
                                    <option value="+376" class="text-gray-900">+376 (AD)</option>
                                    <option value="+377" class="text-gray-900">+377 (MC)</option>
                                    <option value="+378" class="text-gray-900">+378 (SM)</option>
                                    <option value="+380" class="text-gray-900">+380 (UA)</option>
                                    <option value="+381" class="text-gray-900">+381 (RS)</option>
                                    <option value="+382" class="text-gray-900">+382 (ME)</option>
                                    <option value="+383" class="text-gray-900">+383 (XK)</option>
                                    <option value="+385" class="text-gray-900">+385 (HR)</option>
                                    <option value="+386" class="text-gray-900">+386 (SI)</option>
                                    <option value="+387" class="text-gray-900">+387 (BA)</option>
                                    <option value="+389" class="text-gray-900">+389 (MK)</option>
                                    <option value="+420" class="text-gray-900">+420 (CZ)</option>
                                    <option value="+421" class="text-gray-900">+421 (SK)</option>
                                    <option value="+423" class="text-gray-900">+423 (LI)</option>
                                    <option value="+500" class="text-gray-900">+500 (FK)</option>
                                    <option value="+501" class="text-gray-900">+501 (BZ)</option>
                                    <option value="+502" class="text-gray-900">+502 (GT)</option>
                                    <option value="+503" class="text-gray-900">+503 (SV)</option>
                                    <option value="+504" class="text-gray-900">+504 (HN)</option>
                                    <option value="+505" class="text-gray-900">+505 (NI)</option>
                                    <option value="+506" class="text-gray-900">+506 (CR)</option>
                                    <option value="+507" class="text-gray-900">+507 (PA)</option>
                                    <option value="+508" class="text-gray-900">+508 (PM)</option>
                                    <option value="+509" class="text-gray-900">+509 (HT)</option>
                                    <option value="+590" class="text-gray-900">+590 (GP)</option>
                                    <option value="+591" class="text-gray-900">+591 (BO)</option>
                                    <option value="+592" class="text-gray-900">+592 (GY)</option>
                                    <option value="+593" class="text-gray-900">+593 (EC)</option>
                                    <option value="+594" class="text-gray-900">+594 (GF)</option>
                                    <option value="+595" class="text-gray-900">+595 (PY)</option>
                                    <option value="+596" class="text-gray-900">+596 (MQ)</option>
                                    <option value="+597" class="text-gray-900">+597 (SR)</option>
                                    <option value="+598" class="text-gray-900">+598 (UY)</option>
                                    <option value="+599" class="text-gray-900">+599 (CW)</option>
                                    <option value="+670" class="text-gray-900">+670 (TL)</option>
                                    <option value="+672" class="text-gray-900">+672 (NF)</option>
                                    <option value="+673" class="text-gray-900">+673 (BN)</option>
                                    <option value="+674" class="text-gray-900">+674 (NR)</option>
                                    <option value="+675" class="text-gray-900">+675 (PG)</option>
                                    <option value="+676" class="text-gray-900">+676 (TO)</option>
                                    <option value="+677" class="text-gray-900">+677 (SB)</option>
                                    <option value="+678" class="text-gray-900">+678 (VU)</option>
                                    <option value="+679" class="text-gray-900">+679 (FJ)</option>
                                    <option value="+680" class="text-gray-900">+680 (PW)</option>
                                    <option value="+681" class="text-gray-900">+681 (WF)</option>
                                    <option value="+682" class="text-gray-900">+682 (CK)</option>
                                    <option value="+683" class="text-gray-900">+683 (NU)</option>
                                    <option value="+684" class="text-gray-900">+684 (AS)</option>
                                    <option value="+685" class="text-gray-900">+685 (WS)</option>
                                    <option value="+686" class="text-gray-900">+686 (KI)</option>
                                    <option value="+687" class="text-gray-900">+687 (NC)</option>
                                    <option value="+688" class="text-gray-900">+688 (TV)</option>
                                    <option value="+689" class="text-gray-900">+689 (PF)</option>
                                    <option value="+690" class="text-gray-900">+690 (TK)</option>
                                    <option value="+691" class="text-gray-900">+691 (FM)</option>
                                    <option value="+692" class="text-gray-900">+692 (MH)</option>
                                    <option value="+850" class="text-gray-900">+850 (KP)</option>
                                    <option value="+852" class="text-gray-900">+852 (HK)</option>
                                    <option value="+853" class="text-gray-900">+853 (MO)</option>
                                    <option value="+855" class="text-gray-900">+855 (KH)</option>
                                    <option value="+856" class="text-gray-900">+856 (LA)</option>
                                    <option value="+880" class="text-gray-900">+880 (BD)</option>
                                    <option value="+886" class="text-gray-900">+886 (TW)</option>
                                    <option value="+960" class="text-gray-900">+960 (MV)</option>
                                    <option value="+961" class="text-gray-900">+961 (LB)</option>
                                    <option value="+962" class="text-gray-900">+962 (JO)</option>
                                    <option value="+963" class="text-gray-900">+963 (SY)</option>
                                    <option value="+964" class="text-gray-900">+964 (IQ)</option>
                                    <option value="+965" class="text-gray-900">+965 (KW)</option>
                                    <option value="+966" class="text-gray-900">+966 (SA)</option>
                                    <option value="+967" class="text-gray-900">+967 (YE)</option>
                                    <option value="+968" class="text-gray-900">+968 (OM)</option>
                                    <option value="+970" class="text-gray-900">+970 (PS)</option>
                                    <option value="+971" class="text-gray-900">+971 (AE)</option>
                                    <option value="+972" class="text-gray-900">+972 (IL)</option>
                                    <option value="+973" class="text-gray-900">+973 (BH)</option>
                                    <option value="+974" class="text-gray-900">+974 (QA)</option>
                                    <option value="+975" class="text-gray-900">+975 (BT)</option>
                                    <option value="+976" class="text-gray-900">+976 (MN)</option>
                                    <option value="+977" class="text-gray-900">+977 (NP)</option>
                                    <option value="+992" class="text-gray-900">+992 (TJ)</option>
                                    <option value="+993" class="text-gray-900">+993 (TM)</option>
                                    <option value="+994" class="text-gray-900">+994 (AZ)</option>
                                    <option value="+995" class="text-gray-900">+995 (GE)</option>
                                    <option value="+996" class="text-gray-900">+996 (KG)</option>
                                    <option value="+998" class="text-gray-900">+998 (UZ)</option>
                                </select>
                                <input type="tel" id="contactPhone" name="phone" required 
                                       value="<?php echo getFieldValue('phone', $existingRegistration); ?>"
                                       class="flex-1 px-4 py-4 bg-white/10 border-2 border-white/20 border-l-0 rounded-r-xl text-white placeholder-gray-300 focus:border-accent focus:ring-2 focus:ring-accent focus:ring-opacity-50 transition-all duration-300 backdrop-blur-sm"
                                       placeholder="<?php 
                                       if ($currentLanguage === 'en') {
                                           echo 'Phone number';
                                       } else {
                                           echo $translationService->translateText('Phone number', 'en', $currentLanguage) ?? 'Phone number';
                                       }
                                       ?>">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Information Section -->
                <div class="space-y-4">
                <!-- Additional Information Section -->
                <div class="space-y-4">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-white">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Additional Information';
                            } else {
                                echo $translationService->translateText('Additional Information', 'en', $currentLanguage) ?? 'Additional Information';
                            }
                            ?>
                        </h3>
                    </div>
                    
                    <label for="additionalComments" class="block text-lg font-medium text-white">
                        <?php 
                        if ($currentLanguage === 'en') {
                            echo 'Additional Comments or Special Requests';
                        } else {
                            echo $translationService->translateText('Additional Comments or Special Requests', 'en', $currentLanguage) ?? 'Additional Comments or Special Requests';
                        }
                        ?>
                    </label>
                    <textarea id="additionalComments" name="additional_comments" rows="6" 
                              class="w-full px-4 py-3 bg-white/10 border-2 border-white/20 rounded-xl text-white placeholder-gray-300 focus:border-accent focus:ring-2 focus:ring-accent focus:ring-opacity-50 transition-all duration-300 backdrop-blur-sm resize-none"
                              placeholder="<?php 
                              if ($currentLanguage === 'en') {
                                  echo 'Any additional information, special requirements, or comments about your church/group/zone crusade plans';
                              } else {
                                  echo $translationService->translateText('Any additional information, special requirements, or comments about your church/group/zone crusade plans', 'en', $currentLanguage) ?? 'Any additional information, special requirements, or comments about your church/group/zone crusade plans';
                              }
                              ?>"><?php echo getFieldValue('additional_comments', $existingRegistration); ?></textarea>
                </div>

                <!-- Submit Button -->
                <div class="pt-6">
                    <button type="submit" 
                            class="w-full bg-gradient-to-r from-accent to-yellow-300 hover:from-yellow-300 hover:to-accent text-gray-900 font-bold py-4 px-8 rounded-xl transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 text-xl">
                        <?php 
                        if ($currentLanguage === 'en') {
                            echo 'Submit Registration';
                        } else {
                            echo $translationService->translateText('Submit Registration', 'en', $currentLanguage) ?? 'Submit Registration';
                        }
                        ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</section>

<!-- Custom Styles -->
<style>
/* Custom dropdown styles for church crusade types */
.church-crusade-types-dropdown {
    position: relative;
}

.church-crusade-types-options {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 9999;
    max-height: 15rem;
    overflow-y: auto;
}

.church-crusade-types-options.hidden {
    display: none;
}

.church-crusade-types-selected svg {
    transition: transform 0.2s ease-in-out;
}

.church-crusade-types-selected svg.rotate-180 {
    transform: rotate(180deg);
}

/* Enhanced mobile scrolling for countries */
.touch-target {
    min-height: 44px; /* iOS recommended minimum touch target size */
    min-width: 44px;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

/* Better scrollbars for webkit browsers */
*::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

*::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

*::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

*::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}

/* Radio button selection styles */
input[name="church_type"]:checked + span {
    color: #fbbf24 !important; /* accent color for selected text */
}

/* Selected radio button container styles */
label:has(input[name="church_type"]:checked) {
    border-color: #fbbf24 !important; /* accent border */
    background-color: rgba(251, 191, 36, 0.1) !important; /* accent background with opacity */
}

/* Form field transitions */
#zoneSelectionContainer,
#organizationNameField,
#churchFields {
    transition: all 0.3s ease-in-out;
}

/* Hidden state */
.hidden-field {
    display: none !important;
}

/* Visible state with animation */
.visible-field {
    display: block !important;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<!-- JavaScript -->
<script>
// Pass existing registration data to JavaScript
const existingRegistrationData = <?php echo $existingRegistration ? json_encode($existingRegistration) : 'null'; ?>;
const isUpdateMode = <?php echo $isUpdate ? 'true' : 'false'; ?>;

document.addEventListener('DOMContentLoaded', function() {
    let countriesData = [];
    let registrationData = {};
    
    // Make functions and variables globally accessible
    window.selectedCountries = [];
    
    // Initialize form visibility
    updateFormVisibility();
    
    // Initialize cities autocomplete
    initializeCitiesAutocomplete();
    
    // Load countries and registration data with cross-platform support
    Promise.all([
        window.AppUtils.fetchWithFallback('../data/countriesbkd.json').then(response => response.json()),
        window.AppUtils.fetchWithFallback('data.json').then(response => response.json()).catch(() => ({ registrations: [] }))
    ])
    .then(([countriesResponse, registrations]) => {
        countriesData = countriesResponse.countries || [];
        registrationData = registrations;
        initializeCountryGuide();
        updateSelectedCountriesDisplay();
        
        // Pre-populate form if in update mode
        if (isUpdateMode && existingRegistrationData) {
            prePopulateForm();
        }
    })
    .catch(error => {
        console.error('Error loading data:', error);
        initializeCountryGuide(); // Initialize with empty data
    });
    
    function initializeCountryGuide() {
        const countriesGuide = document.getElementById('countriesGuide');
        
        // Get countries that already have registrations
        const selectedCountriesInRegistrations = new Set();
        if (registrationData.registrations) {
            registrationData.registrations.forEach(reg => {
                if (reg.selected_countries) {
                    const countries = reg.selected_countries.split(',').map(c => c.trim().toLowerCase());
                    countries.forEach(country => selectedCountriesInRegistrations.add(country));
                }
            });
        }
        
        // Group countries by continent
        const continents = {};
        countriesData.forEach(country => {
            const countryKey = country.name.toLowerCase().replace(/\s+/g, '-');
            if (!continents[country.continent]) {
                continents[country.continent] = [];
            }
            continents[country.continent].push({
                ...country,
                key: countryKey
            });
        });

        // Create continent sections
        const continentOrder = ['Africa', 'Asia', 'Europe', 'North America', 'South America', 'Oceania'];
        const guideHTML = continentOrder.map(continent => {
            const countries = continents[continent] || [];
            const countriesHTML = countries.map(country => {
                const isSelected = window.selectedCountries.includes(country.key);
                const hasRegistration = selectedCountriesInRegistrations.has(country.key);
                
                let bgClass = isSelected ? 'bg-purple-100 border-purple-300' : 
                             hasRegistration ? 'bg-green-50 border-green-200' : 
                             'bg-orange-50 border-orange-200';
                let textClass = isSelected ? 'text-purple-800' : 
                               hasRegistration ? 'text-green-800' : 
                               'text-orange-800';
                let iconClass = isSelected ? 'text-purple-600' : 
                               hasRegistration ? 'text-green-600' : 
                               'text-orange-600';
                let icon = isSelected ? '✓' : 
                          hasRegistration ? '✓' : 
                          '!';
                
                return `
                    <div class="flex items-center py-1 px-2 rounded border ${bgClass} text-xs cursor-pointer hover:opacity-75 transition-opacity country-option" 
                         data-country="${country.key}" 
                         data-name="${country.name}"
                         data-code="${country.code}"
                         data-region="${country.region}"
                         role="button"
                         tabindex="0">
                        <span class="${textClass} truncate flex-1">${country.name}</span>
                        <span class="ml-1 ${iconClass}">${icon}</span>
                    </div>
                `;
            }).join('');
            
            const needsCoverage = countries.filter(country => 
                !selectedCountriesInRegistrations.has(country.key)
            ).length;
            
            return `
                <div class="bg-white rounded-lg border border-gray-200 p-4">
                    <h4 class="font-semibold text-gray-900 mb-2 flex items-center justify-between">
                        ${continent}
                        <span class="text-xs px-2 py-1 rounded-full ${needsCoverage > 0 ? 'bg-orange-100 text-orange-800' : 'bg-green-100 text-green-800'}">
                            ${needsCoverage > 0 ? `${needsCoverage} need coverage` : 'Complete coverage'}
                        </span>
                    </h4>
                    <div class="grid grid-cols-1 gap-1 max-h-48 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-200" style="-webkit-overflow-scrolling: touch;">
                        ${countriesHTML}
                    </div>
                </div>
            `;
        }).join('');
        
        countriesGuide.innerHTML = guideHTML;
        
        // Remove old event listeners
        const oldOptions = document.querySelectorAll('.country-option');
        oldOptions.forEach(option => {
            option.replaceWith(option.cloneNode(true));
        });
        
        // Add new click handlers to country options with proper touch support
        document.querySelectorAll('.country-option').forEach(option => {
            // Handle touch events for mobile
            let touchStartTime = 0;
            let touchStartPosition = { x: 0, y: 0 };
            
            option.addEventListener('touchstart', function(e) {
                touchStartTime = Date.now();
                const touch = e.touches[0];
                touchStartPosition = { x: touch.clientX, y: touch.clientY };
            }, { passive: true });
            
            option.addEventListener('touchend', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const touchEndTime = Date.now();
                const touchDuration = touchEndTime - touchStartTime;
                
                // Only trigger if it's a quick tap (less than 500ms) and hasn't moved much
                if (touchDuration < 500) {
                    const touch = e.changedTouches[0];
                    const distance = Math.sqrt(
                        Math.pow(touch.clientX - touchStartPosition.x, 2) +
                        Math.pow(touch.clientY - touchStartPosition.y, 2)
                    );
                    
                    if (distance < 10) { // Less than 10px movement
                        toggleCountry(option);
                    }
                }
            });
            
            // Handle mouse clicks for desktop
            option.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                toggleCountry(option);
            });
            
            // Keyboard handler for accessibility
            option.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    e.stopPropagation();
                    toggleCountry(option);
                }
            });
            
            // Add touch-target class for better mobile interaction
            option.classList.add('touch-target');
            option.style.touchAction = 'manipulation'; // Disable double-tap zoom
            option.style.cursor = 'pointer';
            option.style.userSelect = 'none';
            option.style.webkitUserSelect = 'none';
            option.style.webkitTouchCallout = 'none';
        });
    }
    
    function toggleCountry(element) {
        const countryKey = element.dataset.country;
        const countryName = element.dataset.name;
        const countryCode = element.dataset.code;
        const countryRegion = element.dataset.region;
        
        const index = window.selectedCountries.indexOf(countryKey);
        if (index > -1) {
            window.selectedCountries.splice(index, 1);
        } else {
            window.selectedCountries.push(countryKey);
        }
        updateSelectedCountriesDisplay();
        initializeCountryGuide();
        
        // Provide feedback
        element.classList.add('scale-95');
        setTimeout(() => element.classList.remove('scale-95'), 100);
    }
    
    function updateSelectedCountriesDisplay() {
        const selectedCountriesDiv = document.getElementById('selected-countries');
        const countryPlaceholder = document.getElementById('country-placeholder');
        const selectedCountriesInput = document.getElementById('selected_countries');
        
        if (window.selectedCountries.length === 0) {
            selectedCountriesDiv.innerHTML = '<span id="country-placeholder" class="text-gray-500">Click countries in the guide above to select them</span>';
        } else {
            const selectedHTML = window.selectedCountries.map(countryKey => {
                const country = countriesData.find(c => 
                    c.name.toLowerCase().replace(/\s+/g, '-') === countryKey
                );
                
                return `
                    <div class="inline-flex items-center px-2 py-1 bg-purple-100 text-purple-800 text-sm rounded-md">
                        ${country?.name || countryKey}
                        <button type="button" class="ml-1 text-purple-600 hover:text-purple-800" onclick="removeCountry('${countryKey}', event)">×</button>
                    </div>
                `;
            }).join('');
            
            selectedCountriesDiv.innerHTML = selectedHTML;
        }
        
        // Update hidden input with selected countries
        selectedCountriesInput.value = window.selectedCountries.join(',');
    }
    
    // Global function to remove country
    window.removeCountry = function(countryKey, event) {
        event.preventDefault();
        event.stopPropagation();
        const index = window.selectedCountries.indexOf(countryKey);
        if (index > -1) {
            window.selectedCountries.splice(index, 1);
            updateSelectedCountriesDisplay();
            initializeCountryGuide();
        }
    };
    
    // Make other functions globally accessible
    window.updateSelectedCountriesDisplay = updateSelectedCountriesDisplay;
    window.initializeCountryGuide = initializeCountryGuide;
    
    // Add event listeners to church type radio buttons
    document.querySelectorAll('input[name="church_type"]').forEach(input => {
        input.addEventListener('change', handleChurchTypeSelection);
    });
    
    // Initialize crusade types dropdown
    initializeCrusadeTypeDropdown();
    
    // Add form validation for cities selection
    const form = document.getElementById('crusadeRegistrationForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            const citiesInput = document.getElementById('selected_cities_data');
            if (!citiesInput || !citiesInput.value || citiesInput.value === '[]') {
                e.preventDefault();
                alert('Please select at least one target city for your crusades.');
                return false;
            }
        });
    }
});

// Function to pre-populate form with existing data
function prePopulateForm() {
    if (!existingRegistrationData) return;
    
    console.log('Pre-populating form with:', existingRegistrationData);
    
    // 1. Pre-populate countries
    if (existingRegistrationData.selected_countries) {
        const countries = existingRegistrationData.selected_countries.split(',');
        window.selectedCountries = countries.map(country => country.trim());
        updateSelectedCountriesDisplay();
        initializeCountryGuide();
    }
    
    // 2. Pre-populate crusade types
    if (existingRegistrationData.crusade_types && Array.isArray(existingRegistrationData.crusade_types)) {
        existingRegistrationData.crusade_types.forEach(type => {
            const checkbox = document.querySelector(`.church-crusade-type-checkbox[value="${type}"]`);
            if (checkbox) {
                checkbox.checked = true;
            }
        });
        
        // Update the display after checking boxes
        setTimeout(() => {
            if (typeof window.updateSelectedCrusadeTypes === 'function') {
                window.updateSelectedCrusadeTypes();
            }
            
            // Show "Other" field if "other" is selected
            const otherCheckbox = document.querySelector('.church-crusade-type-checkbox[value="other"]');
            if (otherCheckbox && otherCheckbox.checked) {
                const otherField = document.getElementById('churchOtherCrusadeTypesField');
                if (otherField) {
                    otherField.classList.remove('hidden');
                }
            }
        }, 100);
    }
    
    // 3. Pre-populate expected attendance
    if (existingRegistrationData.expected_attendance) {
        const attendanceSelect = document.getElementById('expectedAttendance');
        if (attendanceSelect) {
            attendanceSelect.value = existingRegistrationData.expected_attendance;
        }
    }
    
    // 4. Pre-populate zone selection
    if (existingRegistrationData.zone) {
        const zoneSelect = document.getElementById('zoneSelect');
        if (zoneSelect) {
            zoneSelect.value = existingRegistrationData.zone;
        }
    }
    
    // 5. Pre-populate cities data
    if (existingRegistrationData.selected_cities_data && Array.isArray(existingRegistrationData.selected_cities_data)) {
        // Wait for the cities system to be initialized, then populate
        setTimeout(() => {
            if (typeof window.initializeCitiesSelection === 'function') {
                // Set the selected cities data for the existing system
                const citiesDiv = document.getElementById('selected-cities');
                const citiesInput = document.getElementById('selected_cities_data');
                
                if (citiesDiv && citiesInput) {
                    // Update the display directly
                    const citiesHTML = existingRegistrationData.selected_cities_data.map((city, index) => `
                        <div class="inline-flex items-center px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full border border-green-200">
                            <span class="font-medium">${city.name}</span>
                            <span class="text-xs text-green-600 ml-1">${city.country}</span>
                            <button type="button" class="ml-2 text-green-600 hover:text-green-800 font-bold" onclick="removeCity(${index})">×</button>
                        </div>
                    `).join('');
                    
                    citiesDiv.innerHTML = citiesHTML;
                    citiesInput.value = JSON.stringify(existingRegistrationData.selected_cities_data);
                    
                    // Store in the global selectedCities array if it exists
                    if (typeof window.selectedCities !== 'undefined') {
                        window.selectedCities = [...existingRegistrationData.selected_cities_data];
                    }
                }
            }
        }, 500);
    }
    
    // 6. Trigger form visibility update based on church type
    setTimeout(() => {
        handleChurchTypeSelection();
    }, 100);
}



// Global function to handle church type selection
function handleChurchTypeSelection() {
    updateFormVisibility();
}

// Handle form visibility based on registration type
function updateFormVisibility() {
    const selectedType = document.querySelector('input[name="church_type"]:checked');
    
    // Get elements
    const zoneSelectionContainer = document.getElementById('zoneSelectionContainer');
    const organizationNameField = document.getElementById('organizationNameField');
    const churchFields = document.getElementById('churchFields');
    
    // Check if elements exist before manipulating them
    if (!zoneSelectionContainer || !organizationNameField || !churchFields) {
        console.log('Some form elements not found, retrying...');
        return;
    }
    
    // Get form inputs to manage required attributes
    const zoneSelect = document.getElementById('zoneSelect');
    const organizationNameInput = document.getElementById('organizationName');
    const churchGroupNameInput = document.getElementById('churchGroupName');
    const churchNameInput = document.getElementById('churchName');
    
    if (!selectedType) {
        // Hide all optional sections if nothing is selected
        zoneSelectionContainer.style.display = 'none';
        organizationNameField.style.display = 'none';
        churchFields.style.display = 'none';
        
        // Remove required attributes when hidden
        if (zoneSelect) zoneSelect.removeAttribute('required');
        if (organizationNameInput) organizationNameInput.removeAttribute('required');
        if (churchGroupNameInput) churchGroupNameInput.removeAttribute('required');
        if (churchNameInput) churchNameInput.removeAttribute('required');
        return;
    }
    
    const type = selectedType.value;
    
    if (type === 'zone') {
        // Zone: show zone selection dropdown (they select which existing zone they represent)
        zoneSelectionContainer.style.display = 'block';
        organizationNameField.style.display = 'none';
        churchFields.style.display = 'none';
        
        // Set required attributes
        if (zoneSelect) zoneSelect.setAttribute('required', 'required');
        if (organizationNameInput) organizationNameInput.removeAttribute('required');
        if (churchGroupNameInput) churchGroupNameInput.removeAttribute('required');
        if (churchNameInput) churchNameInput.removeAttribute('required');
    } else if (type === 'group') {
        // Group: show zone selection and group name (they belong to an existing zone)
        zoneSelectionContainer.style.display = 'block';
        organizationNameField.style.display = 'block';
        churchFields.style.display = 'none';
        
        // Set required attributes
        if (zoneSelect) zoneSelect.setAttribute('required', 'required');
        if (organizationNameInput) organizationNameInput.setAttribute('required', 'required');
        if (churchGroupNameInput) churchGroupNameInput.removeAttribute('required');
        if (churchNameInput) churchNameInput.removeAttribute('required');
        
        // Update labels for group context
        const orgLabel = organizationNameField.querySelector('label');
        if (orgLabel) {
            orgLabel.innerHTML = '<?php 
            if ($currentLanguage === "en") {
                echo "Group Name";
            } else {
                echo $translationService->translateText("Group Name", "en", $currentLanguage) ?? "Group Name";
            }
            ?> <span class="text-accent">*</span>';
        }
    } else if (type === 'church') {
        // Church: show zone selection, group name, and church name (full hierarchy)
        zoneSelectionContainer.style.display = 'block';
        organizationNameField.style.display = 'none'; // Hide the single group name field
        churchFields.style.display = 'block'; // Show the church-specific fields (group + church name)
        
        // Set required attributes
        if (zoneSelect) zoneSelect.setAttribute('required', 'required');
        if (organizationNameInput) organizationNameInput.removeAttribute('required');
        if (churchGroupNameInput) churchGroupNameInput.setAttribute('required', 'required');
        if (churchNameInput) churchNameInput.setAttribute('required', 'required');
    }
    
    // Update visual selection state
    document.querySelectorAll('input[name="church_type"]').forEach(input => {
        const label = input.closest('label');
        if (input.checked) {
            label.classList.add('border-accent', 'bg-accent/20');
            label.classList.remove('border-white/20');
        } else {
            label.classList.remove('border-accent', 'bg-accent/20');
            label.classList.add('border-white/20');
        }
    });
}

function initializeCrusadeTypeDropdown() {
    // Get dropdown elements
    const dropdown = document.querySelector('.church-crusade-types-dropdown');
    const selected = dropdown.querySelector('.church-crusade-types-selected');
    const options = dropdown.querySelector('.church-crusade-types-options');
    const placeholder = dropdown.querySelector('.church-crusade-types-placeholder');
    const checkboxes = options.querySelectorAll('input[type="checkbox"]');
    const hiddenSelect = document.getElementById('churchCrusadeTypes');
    const otherCrusadeTypesField = document.getElementById('churchOtherCrusadeTypesField');
    
    // Toggle dropdown on click
    selected.addEventListener('click', function() {
        options.classList.toggle('hidden');
        selected.querySelector('svg').classList.toggle('rotate-180');
    });
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!dropdown.contains(e.target)) {
            options.classList.add('hidden');
            selected.querySelector('svg').classList.remove('rotate-180');
        }
    });
    
    // Handle checkbox changes
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            window.updateSelectedCrusadeTypes();
            
            // Show/hide "Other" field if needed
            if (checkbox.value === 'other') {
                if (otherCrusadeTypesField) {
                    otherCrusadeTypesField.style.display = checkbox.checked ? 'block' : 'none';
                }
            }
        });
    });
    
    // Update selected crusade types display - make it globally accessible
    window.updateSelectedCrusadeTypes = function() {
        const selectedTypes = Array.from(checkboxes)
            .filter(cb => cb.checked)
            .map(cb => {
                const label = cb.closest('label').querySelector('span').textContent;
                return label;
            });
        
        // Update hidden select for form submission
        if (hiddenSelect) {
            Array.from(hiddenSelect.options).forEach(option => {
                option.selected = Array.from(checkboxes)
                    .filter(cb => cb.checked)
                    .map(cb => cb.value)
                    .includes(option.value);
            });
        }
        
        // Update display
        if (selectedTypes.length === 0) {
            placeholder.textContent = '<?php 
            if ($currentLanguage === "en") {
                echo "Select crusade type(s) - You can select multiple";
            } else {
                echo $translationService->translateText("Select crusade type(s) - You can select multiple", "en", $currentLanguage) ?? "Select crusade type(s) - You can select multiple";
            }
            ?>';
            placeholder.classList.remove('hidden');
        } else {
            placeholder.textContent = selectedTypes.join(', ');
            placeholder.classList.remove('text-gray-300');
            placeholder.classList.add('text-white');
        }
    };
}

// Cities Autocomplete Functionality
function initializeCitiesAutocomplete() {
    const citiesInput = document.getElementById('crusade_cities');
    const autocompleteDiv = document.getElementById('cities-autocomplete');
    const suggestionsDiv = document.getElementById('cities-suggestions');
    const selectedCitiesDiv = document.getElementById('selected-cities');
    const selectedCitiesInput = document.getElementById('selected_cities_data');
    const citiesPlaceholder = document.getElementById('cities-placeholder');
    
    let selectedCities = [];
    let searchTimeout;
    let currentFocus = -1;
    
    // Debounced search function
    function debounceSearch(query, delay = 300) {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            searchCities(query);
        }, delay);
    }
    
    // Search cities via API
    async function searchCities(query) {
        if (query.length < 2) {
            hideAutocomplete();
            return;
        }
        
        try {
            const response = await window.AppUtils.fetchWithFallback(`../api/cities-search.php?q=${encodeURIComponent(query)}&maxRows=8`);
            const data = await response.json();
            
            if (data.cities && data.cities.length > 0) {
                displaySuggestions(data.cities);
            } else {
                hideSuggestions();
            }
        } catch (error) {
            console.error('Cities search error:', error);
            hideSuggestions();
        }
    }
    
    // Display search suggestions
    function displaySuggestions(cities) {
        suggestionsDiv.innerHTML = '';
        currentFocus = -1;
        
        // Add header to make it clear these are suggestions
        const headerDiv = document.createElement('div');
        headerDiv.className = 'px-3 py-2 text-xs font-medium text-gray-500 bg-gray-50 border-b border-gray-200';
        headerDiv.innerHTML = 'Select a city from the suggestions below:';
        suggestionsDiv.appendChild(headerDiv);
        
        cities.forEach((city, index) => {
            const suggestionDiv = document.createElement('div');
            suggestionDiv.className = 'city-suggestion px-3 py-2 hover:bg-green-50 cursor-pointer text-sm border-b border-gray-100 last:border-b-0 transition-colors duration-150';
            suggestionDiv.innerHTML = `
                <div class="flex items-center justify-between">
                    <div>
                        <div class="font-medium text-gray-900">${city.name}</div>
                        <div class="text-gray-600 text-xs">${city.admin1 ? city.admin1 + ', ' : ''}${city.country}</div>
                    </div>
                    <div class="text-green-500 opacity-0 group-hover:opacity-100">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                    </div>
                </div>
            `;
            
            suggestionDiv.addEventListener('click', () => {
                selectCity(city);
            });
            
            suggestionDiv.addEventListener('mouseenter', () => {
                currentFocus = index;
                updateFocus();
            });
            
            suggestionsDiv.appendChild(suggestionDiv);
        });
        
        showAutocomplete();
    }
    
    // Select a city from suggestions
    function selectCity(city) {
        // Check if city is already selected
        const existingCity = selectedCities.find(c => 
            c.name.toLowerCase() === city.name.toLowerCase() && 
            c.countryCode === city.countryCode
        );
        
        if (existingCity) {
            hideAutocomplete();
            return;
        }
        
        // Add city to selected list
        selectedCities.push({
            name: city.name,
            country: city.country,
            countryCode: city.countryCode,
            admin1: city.admin1 || '',
            latitude: city.latitude || '',
            longitude: city.longitude || ''
        });
        
        updateSelectedCitiesDisplay();
        clearCurrentInput();
        hideAutocomplete();
        
        // Focus back to input for more selections
        citiesInput.focus();
    }
    
    // Update selected cities display
    function updateSelectedCitiesDisplay() {
        if (selectedCities.length === 0) {
            selectedCitiesDiv.innerHTML = '<span id="cities-placeholder" class="text-gray-500">Please select at least one city (required)</span>';
        } else {
            const citiesHTML = selectedCities.map((city, index) => `
                <div class="inline-flex items-center px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full border border-green-200">
                    <span class="font-medium">${city.name}</span>
                    <span class="text-green-600 ml-1 text-xs">${city.country}</span>
                    <button type="button" 
                            class="ml-2 text-green-600 hover:text-green-800 font-bold focus:outline-none"
                            onclick="removeCity(${index})"
                            title="Remove ${city.name}">×</button>
                </div>
            `).join('');
            
            selectedCitiesDiv.innerHTML = citiesHTML;
        }
        
        // Update hidden input with cities data
        selectedCitiesInput.value = JSON.stringify(selectedCities);
        
        // Auto-add countries if they're not already selected
        autoAddCountriesFromCities();
    }
    
    // Auto-add countries based on selected cities
    function autoAddCountriesFromCities() {
        selectedCities.forEach(city => {
            const countryKey = city.country.toLowerCase().replace(/\s+/g, '-');
            if (!window.selectedCountries.includes(countryKey)) {
                window.selectedCountries.push(countryKey);
            }
        });
        
        // Update countries display if the function exists
        if (typeof updateSelectedCountriesDisplay === 'function') {
            updateSelectedCountriesDisplay();
        }
        if (typeof initializeCountryGuide === 'function') {
            initializeCountryGuide();
        }
    }
    
    // Clear current input (last part after comma)
    function clearCurrentInput() {
        const value = citiesInput.value;
        const lastCommaIndex = value.lastIndexOf(',');
        
        if (lastCommaIndex !== -1) {
            citiesInput.value = value.substring(0, lastCommaIndex + 1) + ' ';
        } else {
            citiesInput.value = '';
        }
    }
    
    // Get current search term (last part after comma)
    function getCurrentSearchTerm() {
        const value = citiesInput.value.trim();
        const parts = value.split(',');
        return parts[parts.length - 1].trim();
    }
    
    // Show autocomplete dropdown
    function showAutocomplete() {
        autocompleteDiv.classList.remove('hidden');
    }
    
    // Hide autocomplete dropdown
    function hideAutocomplete() {
        autocompleteDiv.classList.add('hidden');
        currentFocus = -1;
    }
    
    // Hide suggestions but keep dropdown open
    function hideSuggestions() {
        suggestionsDiv.innerHTML = `
            <div class="px-3 py-2 text-center">
                <div class="text-gray-500 text-sm mb-1">No cities found</div>
                <div class="text-xs text-gray-400">Try searching with different spelling or check the city name</div>
            </div>
        `;
        showAutocomplete();
    }
    
    // Update focus highlighting
    function updateFocus() {
        const suggestions = suggestionsDiv.querySelectorAll('.city-suggestion');
        suggestions.forEach((suggestion, index) => {
            if (index === currentFocus) {
                suggestion.classList.add('bg-gray-100');
            } else {
                suggestion.classList.remove('bg-gray-100');
            }
        });
    }
    
    // Handle keyboard navigation
    function handleKeyDown(e) {
        const suggestions = suggestionsDiv.querySelectorAll('.city-suggestion');
        
        if (suggestions.length === 0) return;
        
        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                currentFocus = Math.min(currentFocus + 1, suggestions.length - 1);
                updateFocus();
                break;
                
            case 'ArrowUp':
                e.preventDefault();
                currentFocus = Math.max(currentFocus - 1, -1);
                updateFocus();
                break;
                
            case 'Enter':
                e.preventDefault();
                if (currentFocus >= 0 && currentFocus < suggestions.length) {
                    suggestions[currentFocus].click();
                }
                break;
                
            case 'Escape':
                hideAutocomplete();
                break;
        }
    }
    
    // Event listeners
    citiesInput.addEventListener('input', (e) => {
        const searchTerm = getCurrentSearchTerm();
        if (searchTerm.length >= 2) {
            debounceSearch(searchTerm);
        } else {
            hideAutocomplete();
        }
    });
    
    citiesInput.addEventListener('keydown', handleKeyDown);
    
    citiesInput.addEventListener('focus', () => {
        const searchTerm = getCurrentSearchTerm();
        if (searchTerm.length >= 2) {
            debounceSearch(searchTerm, 100);
        }
    });
    
    // Hide autocomplete when clicking outside
    document.addEventListener('click', (e) => {
        if (!citiesInput.contains(e.target) && !autocompleteDiv.contains(e.target)) {
            hideAutocomplete();
        }
    });
    
    // Global function to remove city
    window.removeCity = function(index) {
        selectedCities.splice(index, 1);
        updateSelectedCitiesDisplay();
    };
}
</script>

<?php include 'includes/footer.php'; ?>