<?php
session_start();
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once 'includes/UpdateTokenManager.php';

// Input sanitization functions
function sanitizeString($str) {
    $str = strip_tags($str);
    $str = htmlspecialchars($str, ENT_QUOTES, 'UTF-8');
    $str = trim($str);
    return $str;
}

function sanitizeEmail($email) {
    $email = filter_var($email, FILTER_SANITIZE_EMAIL);
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Invalid email format');
    }
    return $email;
}

function sanitizePhone($phone) {
    // Remove everything except digits and plus sign
    $phone = preg_replace('/[^\d+]/', '', $phone);
    if (empty($phone)) {
        throw new Exception('Invalid phone number format');
    }
    return $phone;
}

function sanitizeCountryCode($code) {
    $code = sanitizeString($code);
    if (!preg_match('/^\+\d{1,4}$/', $code)) {
        throw new Exception('Invalid country code format');
    }
    return $code;
}

function sanitizeCountries($countries) {
    if (empty($countries)) {
        throw new Exception('No countries selected');
    }
    $countriesList = explode(',', $countries);
    $sanitizedCountries = array_map(function($country) {
        return sanitizeString(strtolower($country));
    }, $countriesList);
    return implode(',', array_filter($sanitizedCountries));
}

function sanitizeCitiesData($citiesData) {
    if (empty($citiesData)) {
        return [];
    }
    
    $cities = json_decode($citiesData, true);
    if (!is_array($cities)) {
        return [];
    }
    
    $sanitizedCities = array_map(function($city) {
        return [
            'name' => sanitizeString($city['name'] ?? ''),
            'country' => sanitizeString($city['country'] ?? ''),
            'countryCode' => sanitizeString($city['countryCode'] ?? ''),
            'admin1' => sanitizeString($city['admin1'] ?? ''),
            'latitude' => isset($city['latitude']) ? (float)$city['latitude'] : '',
            'longitude' => isset($city['longitude']) ? (float)$city['longitude'] : ''
        ];
    }, $cities);
    
    return array_filter($sanitizedCities, function($city) {
        return !empty($city['name']) && !empty($city['country']);
    });
}

function sanitizeNumber($num) {
    $num = filter_var($num, FILTER_SANITIZE_NUMBER_INT);
    return filter_var($num, FILTER_VALIDATE_INT) !== false ? $num : null;
}

function validateAttendanceRange($range) {
    $validRanges = ['100-500', '500-1000', '1000-2500', '2500-5000', '5000-10000', '10000-25000', '25000-50000', '50000-100000', '100000+'];
    $range = sanitizeString($range);
    if (!in_array($range, $validRanges)) {
        throw new Exception('Invalid attendance range');
    }
    return $range;
}

function validateNetworkType($type) {
    $validTypes = ['REON', 'RIN', 'RIM', 'TNI', 'SAY YES TO KIDS', 'TEEVOLUTION', 'REACHOUT CAMPAIGNS', 'YOUTH AGLOW', 'NO ONE LEFT BEHIND', 'Other'];
    $type = sanitizeString($type);
    if (!in_array($type, $validTypes)) {
        throw new Exception('Invalid network type');
    }
    return $type;
}

function validateCrusadeTypes($types) {
    $validTypes = ['mega', 'tap2read', 'youths-aglow', 'teevolution', 'say-yes-to-kids', 'nolb', 'leading-ladies', 'mighty-men', 'professionals', 'tv', 'social-media', 'online', 'mystreamspace', 'mall', 'school', 'hospital', 'street', 'prison', 'other'];
    $typesList = is_array($types) ? $types : explode(',', $types);
    $sanitizedTypes = array_map(function($type) use ($validTypes) {
        $type = sanitizeString($type);
        if (!in_array($type, $validTypes)) {
            throw new Exception('Invalid crusade type: ' . $type);
        }
        return $type;
    }, $typesList);
    return array_unique(array_filter($sanitizedTypes));
}

function sanitizeURL($url) {
    if (empty($url)) {
        return '';
    }
    $url = filter_var($url, FILTER_SANITIZE_URL);
    if (!filter_var($url, FILTER_VALIDATE_URL)) {
        throw new Exception('Invalid website URL format');
    }
    return $url;
}

try {
    // Validate request method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method');
    }

    // Check if this is an update request
    $isUpdate = isset($_POST['is_update']) && $_POST['is_update'] === '1';
    $updateToken = $_POST['update_token'] ?? '';
    $tokenManager = null;
    $existingRegistration = null;

    if ($isUpdate) {
        if (empty($updateToken)) {
            throw new Exception('Update token is required for updates');
        }
        
        $tokenManager = new UpdateTokenManager();
        $existingRegistration = $tokenManager->getRegistrationByToken($updateToken);
        
        if (!$existingRegistration) {
            throw new Exception('Invalid or expired update token');
        }
        
        if ($existingRegistration['registration_type'] !== 'network') {
            throw new Exception('Invalid registration type for this update link');
        }
    }

    // Sanitize and validate all inputs
    $registration_data = [
        'id' => 'net_' . uniqid(),
        'type' => 'network',
        'registration_type' => 'network',
        'network_type' => validateNetworkType($_POST['organisation_type'] ?? ''),
        'other_network_type' => isset($_POST['other_organisation_type']) ? sanitizeString($_POST['other_organisation_type']) : '',
        'network_name' => sanitizeString($_POST['organisation_type'] ?? ''),
        'first_name' => sanitizeString($_POST['first_name'] ?? ''),
        'last_name' => sanitizeString($_POST['last_name'] ?? ''),
        'email' => sanitizeEmail($_POST['email'] ?? ''),
        'phone_country_code' => sanitizeCountryCode($_POST['phone_country_code'] ?? ''),
        'phone' => sanitizePhone($_POST['phone'] ?? ''),
        'network_website' => isset($_POST['organisation_website']) ? sanitizeURL($_POST['organisation_website']) : '',
        'selected_countries' => sanitizeCountries($_POST['selected_countries'] ?? ''),
        'crusade_types' => validateCrusadeTypes($_POST['crusade_types'] ?? []),
        'other_crusade_types' => isset($_POST['other_crusade_types']) ? sanitizeString($_POST['other_crusade_types']) : '',
        'selected_cities_data' => sanitizeCitiesData($_POST['selected_cities_data'] ?? ''),
        'number_of_crusades' => sanitizeNumber($_POST['number_of_crusades'] ?? ''),
        'expected_attendance' => validateAttendanceRange($_POST['expected_attendance'] ?? ''),
        'additional_comments' => isset($_POST['additional_comments']) ? sanitizeString($_POST['additional_comments']) : '',
        'status' => 'approved',
        'registration_date' => date('Y-m-d H:i:s'),
        'registration_timestamp' => time(),
        'ip_address' => $_SERVER['REMOTE_ADDR'],
        'user_agent' => $_SERVER['HTTP_USER_AGENT'],
        'form_source' => 'network_registration_form',
        'submission_method' => $_SERVER['REQUEST_METHOD']
    ];

    // Add derived fields
    $registration_data['full_name'] = $registration_data['first_name'] . ' ' . $registration_data['last_name'];
    $registration_data['full_phone'] = $registration_data['phone_country_code'] . $registration_data['phone'];
    $registration_data['phone_display'] = $registration_data['full_phone'];
    $registration_data['registration_date_formatted'] = date('F j, Y g:i A', strtotime($registration_data['registration_date']));
    $registration_data['registration_date_iso'] = date('c', strtotime($registration_data['registration_date']));
    
    // Set display name
    $registration_data['display_name'] = $registration_data['network_type'] === 'Other' 
        ? $registration_data['other_network_type']
        : $registration_data['network_name'];
    $registration_data['primary_identifier'] = "Network: {$registration_data['display_name']}";
    
    // Process selected countries
    $registration_data['selected_countries_list'] = array_map('trim', explode(',', $registration_data['selected_countries']));
    $registration_data['countries_count'] = count($registration_data['selected_countries_list']);
    $registration_data['countries_display'] = $registration_data['countries_count'] . ' countries: ' . 
        implode(', ', array_slice($registration_data['selected_countries_list'], 0, 3)) . 
        ($registration_data['countries_count'] > 3 ? '...' : '');

    // Process selected cities
    $registration_data['cities_count'] = count($registration_data['selected_cities_data']);
    if ($registration_data['cities_count'] > 0) {
        $cityNames = array_map(function($city) {
            return $city['name'] . ' (' . $city['country'] . ')';
        }, $registration_data['selected_cities_data']);
        
        $registration_data['cities_display'] = $registration_data['cities_count'] . ' cities: ' . 
            implode(', ', array_slice($cityNames, 0, 3)) . 
            ($registration_data['cities_count'] > 3 ? '...' : '');
            
        // Create a simple list of city names
        $registration_data['cities_list'] = array_map(function($city) {
            return $city['name'];
        }, $registration_data['selected_cities_data']);
        
        // Group cities by country
        $citiesByCountry = [];
        foreach ($registration_data['selected_cities_data'] as $city) {
            $citiesByCountry[$city['country']][] = $city['name'];
        }
        $registration_data['cities_by_country'] = $citiesByCountry;
    } else {
        $registration_data['cities_display'] = 'No specific cities selected';
        $registration_data['cities_list'] = [];
        $registration_data['cities_by_country'] = [];
    }

    // Process crusade types
    $registration_data['crusade_types_string'] = implode(', ', $registration_data['crusade_types']);
    $registration_data['crusade_types_display'] = $registration_data['crusade_types_string'];
    if ($registration_data['other_crusade_types']) {
        $registration_data['crusade_types_display'] .= ", Other: {$registration_data['other_crusade_types']}";
    }

    // Convert expected attendance to numeric for sorting
    $attendance_parts = explode('-', str_replace('+', '', $registration_data['expected_attendance']));
    $registration_data['expected_attendance_numeric'] = (int)$attendance_parts[0];

    // Website display
    $registration_data['website_display'] = $registration_data['network_website'];

    // Server info for debugging
    $registration_data['server_info'] = [
        'server_name' => $_SERVER['SERVER_NAME'],
        'request_uri' => $_SERVER['REQUEST_URI'],
        'http_host' => $_SERVER['HTTP_HOST']
    ];

    // Load existing data
    $data = [];
    if (file_exists('data.json')) {
        $data = json_decode(file_get_contents('data.json'), true) ?: [];
    }

    // Initialize or update registrations array
    if (!isset($data['registrations'])) {
        $data['registrations'] = [];
    }

    if ($isUpdate) {
        // Update existing registration
        $updated = false;
        foreach ($data['registrations'] as &$reg) {
            if ($reg['id'] === $existingRegistration['id']) {
                // Preserve original registration data and merge with updates
                $registration_data['id'] = $existingRegistration['id'];
                $registration_data['registration_date'] = $existingRegistration['registration_date'];
                $registration_data['registration_timestamp'] = $existingRegistration['registration_timestamp'];
                $registration_data['updated_at'] = date('Y-m-d H:i:s');
                $registration_data['last_update_source'] = 'update_form';
                
                $reg = $registration_data;
                $updated = true;
                break;
            }
        }
        
        if (!$updated) {
            throw new Exception('Registration not found for update');
        }
        
        // Update the registration using token manager
        if (!$tokenManager->updateRegistration($updateToken, $registration_data)) {
            throw new Exception('Failed to update registration via token manager');
        }
    } else {
        // Add new registration
        $data['registrations'][] = $registration_data;
    }

    // Update statistics
    $data['total_registrations'] = count($data['registrations']);
    $data['church_registrations'] = count(array_filter($data['registrations'], function($reg) {
        return $reg['type'] === 'church';
    }));
    $data['network_registrations'] = count(array_filter($data['registrations'], function($reg) {
        return $reg['type'] === 'network';
    }));
    $data['last_updated'] = date('c');

    // Save data
    if (!file_put_contents('data.json', json_encode($data, JSON_PRETTY_PRINT))) {
        throw new Exception('Failed to save registration data');
    }

    // Log success
    if ($isUpdate) {
        error_log('Network registration updated: ID=' . $registration_data['id'] . 
                  ', Type=' . $registration_data['network_type'] . 
                  ', Name=' . $registration_data['display_name']);
    } else {
        error_log('Network registration successful: ID=' . $registration_data['id'] . 
                  ', Type=' . $registration_data['network_type'] . 
                  ', Name=' . $registration_data['display_name']);
    }

    // Redirect to success page
    $_SESSION['registration_success'] = true;
    $_SESSION['registration_type'] = 'network';
    $_SESSION['registration_data'] = $registration_data;
    $_SESSION['is_update'] = $isUpdate;
    
    if ($isUpdate) {
        header('Location: update-dashboard.php?token=' . urlencode($updateToken) . '&updated=1');
    } else {
        header('Location: registration-success.php');
    }
    exit;

} catch (Exception $e) {
    error_log('Network registration error: ' . $e->getMessage());
    $_SESSION['registration_error'] = $e->getMessage();
    header('Location: register-network.php');
    exit;
}
?>