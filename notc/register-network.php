<?php 
require_once '../includes/config.php';
require_once '../includes/languages.php';
require_once '../includes/TranslationService.php';
require_once 'includes/UpdateTokenManager.php';

// Initialize translation service
$translationService = TranslationService::getInstance();
$currentLanguage = Language::getCurrentLanguage();

// Check if this is an update request
$isUpdate = false;
$updateToken = $_GET['token'] ?? '';
$existingRegistration = null;

if (!empty($updateToken)) {
    $tokenManager = new UpdateTokenManager();
    $existingRegistration = $tokenManager->getRegistrationByToken($updateToken);
    
    if ($existingRegistration && $existingRegistration['registration_type'] === 'network') {
        $isUpdate = true;
        // Mark token as used for statistics
        $tokenManager->useToken($updateToken);
    } else {
        // Invalid token or wrong registration type
        header('Location: update-dashboard.php?token=' . urlencode($updateToken));
        exit();
    }
}

// Helper function to get field value (existing data or empty)
function getFieldValue($fieldName, $existingData = null) {
    if ($existingData && isset($existingData[$fieldName])) {
        return htmlspecialchars($existingData[$fieldName]);
    }
    return '';
}

// Helper function to check if radio/checkbox should be selected
function isSelected($fieldName, $value, $existingData = null) {
    if ($existingData && isset($existingData[$fieldName])) {
        if (is_array($existingData[$fieldName])) {
            return in_array($value, $existingData[$fieldName]) ? 'checked' : '';
        }
        return $existingData[$fieldName] === $value ? 'checked' : '';
    }
    return '';
}

include 'includes/header.php'; 
?>

<!-- Fixed Video Background -->
<div class="fixed inset-0 w-full h-full z-0">
    <video 
        autoplay 
        muted 
        loop 
        playsinline 
        class="absolute inset-0 w-full h-full object-cover"
        poster="https://rorcloud.org/serve.php?id=269&name=Night_of_thousand_crusades.jpg">
        <source src="https://rorcloud.org/serve.php?id=279&name=output(compress-video-online.com).mp4" type="video/mp4">
        Your browser does not support the video tag.
    </video>
    <!-- Dark Overlay for better text readability -->
    <div class="absolute inset-0 bg-black bg-opacity-80"></div>
    <!-- Additional gradient overlay for better text contrast -->
    <div class="absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-black/50"></div>
</div>

<!-- Registration Form Section -->
<section class="min-h-screen text-white relative z-10 overflow-x-hidden py-20">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 relative w-full">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl sm:text-5xl md:text-6xl font-bold mb-6 drop-shadow-lg">
                <?php 
                if ($currentLanguage === 'en') {
                    echo 'Network Registration';
                } else {
                    echo $translationService->translateText('Network Registration', 'en', $currentLanguage) ?? 'Network Registration';
                }
                ?>
            </h1>
            <p class="text-xl md:text-2xl drop-shadow-lg text-gray-100 max-w-3xl mx-auto">
                <?php 
                if ($currentLanguage === 'en') {
                    echo 'Register your network to coordinate large-scale crusade activities';
                } else {
                    echo $translationService->translateText('Register your network to coordinate large-scale crusade activities', 'en', $currentLanguage) ?? 'Register your network to coordinate large-scale crusade activities';
                }
                ?>
            </p>
            <div class="w-20 h-1 bg-primary mx-auto mt-6"></div>
        </div>

        <!-- Registration Form -->
        <div class="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-8 shadow-2xl">
            <form id="networkRegistrationForm" method="POST" action="register-network-handler.php" class="space-y-8">
                <!-- Network Information Section -->
                <div class="space-y-6">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-accent rounded-full flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-gray-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-white">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Network Information';
                            } else {
                                echo $translationService->translateText('Network Information', 'en', $currentLanguage) ?? 'Network Information';
                            }
                            ?>
                        </h3>
                    </div>

                    <!-- Select Network -->
                    <div class="space-y-4">
                        <label for="networkType" class="block text-lg font-medium text-white">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Select Network';
                            } else {
                                echo $translationService->translateText('Select Network', 'en', $currentLanguage) ?? 'Select Network';
                            }
                            ?> <span class="text-accent">*</span>
                        </label>
                        <select id="networkType" name="organisation_type" required 
                                class="w-full px-4 py-4 bg-white/10 border-2 border-white/20 rounded-xl text-white focus:border-accent focus:ring-2 focus:ring-accent focus:ring-opacity-50 transition-all duration-300 backdrop-blur-sm"
                                onchange="handleNetworkTypeSelection()">
                            <option value="" class="text-gray-900">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Select network';
                                } else {
                                    echo $translationService->translateText('Select network', 'en', $currentLanguage) ?? 'Select network';
                                }
                                ?>
                            </option>
                            <option value="REON" class="text-gray-900">REON</option>
                            <option value="RIN" class="text-gray-900">RIN</option>
                            <option value="RIM" class="text-gray-900">RIM</option>
                            <option value="TNI" class="text-gray-900">TNI</option>
                            <option value="SAY YES TO KIDS" class="text-gray-900">SAY YES TO KIDS</option>
                            <option value="TEEVOLUTION" class="text-gray-900">TEEVOLUTION</option>
                            <option value="REACHOUT CAMPAIGNS" class="text-gray-900">REACHOUT CAMPAIGNS</option>
                            <option value="YOUTH AGLOW" class="text-gray-900">YOUTH AGLOW</option>
                            <option value="NO ONE LEFT BEHIND" class="text-gray-900">NO ONE LEFT BEHIND</option>
                            <option value="Other" class="text-gray-900">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Other';
                                } else {
                                    echo $translationService->translateText('Other', 'en', $currentLanguage) ?? 'Other';
                                }
                                ?>
                            </option>
                        </select>
                    </div>

                    <!-- Other Network Type Field -->
                    <div id="otherNetworkTypeField" class="space-y-4 hidden">
                        <label for="otherNetworkType" class="block text-lg font-medium text-white">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Please specify network';
                            } else {
                                echo $translationService->translateText('Please specify network', 'en', $currentLanguage) ?? 'Please specify network';
                            }
                            ?> <span class="text-accent">*</span>
                        </label>
                        <input type="text" id="otherNetworkType" name="other_organisation_type" 
                               value="<?php echo getFieldValue('other_organisation_type', $existingRegistration); ?>"
                               class="w-full px-4 py-4 bg-white/10 border-2 border-white/20 rounded-xl text-white placeholder-gray-300 focus:border-accent focus:ring-2 focus:ring-accent focus:ring-opacity-50 transition-all duration-300 backdrop-blur-sm"
                               placeholder="<?php 
                               if ($currentLanguage === 'en') {
                                   echo 'Enter your network name';
                               } else {
                                   echo $translationService->translateText('Enter your network name', 'en', $currentLanguage) ?? 'Enter your network name';
                               }
                               ?>">
                    </div>
                </div>

                <!-- Countries Guidance Section -->
                <div class="mb-8 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-6 border border-purple-200">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center justify-between">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Select Your Crusade Locations';
                            } else {
                                echo $translationService->translateText('Select Your Crusade Locations', 'en', $currentLanguage) ?? 'Select Your Crusade Locations';
                            }
                            ?>
                        </div>
                        <div class="text-sm text-purple-600">Click to select countries</div>
                        </h3>
                    
                    <p class="text-gray-700 mb-4">
                        <?php 
                        if ($currentLanguage === 'en') {
                            echo 'Select the countries where your network will be conducting crusades. Countries in <span class="text-orange-600 font-medium">orange</span> have no registered crusades yet - consider including these in your plans.';
                        } else {
                            echo $translationService->translateText('Select the countries where your network will be conducting crusades. Countries in <span class="text-orange-600 font-medium">orange</span> have no registered crusades yet - consider including these in your plans.', 'en', $currentLanguage) ?? 'Select the countries where your network will be conducting crusades. Countries in <span class="text-orange-600 font-medium">orange</span> have no registered crusades yet - consider including these in your plans.';
                        }
                        ?>
                    </p>
                    
                    <div id="countriesGuide" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div class="text-center text-gray-500 py-8">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Loading country coverage information...';
                            } else {
                                echo $translationService->translateText('Loading country coverage information...', 'en', $currentLanguage) ?? 'Loading country coverage information...';
                            }
                            ?>
                        </div>
                    </div>
                    
                    <div class="mt-4 flex flex-wrap gap-4 text-sm">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-green-100 border border-green-300 rounded mr-2"></div>
                            <span class="text-gray-600">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Has registered crusades';
                                } else {
                                    echo $translationService->translateText('Has registered crusades', 'en', $currentLanguage) ?? 'Has registered crusades';
                                }
                                ?>
                            </span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-orange-100 border border-orange-300 rounded mr-2"></div>
                            <span class="text-gray-600">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'No registered crusades yet';
                                } else {
                                    echo $translationService->translateText('No registered crusades yet', 'en', $currentLanguage) ?? 'No registered crusades yet';
                                }
                                ?>
                            </span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-purple-100 border border-purple-300 rounded mr-2"></div>
                            <span class="text-gray-600">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Selected for your crusades';
                                } else {
                                    echo $translationService->translateText('Selected for your crusades', 'en', $currentLanguage) ?? 'Selected for your crusades';
                                }
                                ?>
                            </span>
                        </div>
                        </div>
                    </div>
                    
                <!-- Selected Countries Display -->
                <div class="mb-6">
                    <div class="bg-gradient-to-r from-purple-600 to-indigo-600 rounded-t-lg p-3">
                        <label class="block text-lg font-semibold text-white mb-0">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Selected Countries/Regions';
                            } else {
                                echo $translationService->translateText('Selected Countries/Regions', 'en', $currentLanguage) ?? 'Selected Countries/Regions';
                            }
                            ?> <span class="text-red-200 font-bold">*</span>
                        </label>
                            </div>
                    <div id="selected-countries" class="min-h-[3rem] flex flex-wrap gap-2 p-3 bg-white border border-gray-300 border-t-0 rounded-b-lg">
                        <span id="country-placeholder" class="text-gray-500">Click countries in the guide above to select them</span>
                    </div>
                    <input type="hidden" name="selected_countries" id="selected_countries" required>
                    <p class="mt-1 text-sm text-gray-500">Selected countries where your network plans to conduct crusades</p>
                </div>

                <!-- Cities Selection -->
                <div class="mb-6">
                    <div class="bg-gradient-to-r from-green-600 to-emerald-600 rounded-t-lg p-3">
                        <label for="crusade_cities" class="block text-lg font-semibold text-white mb-0">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Target Cities';
                            } else {
                                echo $translationService->translateText('Target Cities', 'en', $currentLanguage) ?? 'Target Cities';
                            }
                            ?> <span class="text-red-200 font-bold">*</span>
                        </label>
                    </div>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <input type="text" 
                               id="crusade_cities" 
                               name="crusade_cities" 
                               class="w-full pl-10 pr-4 py-4 bg-white border border-gray-300 border-t-0 rounded-b-lg text-gray-900 placeholder-gray-500 focus:border-green-500 focus:ring-2 focus:ring-green-500 focus:ring-opacity-50 transition-all duration-300"
                               placeholder="<?php 
                               if ($currentLanguage === 'en') {
                                   echo 'Search for cities... (e.g., Lagos, Accra, Nairobi)';
                               } else {
                                   echo $translationService->translateText('Search for cities... (e.g., Lagos, Accra, Nairobi)', 'en', $currentLanguage) ?? 'Search for cities... (e.g., Lagos, Accra, Nairobi)';
                               }
                               ?>"
                               autocomplete="off">
                        
                        <!-- Cities Autocomplete Dropdown -->
                        <div id="cities-autocomplete" class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto hidden">
                            <div id="cities-suggestions" class="p-2">
                                <!-- Suggestions will be populated here -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- Selected Cities Display -->
                    <div id="selected-cities" class="min-h-[2rem] flex flex-wrap gap-2 p-3 bg-gray-50 border border-gray-300 rounded-b-lg mt-2">
                        <span id="cities-placeholder" class="text-gray-500">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Please select at least one city (required)';
                            } else {
                                echo $translationService->translateText('Please select at least one city (required)', 'en', $currentLanguage) ?? 'Please select at least one city (required)';
                            }
                            ?>
                        </span>
                    </div>
                    
                    <input type="hidden" name="selected_cities_data" id="selected_cities_data" required>
                    
                    <p class="mt-2 text-sm text-gray-600">
                        <?php 
                        if ($currentLanguage === 'en') {
                            echo 'Type to search for cities and select from the suggestions. You must select at least one city. Cities will be automatically matched to their countries for better planning.';
                        } else {
                            echo $translationService->translateText('Type to search for cities and select from the suggestions. You must select at least one city. Cities will be automatically matched to their countries for better planning.', 'en', $currentLanguage) ?? 'Type to search for cities and select from the suggestions. You must select at least one city. Cities will be automatically matched to their countries for better planning.';
                        }
                        ?>
                    </p>
                </div>

                <!-- Crusade Types Section -->
                <div class="space-y-4">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-accent rounded-full flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-gray-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2zm8 0h-2a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-white">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Crusade Type(s)';
                            } else {
                                echo $translationService->translateText('Crusade Type(s)', 'en', $currentLanguage) ?? 'Crusade Type(s)';
                            }
                            ?> <span class="text-accent">*</span>
                        </h3>
                    </div>
                    
                    <div class="relative">
                        <select id="networkCrusadeTypes" name="crusade_types[]" multiple required 
                                class="w-full px-4 py-3 bg-white/10 border-2 border-white/20 rounded-xl text-white focus:border-accent focus:ring-2 focus:ring-accent focus:ring-opacity-50 transition-all duration-300 backdrop-blur-sm"
                                style="display: none;">
                            <option value="mega">Mega Crusades (10,000+ people)</option>
                            <option value="tap2read">TAP2read Crusades - promoting TAP2read app with evangelism</option>
                            <option value="youths-aglow">Youths Aglow Crusades</option>
                            <option value="teevolution">Teevolution Crusades (Teens-focused)</option>
                            <option value="say-yes-to-kids">Say Yes To Kids Crusades</option>
                            <option value="nolb">No One Left Behind Crusades (for the visually/hearing impaired)</option>
                            <option value="leading-ladies">Leading Ladies Crusades</option>
                            <option value="mighty-men">Mighty Men Crusades</option>
                            <option value="professionals">Specialized Crusades to Professionals</option>
                            <option value="tv">TV Crusades</option>
                            <option value="social-media">Social Media Crusades</option>
                            <option value="online">Online Crusades - targeting digital audiences</option>
                            <option value="mystreamspace">MyStreamSpace Crusades</option>
                            <option value="mall">Mall Crusades</option>
                            <option value="school">School Crusades</option>
                            <option value="hospital">Hospital Crusades</option>
                            <option value="street">Street Crusades</option>
                            <option value="prison">Prison Crusades</option>
                            <option value="other">Other (please specify)</option>
                        </select>
                        
                        <!-- Custom Dropdown Interface -->
                        <div class="network-crusade-types-dropdown">
                            <div class="network-crusade-types-selected w-full px-4 py-3 bg-white/10 border-2 border-white/20 rounded-xl focus:border-accent focus:ring-2 focus:ring-accent focus:ring-opacity-50 transition-all duration-300 backdrop-blur-sm cursor-pointer min-h-[3rem] flex items-center justify-between">
                                <span class="network-crusade-types-placeholder text-gray-300">
                                    <?php 
                                    if ($currentLanguage === 'en') {
                                        echo 'Select crusade type(s) - You can select multiple';
                                    } else {
                                        echo $translationService->translateText('Select crusade type(s) - You can select multiple', 'en', $currentLanguage) ?? 'Select crusade type(s) - You can select multiple';
                                    }
                                    ?>
                                </span>
                                <svg class="w-5 h-5 text-gray-400 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                            <div class="network-crusade-types-options absolute z-50 w-full mt-1 bg-white/95 backdrop-blur-md border-2 border-white/20 rounded-xl shadow-lg max-h-60 overflow-y-auto hidden">
                                <div class="p-2">
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" name="crusade_types[]" value="mega" class="network-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">Mega Crusades (10,000+ people)</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" name="crusade_types[]" value="tap2read" class="network-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">TAP2read Crusades - promoting TAP2read app with evangelism</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" name="crusade_types[]" value="youths-aglow" class="network-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">Youths Aglow Crusades</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" name="crusade_types[]" value="teevolution" class="network-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">Teevolution Crusades (Teens-focused)</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" name="crusade_types[]" value="say-yes-to-kids" class="network-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">Say Yes To Kids Crusades</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" name="crusade_types[]" value="nolb" class="network-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">No One Left Behind Crusades (for the visually/hearing impaired)</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" name="crusade_types[]" value="leading-ladies" class="network-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">Leading Ladies Crusades</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" name="crusade_types[]" value="mighty-men" class="network-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">Mighty Men Crusades</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" name="crusade_types[]" value="professionals" class="network-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">Specialized Crusades to Professionals</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" name="crusade_types[]" value="tv" class="network-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">TV Crusades</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" name="crusade_types[]" value="social-media" class="network-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">Social Media Crusades</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" name="crusade_types[]" value="online" class="network-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">Online Crusades - targeting digital audiences</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" name="crusade_types[]" value="mystreamspace" class="network-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">MyStreamSpace Crusades</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" name="crusade_types[]" value="mall" class="network-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">Mall Crusades</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" name="crusade_types[]" value="school" class="network-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">School Crusades</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" name="crusade_types[]" value="hospital" class="network-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">Hospital Crusades</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" name="crusade_types[]" value="street" class="network-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">Street Crusades</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" name="crusade_types[]" value="prison" class="network-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">Prison Crusades</span>
                                    </label>
                                    <label class="flex items-center p-2 hover:bg-gray-100/20 rounded cursor-pointer">
                                        <input type="checkbox" name="crusade_types[]" value="other" class="network-crusade-type-checkbox mr-3 text-accent focus:ring-accent border-gray-300 rounded">
                                        <span class="text-gray-900 font-medium">Other (please specify)</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Other Crusade Types Field -->
                    <div id="networkOtherCrusadeTypesField" class="space-y-4 hidden">
                        <label for="networkOtherCrusadeTypes" class="block text-lg font-medium text-white">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Please specify other crusade type(s)';
                            } else {
                                echo $translationService->translateText('Please specify other crusade type(s)', 'en', $currentLanguage) ?? 'Please specify other crusade type(s)';
                            }
                            ?> <span class="text-accent">*</span>
                        </label>
                        <input type="text" id="networkOtherCrusadeTypes" name="other_crusade_types" 
                               value="<?php echo getFieldValue('other_crusade_types', $existingRegistration); ?>"
                               class="w-full px-4 py-4 bg-white/10 border-2 border-white/20 rounded-xl text-white placeholder-gray-300 focus:border-accent focus:ring-2 focus:ring-accent focus:ring-opacity-50 transition-all duration-300 backdrop-blur-sm"
                               placeholder="<?php 
                               if ($currentLanguage === 'en') {
                                   echo 'Please describe your other crusade type(s)';
                               } else {
                                   echo $translationService->translateText('Please describe your other crusade type(s)', 'en', $currentLanguage) ?? 'Please describe your other crusade type(s)';
                               }
                               ?>">
                    </div>
                </div>

                <!-- Number of Crusades Section -->
                <div class="space-y-4">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-accent rounded-full flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-gray-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-white">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Number of Crusades';
                            } else {
                                echo $translationService->translateText('Number of Crusades', 'en', $currentLanguage) ?? 'Number of Crusades';
                            }
                            ?>
                        </h3>
                    </div>
                    
                    <label for="numberOfCrusades" class="block text-lg font-medium text-white">
                        <?php 
                        if ($currentLanguage === 'en') {
                            echo 'Number of Crusades';
                        } else {
                            echo $translationService->translateText('Number of Crusades', 'en', $currentLanguage) ?? 'Number of Crusades';
                        }
                        ?> <span class="text-accent">*</span>
                    </label>
                    <input type="number" id="numberOfCrusades" name="number_of_crusades" required min="1" max="1000"
                           value="<?php echo getFieldValue('number_of_crusades', $existingRegistration); ?>"
                           class="w-full px-4 py-4 bg-white/10 border-2 border-white/20 rounded-xl text-white placeholder-gray-300 focus:border-accent focus:ring-2 focus:ring-accent focus:ring-opacity-50 transition-all duration-300 backdrop-blur-sm"
                           placeholder="<?php 
                           if ($currentLanguage === 'en') {
                               echo 'Enter number of crusades';
                           } else {
                               echo $translationService->translateText('Enter number of crusades', 'en', $currentLanguage) ?? 'Enter number of crusades';
                           }
                           ?>">
                </div>

                <!-- Expected Attendance Section -->
                <div class="space-y-4">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-accent rounded-full flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-gray-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-white">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Expected Attendance';
                            } else {
                                echo $translationService->translateText('Expected Attendance', 'en', $currentLanguage) ?? 'Expected Attendance';
                            }
                            ?>
                        </h3>
                    </div>
                    
                    <label for="expectedAttendance" class="block text-lg font-medium text-white">
                        <?php 
                        if ($currentLanguage === 'en') {
                            echo 'Expected Total Attendance Across All Crusades';
                        } else {
                            echo $translationService->translateText('Expected Total Attendance Across All Crusades', 'en', $currentLanguage) ?? 'Expected Total Attendance Across All Crusades';
                        }
                        ?> <span class="text-accent">*</span>
                    </label>
                    <select id="expectedAttendance" name="expected_attendance" required 
                            class="w-full px-4 py-4 bg-white/10 border-2 border-white/20 rounded-xl text-white focus:border-accent focus:ring-2 focus:ring-accent focus:ring-opacity-50 transition-all duration-300 backdrop-blur-sm">
                        <option value="" class="text-gray-900">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Select expected attendance';
                            } else {
                                echo $translationService->translateText('Select expected attendance', 'en', $currentLanguage) ?? 'Select expected attendance';
                            }
                            ?>
                        </option>
                        <option value="500-1000" class="text-gray-900">500 - 1,000</option>
                        <option value="1000-5000" class="text-gray-900">1,000 - 5,000</option>
                        <option value="5000-10000" class="text-gray-900">5,000 - 10,000</option>
                        <option value="10000-25000" class="text-gray-900">10,000 - 25,000</option>
                        <option value="25000-50000" class="text-gray-900">25,000 - 50,000</option>
                        <option value="50000-100000" class="text-gray-900">50,000 - 100,000</option>
                        <option value="100000+" class="text-gray-900">100,000+</option>
                        <option value="unsure" class="text-gray-900">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Unsure';
                            } else {
                                echo $translationService->translateText('Unsure', 'en', $currentLanguage) ?? 'Unsure';
                            }
                            ?>
                        </option>
                    </select>
                </div>

                <!-- Contact Information Section -->
                <div class="space-y-6">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-accent rounded-full flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-gray-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-white">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Contact Information';
                            } else {
                                echo $translationService->translateText('Contact Information', 'en', $currentLanguage) ?? 'Contact Information';
                            }
                            ?>
                        </h3>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <label for="contactFirstName" class="block text-lg font-medium text-white">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'First Name';
                                } else {
                                    echo $translationService->translateText('First Name', 'en', $currentLanguage) ?? 'First Name';
                                }
                                ?> <span class="text-accent">*</span>
                            </label>
                            <input type="text" id="contactFirstName" name="first_name" required 
                                   value="<?php echo getFieldValue('first_name', $existingRegistration); ?>"
                                   class="w-full px-4 py-4 bg-white/10 border-2 border-white/20 rounded-xl text-white placeholder-gray-300 focus:border-accent focus:ring-2 focus:ring-accent focus:ring-opacity-50 transition-all duration-300 backdrop-blur-sm"
                                   placeholder="<?php 
                                   if ($currentLanguage === 'en') {
                                       echo 'Enter your first name';
                                   } else {
                                       echo $translationService->translateText('Enter your first name', 'en', $currentLanguage) ?? 'Enter your first name';
                                   }
                                   ?>">
                        </div>
                        
                        <div class="space-y-4">
                            <label for="contactLastName" class="block text-lg font-medium text-white">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Last Name';
                                } else {
                                    echo $translationService->translateText('Last Name', 'en', $currentLanguage) ?? 'Last Name';
                                }
                                ?> <span class="text-accent">*</span>
                            </label>
                            <input type="text" id="contactLastName" name="last_name" required 
                                   value="<?php echo getFieldValue('last_name', $existingRegistration); ?>"
                                   class="w-full px-4 py-4 bg-white/10 border-2 border-white/20 rounded-xl text-white placeholder-gray-300 focus:border-accent focus:ring-2 focus:ring-accent focus:ring-opacity-50 transition-all duration-300 backdrop-blur-sm"
                                   placeholder="<?php 
                                   if ($currentLanguage === 'en') {
                                       echo 'Enter your last name';
                                   } else {
                                       echo $translationService->translateText('Enter your last name', 'en', $currentLanguage) ?? 'Enter your last name';
                                   }
                                   ?>">
                        </div>
                        
                        <div class="space-y-4">
                            <label for="contactEmail" class="block text-lg font-medium text-white">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Email Address';
                                } else {
                                    echo $translationService->translateText('Email Address', 'en', $currentLanguage) ?? 'Email Address';
                                }
                                ?> <span class="text-accent">*</span>
                            </label>
                            <input type="email" id="contactEmail" name="email" required 
                                   value="<?php echo getFieldValue('email', $existingRegistration); ?>"
                                   class="w-full px-4 py-4 bg-white/10 border-2 border-white/20 rounded-xl text-white placeholder-gray-300 focus:border-accent focus:ring-2 focus:ring-accent focus:ring-opacity-50 transition-all duration-300 backdrop-blur-sm"
                                   placeholder="<?php 
                                   if ($currentLanguage === 'en') {
                                       echo '<EMAIL>';
                                   } else {
                                       echo $translationService->translateText('<EMAIL>', 'en', $currentLanguage) ?? '<EMAIL>';
                                   }
                                   ?>">
                        </div>
                        
                        <div class="space-y-4">
                            <label for="contactPhone" class="block text-lg font-medium text-white">
                                <?php 
                                if ($currentLanguage === 'en') {
                                    echo 'Phone Number';
                                } else {
                                    echo $translationService->translateText('Phone Number', 'en', $currentLanguage) ?? 'Phone Number';
                                }
                                ?> <span class="text-accent">*</span>
                            </label>
                            <div class="flex">
                                <select id="phoneCountryCode" name="phone_country_code" required 
                                        class="px-3 py-4 bg-white/10 border-2 border-white/20 border-r-0 rounded-l-xl text-white focus:border-accent focus:ring-2 focus:ring-accent focus:ring-opacity-50 transition-all duration-300 backdrop-blur-sm">
                                    <option value="+1" class="text-gray-900">+1 (US/CA)</option>
                                    <option value="+44" class="text-gray-900">+44 (GB)</option>
                                    <option value="+234" class="text-gray-900">+234 (NG)</option>
                                    <option value="+27" class="text-gray-900">+27 (ZA)</option>
                                    <option value="+91" class="text-gray-900">+91 (IN)</option>
                                    <option value="+63" class="text-gray-900">+63 (PH)</option>
                                    <option value="+55" class="text-gray-900">+55 (BR)</option>
                                    <option value="+49" class="text-gray-900">+49 (DE)</option>
                                    <option value="+33" class="text-gray-900">+33 (FR)</option>
                                    <option value="+61" class="text-gray-900">+61 (AU)</option>
                                </select>
                                <input type="tel" id="contactPhone" name="phone" required 
                                       value="<?php echo getFieldValue('phone', $existingRegistration); ?>"
                                       class="flex-1 px-4 py-4 bg-white/10 border-2 border-white/20 border-l-0 rounded-r-xl text-white placeholder-gray-300 focus:border-accent focus:ring-2 focus:ring-accent focus:ring-opacity-50 transition-all duration-300 backdrop-blur-sm"
                                       placeholder="<?php 
                                       if ($currentLanguage === 'en') {
                                           echo 'Phone number';
                                       } else {
                                           echo $translationService->translateText('Phone number', 'en', $currentLanguage) ?? 'Phone number';
                                       }
                                       ?>">
                            </div>
                        </div>
                    </div>

                    <!-- Network Website -->
                    <div class="space-y-4">
                        <label for="networkWebsite" class="block text-lg font-medium text-white">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Network Website';
                            } else {
                                echo $translationService->translateText('Network Website', 'en', $currentLanguage) ?? 'Network Website';
                            }
                            ?>
                        </label>
                        <input type="url" id="networkWebsite" name="organisation_website" 
                               class="w-full px-4 py-4 bg-white/10 border-2 border-white/20 rounded-xl text-white placeholder-gray-300 focus:border-accent focus:ring-2 focus:ring-accent focus:ring-opacity-50 transition-all duration-300 backdrop-blur-sm"
                               placeholder="<?php 
                               if ($currentLanguage === 'en') {
                                   echo 'https://www.network.com';
                               } else {
                                   echo $translationService->translateText('https://www.network.com', 'en', $currentLanguage) ?? 'https://www.network.com';
                               }
                               ?>">
                    </div>
                </div>

                <!-- Additional Information Section -->
                <div class="space-y-4">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-white">
                            <?php 
                            if ($currentLanguage === 'en') {
                                echo 'Additional Information';
                            } else {
                                echo $translationService->translateText('Additional Information', 'en', $currentLanguage) ?? 'Additional Information';
                            }
                            ?>
                        </h3>
                    </div>
                    
                    <label for="additionalComments" class="block text-lg font-medium text-white">
                        <?php 
                        if ($currentLanguage === 'en') {
                            echo 'Additional Comments or Special Requests';
                        } else {
                            echo $translationService->translateText('Additional Comments or Special Requests', 'en', $currentLanguage) ?? 'Additional Comments or Special Requests';
                        }
                        ?>
                    </label>
                    <textarea id="additionalComments" name="additional_comments" rows="6" 
                              class="w-full px-4 py-3 bg-white/10 border-2 border-white/20 rounded-xl text-white placeholder-gray-300 focus:border-accent focus:ring-2 focus:ring-accent focus:ring-opacity-50 transition-all duration-300 backdrop-blur-sm resize-none"
                              placeholder="<?php 
                              if ($currentLanguage === 'en') {
                                  echo 'Any additional information, special requirements, or comments about your network crusade plans';
                              } else {
                                  echo $translationService->translateText('Any additional information, special requirements, or comments about your network crusade plans', 'en', $currentLanguage) ?? 'Any additional information, special requirements, or comments about your network crusade plans';
                              }
                              ?>"><?php echo getFieldValue('additional_comments', $existingRegistration); ?></textarea>
                </div>

                <!-- Submit Button -->
                <div class="pt-6">
                    <button type="submit" 
                            class="w-full bg-gradient-to-r from-accent to-yellow-300 hover:from-yellow-300 hover:to-accent text-gray-900 font-bold py-4 px-8 rounded-xl transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 text-xl">
                        <?php 
                        if ($currentLanguage === 'en') {
                            echo 'Register Network';
                        } else {
                            echo $translationService->translateText('Register Network', 'en', $currentLanguage) ?? 'Register Network';
                        }
                        ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</section>

<!-- Custom Styles -->
<style>
/* Custom dropdown styles for network crusade types */
.network-crusade-types-dropdown {
    position: relative;
}

.network-crusade-types-options {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 9999;
    max-height: 15rem;
    overflow-y: auto;
}

.network-crusade-types-options.hidden {
    display: none;
}

.network-crusade-types-selected svg {
    transition: transform 0.2s ease-in-out;
}

.network-crusade-types-selected svg.rotate-180 {
    transform: rotate(180deg);
}
</style>

<!-- JavaScript -->
<script>
// Pass existing registration data to JavaScript
const existingRegistrationData = <?php echo $existingRegistration ? json_encode($existingRegistration) : 'null'; ?>;
const isUpdateMode = <?php echo $isUpdate ? 'true' : 'false'; ?>;

document.addEventListener('DOMContentLoaded', function() {
    let countriesData = [];
    let registrationData = {};
    
    // Initialize crusade types dropdown
    initializeCrusadeTypeDropdown();
    
    // Initialize cities autocomplete
    initializeCitiesAutocomplete();
    
    // Debug form submission
    const form = document.getElementById('networkRegistrationForm');
    console.log('Form element found:', form);
    
    if (form) {
        console.log('Adding submit event listener to form');
        
        form.addEventListener('submit', function(e) {
            console.log('=== FORM SUBMISSION EVENT FIRED ===');
            console.log('Event object:', e);
            console.log('Form action:', form.action);
            console.log('Form method:', form.method);
            
            // Check if form is valid according to HTML5 validation
            console.log('Form validity:', form.checkValidity());
            
            // Log all form data
            const formData = new FormData(form);
            console.log('Form data:');
            for (let [key, value] of formData.entries()) {
                console.log(`  ${key}: ${value}`);
            }
            
            console.log('Allowing form to submit...');
            // Don't prevent default - let it submit
        });
        
        // Also add a click listener to the submit button
        const submitButton = form.querySelector('button[type="submit"]');
        if (submitButton) {
            console.log('Submit button found:', submitButton);
            submitButton.addEventListener('click', function(e) {
                console.log('Submit button clicked');
            });
        }
    } else {
        console.error('Form not found!');
    }
    
    // Load countries and registration data with cross-platform support
    Promise.all([
        fetch('../data/countriesbkd.json').then(response => response.json()),
        fetch('data.json').then(response => response.json()).catch(() => ({ registrations: [] }))
    ])
    .then(([countriesResponse, registrations]) => {
        countriesData = countriesResponse.countries || [];
        registrationData = registrations;
        initializeCountryGuide();
        updateSelectedCountriesDisplay();
        
        // Pre-populate form if in update mode
        if (isUpdateMode && existingRegistrationData) {
            prePopulateNetworkForm();
        }
    })
    .catch(error => {
        console.error('Error loading data:', error);
        initializeCountryGuide(); // Initialize with empty data
    });
    
    function initializeCountryGuide() {
        const countriesGuide = document.getElementById('countriesGuide');
        
        // Get countries that already have registrations
        const selectedCountriesInRegistrations = new Set();
        if (registrationData.registrations) {
            registrationData.registrations.forEach(reg => {
                if (reg.selected_countries) {
                    const countries = reg.selected_countries.split(',').map(c => c.trim().toLowerCase());
                    countries.forEach(country => selectedCountriesInRegistrations.add(country));
                }
            });
        }
        
        // Group countries by continent
        const continents = {};
        countriesData.forEach(country => {
            const countryKey = country.name.toLowerCase().replace(/\s+/g, '-');
            if (!continents[country.continent]) {
                continents[country.continent] = [];
            }
            continents[country.continent].push({
                ...country,
                key: countryKey
    });
});

        // Create continent sections
        const continentOrder = ['Africa', 'Asia', 'Europe', 'North America', 'South America', 'Oceania'];
        const guideHTML = continentOrder.map(continent => {
            const countries = continents[continent] || [];
            const countriesHTML = countries.map(country => {
                const isSelected = window.selectedCountries.includes(country.key);
                const hasRegistration = selectedCountriesInRegistrations.has(country.key);
                
                let bgClass = isSelected ? 'bg-purple-100 border-purple-300' : 
                             hasRegistration ? 'bg-green-50 border-green-200' : 
                             'bg-orange-50 border-orange-200';
                let textClass = isSelected ? 'text-purple-800' : 
                               hasRegistration ? 'text-green-800' : 
                               'text-orange-800';
                let iconClass = isSelected ? 'text-purple-600' : 
                               hasRegistration ? 'text-green-600' : 
                               'text-orange-600';
                let icon = isSelected ? '✓' : 
                          hasRegistration ? '✓' : 
                          '!';
                
                return `
                    <div class="flex items-center py-1 px-2 rounded border ${bgClass} text-xs cursor-pointer hover:opacity-75 transition-opacity country-option" 
                         data-country="${country.key}" 
                         data-name="${country.name}"
                         data-code="${country.code}"
                         data-region="${country.region}"
                         role="button"
                         tabindex="0">
                        <span class="${textClass} truncate flex-1">${country.name}</span>
                        <span class="ml-1 ${iconClass}">${icon}</span>
                    </div>
                `;
            }).join('');
            
            const needsCoverage = countries.filter(country => 
                !selectedCountriesInRegistrations.has(country.key)
            ).length;
            
            return `
                <div class="bg-white rounded-lg border border-gray-200 p-4">
                    <h4 class="font-semibold text-gray-900 mb-2 flex items-center justify-between">
                        ${continent}
                        <span class="text-xs px-2 py-1 rounded-full ${needsCoverage > 0 ? 'bg-orange-100 text-orange-800' : 'bg-green-100 text-green-800'}">
                            ${needsCoverage > 0 ? `${needsCoverage} need coverage` : 'Complete coverage'}
                        </span>
                    </h4>
                    <div class="grid grid-cols-1 gap-1 max-h-32 overflow-y-auto">
                        ${countriesHTML}
                    </div>
                </div>
            `;
        }).join('');
        
        countriesGuide.innerHTML = guideHTML;
        
        // Remove old event listeners
        const oldOptions = document.querySelectorAll('.country-option');
        oldOptions.forEach(option => {
            option.replaceWith(option.cloneNode(true));
        });
        
        // Add new click handlers to country options with proper touch support
        document.querySelectorAll('.country-option').forEach(option => {
            // Handle touch events for mobile
            let touchStartTime = 0;
            let touchStartPosition = { x: 0, y: 0 };
            
            option.addEventListener('touchstart', function(e) {
                touchStartTime = Date.now();
                const touch = e.touches[0];
                touchStartPosition = { x: touch.clientX, y: touch.clientY };
            }, { passive: true });
            
            option.addEventListener('touchend', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const touchEndTime = Date.now();
                const touchDuration = touchEndTime - touchStartTime;
                
                // Only trigger if it's a quick tap (less than 500ms) and hasn't moved much
                if (touchDuration < 500) {
                    const touch = e.changedTouches[0];
                    const distance = Math.sqrt(
                        Math.pow(touch.clientX - touchStartPosition.x, 2) +
                        Math.pow(touch.clientY - touchStartPosition.y, 2)
                    );
                    
                    if (distance < 10) { // Less than 10px movement
                        toggleCountry(this);
                    }
                }
            });
            
            // Handle mouse clicks for desktop
            option.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                toggleCountry(this);
            });
            
            // Keyboard handler for accessibility
            option.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    e.stopPropagation();
                    toggleCountry(this);
                }
            });
            
            // Add touch-target class for better mobile interaction
            option.classList.add('touch-target');
            option.style.touchAction = 'manipulation'; // Disable double-tap zoom
        });
    }
    
    function toggleCountry(element) {
        const countryKey = element.dataset.country;
        const countryName = element.dataset.name;
        const countryCode = element.dataset.code;
        const countryRegion = element.dataset.region;
        
        const index = window.selectedCountries.indexOf(countryKey);
        if (index > -1) {
            window.selectedCountries.splice(index, 1);
    } else {
            window.selectedCountries.push(countryKey);
        }
        updateSelectedCountriesDisplay();
        initializeCountryGuide();
        
        // Provide feedback
        element.classList.add('scale-95');
        setTimeout(() => element.classList.remove('scale-95'), 100);
    }
    
    function updateSelectedCountriesDisplay() {
        const selectedCountriesDiv = document.getElementById('selected-countries');
        const countryPlaceholder = document.getElementById('country-placeholder');
        const selectedCountriesInput = document.getElementById('selected_countries');
        
        if (window.selectedCountries.length === 0) {
            selectedCountriesDiv.innerHTML = '<span id="country-placeholder" class="text-gray-500">Click countries in the guide above to select them</span>';
        } else {
            const selectedHTML = window.selectedCountries.map(countryKey => {
                const country = countriesData.find(c => 
                    c.name.toLowerCase().replace(/\s+/g, '-') === countryKey
                );
                
                return `
                    <div class="inline-flex items-center px-2 py-1 bg-purple-100 text-purple-800 text-sm rounded-md">
                        ${country?.name || countryKey}
                        <button type="button" class="ml-1 text-purple-600 hover:text-purple-800" onclick="removeCountry('${countryKey}', event)">×</button>
                    </div>
                `;
            }).join('');
            
            selectedCountriesDiv.innerHTML = selectedHTML;
        }
        
        // Update hidden input with selected countries
        selectedCountriesInput.value = window.selectedCountries.join(',');
    }
    
    // Global function to remove country
    window.removeCountry = function(countryKey, event) {
        event.preventDefault();
        event.stopPropagation();
        const index = window.selectedCountries.indexOf(countryKey);
        if (index > -1) {
            window.selectedCountries.splice(index, 1);
            updateSelectedCountriesDisplay();
            initializeCountryGuide();
        }
    };
    
    // Make functions and variables globally accessible
    window.selectedCountries = [];
    window.updateSelectedCountriesDisplay = updateSelectedCountriesDisplay;
    window.initializeCountryGuide = initializeCountryGuide;

    // Function to pre-populate network form with existing data
    function prePopulateNetworkForm() {
        if (!existingRegistrationData) return;
        
        console.log('Pre-populating network form with:', existingRegistrationData);
        
        // 1. Pre-populate countries
        if (existingRegistrationData.selected_countries) {
            const countries = existingRegistrationData.selected_countries.split(',');
            window.selectedCountries = countries.map(country => country.trim());
            updateSelectedCountriesDisplay();
            initializeCountryGuide();
        }
        
        // 2. Pre-populate crusade types
        if (existingRegistrationData.crusade_types && Array.isArray(existingRegistrationData.crusade_types)) {
            existingRegistrationData.crusade_types.forEach(type => {
                const checkbox = document.querySelector(`.network-crusade-type-checkbox[value="${type}"]`);
                if (checkbox) {
                    checkbox.checked = true;
                }
            });
            
            // Update the display after checking boxes
            setTimeout(() => {
                if (typeof window.updateSelectedCrusadeTypes === 'function') {
                    window.updateSelectedCrusadeTypes();
                }
                
                // Show "Other" field if "other" is selected
                const otherCheckbox = document.querySelector('.network-crusade-type-checkbox[value="other"]');
                if (otherCheckbox && otherCheckbox.checked) {
                    const otherField = document.getElementById('networkOtherCrusadeTypesField');
                    if (otherField) {
                        otherField.classList.remove('hidden');
                    }
                }
            }, 100);
        }
        
        // 3. Pre-populate expected attendance
        if (existingRegistrationData.expected_attendance) {
            const attendanceInput = document.getElementById('expectedAttendance');
            if (attendanceInput) {
                attendanceInput.value = existingRegistrationData.expected_attendance;
            }
        }
        
        // 4. Pre-populate network type
        if (existingRegistrationData.network_type) {
            const networkTypeSelect = document.getElementById('networkType');
            if (networkTypeSelect) {
                networkTypeSelect.value = existingRegistrationData.network_type;
                // Trigger change event to show/hide other field
                networkTypeSelect.dispatchEvent(new Event('change'));
            }
        }
        
        // 5. Pre-populate other network type if applicable
        if (existingRegistrationData.other_network_type) {
            const otherNetworkInput = document.getElementById('otherNetworkType');
            if (otherNetworkInput) {
                otherNetworkInput.value = existingRegistrationData.other_network_type;
            }
        }
        
        // 6. Pre-populate cities data
        if (existingRegistrationData.selected_cities_data && Array.isArray(existingRegistrationData.selected_cities_data)) {
            setTimeout(() => {
                const citiesDiv = document.getElementById('selected-cities');
                const citiesInput = document.getElementById('selected_cities_data');
                
                if (citiesDiv && citiesInput) {
                    const citiesHTML = existingRegistrationData.selected_cities_data.map((city, index) => `
                        <div class="inline-flex items-center px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full border border-green-200">
                            <span class="font-medium">${city.name}</span>
                            <span class="text-xs text-green-600 ml-1">${city.country}</span>
                            <button type="button" class="ml-2 text-green-600 hover:text-green-800 font-bold" onclick="removeCity(${index})">×</button>
                        </div>
                    `).join('');
                    
                    citiesDiv.innerHTML = citiesHTML;
                    citiesInput.value = JSON.stringify(existingRegistrationData.selected_cities_data);
                    
                    // Store in the global selectedCities array if it exists
                    if (typeof window.selectedCities !== 'undefined') {
                        window.selectedCities = [...existingRegistrationData.selected_cities_data];
                    }
                }
            }, 500);
        }
    }

    // Handle network type visibility
    const networkTypeSelect = document.getElementById('networkType');
    const otherNetworkContainer = document.getElementById('otherNetworkTypeField');

    if (networkTypeSelect && otherNetworkContainer) {
        networkTypeSelect.addEventListener('change', function() {
            if (this.value === 'Other') {
                otherNetworkContainer.classList.remove('hidden');
            } else {
                otherNetworkContainer.classList.add('hidden');
                document.getElementById('otherNetworkType').value = '';
            }
        });
    }

    // Handle crusade types with "Other" option
    function setupCrusadeTypes() {
        const crusadeTypesContainer = document.getElementById('networkCrusadeTypes');
        const otherSpecifyContainer = document.getElementById('networkOtherCrusadeTypesField');
        const otherCheckbox = document.querySelector('input[value="other"]');
        
        if (crusadeTypesContainer && otherSpecifyContainer && otherCheckbox) {
            // Toggle "Other" option visibility
            otherCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    otherSpecifyContainer.classList.remove('hidden');
        } else {
                    otherSpecifyContainer.classList.add('hidden');
                    document.getElementById('networkOtherCrusadeTypes').value = '';
        }
            });
        }
    }

    setupCrusadeTypes();

    // Phone number formatting - Create hidden input for full phone if needed
    const phoneInput = document.getElementById('contactPhone');
    const phoneCountryCode = document.getElementById('phoneCountryCode');
    
    // Note: We don't need to create a hidden full phone input since the handler 
    // combines phone_country_code + phone on the server side
});

function initializeCrusadeTypeDropdown() {
    // Get dropdown elements
    const dropdown = document.querySelector('.network-crusade-types-dropdown');
    const selected = dropdown.querySelector('.network-crusade-types-selected');
    const options = dropdown.querySelector('.network-crusade-types-options');
    const placeholder = dropdown.querySelector('.network-crusade-types-placeholder');
    const checkboxes = options.querySelectorAll('input[type="checkbox"]');
    const hiddenSelect = document.getElementById('networkCrusadeTypes');
    const otherCrusadeTypesField = document.getElementById('networkOtherCrusadeTypesField');
    
    // Toggle dropdown on click
    selected.addEventListener('click', function() {
        options.classList.toggle('hidden');
        selected.querySelector('svg').classList.toggle('rotate-180');
    });
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!dropdown.contains(e.target)) {
            options.classList.add('hidden');
            selected.querySelector('svg').classList.remove('rotate-180');
        }
    });
    
    // Handle checkbox changes
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            window.updateSelectedCrusadeTypes();
            
            // Show/hide "Other" field if needed
            if (checkbox.value === 'other') {
                otherCrusadeTypesField.style.display = checkbox.checked ? 'block' : 'none';
            }
        });
    });
    
    // Update selected crusade types display - make it globally accessible
    window.updateSelectedCrusadeTypes = function() {
        const selectedTypes = Array.from(checkboxes)
            .filter(cb => cb.checked)
            .map(cb => {
                const label = cb.closest('label').querySelector('span').textContent;
                return label;
            });
        
        // Update hidden select for form submission
        Array.from(hiddenSelect.options).forEach(option => {
            option.selected = Array.from(checkboxes)
                .filter(cb => cb.checked)
                .map(cb => cb.value)
                .includes(option.value);
        });
        
        // Update display
        if (selectedTypes.length === 0) {
            placeholder.textContent = '<?php 
            if ($currentLanguage === 'en') {
                echo 'Select crusade type(s) - You can select multiple';
            } else {
                echo $translationService->translateText('Select crusade type(s) - You can select multiple', 'en', $currentLanguage) ?? 'Select crusade type(s) - You can select multiple';
            }
            ?>';
            placeholder.classList.remove('hidden');
        } else {
            placeholder.textContent = selectedTypes.join(', ');
            placeholder.classList.remove('text-gray-300');
            placeholder.classList.add('text-white');
        }
    };
}

// Cities Autocomplete Functionality
function initializeCitiesAutocomplete() {
    const citiesInput = document.getElementById('crusade_cities');
    const autocompleteDiv = document.getElementById('cities-autocomplete');
    const suggestionsDiv = document.getElementById('cities-suggestions');
    const selectedCitiesDiv = document.getElementById('selected-cities');
    const selectedCitiesInput = document.getElementById('selected_cities_data');
    const citiesPlaceholder = document.getElementById('cities-placeholder');
    
    let selectedCities = [];
    let searchTimeout;
    let currentFocus = -1;
    
    // Debounced search function
    function debounceSearch(query, delay = 300) {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            searchCities(query);
        }, delay);
    }
    
    // Search cities via API
    async function searchCities(query) {
        if (query.length < 2) {
            hideAutocomplete();
            return;
        }
        
        try {
            const response = await fetch(`../api/cities-search.php?q=${encodeURIComponent(query)}&maxRows=8`);
            const data = await response.json();
            
            if (data.cities && data.cities.length > 0) {
                displaySuggestions(data.cities);
            } else {
                hideSuggestions();
            }
        } catch (error) {
            console.error('Cities search error:', error);
            hideSuggestions();
        }
    }
    
    // Display search suggestions
    function displaySuggestions(cities) {
        suggestionsDiv.innerHTML = '';
        currentFocus = -1;
        
        // Add header to make it clear these are suggestions
        const headerDiv = document.createElement('div');
        headerDiv.className = 'px-3 py-2 text-xs font-medium text-gray-500 bg-gray-50 border-b border-gray-200';
        headerDiv.innerHTML = 'Select a city from the suggestions below:';
        suggestionsDiv.appendChild(headerDiv);
        
        cities.forEach((city, index) => {
            const suggestionDiv = document.createElement('div');
            suggestionDiv.className = 'city-suggestion px-3 py-2 hover:bg-green-50 cursor-pointer text-sm border-b border-gray-100 last:border-b-0 transition-colors duration-150';
            suggestionDiv.innerHTML = `
                <div class="flex items-center justify-between">
                    <div>
                        <div class="font-medium text-gray-900">${city.name}</div>
                        <div class="text-gray-600 text-xs">${city.admin1 ? city.admin1 + ', ' : ''}${city.country}</div>
                    </div>
                    <div class="text-green-500 opacity-0 group-hover:opacity-100">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                    </div>
                </div>
            `;
            
            suggestionDiv.addEventListener('click', () => {
                selectCity(city);
            });
            
            suggestionDiv.addEventListener('mouseenter', () => {
                currentFocus = index;
                updateFocus();
            });
            
            suggestionsDiv.appendChild(suggestionDiv);
        });
        
        showAutocomplete();
    }
    
    // Select a city from suggestions
    function selectCity(city) {
        // Check if city is already selected
        const existingCity = selectedCities.find(c => 
            c.name.toLowerCase() === city.name.toLowerCase() && 
            c.countryCode === city.countryCode
        );
        
        if (existingCity) {
            hideAutocomplete();
            return;
        }
        
        // Add city to selected list
        selectedCities.push({
            name: city.name,
            country: city.country,
            countryCode: city.countryCode,
            admin1: city.admin1 || '',
            latitude: city.latitude || '',
            longitude: city.longitude || ''
        });
        
        updateSelectedCitiesDisplay();
        clearCurrentInput();
        hideAutocomplete();
        
        // Focus back to input for more selections
        citiesInput.focus();
    }
    
    // Update selected cities display
    function updateSelectedCitiesDisplay() {
        if (selectedCities.length === 0) {
            selectedCitiesDiv.innerHTML = '<span id="cities-placeholder" class="text-gray-500">Please select at least one city (required)</span>';
        } else {
            const citiesHTML = selectedCities.map((city, index) => `
                <div class="inline-flex items-center px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full border border-green-200">
                    <span class="font-medium">${city.name}</span>
                    <span class="text-green-600 ml-1 text-xs">${city.country}</span>
                    <button type="button" 
                            class="ml-2 text-green-600 hover:text-green-800 font-bold focus:outline-none"
                            onclick="removeCity(${index})"
                            title="Remove ${city.name}">×</button>
                </div>
            `).join('');
            
            selectedCitiesDiv.innerHTML = citiesHTML;
        }
        
        // Update hidden input with cities data
        selectedCitiesInput.value = JSON.stringify(selectedCities);
        
        // Auto-add countries if they're not already selected
        autoAddCountriesFromCities();
    }
    
    // Auto-add countries based on selected cities
    function autoAddCountriesFromCities() {
        selectedCities.forEach(city => {
            const countryKey = city.country.toLowerCase().replace(/\s+/g, '-');
            if (!window.selectedCountries.includes(countryKey)) {
                window.selectedCountries.push(countryKey);
            }
        });
        
        // Update countries display if the function exists
        if (typeof updateSelectedCountriesDisplay === 'function') {
            updateSelectedCountriesDisplay();
        }
        if (typeof initializeCountryGuide === 'function') {
            initializeCountryGuide();
        }
    }
    
    // Clear current input (last part after comma)
    function clearCurrentInput() {
        const value = citiesInput.value;
        const lastCommaIndex = value.lastIndexOf(',');
        
        if (lastCommaIndex !== -1) {
            citiesInput.value = value.substring(0, lastCommaIndex + 1) + ' ';
        } else {
            citiesInput.value = '';
        }
    }
    
    // Get current search term (last part after comma)
    function getCurrentSearchTerm() {
        const value = citiesInput.value.trim();
        const parts = value.split(',');
        return parts[parts.length - 1].trim();
    }
    
    // Show autocomplete dropdown
    function showAutocomplete() {
        autocompleteDiv.classList.remove('hidden');
    }
    
    // Hide autocomplete dropdown
    function hideAutocomplete() {
        autocompleteDiv.classList.add('hidden');
        currentFocus = -1;
    }
    
    // Hide suggestions but keep dropdown open
    function hideSuggestions() {
        suggestionsDiv.innerHTML = `
            <div class="px-3 py-2 text-center">
                <div class="text-gray-500 text-sm mb-1">No cities found</div>
                <div class="text-xs text-gray-400">Try searching with different spelling or check the city name</div>
            </div>
        `;
        showAutocomplete();
    }
    
    // Update focus highlighting
    function updateFocus() {
        const suggestions = suggestionsDiv.querySelectorAll('.city-suggestion');
        suggestions.forEach((suggestion, index) => {
            if (index === currentFocus) {
                suggestion.classList.add('bg-gray-100');
            } else {
                suggestion.classList.remove('bg-gray-100');
            }
        });
    }
    
    // Handle keyboard navigation
    function handleKeyDown(e) {
        const suggestions = suggestionsDiv.querySelectorAll('.city-suggestion');
        
        if (suggestions.length === 0) return;
        
        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                currentFocus = Math.min(currentFocus + 1, suggestions.length - 1);
                updateFocus();
                break;
                
            case 'ArrowUp':
                e.preventDefault();
                currentFocus = Math.max(currentFocus - 1, -1);
                updateFocus();
                break;
                
            case 'Enter':
                e.preventDefault();
                if (currentFocus >= 0 && currentFocus < suggestions.length) {
                    suggestions[currentFocus].click();
                }
                break;
                
            case 'Escape':
                hideAutocomplete();
                break;
        }
    }
    
    // Event listeners
    citiesInput.addEventListener('input', (e) => {
        const searchTerm = getCurrentSearchTerm();
        if (searchTerm.length >= 2) {
            debounceSearch(searchTerm);
        } else {
            hideAutocomplete();
        }
    });
    
    citiesInput.addEventListener('keydown', handleKeyDown);
    
    citiesInput.addEventListener('focus', () => {
        const searchTerm = getCurrentSearchTerm();
        if (searchTerm.length >= 2) {
            debounceSearch(searchTerm, 100);
        }
    });
    
    // Hide autocomplete when clicking outside
    document.addEventListener('click', (e) => {
        if (!citiesInput.contains(e.target) && !autocompleteDiv.contains(e.target)) {
            hideAutocomplete();
        }
    });
    
    // Global function to remove city
    window.removeCity = function(index) {
        selectedCities.splice(index, 1);
        updateSelectedCitiesDisplay();
    };
}
</script>

<?php include 'includes/footer.php'; ?>