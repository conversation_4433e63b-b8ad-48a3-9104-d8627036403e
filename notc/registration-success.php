<?php 
session_start();
include 'includes/header.php'; 

// Get error parameter first
$error = $_GET['error'] ?? '';

// Check if registration was successful or if there's an error to display
if (!isset($_SESSION['registration_success']) || !$_SESSION['registration_success']) {
    // If there's no session but there's an error parameter, allow access to show the error
    if (empty($error)) {
        header('Location: index.php');
        exit;
    }
}

// Get registration data from session
$type = $_SESSION['registration_type'] ?? 'registration';
$registration_data = $_SESSION['registration_data'] ?? [];
$id = $registration_data['id'] ?? 'unknown';
$name = $registration_data['display_name'] ?? '';

// Check if this is an error page
$isError = !empty($error);

// Clear session data after use (only if not showing an error)
if (empty($error)) {
    unset($_SESSION['registration_success']);
    unset($_SESSION['registration_type']);
    unset($_SESSION['registration_data']);
}
?>

<!-- Success/Error Page Section -->
<section class="min-h-screen text-white relative z-10 overflow-x-hidden py-20" style="background: linear-gradient(135deg, <?php echo $isError ? '#dc2626 0%, #991b1b 100%' : '#2563eb 0%, #1e40af 100%'; ?>);">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 relative w-full text-center">
        
        <?php if ($isError): ?>
            <!-- Error Icon -->
            <div class="mb-8">
                <div class="mx-auto w-24 h-24 bg-red-500 rounded-full flex items-center justify-center">
                    <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </div>
            </div>

            <!-- Error Message -->
            <h1 class="text-4xl sm:text-5xl md:text-6xl font-bold mb-6 drop-shadow-lg">
                Registration Error
            </h1>
            
            <p class="text-xl md:text-2xl drop-shadow-lg text-gray-100 max-w-3xl mx-auto mb-8">
                We encountered an issue processing your registration. Please review the error below and try again.
            </p>

            <!-- Error Details -->
            <div class="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-2xl mb-8 max-w-md mx-auto">
                <h3 class="text-xl font-semibold mb-2 text-red-300">Error Details</h3>
                <p class="text-gray-200 text-sm break-words"><?php echo htmlspecialchars($error); ?></p>
            </div>

            <!-- Retry Actions -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button onclick="history.back()" 
                       class="bg-gradient-to-r from-red-500 to-red-400 hover:from-red-400 hover:to-red-500 text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105">
                    Try Again
                </button>
                
                <a href="../notc" 
                   class="bg-white/10 border-2 border-white/20 text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 hover:bg-white/20">
                    Return to Home
                </a>
            </div>

        <?php else: ?>
            <!-- Success Icon -->
            <div class="mb-8">
                <div class="mx-auto w-24 h-24 bg-green-500 rounded-full flex items-center justify-center">
                    <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
            </div>

            <!-- Success Message -->
            <h1 class="text-4xl sm:text-5xl md:text-6xl font-bold mb-6 drop-shadow-lg">
                Registration Successful!
            </h1>
            
            <p class="text-xl md:text-2xl drop-shadow-lg text-gray-100 max-w-3xl mx-auto mb-8">
                <?php 
                if ($type === 'church') {
                    $church_type = $registration_data['church_type'] ?? '';
                    switch ($church_type) {
                        case 'zone':
                            echo 'Your zone registration has been approved and activated successfully. You can now coordinate crusades across your zone.';
                            break;
                        case 'group':
                            echo 'Your group registration has been approved and activated successfully. You can now organize crusades with your group.';
                            break;
                        case 'church':
                            echo 'Your church registration has been approved and activated successfully. You can now participate in the global crusade movement.';
                            break;
                        default:
                            echo 'Your church registration has been approved and activated successfully.';
                    }
                } elseif ($type === 'network') {
                    echo 'Your network registration has been approved and activated successfully.';
                } else {
                    echo 'Your registration has been approved and activated successfully.';
                }
                ?>
            </p>

            <!-- Registration Details -->
            <div class="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-2xl mb-8 max-w-lg mx-auto">
                <h3 class="text-xl font-semibold mb-4">Registration Details</h3>
                <div class="space-y-3 text-left">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-300">Type:</span>
                        <span class="text-white font-medium"><?php 
                            if ($type === 'church' && isset($registration_data['church_type'])) {
                                echo ucfirst(htmlspecialchars($registration_data['church_type']));
                            } else {
                                echo ucfirst(htmlspecialchars($type)); 
                            }
                        ?></span>
                    </div>
                    
                    <?php if ($name): ?>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-300">Name:</span>
                        <span class="text-white font-medium"><?php echo htmlspecialchars($name); ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (isset($registration_data['zone']) && $registration_data['zone']): ?>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-300">Zone:</span>
                        <span class="text-white font-medium"><?php echo htmlspecialchars($registration_data['zone']); ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (isset($registration_data['countries_display']) && $registration_data['countries_display']): ?>
                    <div class="flex justify-between items-start">
                        <span class="text-gray-300">Countries:</span>
                        <span class="text-white font-medium text-right text-sm"><?php echo htmlspecialchars($registration_data['countries_display']); ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (isset($registration_data['cities_display']) && $registration_data['cities_display'] && $registration_data['cities_display'] !== 'No specific cities selected'): ?>
                    <div class="flex justify-between items-start">
                        <span class="text-gray-300">Cities:</span>
                        <span class="text-white font-medium text-right text-sm"><?php echo htmlspecialchars($registration_data['cities_display']); ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-gray-300">Status:</span>
                        <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full font-medium">✓ Approved & Active</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-gray-300">Approved:</span>
                        <span class="text-white text-sm"><?php 
                            echo isset($registration_data['registration_date_formatted']) 
                                ? htmlspecialchars($registration_data['registration_date_formatted'])
                                : date('M j, Y g:i A'); 
                        ?></span>
                    </div>
                </div>
            </div>

            <!-- Next Steps -->
            <div class="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-8 shadow-2xl mb-8">
                <h3 class="text-2xl font-semibold mb-6">Next Steps</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-left">
                    <div class="flex items-start">
                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3 mt-1 flex-shrink-0">
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-semibold mb-2 text-green-300">Registration Complete</h4>
                            <p class="text-gray-300 text-sm">Your registration is now active and ready to go.</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="w-8 h-8 bg-accent rounded-full flex items-center justify-center mr-3 mt-1 flex-shrink-0">
                            <span class="text-gray-900 font-bold text-sm">2</span>
                        </div>
                        <div>
                            <h4 class="font-semibold mb-2">Visit RhapsodyCrusades.org</h4>
                            <p class="text-gray-300 text-sm">Access all crusade resources, materials, and support tools at <a href="https://rhapsodycrusades.org" target="_blank" class="text-accent hover:text-yellow-300 underline">rhapsodycrusades.org</a></p>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="w-8 h-8 bg-accent rounded-full flex items-center justify-center mr-3 mt-1 flex-shrink-0">
                            <span class="text-gray-900 font-bold text-sm">3</span>
                        </div>
                        <div>
                            <h4 class="font-semibold mb-2">Begin Your Impact</h4>
                            <p class="text-gray-300 text-sm">Start planning and executing your crusades with our full support and guidance.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="https://rhapsodycrusades.org" target="_blank"
                   class="bg-gradient-to-r from-accent to-yellow-300 hover:from-yellow-300 hover:to-accent text-gray-900 font-bold py-4 px-8 rounded-xl transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105">
                    Visit RhapsodyCrusades.org
                </a>
                
                <a href="../notc" 
                   class="bg-white/10 border-2 border-white/20 text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 hover:bg-white/20">
                    Return to Home
                </a>
            </div>

            <!-- Support Contact -->
            <div class="mt-12 text-center">
                <p class="text-gray-300 mb-2">Need help or have questions?</p>
                <div class="flex flex-col sm:flex-row items-center justify-center gap-4">
                    <p class="text-accent">Contact us at <a href="https://rhapsodycrusades.org/contact" target="_blank" class="underline hover:text-yellow-300">rhapsodycrusades.org/contact</a></p>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>

<?php include 'includes/footer.php'; ?>