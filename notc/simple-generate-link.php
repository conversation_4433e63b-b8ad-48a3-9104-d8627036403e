<?php
header('Content-Type: application/json');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method not allowed');
    }

    $registrationId = $_POST['registration_id'] ?? '';
    $registrationType = $_POST['registration_type'] ?? '';

    if (empty($registrationId) || empty($registrationType)) {
        throw new Exception('Missing parameters');
    }

    // Simple token generation without using the UpdateTokenManager
    $token = bin2hex(random_bytes(32));
    $updateLink = 'https://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/update-dashboard.php?token=' . $token;

    echo json_encode([
        'success' => true,
        'token' => $token,
        'update_link' => $updateLink,
        'is_new' => true,
        'debug' => [
            'registration_id' => $registrationId,
            'registration_type' => $registrationType,
            'method' => $_SERVER['REQUEST_METHOD']
        ]
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}
?>