<?php
// Test the AJAX call to generate-update-link.php

echo "Testing AJAX call to generate-update-link.php...\n\n";

// Prepare POST data
$postData = http_build_query([
    'registration_id' => 'church_4',
    'registration_type' => 'church'
]);

// Set up context for POST request
$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => "Content-Type: application/x-www-form-urlencoded\r\n" .
                   "Content-Length: " . strlen($postData) . "\r\n",
        'content' => $postData
    ]
]);

// Make the request
$url = 'http://localhost/crusades/notc/generate-update-link.php';
echo "Making request to: $url\n";
echo "POST data: $postData\n\n";

$response = file_get_contents($url, false, $context);

if ($response === false) {
    echo "Failed to make request\n";
    print_r(error_get_last());
} else {
    echo "Response received:\n";
    echo $response . "\n\n";
    
    // Try to decode JSON
    $decoded = json_decode($response, true);
    if ($decoded) {
        echo "Decoded JSON:\n";
        print_r($decoded);
    } else {
        echo "Failed to decode JSON response\n";
    }
}
?>