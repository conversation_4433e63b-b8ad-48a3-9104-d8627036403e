<?php
// Test file writing capabilities

echo "Testing file write capabilities...\n";

$tokensFile = __DIR__ . '/data/update_tokens.json';
echo "Tokens file path: $tokensFile\n";
echo "File exists: " . (file_exists($tokensFile) ? 'yes' : 'no') . "\n";
echo "Directory writable: " . (is_writable(dirname($tokensFile)) ? 'yes' : 'no') . "\n";
echo "File writable: " . (file_exists($tokensFile) && is_writable($tokensFile) ? 'yes' : 'no') . "\n";

// Test writing to the file
$testData = [
    'tokens' => [
        'test_token' => [
            'token' => 'test_token',
            'registration_id' => 'test_id',
            'registration_type' => 'test_type',
            'created_at' => date('Y-m-d H:i:s'),
            'expires_at' => date('Y-m-d H:i:s', strtotime('+1 year')),
            'used_count' => 0,
            'last_used' => null,
            'is_active' => true
        ]
    ],
    'created_at' => date('Y-m-d H:i:s'),
    'last_updated' => date('Y-m-d H:i:s')
];

echo "\nAttempting to write test data...\n";
$result = file_put_contents($tokensFile, json_encode($testData, JSON_PRETTY_PRINT));

if ($result === false) {
    echo "FAILED to write to file\n";
    $error = error_get_last();
    echo "Last error: " . print_r($error, true) . "\n";
} else {
    echo "SUCCESS: Wrote $result bytes to file\n";
}

// Test reading the file back
echo "\nAttempting to read file back...\n";
$content = file_get_contents($tokensFile);
if ($content === false) {
    echo "FAILED to read file\n";
} else {
    echo "SUCCESS: Read " . strlen($content) . " bytes from file\n";
    $decoded = json_decode($content, true);
    if ($decoded) {
        echo "JSON decode successful\n";
        echo "Tokens count: " . count($decoded['tokens']) . "\n";
    } else {
        echo "JSON decode failed\n";
    }
}

// Test UpdateTokenManager
echo "\nTesting UpdateTokenManager...\n";
try {
    require_once 'includes/UpdateTokenManager.php';
    $tokenManager = new UpdateTokenManager();
    echo "UpdateTokenManager created successfully\n";
    
    $token = $tokenManager->generateUpdateToken('test_church_1', 'church');
    if ($token) {
        echo "Token generated successfully: $token\n";
    } else {
        echo "Token generation failed\n";
    }
    
} catch (Exception $e) {
    echo "UpdateTokenManager error: " . $e->getMessage() . "\n";
}
?>