<?php
// Simulate POST request to test generate-update-link.php
$_SERVER['REQUEST_METHOD'] = 'POST';
$_POST['registration_id'] = 'church_4';
$_POST['registration_type'] = 'church';
$_SERVER['HTTP_HOST'] = 'localhost';
$_SERVER['REQUEST_URI'] = '/crusades/notc/test-generate-link.php';

// Capture output
ob_start();
include 'generate-update-link.php';
$output = ob_get_clean();

echo "Output from generate-update-link.php:\n";
echo $output . "\n";

// Parse JSON response
$response = json_decode($output, true);
if ($response) {
    echo "\nParsed response:\n";
    print_r($response);
} else {
    echo "\nFailed to parse JSON response\n";
}
?>