<?php
require_once 'includes/UpdateTokenManager.php';

try {
    echo "Testing UpdateTokenManager...\n";
    
    $tokenManager = new UpdateTokenManager();
    echo "✓ UpdateTokenManager created successfully\n";
    
    // Test generating a token
    $token = $tokenManager->generateUpdateToken('church_4', 'church');
    
    if ($token) {
        echo "✓ Token generated successfully: $token\n";
        
        // Test validating the token
        $tokenData = $tokenManager->validateToken($token);
        if ($tokenData) {
            echo "✓ Token validation successful\n";
            echo "  Registration ID: " . $tokenData['registration_id'] . "\n";
            echo "  Registration Type: " . $tokenData['registration_type'] . "\n";
        } else {
            echo "✗ Token validation failed\n";
        }
        
        // Test getting registration by token
        $registration = $tokenManager->getRegistrationByToken($token);
        if ($registration) {
            echo "✓ Registration retrieved successfully\n";
            echo "  Display Name: " . ($registration['display_name'] ?? 'N/A') . "\n";
        } else {
            echo "✗ Failed to retrieve registration\n";
        }
        
    } else {
        echo "✗ Failed to generate token\n";
    }
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>