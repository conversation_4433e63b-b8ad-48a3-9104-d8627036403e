<?php
header('Content-Type: text/plain');

echo "Testing UpdateTokenManager via web...\n";

try {
    require_once 'includes/UpdateTokenManager.php';
    echo "UpdateTokenManager included successfully\n";
    
    $tokenManager = new UpdateTokenManager();
    echo "UpdateTokenManager created successfully\n";
    
    $token = $tokenManager->generateUpdateToken('web_test_church', 'church');
    if ($token) {
        echo "Token generated successfully: $token\n";
        
        // Test validation
        $tokenData = $tokenManager->validateToken($token);
        if ($tokenData) {
            echo "Token validation successful\n";
            echo "Registration ID: " . $tokenData['registration_id'] . "\n";
        } else {
            echo "Token validation failed\n";
        }
        
    } else {
        echo "Token generation failed\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>