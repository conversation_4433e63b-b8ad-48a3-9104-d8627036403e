<?php
require_once 'includes/UpdateTokenManager.php';

// Get token from URL
$token = $_GET['token'] ?? '';
$justUpdated = isset($_GET['updated']) && $_GET['updated'] === '1';

if (empty($token)) {
    header('Location: index.php');
    exit();
}

// Initialize token manager
$tokenManager = new UpdateTokenManager();

// Validate token and get registration data
$registration = $tokenManager->getRegistrationByToken($token);

if (!$registration) {
    $error = "Invalid or expired update link. Please contact the administrator for a new link.";
} else {
    // Mark token as used (for statistics)
    $tokenManager->useToken($token);
}

include 'includes/header.php';
?>

<section class="min-h-screen bg-gray-50 relative z-10 py-12 sm:py-20 pt-16 sm:pt-20 md:pt-24">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <?php if (isset($error)): ?>
        <!-- Error Message -->
        <div class="text-center mb-8">
            <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                <div class="flex items-center justify-center mb-4">
                    <svg class="w-12 h-12 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <h2 class="text-xl font-bold text-red-800 mb-2">Access Denied</h2>
                <p class="text-red-700"><?php echo htmlspecialchars($error); ?></p>
                <div class="mt-4">
                    <a href="index.php" class="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Go to Homepage
                    </a>
                </div>
            </div>
        </div>
        
        <?php else: ?>
        
        <?php if ($justUpdated): ?>
        <!-- Success Message -->
        <div class="text-center mb-8">
            <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                <div class="flex items-center justify-center mb-4">
                    <svg class="w-12 h-12 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h2 class="text-xl font-bold text-green-800 mb-2">Registration Updated Successfully!</h2>
                <p class="text-green-700">Your registration has been updated and the changes are now reflected in the system.</p>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl sm:text-4xl font-bold mb-4 text-gray-900">
                Update Your Registration
            </h1>
            <p class="text-lg text-gray-600 mb-2">
                Welcome back, <strong><?php echo htmlspecialchars($registration['display_name'] ?? 'Organization'); ?></strong>
            </p>
            <p class="text-sm text-gray-500">
                Registration Type: <?php echo ucfirst($registration['registration_type'] ?? 'Unknown'); ?>
            </p>
            <div class="w-20 h-1 bg-primary mx-auto mt-4"></div>
        </div>

        <!-- Current Registration Summary -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Current Registration Details
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Basic Information -->
                <div>
                    <h3 class="font-medium text-gray-900 mb-3">Basic Information</h3>
                    <div class="space-y-2 text-sm">
                        <?php if ($registration['registration_type'] === 'church'): ?>
                            <div><span class="font-medium">Zone:</span> <?php echo htmlspecialchars($registration['zone'] ?? 'N/A'); ?></div>
                            <?php if (!empty($registration['church_group_name'])): ?>
                                <div><span class="font-medium">Group:</span> <?php echo htmlspecialchars($registration['church_group_name']); ?></div>
                            <?php endif; ?>
                            <?php if (!empty($registration['church_name'])): ?>
                                <div><span class="font-medium">Church:</span> <?php echo htmlspecialchars($registration['church_name']); ?></div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div><span class="font-medium">Network:</span> <?php echo htmlspecialchars($registration['network_name'] ?? 'N/A'); ?></div>
                            <div><span class="font-medium">Contact Person:</span> <?php echo htmlspecialchars($registration['full_name'] ?? 'N/A'); ?></div>
                        <?php endif; ?>
                        <div><span class="font-medium">Email:</span> <?php echo htmlspecialchars($registration['email'] ?? 'N/A'); ?></div>
                        <div><span class="font-medium">Phone:</span> <?php echo htmlspecialchars($registration['phone_display'] ?? 'N/A'); ?></div>
                    </div>
                </div>
                
                <!-- Crusade Information -->
                <div>
                    <h3 class="font-medium text-gray-900 mb-3">Crusade Information</h3>
                    <div class="space-y-2 text-sm">
                        <div><span class="font-medium">Countries:</span> <?php echo htmlspecialchars($registration['countries_display'] ?? 'N/A'); ?></div>
                        <div><span class="font-medium">Number of Crusades:</span> <?php echo htmlspecialchars($registration['number_of_crusades'] ?? 'N/A'); ?></div>
                        <?php if (!empty($registration['expected_attendance'])): ?>
                            <div><span class="font-medium">Expected Attendance:</span> <?php echo htmlspecialchars($registration['expected_attendance']); ?></div>
                        <?php endif; ?>
                        <?php if (!empty($registration['crusade_types_display'])): ?>
                            <div><span class="font-medium">Crusade Types:</span> <?php echo htmlspecialchars($registration['crusade_types_display']); ?></div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <?php if (!empty($registration['additional_comments'])): ?>
            <div class="mt-4 pt-4 border-t border-gray-200">
                <h3 class="font-medium text-gray-900 mb-2">Additional Comments</h3>
                <p class="text-sm text-gray-600"><?php echo nl2br(htmlspecialchars($registration['additional_comments'])); ?></p>
            </div>
            <?php endif; ?>
            
            <div class="mt-4 pt-4 border-t border-gray-200 text-xs text-gray-500">
                <div class="flex justify-between">
                    <span>Registered: <?php echo htmlspecialchars($registration['registration_date_formatted'] ?? 'N/A'); ?></span>
                    <?php if (!empty($registration['updated_at'])): ?>
                        <span>Last Updated: <?php echo date('M j, Y g:i A', strtotime($registration['updated_at'])); ?></span>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="<?php echo $registration['registration_type'] === 'church' ? 'register-church.php' : 'register-network.php'; ?>?token=<?php echo urlencode($token); ?>" 
               class="inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 transition-colors">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Update Registration
            </a>
            
            <?php 
            // Determine the appropriate dashboard URL based on registration type
            $dashboardUrl = 'public-dashboard.php'; // fallback
            $dashboardText = 'View Dashboard';
            
            if ($registration['registration_type'] === 'church' && !empty($registration['zone'])) {
                $dashboardUrl = 'zone-dashboard.php?zone=' . urlencode($registration['zone']);
                $dashboardText = 'View Zone Dashboard';
            } elseif ($registration['registration_type'] === 'network' && !empty($registration['network_name'])) {
                $dashboardUrl = 'zone-dashboard.php?network=' . urlencode($registration['network_name']);
                $dashboardText = 'View Network Dashboard';
            }
            ?>
            <a href="<?php echo $dashboardUrl; ?>" 
               class="inline-flex items-center justify-center px-6 py-3 bg-gray-600 text-white font-medium rounded-md hover:bg-gray-700 transition-colors">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                <?php echo $dashboardText; ?>
            </a>
        </div>

        <!-- Security Notice -->
        <div class="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div class="flex items-start">
                <svg class="w-5 h-5 text-yellow-600 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <div>
                    <h3 class="text-sm font-medium text-yellow-800">Security Notice</h3>
                    <p class="text-sm text-yellow-700 mt-1">
                        This update link is unique to your registration and should not be shared with others. 
                        If you believe this link has been compromised, please contact the administrator immediately.
                    </p>
                </div>
            </div>
        </div>
        
        <?php endif; ?>
    </div>
</section>

<?php include 'includes/footer.php'; ?>