<?php
header('Content-Type: text/plain');

echo "Web-based UpdateTokenManager test\n";
echo "==================================\n\n";

try {
    require_once 'includes/UpdateTokenManager.php';
    
    echo "UpdateTokenManager loaded successfully\n";
    
    $tokenManager = new UpdateTokenManager();
    echo "UpdateTokenManager instantiated\n";
    
    // Test with a registration that exists
    $registrationId = 'church_4';
    $registrationType = 'church';
    
    echo "Testing token generation for ID: $registrationId, Type: $registrationType\n";
    
    $token = $tokenManager->generateUpdateToken($registrationId, $registrationType);
    
    if ($token) {
        echo "SUCCESS: Token generated: $token\n";
        
        // Test validation
        $validation = $tokenManager->validateToken($token);
        if ($validation) {
            echo "SUCCESS: Token validation passed\n";
        } else {
            echo "ERROR: Token validation failed\n";
        }
        
    } else {
        echo "ERROR: Token generation failed\n";
    }
    
} catch (Exception $e) {
    echo "EXCEPTION: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\nPHP Error Log (last few lines):\n";
echo "================================\n";
$errorLog = error_get_last();
if ($errorLog) {
    print_r($errorLog);
} else {
    echo "No recent errors\n";
}
?>