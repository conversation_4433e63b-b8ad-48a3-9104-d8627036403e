<?php
require_once 'includes/config.php';
require_once 'includes/languages.php';
require_once 'includes/TranslationService.php';

// Get translation service and current language
$translationService = TranslationService::getInstance();
$currentLanguage = Language::getCurrentLanguage();

// Set page title and description with translations
$pageTitle = $translationService->translateText('Sponsor Our Crusades', 'en', $currentLanguage);
$pageDescription = $translationService->translateText('Your generous support helps us reach millions with the Gospel', 'en', $currentLanguage);

include 'includes/header.php';
?>

<?php
// Set page title for translation
$page_title = __('sponsor_page_title', 'Sponsor Our Crusades');
$page_description = __('sponsor_page_meta_description', 'Support our mission by sponsoring crusades and helping spread the Gospel worldwide.');

// Set meta tags for SEO
$meta_tags = [
    'title' => $page_title,
    'description' => $page_description,
    'og:title' => $page_title,
    'og:description' => $page_description,
    'og:type' => 'website',
    'og:url' => (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]"
];
?>

<!-- Sponsor Page Main Content -->
<div class="py-16 sm:py-20 md:py-24 bg-white relative overflow-hidden">
    <!-- Language Switcher -->
    <div class="absolute top-4 right-4 z-20">
        <?php include 'includes/language_switcher.php'; ?>
    </div>
    
    <!-- Page Header -->
    <div class="max-w-6xl mx-auto px-4 sm:px-6 mb-12 sm:mb-16 md:mb-20">
        <div class="text-center">
            <h1 class="text-3xl sm:text-4xl md:text-5xl font-bold text-gray-900 mb-4"><?php echo $pageTitle; ?></h1>
            <div class="mb-6">
                <p class="text-base sm:text-lg md:text-xl text-gray-600">
                    <?php echo $pageDescription; ?>
                </p>
            </div>
            <div class="w-16 h-px bg-gradient-to-r from-primary via-accent to-gold mx-auto"></div>
        </div>
    </div>

    <!-- Floating decorative empty boxes -->
    <div class="absolute inset-0 pointer-events-none">
        <!-- Top left floating box -->
        <div class="absolute top-8 sm:top-16 left-2 sm:left-8 transform -rotate-12 opacity-5 sm:opacity-10">
            <div class="bg-primary w-10 h-8 sm:w-16 sm:h-12 rounded border sm:border-2 border-primary"></div>
        </div>

        <!-- Top right floating box -->
        <div class="absolute top-20 sm:top-32 right-4 sm:right-12 transform rotate-6 opacity-5 sm:opacity-10">
            <div class="bg-accent w-12 h-9 sm:w-20 sm:h-14 rounded border sm:border-2 border-gold"></div>
        </div>

        <!-- Middle left floating box -->
        <div class="absolute top-40 sm:top-64 left-6 sm:left-16 transform rotate-3 opacity-5 sm:opacity-10">
            <div class="bg-gold w-8 h-6 sm:w-14 sm:h-10 rounded border sm:border-2 border-accent"></div>
        </div>

        <!-- Middle right floating box -->
        <div class="absolute top-32 sm:top-48 right-2 sm:right-8 transform -rotate-6 opacity-5 sm:opacity-10">
            <div class="bg-blue-400 w-10 h-8 sm:w-18 sm:h-12 rounded border sm:border-2 border-primary"></div>
        </div>

        <!-- Bottom left floating box -->
        <div class="absolute bottom-20 sm:bottom-32 left-1 sm:left-4 transform rotate-12 opacity-5 sm:opacity-10">
            <div class="bg-primary w-10 h-6 sm:w-16 sm:h-10 rounded border sm:border-2 border-primaryDark"></div>
        </div>

        <!-- Bottom right floating box -->
        <div class="absolute bottom-8 sm:bottom-16 right-6 sm:right-16 transform -rotate-3 opacity-5 sm:opacity-10">
            <div class="bg-accent w-12 h-9 sm:w-20 sm:h-14 rounded border sm:border-2 border-gold"></div>
        </div>

        <!-- Additional middle boxes for larger screens -->
        <div class="hidden lg:block absolute top-20 left-1/4 transform rotate-6 opacity-10">
            <div class="bg-blue-300 w-16 h-12 rounded-lg border-2 border-primary"></div>
        </div>

        <div class="hidden lg:block absolute bottom-24 right-1/4 transform -rotate-9 opacity-10">
            <div class="bg-gold w-14 h-10 rounded-lg border-2 border-accent"></div>
        </div>

        <!-- Mobile-only smaller decorative boxes -->
        <div class="block sm:hidden absolute top-48 left-1/2 transform -translate-x-1/2 rotate-45 opacity-5">
            <div class="bg-blue-300 w-6 h-6 rounded border border-primary"></div>
        </div>

        <div class="block sm:hidden absolute bottom-40 left-1/3 transform rotate-12 opacity-5">
            <div class="bg-gold w-7 h-5 rounded border border-accent"></div>
        </div>
    </div>

    <div class="max-w-6xl mx-auto px-4 sm:px-6 relative z-10">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-12 md:gap-16 items-start lg:items-center">
            <!-- Left Content -->
            <div class="space-y-6 sm:space-y-8 order-1 lg:order-1">
                <h3 class="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900"><?php echo $translationService->translateText('Join Us in Reaching the World', 'en', $currentLanguage); ?></h3>
                <div class="give-description-container">
                    <p class="text-gray-600 leading-relaxed text-sm sm:text-base">
                        <?php echo $translationService->translateText('Your generous support enables us to reach millions with the Gospel through our global crusades. Every contribution makes a significant impact in transforming lives and communities around the world.', 'en', $currentLanguage); ?>
                    </p>
                </div>

                <div class="space-y-3 sm:space-y-4">
                    <div class="flex items-start text-gray-600">
                        <svg class="w-5 h-5 text-primary mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span class="text-sm sm:text-base"><?php echo $translationService->translateText('Sponsor crusades in regions with limited resources', 'en', $currentLanguage); ?></span>
                    </div>
                    <div class="flex items-start text-gray-600">
                        <svg class="w-5 h-5 text-primary mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span class="text-sm sm:text-base"><?php echo $translationService->translateText('Fund outreach materials and equipment for evangelism', 'en', $currentLanguage); ?></span>
                    </div>
                    <div class="flex items-start text-gray-600">
                        <svg class="w-5 h-5 text-primary mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span class="text-sm sm:text-base"><?php echo $translationService->translateText('Enable distribution of Bibles and spiritual materials', 'en', $currentLanguage); ?></span>
                    </div>
                </div>
            </div>

            <!-- Right Payment Form -->
            <div class="bg-gray-50 p-4 sm:p-6 md:p-8 border border-gray-200 rounded-lg sm:rounded-none order-2 lg:order-2">
                <h4 class="text-lg sm:text-xl md:text-2xl font-light text-gray-900 mb-4 sm:mb-6"><?php echo $translationService->translateText('Select Donation Amount', 'en', $currentLanguage); ?></h4>

                <!-- Payment Method Selection -->
                <div class="mb-4 sm:mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3"><?php echo $translationService->translateText('Choose Payment Method', 'en', $currentLanguage); ?></label>
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        <label class="flex flex-col items-center p-3 sm:p-4 border border-gray-200 rounded-lg cursor-pointer hover:border-primary transition-colors duration-200 payment-method-option">
                            <input type="radio" name="payment_method" value="card_payment" checked class="sr-only">
                            <svg class="w-6 h-6 sm:w-8 sm:h-8 mb-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                            </svg>
                            <span class="text-xs sm:text-sm font-medium text-gray-900"><?php echo $translationService->translateText('Card Payment', 'en', $currentLanguage); ?></span>
                        </label>
                        <label class="flex flex-col items-center p-3 sm:p-4 border border-gray-200 rounded-lg cursor-pointer hover:border-primary transition-colors duration-200 payment-method-option">
                            <input type="radio" name="payment_method" value="espees" class="sr-only">
                            <img src="https://web.espees.org/espeesCoinOption5.png" alt="ESPEES" class="w-6 h-6 sm:w-8 sm:h-8 mb-2">
                            <span class="text-xs sm:text-sm font-medium text-gray-900"><?php echo 'ESPEES'; // Acronym, no translation needed ?></span>
                        </label>
                    </div>
                </div>

                <!-- Card Payment Section (shown by default) -->
                <div id="card-payment-section">
                    <!-- Preset Amounts -->
                    <div class="grid grid-cols-3 gap-2 sm:gap-3 mb-4 sm:mb-6">
                        <button onclick="selectAmount(100)" class="amount-btn p-2 sm:p-3 md:p-4 border border-gray-200 text-center hover:border-primary hover:bg-primary hover:text-white transition-all duration-200 rounded-lg">
                            <div class="text-sm sm:text-base md:text-lg font-medium">$100</div>
                        </button>
                        <button onclick="selectAmount(500)" class="amount-btn p-2 sm:p-3 md:p-4 border border-gray-200 text-center hover:border-primary hover:bg-primary hover:text-white transition-all duration-200 rounded-lg">
                            <div class="text-sm sm:text-base md:text-lg font-medium">$500</div>
                        </button>
                        <button onclick="selectAmount(1000)" class="amount-btn p-2 sm:p-3 md:p-4 border border-gray-200 text-center hover:border-primary hover:bg-primary hover:text-white transition-all duration-200 rounded-lg">
                            <div class="text-sm sm:text-base md:text-lg font-medium">$1000</div>
                        </button>
                    </div>

                    <!-- Custom Amount -->
                    <div class="mb-4 sm:mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <?php echo $translationService->translateText('Custom Amount', 'en', $currentLanguage); ?>
                        </label>
                        <div class="relative">
                            <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                            <input type="number" id="customAmount" 
                                   placeholder="<?php echo $translationService->translateText('Enter amount', 'en', $currentLanguage); ?>"
                                   class="w-full pl-8 pr-4 py-2 sm:py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200 rounded-lg"
                                   min="1" step="0.01">
                        </div>
                    </div>
                </div>

                <!-- Donor Information (shown only for card payment) -->
                <div id="donor-info-section" class="space-y-3 sm:space-y-4 mb-4 sm:mb-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <?php echo $translationService->translateText('Full Name', 'en', $currentLanguage); ?>
                        </label>
                        <input type="text" id="donorName"
                               class="w-full px-4 py-2 sm:py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200 rounded-lg"
                               placeholder="<?php echo $translationService->translateText('Enter your full name', 'en', $currentLanguage); ?>">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <?php echo $translationService->translateText('Email Address', 'en', $currentLanguage); ?>
                        </label>
                        <input type="email" id="donorEmail"
                               class="w-full px-4 py-2 sm:py-3 border border-gray-200 focus:border-primary focus:outline-none transition-colors duration-200 rounded-lg"
                               placeholder="<?php echo $translationService->translateText('Enter your email address', 'en', $currentLanguage); ?>">
                    </div>
                </div>

                <!-- Card Payment Button -->
                <button type="button" id="donate-button"
                        class="w-full py-3 sm:py-4 bg-gradient-to-r from-primary to-primaryDark text-white font-medium hover:from-primaryDark hover:to-primary transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed border border-accent rounded-lg">
                    <?php echo $translationService->translateText('Donate Now', 'en', $currentLanguage); ?>
                </button>

                <!-- ESPEES Code Display (hidden by default) -->
                <div id="espees-donation-section" class="hidden">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                        <h4 class="text-lg font-medium text-blue-900 mb-3">
                            <?php echo $translationService->translateText('ESPEES Payment Code', 'en', $currentLanguage); ?>
                        </h4>
                        <div class="bg-white border border-blue-300 rounded-lg p-4 text-center">
                            <span class="text-2xl font-bold text-blue-700 tracking-wider"><?php echo 'RORETC'; // Code, no translation needed ?></span>
                        </div>
                        <p class="text-sm text-blue-700 mt-3">
                            <?php echo $translationService->translateText('Use this code for your ESPEES payment. No verification required.', 'en', $currentLanguage); ?>
                        </p>
                    </div>
                </div>

                <!-- Security Notice -->
                <div id="security-notice" class="mt-4 sm:mt-6">
                    <p class="text-xs text-gray-500 text-center flex items-center justify-center">
                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                        <?php echo $translationService->translateText('Secure payment processing. Your information is protected.', 'en', $currentLanguage); ?>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Stripe.js -->
<script src="https://js.stripe.com/v3/"></script>
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<!-- Donation JS -->
<script src="assets/js/donation.js"></script>
<script>
    // Make translations available globally
    window.translations = {
        payment_complete_giving: '<?php echo $translationService->translateText('Complete Your Giving', 'en', $currentLanguage); ?>',
        payment_giving_amount: '<?php echo $translationService->translateText('Giving Amount:', 'en', $currentLanguage); ?>',
        payment_donor: '<?php echo $translationService->translateText('Donor:', 'en', $currentLanguage); ?>',
        payment_complete_giving_btn: '<?php echo $translationService->translateText('Complete Giving', 'en', $currentLanguage); ?>',
        payment_processing: '<?php echo $translationService->translateText('Processing...', 'en', $currentLanguage); ?>',
        payment_secure_stripe: '<?php echo $translationService->translateText('Secure payment powered by Stripe', 'en', $currentLanguage); ?>',
        success_thank_you: '<?php echo $translationService->translateText('Thank You!', 'en', $currentLanguage); ?>',
        success_message: '<?php echo $translationService->translateText('Your generous giving has been successfully processed. You will receive a receipt via email shortly.', 'en', $currentLanguage); ?>',
        success_continue: '<?php echo $translationService->translateText('Continue', 'en', $currentLanguage); ?>',
        copyCode: '<?php echo $translationService->translateText('Copy Code', 'en', $currentLanguage); ?>',
        codeCopied: '<?php echo $translationService->translateText('Code Copied!', 'en', $currentLanguage); ?>',
        errorOccurred: '<?php echo $translationService->translateText('An error occurred. Please try again.', 'en', $currentLanguage); ?>',
        enterValidAmount: '<?php echo $translationService->translateText('Please enter a valid amount.', 'en', $currentLanguage); ?>',
        enterName: '<?php echo $translationService->translateText('Please enter your name.', 'en', $currentLanguage); ?>',
        enterValidEmail: '<?php echo $translationService->translateText('Please enter a valid email address.', 'en', $currentLanguage); ?>',
        donationProcessing: '<?php echo $translationService->translateText('Processing...', 'en', $currentLanguage); ?>',
        donationSuccess: '<?php echo $translationService->translateText('Thank you for your donation!', 'en', $currentLanguage); ?>',
        donateNow: '<?php echo $translationService->translateText('Donate Now', 'en', $currentLanguage); ?>'
    };

    // Handle payment method selection for donations
    document.addEventListener('DOMContentLoaded', function() {
        const paymentMethodRadios = document.querySelectorAll('input[name="payment_method"]');
        const paymentMethodOptions = document.querySelectorAll('.payment-method-option');
        const cardPaymentSection = document.getElementById('card-payment-section');
        const donorInfoSection = document.getElementById('donor-info-section');
        const donateButton = document.getElementById('donate-button');
        const espeesSection = document.getElementById('espees-donation-section');
        const securityNotice = document.getElementById('security-notice');

        // Update visual appearance of payment method selection
        function updatePaymentMethodUI() {
            paymentMethodOptions.forEach(option => {
                const radio = option.querySelector('input[type="radio"]');
                if (radio.checked) {
                    option.classList.add('border-primary', 'bg-blue-50');
                    option.classList.remove('border-gray-200');
                } else {
                    option.classList.remove('border-primary', 'bg-blue-50');
                    option.classList.add('border-gray-200');
                }
            });
        }

        paymentMethodRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                updatePaymentMethodUI();
                
                if (this.value === 'card_payment') {
                    cardPaymentSection.classList.remove('hidden');
                    donorInfoSection.classList.remove('hidden');
                    donateButton.classList.remove('hidden');
                    espeesSection.classList.add('hidden');
                    securityNotice.classList.remove('hidden');
                } else if (this.value === 'espees') {
                    cardPaymentSection.classList.add('hidden');
                    donorInfoSection.classList.add('hidden');
                    donateButton.classList.add('hidden');
                    espeesSection.classList.remove('hidden');
                    securityNotice.classList.add('hidden');
                }
            });
        });

        // Initialize the UI
        updatePaymentMethodUI();

        // Add donate button event listener
        const donateBtn = document.getElementById('donate-button');
        if (donateBtn) {
            donateBtn.addEventListener('click', function() {
                if (typeof processDonation === 'function') {
                    processDonation();
                } else {
                    console.error('processDonation function not found. Make sure donation.js is loaded.');
                    // Fallback error message to user
                    if (typeof Swal !== 'undefined') {
                        Swal.fire({
                            title: 'Error',
                            text: 'Payment system not ready. Please refresh the page and try again.',
                            icon: 'error',
                            confirmButtonColor: '#2563eb'
                        });
                    } else {
                        alert('Payment system not ready. Please refresh the page and try again.');
                    }
                }
            });
        }
    });

    // Select amount function
    function selectAmount(amount) {
        document.getElementById('customAmount').value = amount;
        document.querySelectorAll('.amount-btn').forEach(btn => {
            btn.classList.remove('bg-primary', 'text-white', 'border-primary');
            btn.classList.add('border-gray-200');
        });
        event.currentTarget.classList.add('bg-primary', 'text-white', 'border-primary');
        event.currentTarget.classList.remove('border-gray-200');
    }
</script>

<?php include 'includes/footer.php'; ?>