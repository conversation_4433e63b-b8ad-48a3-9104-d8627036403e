<?php

function transformMigrateData() {
    $inputFile = 'migrate.json';
    $outputFile = 'migrate_transformed.json';
    
    // Read the TSV data from migrate.json
    $content = file_get_contents($inputFile);
    $lines = explode("\n", trim($content));
    
    // Skip the header line and process data lines
    $registrations = [];
    $headers = str_getcsv($lines[0], "\t", '"', "\\");
    
    for ($i = 1; $i < count($lines); $i++) {
        if (empty(trim($lines[$i]))) continue;
        
        $data = str_getcsv($lines[$i], "\t", '"', "\\");
        if (count($data) < count($headers)) continue;
        
        // Create mapping from TSV columns to values
        $row = array_combine($headers, $data);
        
        // Parse name
        $nameParts = explode(' ', trim($row['Name']), 2);
        $firstName = $nameParts[0] ?? '';
        $lastName = $nameParts[1] ?? '';
        
        // All entries in migrate.json are network registrations
        $registrationType = strtolower(trim($row['Registration Type eg. Individual, Church/Group/Zone, Organisation/Network']));
        $type = 'network'; // All are networks
        $churchType = 'network'; // All are network type
        
        // Keep the church_type as 'network' for all entries since they're all networks
        
        // Parse crusade types
        $crusadeTypeMap = [
            'OPEN' => ['mega'],
            'Mega Crusade' => ['mega'],
            'City Crusade' => ['mega'],
            'Crusade' => ['street'],
            'ONLINE' => ['online', 'social-media']
        ];
        
        $crusadeTypeRaw = trim($row['Crusade Type']);
        $crusadeTypes = $crusadeTypeMap[$crusadeTypeRaw] ?? ['street'];
        
        // Parse expected attendance
        $attendanceRaw = trim($row['Expected Attendance']);
        $expectedAttendance = '500-1000'; // default
        $expectedAttendanceNumeric = 500;
        
        if (preg_match('/(\d+)/', $attendanceRaw, $matches)) {
            $num = intval($matches[1]);
            if ($num >= 10000) {
                $expectedAttendance = '10000+';
                $expectedAttendanceNumeric = 10000;
            } elseif ($num >= 5000) {
                $expectedAttendance = '5000-10000';
                $expectedAttendanceNumeric = 5000;
            } elseif ($num >= 2500) {
                $expectedAttendance = '2500-5000';
                $expectedAttendanceNumeric = 2500;
            } elseif ($num >= 1000) {
                $expectedAttendance = '1000-2500';
                $expectedAttendanceNumeric = 1000;
            } elseif ($num >= 500) {
                $expectedAttendance = '500-1000';
                $expectedAttendanceNumeric = 500;
            } elseif ($num >= 250) {
                $expectedAttendance = '250-500';
                $expectedAttendanceNumeric = 250;
            } else {
                $expectedAttendance = '100-250';
                $expectedAttendanceNumeric = 100;
            }
        }
        
        // Parse countries
        $country = trim($row['Country']);
        $selectedCountries = strtolower(str_replace(' ', '-', $country));
        
        // Parse cities
        $city = trim($row[' City']);
        $selectedCitiesData = [];
        if (!empty($city) && $city !== 'TBC') {
            // Try to extract city info - this is simplified
            $selectedCitiesData[] = [
                'name' => $city,
                'country' => $country,
                'countryCode' => getCountryCode($country),
                'admin1' => '',
                'latitude' => 0,
                'longitude' => 0
            ];
        }
        
        // Generate unique ID for network
        $id = 'network_' . uniqid();
        
        // Current timestamp
        $currentTime = time();
        $registrationDate = date('Y-m-d H:i:s', $currentTime);
        
        // Build registration object
        $registration = [
            'id' => $id,
            'type' => $type,
            'registration_type' => $type,
            'church_type' => $churchType,
            'zone' => trim($row['Zone']) ?: 'General Zone',
            'church_group_name' => trim($row['Group']) ?: '',
            'church_name' => trim($row['Church']) ?: '',
            'organization_name' => trim($row['Organization / Network Name']) ?: '',
            'first_name' => $firstName,
            'last_name' => $lastName,
            'email' => trim($row['Email']) ?: '',
            'phone_country_code' => '+234', // Default to Nigeria
            'phone' => trim($row['Phone']) ?: '',
            'selected_countries' => $selectedCountries,
            'crusade_types' => $crusadeTypes,
            'other_crusade_types' => '',
            'selected_cities_data' => $selectedCitiesData,
            'number_of_crusades' => trim($row['Number of Crusades']) ?: '1',
            'expected_attendance' => $expectedAttendance,
            'additional_comments' => trim($row['Comments or Special Requests']) . ' ' . trim($row['Additional Comments']),
            'status' => 'approved',
            'registration_date' => $registrationDate,
            'registration_timestamp' => $currentTime,
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Migration Script',
            'form_source' => 'network_migration_from_tsv',
            'submission_method' => 'MIGRATION',
            // Computed fields
            'full_name' => trim($firstName . ' ' . $lastName),
            'full_phone' => '+234' . trim($row['Phone']),
            'phone_display' => '+234' . trim($row['Phone']),
            'registration_date_formatted' => date('F j, Y g:i A', $currentTime),
            'registration_date_iso' => date('c', $currentTime),
            'display_name' => getDisplayName($churchType, trim($row['Zone']), trim($row['Group']), trim($row['Church']), trim($row['Organization / Network Name'])),
            'primary_identifier' => getPrimaryIdentifier($churchType, trim($row['Zone']), trim($row['Group']), trim($row['Church']), trim($row['Organization / Network Name'])),
            'selected_countries_list' => [$selectedCountries],
            'countries_count' => 1,
            'countries_display' => "1 countries: " . $selectedCountries,
            'cities_count' => count($selectedCitiesData),
            'cities_display' => count($selectedCitiesData) > 0 ? count($selectedCitiesData) . " cities: " . $city : "No specific cities selected",
            'cities_list' => count($selectedCitiesData) > 0 ? [$city] : [],
            'cities_by_country' => count($selectedCitiesData) > 0 ? [$country => [$city]] : [],
            'crusade_types_string' => implode(', ', $crusadeTypes),
            'crusade_types_display' => implode(', ', $crusadeTypes),
            'expected_attendance_numeric' => $expectedAttendanceNumeric,
            'server_info' => [
                'server_name' => 'rhapsodycrusades.org',
                'request_uri' => '/migration/transform.php',
                'http_host' => 'rhapsodycrusades.org'
            ]
        ];
        
        $registrations[] = $registration;
    }
    
    // Create final structure
    $output = [
        'registrations' => $registrations
    ];
    
    // Write to output file
    file_put_contents($outputFile, json_encode($output, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    
    echo "Transformation complete. " . count($registrations) . " registrations processed.\n";
    echo "Output written to: " . $outputFile . "\n";
    
    return $output;
}

function getCountryCode($country) {
    $countryCodes = [
        'Nigeria' => 'NG',
        'Ecuador' => 'EC',
        'Venezuela' => 'VE',
        'Honduras' => 'HN',
        'Guatemala' => 'GT',
        'Colombia' => 'CO',
        'Nicaragua' => 'NI',
        'Burundi' => 'BI',
        'Gabon' => 'GA',
        'Costa Rica' => 'CR',
        'Pakistan' => 'PK',
        'Cambodia' => 'KH',
        'Afghanistan' => 'AF',
        'Albania' => 'AL',
        'Algeria' => 'DZ'
    ];
    
    return $countryCodes[$country] ?? 'XX';
}

function getDisplayName($churchType, $zone, $group, $church, $organization) {
    if (!empty($organization)) {
        return $organization;
    } elseif (!empty($church)) {
        return $group ? "$group - $church" : $church;
    } elseif (!empty($group)) {
        return $group;
    } else {
        return $zone ?: 'General Zone';
    }
}

function getPrimaryIdentifier($churchType, $zone, $group, $church, $organization) {
    $displayName = getDisplayName($churchType, $zone, $group, $church, $organization);
    
    switch ($churchType) {
        case 'network':
            return "Network: $displayName" . ($zone ? " (Zone: $zone)" : '');
        case 'church':
            return "Church: $displayName" . ($zone ? " (Zone: $zone)" : '');
        case 'group':
            return "Group: $displayName" . ($zone ? " (Zone: $zone)" : '');
        case 'zone':
        default:
            return "Zone: $displayName";
    }
}

// Run the transformation
if (php_sapi_name() === 'cli' || !empty($_GET['run'])) {
    transformMigrateData();
}

?>