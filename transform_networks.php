<?php
/**
 * Transform networks CSV data to match NOTC data.json structure
 */

function transformNetworksData($csvFile) {
    $registrations = [];
    $totalRegistrations = 0;
    $networkRegistrations = 0;
    
    // Read TSV file
    if (($handle = fopen($csvFile, "r")) !== FALSE) {
        $headers = fgetcsv($handle, 0, "\t", '"', "\\"); // Tab-separated
        
        while (($data = fgetcsv($handle, 0, "\t", '"', "\\")) !== FALSE) {
            if (count($data) < 10) continue; // Skip incomplete rows
            
            // Skip header row or empty rows
            if (strpos($data[0], 'Registration Type') !== false || empty($data[1])) {
                continue;
            }
            
            // Only process Network registrations
            if (strtolower(trim($data[0])) !== 'network') {
                continue;
            }
            
            $totalRegistrations++;
            $networkRegistrations++;
            
            // Generate unique ID
            $id = 'net_' . uniqid();
            
            // Parse name
            $fullName = trim($data[1]);
            $nameParts = explode(' ', $fullName, 2);
            $firstName = $nameParts[0] ?? '';
            $lastName = $nameParts[1] ?? '';
            
            // Parse organization/network name
            $networkName = trim($data[2]) ?: $fullName;
            
            // Parse contact info
            $email = trim($data[4]) ?: '';
            $phone = trim($data[5]) ?: '';
            
            // Parse location data
            $country = strtolower(str_replace(' ', '-', trim($data[12])));
            $city = trim($data[13]);
            
            // Parse crusade info
            $crusadeType = strtolower(str_replace(' ', '-', trim($data[14])));
            $numberOfCrusades = trim($data[15]) ?: '1';
            $venue = trim($data[16]);
            $expectedAttendance = trim($data[17]);
            $comments = trim($data[18]);
            $additionalComments = trim($data[19]) ?: '';
            
            // Map crusade types to standard format
            $crusadeTypes = [];
            switch ($crusadeType) {
                case 'mega':
                case 'mega-crusade':
                    $crusadeTypes[] = 'mega';
                    break;
                case 'open':
                case 'crusade':
                    $crusadeTypes[] = 'street';
                    break;
                case 'online':
                    $crusadeTypes[] = 'online';
                    break;
                case 'city-crusade':
                    $crusadeTypes[] = 'mega';
                    break;
                default:
                    $crusadeTypes[] = 'other';
            }
            
            // Parse expected attendance to numeric
            $expectedAttendanceNumeric = 0;
            if (preg_match('/(\d+)/', $expectedAttendance, $matches)) {
                $expectedAttendanceNumeric = (int)$matches[1];
            }
            
            // Create cities data structure
            $selectedCitiesData = [];
            if (!empty($city) && $city !== 'TBC') {
                $selectedCitiesData[] = [
                    'name' => $city,
                    'country' => ucwords(str_replace('-', ' ', $country)),
                    'countryCode' => getCountryCode($country),
                    'admin1' => '',
                    'latitude' => 0.0,
                    'longitude' => 0.0
                ];
            }
            
            // Create registration entry
            $registration = [
                'id' => $id,
                'type' => 'network',
                'registration_type' => 'network',
                'network_type' => determineNetworkType($networkName),
                'other_network_type' => '',
                'network_name' => $networkName,
                'network_website' => '',
                'first_name' => $firstName,
                'last_name' => $lastName,
                'email' => $email,
                'phone_country_code' => '+1', // Default, would need parsing
                'phone' => $phone,
                'selected_countries' => $country,
                'crusade_types' => $crusadeTypes,
                'other_crusade_types' => '',
                'selected_cities_data' => $selectedCitiesData,
                'number_of_crusades' => $numberOfCrusades,
                'expected_attendance' => $expectedAttendance,
                'additional_comments' => $comments . ' ' . $additionalComments,
                'status' => 'approved',
                'registration_date' => date('Y-m-d H:i:s'),
                'registration_timestamp' => time(),
                'ip_address' => '127.0.0.1',
                'user_agent' => 'Network Import Script',
                'form_source' => 'network_registration_form',
                'submission_method' => 'IMPORT',
                'full_name' => $fullName,
                'full_phone' => $phone,
                'phone_display' => $phone,
                'registration_date_formatted' => date('F j, Y g:i A'),
                'registration_date_iso' => date('c'),
                'display_name' => $networkName,
                'primary_identifier' => 'Network: ' . $networkName,
                'selected_countries_list' => [$country],
                'countries_count' => 1,
                'countries_display' => "1 countries: $country",
                'cities_count' => count($selectedCitiesData),
                'cities_display' => count($selectedCitiesData) . ' cities selected',
                'cities_list' => array_column($selectedCitiesData, 'name'),
                'cities_by_country' => [
                    [
                        'country' => ucwords(str_replace('-', ' ', $country)),
                        'cities' => array_column($selectedCitiesData, 'name')
                    ]
                ],
                'crusade_types_string' => implode(', ', $crusadeTypes),
                'crusade_types_display' => implode(', ', $crusadeTypes),
                'expected_attendance_numeric' => $expectedAttendanceNumeric,
                'website_display' => '',
                'server_info' => [
                    'server_name' => 'rhapsodycrusades.org',
                    'request_uri' => '/notc/register-network-handler.php',
                    'http_host' => 'rhapsodycrusades.org'
                ]
            ];
            
            $registrations[] = $registration;
        }
        
        fclose($handle);
    }
    
    // Create final structure
    $result = [
        'registrations' => $registrations,
        'total_registrations' => $totalRegistrations,
        'church_registrations' => 0,
        'network_registrations' => $networkRegistrations,
        'last_updated' => date('c')
    ];
    
    return $result;
}

function determineNetworkType($networkName) {
    $name = strtolower($networkName);
    
    if (strpos($name, 'rim') !== false) return 'RIM';
    if (strpos($name, 'reon') !== false) return 'REON';
    if (strpos($name, 'tni') !== false) return 'TNI';
    if (strpos($name, 'ministry') !== false) return 'ministry';
    if (strpos($name, 'church') !== false) return 'church';
    
    return 'other';
}

function getCountryCode($country) {
    $countryCodes = [
        'afghanistan' => 'AF',
        'albania' => 'AL',
        'algeria' => 'DZ',
        'andorra' => 'AD',
        'angola' => 'AO',
        'argentina' => 'AR',
        'armenia' => 'AM',
        'australia' => 'AU',
        'austria' => 'AT',
        'azerbaijan' => 'AZ',
        'bahamas' => 'BS',
        'bahrain' => 'BH',
        'bangladesh' => 'BD',
        'barbados' => 'BB',
        'belarus' => 'BY',
        'belgium' => 'BE',
        'belize' => 'BZ',
        'benin' => 'BJ',
        'bhutan' => 'BT',
        'bolivia' => 'BO',
        'bosnia-and-herzegovina' => 'BA',
        'botswana' => 'BW',
        'brazil' => 'BR',
        'brunei' => 'BN',
        'bulgaria' => 'BG',
        'burkina-faso' => 'BF',
        'burundi' => 'BI',
        'cambodia' => 'KH',
        'cameroon' => 'CM',
        'canada' => 'CA',
        'cape-verde' => 'CV',
        'central-african-republic' => 'CF',
        'chad' => 'TD',
        'chile' => 'CL',
        'china' => 'CN',
        'colombia' => 'CO',
        'comoros' => 'KM',
        'congo-drc' => 'CD',
        'congo-republic' => 'CG',
        'costa-rica' => 'CR',
        'croatia' => 'HR',
        'cuba' => 'CU',
        'cyprus' => 'CY',
        'czech-republic' => 'CZ',
        'denmark' => 'DK',
        'djibouti' => 'DJ',
        'dominica' => 'DM',
        'dominican-republic' => 'DO',
        'east-timor' => 'TL',
        'ecuador' => 'EC',
        'egypt' => 'EG',
        'el-salvador' => 'SV',
        'equatorial-guinea' => 'GQ',
        'eritrea' => 'ER',
        'estonia' => 'EE',
        'eswatini' => 'SZ',
        'ethiopia' => 'ET',
        'fiji-islands' => 'FJ',
        'finland' => 'FI',
        'france' => 'FR',
        'gabon' => 'GA',
        'gambia' => 'GM',
        'georgia' => 'GE',
        'germany' => 'DE',
        'ghana' => 'GH',
        'greece' => 'GR',
        'grenada' => 'GD',
        'guatemala' => 'GT',
        'guinea' => 'GN',
        'guinea-bissau' => 'GW',
        'guyana' => 'GY',
        'haiti' => 'HT',
        'honduras' => 'HN',
        'hungary' => 'HU',
        'iceland' => 'IS',
        'india' => 'IN',
        'indonesia' => 'ID',
        'iran' => 'IR',
        'iraq' => 'IQ',
        'ireland' => 'IE',
        'israel' => 'IL',
        'italy' => 'IT',
        'jamaica' => 'JM',
        'japan' => 'JP',
        'jordan' => 'JO',
        'kazakhstan' => 'KZ',
        'kenya' => 'KE',
        'kiribati' => 'KI',
        'kuwait' => 'KW',
        'kyrgyzstan' => 'KG',
        'laos' => 'LA',
        'latvia' => 'LV',
        'lebanon' => 'LB',
        'lesotho' => 'LS',
        'liberia' => 'LR',
        'libya' => 'LY',
        'liechtenstein' => 'LI',
        'lithuania' => 'LT',
        'luxembourg' => 'LU',
        'madagascar' => 'MG',
        'malawi' => 'MW',
        'malaysia' => 'MY',
        'maldives' => 'MV',
        'mali' => 'ML',
        'malta' => 'MT',
        'marshall-islands' => 'MH',
        'mauritania' => 'MR',
        'mauritius' => 'MU',
        'mexico' => 'MX',
        'micronesia' => 'FM',
        'moldova' => 'MD',
        'monaco' => 'MC',
        'mongolia' => 'MN',
        'montenegro' => 'ME',
        'morocco' => 'MA',
        'mozambique' => 'MZ',
        'myanmar' => 'MM',
        'namibia' => 'NA',
        'nauru' => 'NR',
        'nepal' => 'NP',
        'netherlands' => 'NL',
        'new-zealand' => 'NZ',
        'nicaragua' => 'NI',
        'niger' => 'NE',
        'nigeria' => 'NG',
        'north-korea' => 'KP',
        'north-macedonia' => 'MK',
        'norway' => 'NO',
        'oman' => 'OM',
        'pakistan' => 'PK',
        'palau' => 'PW',
        'panama' => 'PA',
        'papua-new-guinea' => 'PG',
        'paraguay' => 'PY',
        'peru' => 'PE',
        'philippines' => 'PH',
        'poland' => 'PL',
        'portugal' => 'PT',
        'qatar' => 'QA',
        'romania' => 'RO',
        'russia' => 'RU',
        'rwanda' => 'RW',
        'saint-kitts-and-nevis' => 'KN',
        'saint-lucia' => 'LC',
        'saint-vincent-and-the-grenadines' => 'VC',
        'samoa' => 'WS',
        'san-marino' => 'SM',
        'sao-tome-and-principe' => 'ST',
        'saudi-arabia' => 'SA',
        'senegal' => 'SN',
        'serbia' => 'RS',
        'seychelles' => 'SC',
        'sierra-leone' => 'SL',
        'singapore' => 'SG',
        'slovakia' => 'SK',
        'slovenia' => 'SI',
        'solomon-islands' => 'SB',
        'somalia' => 'SO',
        'south-africa' => 'ZA',
        'south-korea' => 'KR',
        'south-sudan' => 'SS',
        'spain' => 'ES',
        'sri-lanka' => 'LK',
        'sudan' => 'SD',
        'suriname' => 'SR',
        'sweden' => 'SE',
        'switzerland' => 'CH',
        'syria' => 'SY',
        'taiwan' => 'TW',
        'tajikistan' => 'TJ',
        'tanzania' => 'TZ',
        'thailand' => 'TH',
        'togo' => 'TG',
        'tonga' => 'TO',
        'trinidad-and-tobago' => 'TT',
        'tunisia' => 'TN',
        'turkey' => 'TR',
        'turkmenistan' => 'TM',
        'tuvalu' => 'TV',
        'uganda' => 'UG',
        'ukraine' => 'UA',
        'united-arab-emirates' => 'AE',
        'united-kingdom' => 'GB',
        'united-states' => 'US',
        'uruguay' => 'UY',
        'uzbekistan' => 'UZ',
        'vanuatu' => 'VU',
        'vatican-city' => 'VA',
        'venezuela' => 'VE',
        'vietnam' => 'VN',
        'yemen' => 'YE',
        'zambia' => 'ZM',
        'zimbabwe' => 'ZW'
    ];
    
    return $countryCodes[$country] ?? 'XX';
}

// Usage
if (php_sapi_name() === 'cli') {
    $csvFile = $argv[1] ?? 'migrate.json';
    $outputFile = $argv[2] ?? 'networks_data.json';
    
    echo "Transforming networks data from $csvFile...\n";
    
    $transformedData = transformNetworksData($csvFile);
    
    $jsonOutput = json_encode($transformedData, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
    
    file_put_contents($outputFile, $jsonOutput);
    
    echo "Transformation complete! Output saved to $outputFile\n";
    echo "Total registrations: " . $transformedData['total_registrations'] . "\n";
    echo "Network registrations: " . $transformedData['network_registrations'] . "\n";
}
?>