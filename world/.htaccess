# World Directory - Clean URL Configuration
# Ensures .php extension removal works in all hosting environments

<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Skip rewriting for existing files and directories
    RewriteCond %{REQUEST_FILENAME} -f [OR]
    RewriteCond %{REQUEST_FILENAME} -d
    RewriteRule ^ - [L]
    
    # Skip rewriting for API and data files
    RewriteRule ^(api\.php|.*\.json|assets/) - [L]
    
    # Remove .php extension from URLs (handles compound names)
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^([^./]+(?:-[^./]+)*)/?$ $1.php [QSA,L]
    
    # Redirect .php URLs to clean URLs
    RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+world/([^/\s]*?)\.php[\s?] [NC]
    RewriteCond %1 !^(api|.*-handler)$ [NC]
    RewriteRule ^ /world/%1? [R=301,L]
</IfModule>