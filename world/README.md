# World Crusades Dashboard

A comprehensive dashboard for managing countries, cities, and crusade data displayed in the "View Crusades" modal on the main website.

## Features

### Dashboard (dashboard.php)
- **Authentication**: Simple admin login system
- **Country Management**: Add, edit, delete, and toggle countries
- **Data Visualization**: Charts showing crusades by country and status distribution
- **Real-time Statistics**: Live statistics including total countries, active countries, total crusades, and averages
- **Export Functionality**: Export country data as JSON
- **Responsive Design**: Works on desktop and mobile devices

### API (api.php)
- **REST API**: Provides data endpoints for the frontend
- **Multiple Endpoints**:
  - `/api.php?endpoint=countries` - Get all enabled countries
  - `/api.php?endpoint=country&id=<country_id>` - Get specific country
  - `/api.php?endpoint=stats` - Get statistics
- **CORS Support**: Cross-origin resource sharing enabled

### Frontend Integration
- **Dynamic Loading**: Countries load dynamically from the dashboard data
- **Error Handling**: Graceful error handling with retry functionality
- **Animations**: Smooth animations and transitions
- **Real-time Updates**: Data updates immediately when dashboard changes are made

## File Structure

```
world/
├── dashboard.php          # Main dashboard interface
├── api.php               # API endpoints for frontend
├── crusade-data.json     # Data storage file (auto-created)
├── countries.json        # List of available countries
├── index.php            # Main website page (updated)
└── README.md           # This documentation
```

## Installation & Setup

1. **Access the Dashboard**:
   - Navigate to `world/dashboard.php` in your browser
   - Default login credentials: `admin` / `admin123`

2. **Initial Data**:
   - The dashboard auto-creates initial data on first run
   - Default countries are populated with sample data

3. **Integration**:
   - The main `index.php` file has been updated to use dynamic data
   - Countries in the "View Crusades" modal now load from the dashboard

## Usage

### Adding a New Country
1. Click "Add Country" button in dashboard
2. Fill in country name, cities, and crusade count
3. Click "Add Country" to save

### Editing a Country
1. Click the edit icon (📝) next to any country
2. Modify the details in the modal
3. Click "Update Country" to save changes

### Managing Country Status
- Click the toggle icon (🔘) to enable/disable countries
- Disabled countries won't appear in the frontend modal

### Deleting a Country
- Click the delete icon (🗑️) to remove a country
- Confirmation dialog will appear before deletion

### Exporting Data
- Click "Export Data" to download current country data as JSON
- Useful for backups or data migration

## API Usage

### Get All Countries
```javascript
fetch('world/api.php?endpoint=countries')
  .then(response => response.json())
  .then(data => {
    console.log(data.countries); // Array of enabled countries
  });
```

### Get Specific Country
```javascript
fetch('world/api.php?endpoint=country&id=usa')
  .then(response => response.json())
  .then(data => {
    console.log(data.country); // Single country object
  });
```

### Get Statistics
```javascript
fetch('world/api.php?endpoint=stats')
  .then(response => response.json())
  .then(data => {
    console.log(data.stats); // Statistics object
  });
```

## Data Structure

### Country Object
```json
{
  "id": "usa",
  "name": "UNITED STATES",
  "cities": "New York, Los Angeles, Chicago",
  "crusade_count": 12,
  "enabled": true,
  "created_at": "2025-01-09 12:00:00",
  "updated_at": "2025-01-09 12:00:00"
}
```

### API Response Format
```json
{
  "success": true,
  "countries": [...],
  "total_countries": 17,
  "total_crusades": 187,
  "last_updated": "2025-01-09 12:00:00"
}
```

## Customization

### Styling
- The dashboard uses Tailwind CSS for styling
- Mobile devices use rounded corners, desktop uses sharp edges (per workspace rules)
- Colors and animations can be customized in the CSS section

### Authentication
- Currently uses simple hardcoded credentials
- Can be integrated with existing admin authentication system
- Session-based authentication is implemented

### Data Storage
- Currently uses JSON file storage
- Can be easily migrated to database storage
- Data format is consistent and portable

## Security Considerations

1. **Change Default Credentials**: Update the default login credentials in production
2. **File Permissions**: Ensure proper file permissions on data files
3. **Input Validation**: All inputs are validated and sanitized
4. **Session Security**: Session-based authentication with timeout

## Troubleshooting

### Dashboard Not Loading
- Check file permissions on the `world/` directory
- Ensure PHP has write permissions for creating `crusade-data.json`

### Countries Not Appearing in Frontend
- Verify API endpoint is accessible at `world/api.php`
- Check browser console for JavaScript errors
- Ensure countries are enabled in the dashboard

### Data Not Saving
- Check PHP error logs
- Verify write permissions on `crusade-data.json`
- Ensure JSON format is valid

## Support

For issues or questions:
1. Check the browser console for JavaScript errors
2. Check PHP error logs for server-side issues
3. Verify file permissions and directory structure
4. Ensure all dependencies are properly loaded 