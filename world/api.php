<?php
/**
 * API endpoint for World Crusades data
 * Provides data for the crusades modal on the main site
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// File paths
$crusadeDataFile = __DIR__ . '/crusade-data.json';

// Function to load crusade data
function loadCrusadeData() {
    global $crusadeDataFile;
    if (file_exists($crusadeDataFile)) {
        $data = file_get_contents($crusadeDataFile);
        return json_decode($data, true) ?: ['countries' => [], 'settings' => []];
    }
    return ['countries' => [], 'settings' => []];
}

// Function to format country data for frontend
function formatCountryForFrontend($country) {
    return [
        'id' => $country['id'],
        'name' => $country['name'],
        'cities' => $country['cities'],
        'crusade_count' => $country['crusade_count'],
        'enabled' => $country['enabled'],
        'updated_at' => $country['updated_at']
    ];
}

// Handle different endpoints
$endpoint = $_GET['endpoint'] ?? 'countries';

switch ($endpoint) {
    case 'countries':
        $data = loadCrusadeData();
        $enabledCountries = array_filter($data['countries'], function($country) {
            return $country['enabled'] === true;
        });
        
        $response = [
            'success' => true,
            'countries' => array_map('formatCountryForFrontend', array_values($enabledCountries)),
            'total_countries' => count($enabledCountries),
            'total_crusades' => array_sum(array_column($enabledCountries, 'crusade_count')),
            'last_updated' => $data['settings']['last_updated'] ?? date('Y-m-d H:i:s')
        ];
        break;
        
    case 'country':
        $countryId = $_GET['id'] ?? '';
        $data = loadCrusadeData();
        $country = null;
        
        foreach ($data['countries'] as $c) {
            if ($c['id'] === $countryId && $c['enabled']) {
                $country = formatCountryForFrontend($c);
                break;
            }
        }
        
        if ($country) {
            $response = [
                'success' => true,
                'country' => $country
            ];
        } else {
            $response = [
                'success' => false,
                'message' => 'Country not found or disabled'
            ];
        }
        break;
        
    case 'stats':
        $data = loadCrusadeData();
        $enabledCountries = array_filter($data['countries'], function($country) {
            return $country['enabled'] === true;
        });
        
        $response = [
            'success' => true,
            'stats' => [
                'total_countries' => count($enabledCountries),
                'total_crusades' => array_sum(array_column($enabledCountries, 'crusade_count')),
                'average_crusades_per_country' => count($enabledCountries) > 0 ? 
                    round(array_sum(array_column($enabledCountries, 'crusade_count')) / count($enabledCountries), 1) : 0,
                'most_crusades_country' => count($enabledCountries) > 0 ? 
                    array_reduce($enabledCountries, function($max, $country) {
                        return ($max === null || $country['crusade_count'] > $max['crusade_count']) ? $country : $max;
                    })['name'] : 'N/A',
                'last_updated' => $data['settings']['last_updated'] ?? date('Y-m-d H:i:s')
            ]
        ];
        break;
        
    default:
        $response = [
            'success' => false,
            'message' => 'Invalid endpoint'
        ];
        break;
}

echo json_encode($response, JSON_PRETTY_PRINT);
?> 