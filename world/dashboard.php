<?php
// Start session for admin authentication
session_start();

// Simple authentication - you can integrate with your existing admin system
if (!isset($_SESSION['admin_logged_in'])) {
    // Check for simple login (only if it's a login attempt, not an AJAX action)
    if (isset($_POST['username']) && isset($_POST['password'])) {
        if ($_POST['username'] === 'admin' && $_POST['password'] === 'admin123') {
            $_SESSION['admin_logged_in'] = true;
        } else {
            $error = 'Invalid credentials';
        }
    }
}

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: dashboard.php');
    exit;
}

// File paths
$crusadeDataFile = __DIR__ . '/crusade-data.json';
$countriesFile = __DIR__ . '/countries.json';

// Initialize files if they don't exist
if (!file_exists($crusadeDataFile)) {
    $defaultData = [
        'countries' => [
            [
                'id' => 'usa',
                'name' => 'UNITED STATES',
                'cities' => 'New York, Los Angeles, Chicago',
                'crusade_count' => 12,
                'enabled' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'uk',
                'name' => 'UNITED KINGDOM',
                'cities' => 'London, Manchester, Birmingham',
                'crusade_count' => 8,
                'enabled' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'canada',
                'name' => 'CANADA',
                'cities' => 'Toronto, Vancouver, Montreal',
                'crusade_count' => 6,
                'enabled' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'australia',
                'name' => 'AUSTRALIA',
                'cities' => 'Sydney, Melbourne, Brisbane',
                'crusade_count' => 5,
                'enabled' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'germany',
                'name' => 'GERMANY',
                'cities' => 'Berlin, Munich, Hamburg',
                'crusade_count' => 7,
                'enabled' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'france',
                'name' => 'FRANCE',
                'cities' => 'Paris, Lyon, Marseille',
                'crusade_count' => 9,
                'enabled' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'brazil',
                'name' => 'BRAZIL',
                'cities' => 'São Paulo, Rio de Janeiro, Salvador',
                'crusade_count' => 15,
                'enabled' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'nigeria',
                'name' => 'NIGERIA',
                'cities' => 'Lagos, Abuja, Kano',
                'crusade_count' => 20,
                'enabled' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'india',
                'name' => 'INDIA',
                'cities' => 'Mumbai, Delhi, Bangalore',
                'crusade_count' => 18,
                'enabled' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'south-africa',
                'name' => 'SOUTH AFRICA',
                'cities' => 'Cape Town, Johannesburg, Durban',
                'crusade_count' => 11,
                'enabled' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'kenya',
                'name' => 'KENYA',
                'cities' => 'Nairobi, Mombasa, Kisumu',
                'crusade_count' => 14,
                'enabled' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'ghana',
                'name' => 'GHANA',
                'cities' => 'Accra, Kumasi, Tamale',
                'crusade_count' => 16,
                'enabled' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'philippines',
                'name' => 'PHILIPPINES',
                'cities' => 'Manila, Cebu, Davao',
                'crusade_count' => 13,
                'enabled' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'mexico',
                'name' => 'MEXICO',
                'cities' => 'Mexico City, Guadalajara, Monterrey',
                'crusade_count' => 10,
                'enabled' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'italy',
                'name' => 'ITALY',
                'cities' => 'Rome, Milan, Naples',
                'crusade_count' => 4,
                'enabled' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'spain',
                'name' => 'SPAIN',
                'cities' => 'Madrid, Barcelona, Valencia',
                'crusade_count' => 6,
                'enabled' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'argentina',
                'name' => 'ARGENTINA',
                'cities' => 'Buenos Aires, Córdoba, Rosario',
                'crusade_count' => 8,
                'enabled' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ],
        'settings' => [
            'last_updated' => date('Y-m-d H:i:s'),
            'version' => '1.0'
        ]
    ];
    file_put_contents($crusadeDataFile, json_encode($defaultData, JSON_PRETTY_PRINT));
}

// Functions for data management
function loadCrusadeData() {
    global $crusadeDataFile;
    $data = file_get_contents($crusadeDataFile);
    return json_decode($data, true) ?: ['countries' => [], 'settings' => []];
}

function saveCrusadeData($data) {
    global $crusadeDataFile;
    
    // Check if directory is writable
    $dir = dirname($crusadeDataFile);
    if (!is_writable($dir)) {
        error_log("Dashboard: Directory not writable: $dir");
        return false;
    }
    
    // Prepare data
    $data['settings']['last_updated'] = date('Y-m-d H:i:s');
    $jsonData = json_encode($data, JSON_PRETTY_PRINT);
    
    if ($jsonData === false) {
        error_log("Dashboard: JSON encoding failed: " . json_last_error_msg());
        return false;
    }
    
    // Attempt to write file
    $result = file_put_contents($crusadeDataFile, $jsonData);
    
    if ($result === false) {
        error_log("Dashboard: Failed to write file: $crusadeDataFile");
        error_log("Dashboard: Directory permissions: " . substr(sprintf('%o', fileperms($dir)), -4));
        if (file_exists($crusadeDataFile)) {
            error_log("Dashboard: File permissions: " . substr(sprintf('%o', fileperms($crusadeDataFile)), -4));
        }
        return false;
    }
    
    // Set proper permissions on the newly created file
    if (file_exists($crusadeDataFile)) {
        chmod($crusadeDataFile, 0664);
    }
    
    error_log("Dashboard: Successfully saved data to $crusadeDataFile");
    return true;
}

function loadCountries() {
    global $countriesFile;
    if (file_exists($countriesFile)) {
        $data = file_get_contents($countriesFile);
        return json_decode($data, true) ?: [];
    }
    return [];
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    // Add debug logging
    error_log("Dashboard POST request received. Action: " . ($_POST['action'] ?? 'NOT_SET'));
    error_log("POST data: " . print_r($_POST, true));
    error_log("Session admin logged in: " . (isset($_SESSION['admin_logged_in']) ? 'true' : 'false'));
    
    // Check if admin is logged in
    if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
        echo json_encode(['success' => false, 'message' => 'Not authenticated. Please log in first.']);
        exit;
    }
    
    $data = loadCrusadeData();
    $response = ['success' => false, 'message' => 'Unknown action'];
    
    switch ($_POST['action']) {
        case 'add_country':
            // Validate required fields
            if (empty($_POST['name']) || empty($_POST['cities']) || !isset($_POST['crusade_count'])) {
                $response = ['success' => false, 'message' => 'All fields are required'];
                break;
            }
            
            $country = [
                'id' => strtolower(str_replace(' ', '-', $_POST['name'])),
                'name' => strtoupper($_POST['name']),
                'cities' => $_POST['cities'],
                'crusade_count' => (int)$_POST['crusade_count'],
                'enabled' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            $data['countries'][] = $country;
            if (saveCrusadeData($data)) {
                $response = ['success' => true, 'message' => 'Country added successfully', 'country' => $country];
            } else {
                $response = ['success' => false, 'message' => 'Failed to save country data'];
            }
            break;
            
        case 'update_country':
            // Validate required fields
            if (empty($_POST['id']) || empty($_POST['name']) || empty($_POST['cities']) || !isset($_POST['crusade_count'])) {
                $response = ['success' => false, 'message' => 'All fields are required'];
                break;
            }
            
            $id = $_POST['id'];
            $found = false;
            foreach ($data['countries'] as &$country) {
                if ($country['id'] === $id) {
                    $country['name'] = strtoupper($_POST['name']);
                    $country['cities'] = $_POST['cities'];
                    $country['crusade_count'] = (int)$_POST['crusade_count'];
                    $country['updated_at'] = date('Y-m-d H:i:s');
                    $found = true;
                    break;
                }
            }
            
            if (!$found) {
                $response = ['success' => false, 'message' => 'Country not found'];
            } else if (saveCrusadeData($data)) {
                $response = ['success' => true, 'message' => 'Country updated successfully'];
            } else {
                $response = ['success' => false, 'message' => 'Failed to save country data'];
            }
            break;
            
        case 'delete_country':
            if (empty($_POST['id'])) {
                $response = ['success' => false, 'message' => 'Country ID is required'];
                break;
            }
            
            $id = $_POST['id'];
            $originalCount = count($data['countries']);
            $data['countries'] = array_filter($data['countries'], function($country) use ($id) {
                return $country['id'] !== $id;
            });
            $data['countries'] = array_values($data['countries']); // Reindex array
            
            if (count($data['countries']) === $originalCount) {
                $response = ['success' => false, 'message' => 'Country not found'];
            } else if (saveCrusadeData($data)) {
                $response = ['success' => true, 'message' => 'Country deleted successfully'];
            } else {
                $response = ['success' => false, 'message' => 'Failed to save country data'];
            }
            break;
            
        case 'toggle_country':
            if (empty($_POST['id'])) {
                $response = ['success' => false, 'message' => 'Country ID is required'];
                break;
            }
            
            $id = $_POST['id'];
            $found = false;
            foreach ($data['countries'] as &$country) {
                if ($country['id'] === $id) {
                    $country['enabled'] = !$country['enabled'];
                    $country['updated_at'] = date('Y-m-d H:i:s');
                    $found = true;
                    break;
                }
            }
            
            if (!$found) {
                $response = ['success' => false, 'message' => 'Country not found'];
            } else if (saveCrusadeData($data)) {
                $response = ['success' => true, 'message' => 'Country status updated successfully'];
            } else {
                $response = ['success' => false, 'message' => 'Failed to save country data'];
            }
            break;
            
        case 'get_countries':
            $response = ['success' => true, 'countries' => $data['countries']];
            break;
            
        default:
            $response = ['success' => false, 'message' => 'Unknown action: ' . $_POST['action']];
            error_log("Unknown action received: " . $_POST['action']);
            break;
    }
    
    error_log("Dashboard response: " . json_encode($response));
    echo json_encode($response);
    exit;
}

// If not logged in, show login form
if (!isset($_SESSION['admin_logged_in'])) {
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>World Crusades Dashboard - Login</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Ensure no rounded corners for desktop */
        * {
            border-radius: 0 !important;
        }
        @media (max-width: 768px) {
            * {
                border-radius: 0.5rem !important;
            }
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center">
    <div class="bg-white p-8 shadow-lg w-full max-w-md">
        <div class="text-center mb-8">
            <h1 class="text-2xl font-bold text-gray-900">World Crusades Dashboard</h1>
            <p class="text-gray-600 mt-2">Admin Login Required</p>
        </div>
        
        <?php if (isset($error)): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 mb-4">
            <?php echo $error; ?>
        </div>
        <?php endif; ?>
        
        <form method="POST">
            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2">Username</label>
                <input type="text" name="username" class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-blue-500" required>
            </div>
            <div class="mb-6">
                <label class="block text-gray-700 text-sm font-bold mb-2">Password</label>
                <input type="password" name="password" class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-blue-500" required>
            </div>
            <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 hover:bg-blue-700 transition-colors">
                Login
            </button>
        </form>
        
        <div class="mt-6 text-center">
            <a href="index.php" class="text-blue-600 hover:text-blue-800">← Back to Website</a>
        </div>
    </div>
</body>
</html>
<?php
exit;
}

// Load current data
$crusadeData = loadCrusadeData();
$countries = $crusadeData['countries'] ?? [];
$availableCountries = loadCountries();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>World Crusades Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* Ensure no rounded corners for desktop */
        * {
            border-radius: 0 !important;
        }
        @media (max-width: 768px) {
            * {
                border-radius: 0.5rem !important;
            }
        }
        
        .dashboard-card {
            transition: all 0.3s ease;
        }
        
        .dashboard-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .country-status.enabled {
            background: linear-gradient(135deg, #10b981, #065f46);
        }
        
        .country-status.disabled {
            background: linear-gradient(135deg, #ef4444, #991b1b);
        }
        
        .table-row:hover {
            background-color: #f8fafc;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <img src="assets/images/logo.webp" alt="Logo" class="h-8 w-auto mr-4">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">World Crusades Dashboard</h1>
                        <p class="text-sm text-gray-600">Manage countries and crusade data</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="index.php" class="text-blue-600 hover:text-blue-800">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Website
                    </a>
                    <a href="?logout=1" class="bg-red-600 text-white px-4 py-2 hover:bg-red-700 transition-colors">
                        <i class="fas fa-sign-out-alt mr-2"></i>Logout
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="dashboard-card bg-white p-6 shadow-sm border">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-100 flex items-center justify-center">
                            <i class="fas fa-globe text-blue-600"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Countries</p>
                        <p class="text-2xl font-semibold text-gray-900" id="total-countries"><?php echo count($countries); ?></p>
                    </div>
                </div>
            </div>
            
            <div class="dashboard-card bg-white p-6 shadow-sm border">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-100 flex items-center justify-center">
                            <i class="fas fa-check-circle text-green-600"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Active Countries</p>
                        <p class="text-2xl font-semibold text-gray-900" id="active-countries">
                            <?php echo count(array_filter($countries, function($c) { return $c['enabled']; })); ?>
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="dashboard-card bg-white p-6 shadow-sm border">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-100 flex items-center justify-center">
                            <i class="fas fa-church text-purple-600"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Crusades</p>
                        <p class="text-2xl font-semibold text-gray-900" id="total-crusades">
                            <?php echo array_sum(array_column($countries, 'crusade_count')); ?>
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="dashboard-card bg-white p-6 shadow-sm border">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-orange-100 flex items-center justify-center">
                            <i class="fas fa-chart-line text-orange-600"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Avg per Country</p>
                        <p class="text-2xl font-semibold text-gray-900" id="avg-crusades">
                            <?php echo count($countries) > 0 ? round(array_sum(array_column($countries, 'crusade_count')) / count($countries), 1) : 0; ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-3">
            <h2 class="text-xl font-semibold text-gray-900">Manage Countries</h2>
            <div class="flex space-x-3">
                <button onclick="exportData()" class="bg-green-600 text-white px-4 py-2 hover:bg-green-700 transition-colors">
                    <i class="fas fa-download mr-2"></i>Export Data
                </button>
                <button onclick="openAddModal()" class="bg-blue-600 text-white px-4 py-2 hover:bg-blue-700 transition-colors">
                    <i class="fas fa-plus mr-2"></i>Add Country
                </button>
            </div>
        </div>

        <!-- Countries Table -->
        <div class="bg-white shadow-sm border overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Country</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cities</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Crusades</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="countries-table" class="bg-white divide-y divide-gray-200">
                        <?php foreach ($countries as $country): ?>
                        <tr class="table-row" data-country-id="<?php echo $country['id']; ?>">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($country['name']); ?></div>
                                <div class="text-sm text-gray-500">ID: <?php echo htmlspecialchars($country['id']); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900 max-w-xs"><?php echo htmlspecialchars($country['cities']); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-semibold text-gray-900"><?php echo $country['crusade_count']; ?> Confirmed</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="country-status <?php echo $country['enabled'] ? 'enabled' : 'disabled'; ?> inline-flex px-2 py-1 text-xs font-semibold text-white">
                                    <?php echo $country['enabled'] ? 'Active' : 'Disabled'; ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo date('M j, Y', strtotime($country['updated_at'])); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="editCountry('<?php echo $country['id']; ?>')" class="text-blue-600 hover:text-blue-900">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button onclick="toggleCountry('<?php echo $country['id']; ?>')" class="text-yellow-600 hover:text-yellow-900">
                                    <i class="fas fa-toggle-<?php echo $country['enabled'] ? 'on' : 'off'; ?>"></i>
                                </button>
                                <button onclick="deleteCountry('<?php echo $country['id']; ?>')" class="text-red-600 hover:text-red-900">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="bg-white p-6 shadow-sm border">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Crusades by Country</h3>
                <canvas id="crusadesChart" width="400" height="200"></canvas>
            </div>
            <div class="bg-white p-6 shadow-sm border">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Country Status</h3>
                <canvas id="statusChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Add/Edit Modal -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white p-6 w-full max-w-md">
                <div class="flex justify-between items-center mb-4">
                    <h3 id="modal-title" class="text-lg font-semibold text-gray-900">Add Country</h3>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form id="country-form">
                    <input type="hidden" id="country-id" name="id">
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Country Name</label>
                        <input type="text" id="country-name" name="name" class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-blue-500" required>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Cities</label>
                        <textarea id="country-cities" name="cities" rows="3" class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-blue-500" placeholder="Separate cities with commas" required></textarea>
                    </div>
                    
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Number of Crusades</label>
                        <input type="number" id="country-crusades" name="crusade_count" min="0" class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-blue-500" required>
                    </div>
                    
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="closeModal()" class="px-4 py-2 text-gray-700 border border-gray-300 hover:bg-gray-50 transition-colors">
                            Cancel
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 transition-colors">
                            <span id="submit-text">Add Country</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div id="toast" class="fixed top-4 right-4 px-6 py-3 text-white shadow-lg hidden z-50">
        <span id="toast-message"></span>
    </div>

    <script>
        let countries = <?php echo json_encode($countries); ?>;
        let editingCountryId = null;

        // Initialize charts
        function initCharts() {
            // Crusades by Country Chart
            const crusadesCtx = document.getElementById('crusadesChart').getContext('2d');
            new Chart(crusadesCtx, {
                type: 'bar',
                data: {
                    labels: countries.map(c => c.name),
                    datasets: [{
                        label: 'Confirmed Crusades',
                        data: countries.map(c => c.crusade_count),
                        backgroundColor: 'rgba(59, 130, 246, 0.8)',
                        borderColor: 'rgba(59, 130, 246, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });

            // Status Chart
            const statusCtx = document.getElementById('statusChart').getContext('2d');
            const activeCount = countries.filter(c => c.enabled).length;
            const inactiveCount = countries.length - activeCount;
            
            new Chart(statusCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Active', 'Disabled'],
                    datasets: [{
                        data: [activeCount, inactiveCount],
                        backgroundColor: ['#10b981', '#ef4444'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // Modal functions
        function openAddModal() {
            editingCountryId = null;
            document.getElementById('modal-title').textContent = 'Add Country';
            document.getElementById('submit-text').textContent = 'Add Country';
            document.getElementById('country-form').reset();
            document.getElementById('modal').classList.remove('hidden');
        }

        function editCountry(id) {
            editingCountryId = id;
            const country = countries.find(c => c.id === id);
            
            document.getElementById('modal-title').textContent = 'Edit Country';
            document.getElementById('submit-text').textContent = 'Update Country';
            document.getElementById('country-id').value = country.id;
            document.getElementById('country-name').value = country.name;
            document.getElementById('country-cities').value = country.cities;
            document.getElementById('country-crusades').value = country.crusade_count;
            document.getElementById('modal').classList.remove('hidden');
        }

        function closeModal() {
            document.getElementById('modal').classList.add('hidden');
            editingCountryId = null;
        }

        // Form submission
        document.getElementById('country-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData();
            formData.append('action', editingCountryId ? 'update_country' : 'add_country');
            formData.append('name', document.getElementById('country-name').value);
            formData.append('cities', document.getElementById('country-cities').value);
            formData.append('crusade_count', document.getElementById('country-crusades').value);
            
            if (editingCountryId) {
                formData.append('id', editingCountryId);
            }
            
            fetch('dashboard.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                    closeModal();
                    location.reload(); // Refresh to update table and charts
                } else {
                    showToast(data.message, 'error');
                }
            })
            .catch(error => {
                showToast('An error occurred', 'error');
            });
        });

        // Toggle country status
        function toggleCountry(id) {
            const formData = new FormData();
            formData.append('action', 'toggle_country');
            formData.append('id', id);
            
            fetch('dashboard.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                    location.reload();
                } else {
                    showToast(data.message, 'error');
                }
            });
        }

        // Delete country
        function deleteCountry(id) {
            if (confirm('Are you sure you want to delete this country? This action cannot be undone.')) {
                const formData = new FormData();
                formData.append('action', 'delete_country');
                formData.append('id', id);
                
                fetch('dashboard.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast(data.message, 'success');
                        location.reload();
                    } else {
                        showToast(data.message, 'error');
                    }
                });
            }
        }

        // Export data
        function exportData() {
            const dataStr = JSON.stringify(countries, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'crusade-countries-' + new Date().toISOString().split('T')[0] + '.json';
            link.click();
            URL.revokeObjectURL(url);
        }

        // Toast notifications
        function showToast(message, type) {
            const toast = document.getElementById('toast');
            const toastMessage = document.getElementById('toast-message');
            
            toastMessage.textContent = message;
            toast.className = `fixed top-4 right-4 px-6 py-3 text-white shadow-lg z-50 ${type === 'success' ? 'bg-green-500' : 'bg-red-500'}`;
            toast.classList.remove('hidden');
            
            setTimeout(() => {
                toast.classList.add('hidden');
            }, 3000);
        }

        // Close modal when clicking outside
        document.getElementById('modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // Initialize charts when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
        });
    </script>
</body>
</html> 