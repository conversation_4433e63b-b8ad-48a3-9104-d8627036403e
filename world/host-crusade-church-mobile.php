<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Host Your Crusade - Church Registration Mobile</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <style>
        /* Ensure all elements have rounded corners for mobile */
        * {
            border-radius: 0.375rem !important;
            box-sizing: border-box;
        }
        
        /* Mobile-first background */
        body {
            background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        /* Mobile glass effect */
        .mobile-glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        /* Mobile navigation */
        .mobile-nav {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 50;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1rem;
        }
        
        /* Mobile form container */
        .mobile-form-container {
            padding-top: 7rem;
            padding-bottom: 2rem;
            min-height: 100vh;
        }
        
        /* Mobile form section */
        .mobile-form-section {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 1rem;
            padding: 1.5rem;
            transition: all 0.3s ease;
        }
        
        /* Mobile form fields */
        .mobile-form-field {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 1rem;
            width: 100%;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        
        .mobile-form-field:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #60a5fa;
            box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.2);
            outline: none;
        }
        
        .mobile-form-field::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        
        /* Mobile select styling */
        select.mobile-form-field {
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.5rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-right: 2.5rem;
        }
        
        select.mobile-form-field option {
            background-color: #1e293b;
            color: white;
            padding: 0.5rem;
        }
        
        /* Mobile form title */
        .mobile-form-title {
            font-size: 1.5rem;
            font-weight: 900;
            color: white;
            margin-bottom: 1.5rem;
            text-align: center;
        }
        
        /* Mobile label */
        .mobile-label {
            display: block;
            color: white;
            font-weight: 300;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
        
        /* Mobile button */
        .mobile-btn {
            background: rgba(96, 165, 250, 0.8);
            color: white;
            padding: 1rem 2rem;
            width: 100%;
            margin: 0.5rem 0;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        
        .mobile-btn:hover {
            background: rgba(96, 165, 250, 1);
            transform: translateY(-2px);
        }
        
        .mobile-btn:disabled {
            background: rgba(255, 255, 255, 0.2);
            cursor: not-allowed;
            transform: none;
        }
        
        .mobile-btn.secondary {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .mobile-btn.secondary:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        /* Mobile progress */
        .mobile-progress {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        
        .mobile-progress-dot {
            width: 12px;
            height: 12px;
            background: rgba(255, 255, 255, 0.3);
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }
        
        .mobile-progress-dot.active {
            background: #60a5fa;
            transform: scale(1.2);
        }
        
        /* Mobile step */
        .mobile-step {
            display: none;
        }
        
        .mobile-step.active {
            display: block;
            animation: slideIn 0.3s ease-out;
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        /* Mobile overlay styles */
        .mobile-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.95);
            z-index: 90;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            padding: 1rem;
        }
        
        .mobile-overlay.active {
            opacity: 1;
            visibility: visible;
        }
        
        .mobile-overlay-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-top: 1rem;
        }
        
        .mobile-overlay-title {
            font-size: 1.5rem;
            font-weight: 900;
            color: white;
        }
        
        .mobile-close-btn {
            font-size: 2rem;
            color: white;
            background: none;
            border: none;
            padding: 0.5rem;
            transition: transform 0.3s ease;
        }
        
        .mobile-close-btn:hover {
            transform: rotate(90deg);
        }
        
        /* Mobile language list */
        .mobile-language-list {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
            max-height: 70vh;
            overflow-y: auto;
            padding-right: 0.5rem;
        }
        
        .mobile-language-item {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1.5rem;
            color: white;
            font-size: 1.1rem;
            font-weight: 700;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .mobile-language-item:hover {
            background: rgba(255, 255, 255, 0.2);
            color: #60a5fa;
            transform: translateY(-2px);
        }
        
        /* Mobile scroll styling */
        .mobile-scroll::-webkit-scrollbar {
            width: 4px;
        }
        
        .mobile-scroll::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }
        
        .mobile-scroll::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
        }
        
        /* Validation message */
        .mobile-validation-message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(239, 68, 68, 0.95);
            color: white;
            padding: 1rem;
            z-index: 1000;
            font-weight: 500;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            max-width: 90%;
            text-align: center;
        }
        
        .mobile-validation-message.show {
            opacity: 1;
            visibility: visible;
        }
        
        .mobile-validation-message.success {
            background: rgba(34, 197, 94, 0.95);
        }
    </style>
</head>
<body>
    <!-- Mobile Navigation -->
    <div class="mobile-nav">
        <div class="flex justify-between items-center">
            <div class="flex items-center">
                <a href="index-mobile.php" class="flex items-center">
                    <img src="assets/images/logo.webp" alt="Logo" class="h-8 w-auto mr-4 cursor-pointer hover:opacity-80 transition-opacity">
                    <span class="text-slate-800 font-bold text-lg">Church Registration</span>
                </a>
            </div>
            <button id="mobile-language-btn" class="mobile-glass px-3 py-2 text-slate-800 hover:text-blue-600 transition-colors">
                <div class="flex items-center space-x-2">
                    <span class="text-sm font-medium">EN</span>
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </div>
            </button>
        </div>
    </div>

    <!-- Mobile Form Container -->
    <div class="mobile-form-container">
        <div class="max-w-md mx-auto px-4">
            <!-- Form Header -->
            <div class="text-center mb-6">
                <h1 class="text-3xl font-black text-white mb-4">
                    HOST YOUR CHURCH CRUSADE
                </h1>
                <p class="text-white font-light leading-relaxed">
                    Register your church or organization to host a crusade.
                </p>
            </div>

            <!-- Mobile Progress Indicator -->
            <div class="mobile-progress">
                <div class="mobile-progress-dot active"></div>
                <div class="mobile-progress-dot"></div>
                <div class="mobile-progress-dot"></div>
                <div class="mobile-progress-dot"></div>
            </div>

            <!-- Mobile Form -->
            <form id="mobile-church-form">
                <!-- Hidden field to identify this as church registration -->
                <input type="hidden" name="registration_type" value="church">
                
                <div class="mobile-form-content">
                    <!-- Step 1: Personal Information -->
                    <div id="mobile-step-1" class="mobile-step active">
                        <div class="mobile-form-section">
                            <h2 class="mobile-form-title">Personal Information</h2>
                            
                            <label class="mobile-label">Designation *</label>
                            <select class="mobile-form-field" name="designation" required>
                                <option value="">Select your designation</option>
                                <option value="brother">Brother</option>
                                <option value="sister">Sister</option>
                                <option value="deacon">Deacon</option>
                                <option value="deaconess">Deaconess</option>
                                <option value="pastor">Pastor</option>
                                <option value="evangelist">Evangelist</option>
                                <option value="dr">Dr</option>
                                <option value="apostle">Apostle</option>
                            </select>
                            
                            <label class="mobile-label">First Name *</label>
                            <input type="text" class="mobile-form-field" name="first_name" placeholder="Enter your first name" required>
                            
                            <label class="mobile-label">Last Name *</label>
                            <input type="text" class="mobile-form-field" name="last_name" placeholder="Enter your last name" required>
                            
                            <label class="mobile-label">Email Address *</label>
                            <input type="email" class="mobile-form-field" name="email" placeholder="Enter your email address" required>
                            
                            <label class="mobile-label">Phone Number *</label>
                            <div class="flex">
                                <select class="mobile-form-field w-24 mr-2" name="country_code" required>
                                    <option value="+1" data-country="US">+1</option>
                                    <option value="+1" data-country="CA">+1</option>
                                    <option value="******" data-country="BS">******</option>
                                    <option value="******" data-country="BB">******</option>
                                    <option value="******" data-country="AI">******</option>
                                    <option value="******" data-country="AG">******</option>
                                    <option value="******" data-country="VG">******</option>
                                    <option value="******" data-country="VI">******</option>
                                    <option value="******" data-country="KY">******</option>
                                    <option value="******" data-country="BM">******</option>
                                    <option value="+1 473" data-country="GD">+1 473</option>
                                    <option value="+1 649" data-country="TC">+1 649</option>
                                    <option value="+1 664" data-country="MS">+1 664</option>
                                    <option value="+1 670" data-country="MP">+1 670</option>
                                    <option value="+1 671" data-country="GU">+1 671</option>
                                    <option value="+1 684" data-country="AS">+1 684</option>
                                    <option value="+1 758" data-country="LC">+1 758</option>
                                    <option value="+1 767" data-country="DM">+1 767</option>
                                    <option value="+1 784" data-country="VC">+1 784</option>
                                    <option value="+1 787" data-country="PR">+1 787</option>
                                    <option value="+1 809" data-country="DO">+1 809</option>
                                    <option value="+1 868" data-country="TT">+1 868</option>
                                    <option value="+1 869" data-country="KN">+1 869</option>
                                    <option value="+1 876" data-country="JM">+1 876</option>
                                    <option value="+7" data-country="RU">+7</option>
                                    <option value="+7" data-country="KZ">+7</option>
                                    <option value="+7 840" data-country="AB">+7 840</option>
                                    <option value="+20" data-country="EG">+20</option>
                                    <option value="+27" data-country="ZA">+27</option>
                                    <option value="+30" data-country="GR">+30</option>
                                    <option value="+31" data-country="NL">+31</option>
                                    <option value="+32" data-country="BE">+32</option>
                                    <option value="+33" data-country="FR">+33</option>
                                    <option value="+34" data-country="ES">+34</option>
                                    <option value="+36" data-country="HU">+36</option>
                                    <option value="+39" data-country="IT">+39</option>
                                    <option value="+40" data-country="RO">+40</option>
                                    <option value="+41" data-country="CH">+41</option>
                                    <option value="+43" data-country="AT">+43</option>
                                    <option value="+44" data-country="GB">+44</option>
                                    <option value="+45" data-country="DK">+45</option>
                                    <option value="+46" data-country="SE">+46</option>
                                    <option value="+47" data-country="NO">+47</option>
                                    <option value="+48" data-country="PL">+48</option>
                                    <option value="+49" data-country="DE">+49</option>
                                    <option value="+51" data-country="PE">+51</option>
                                    <option value="+52" data-country="MX">+52</option>
                                    <option value="+53" data-country="CU">+53</option>
                                    <option value="+54" data-country="AR">+54</option>
                                    <option value="+55" data-country="BR">+55</option>
                                    <option value="+56" data-country="CL">+56</option>
                                    <option value="+57" data-country="CO">+57</option>
                                    <option value="+58" data-country="VE">+58</option>
                                    <option value="+60" data-country="MY">+60</option>
                                    <option value="+61" data-country="AU">+61</option>
                                    <option value="+61" data-country="CX">+61</option>
                                    <option value="+61" data-country="CC">+61</option>
                                    <option value="+62" data-country="ID">+62</option>
                                    <option value="+63" data-country="PH">+63</option>
                                    <option value="+64" data-country="NZ">+64</option>
                                    <option value="+65" data-country="SG">+65</option>
                                    <option value="+66" data-country="TH">+66</option>
                                    <option value="+81" data-country="JP">+81</option>
                                    <option value="+82" data-country="KR">+82</option>
                                    <option value="+84" data-country="VN">+84</option>
                                    <option value="+86" data-country="CN">+86</option>
                                    <option value="+90" data-country="TR">+90</option>
                                    <option value="+91" data-country="IN">+91</option>
                                    <option value="+92" data-country="PK">+92</option>
                                    <option value="+93" data-country="AF">+93</option>
                                    <option value="+94" data-country="LK">+94</option>
                                    <option value="+95" data-country="MM">+95</option>
                                    <option value="+98" data-country="IR">+98</option>
                                    <option value="+212" data-country="MA">+212</option>
                                    <option value="+212" data-country="EH">+212</option>
                                    <option value="+213" data-country="DZ">+213</option>
                                    <option value="+216" data-country="TN">+216</option>
                                    <option value="+218" data-country="LY">+218</option>
                                    <option value="+220" data-country="GM">+220</option>
                                    <option value="+221" data-country="SN">+221</option>
                                    <option value="+222" data-country="MR">+222</option>
                                    <option value="+223" data-country="ML">+223</option>
                                    <option value="+224" data-country="GN">+224</option>
                                    <option value="+225" data-country="CI">+225</option>
                                    <option value="+226" data-country="BF">+226</option>
                                    <option value="+227" data-country="NE">+227</option>
                                    <option value="+228" data-country="TG">+228</option>
                                    <option value="+229" data-country="BJ">+229</option>
                                    <option value="+230" data-country="MU">+230</option>
                                    <option value="+231" data-country="LR">+231</option>
                                    <option value="+232" data-country="SL">+232</option>
                                    <option value="+233" data-country="GH">+233</option>
                                    <option value="+234" data-country="NG">+234</option>
                                    <option value="+235" data-country="TD">+235</option>
                                    <option value="+236" data-country="CF">+236</option>
                                    <option value="+237" data-country="CM">+237</option>
                                    <option value="+238" data-country="CV">+238</option>
                                    <option value="+239" data-country="ST">+239</option>
                                    <option value="+240" data-country="GQ">+240</option>
                                    <option value="+241" data-country="GA">+241</option>
                                    <option value="+242" data-country="CG">+242</option>
                                    <option value="+243" data-country="CD">+243</option>
                                    <option value="+244" data-country="AO">+244</option>
                                    <option value="+245" data-country="GW">+245</option>
                                    <option value="+246" data-country="IO">+246</option>
                                    <option value="+248" data-country="SC">+248</option>
                                    <option value="+249" data-country="SD">+249</option>
                                    <option value="+250" data-country="RW">+250</option>
                                    <option value="+251" data-country="ET">+251</option>
                                    <option value="+252" data-country="SO">+252</option>
                                    <option value="+253" data-country="DJ">+253</option>
                                    <option value="+254" data-country="KE">+254</option>
                                    <option value="+255" data-country="TZ">+255</option>
                                    <option value="+256" data-country="UG">+256</option>
                                    <option value="+257" data-country="BI">+257</option>
                                    <option value="+258" data-country="MZ">+258</option>
                                    <option value="+260" data-country="ZM">+260</option>
                                    <option value="+261" data-country="MG">+261</option>
                                    <option value="+262" data-country="YT">+262</option>
                                    <option value="+262" data-country="RE">+262</option>
                                    <option value="+263" data-country="ZW">+263</option>
                                    <option value="+264" data-country="NA">+264</option>
                                    <option value="+265" data-country="MW">+265</option>
                                    <option value="+266" data-country="LS">+266</option>
                                    <option value="+267" data-country="BW">+267</option>
                                    <option value="+268" data-country="SZ">+268</option>
                                    <option value="+269" data-country="KM">+269</option>
                                    <option value="+290" data-country="SH">+290</option>
                                    <option value="+291" data-country="ER">+291</option>
                                    <option value="+297" data-country="AW">+297</option>
                                    <option value="+298" data-country="FO">+298</option>
                                    <option value="+299" data-country="GL">+299</option>
                                    <option value="+350" data-country="GI">+350</option>
                                    <option value="+351" data-country="PT">+351</option>
                                    <option value="+352" data-country="LU">+352</option>
                                    <option value="+353" data-country="IE">+353</option>
                                    <option value="+354" data-country="IS">+354</option>
                                    <option value="+355" data-country="AL">+355</option>
                                    <option value="+356" data-country="MT">+356</option>
                                    <option value="+357" data-country="CY">+357</option>
                                    <option value="+358" data-country="FI">+358</option>
                                    <option value="+359" data-country="BG">+359</option>
                                    <option value="+370" data-country="LT">+370</option>
                                    <option value="+371" data-country="LV">+371</option>
                                    <option value="+372" data-country="EE">+372</option>
                                    <option value="+373" data-country="MD">+373</option>
                                    <option value="+374" data-country="AM">+374</option>
                                    <option value="+375" data-country="BY">+375</option>
                                    <option value="+376" data-country="AD">+376</option>
                                    <option value="+377" data-country="MC">+377</option>
                                    <option value="+378" data-country="SM">+378</option>
                                    <option value="+380" data-country="UA">+380</option>
                                    <option value="+381" data-country="RS">+381</option>
                                    <option value="+382" data-country="ME">+382</option>
                                    <option value="+385" data-country="HR">+385</option>
                                    <option value="+386" data-country="SI">+386</option>
                                    <option value="+387" data-country="BA">+387</option>
                                    <option value="+389" data-country="MK">+389</option>
                                    <option value="+420" data-country="CZ">+420</option>
                                    <option value="+421" data-country="SK">+421</option>
                                    <option value="+423" data-country="LI">+423</option>
                                    <option value="+500" data-country="FK">+500</option>
                                    <option value="+501" data-country="BZ">+501</option>
                                    <option value="+502" data-country="GT">+502</option>
                                    <option value="+503" data-country="SV">+503</option>
                                    <option value="+504" data-country="HN">+504</option>
                                    <option value="+505" data-country="NI">+505</option>
                                    <option value="+506" data-country="CR">+506</option>
                                    <option value="+507" data-country="PA">+507</option>
                                    <option value="+508" data-country="PM">+508</option>
                                    <option value="+509" data-country="HT">+509</option>
                                    <option value="+590" data-country="GP">+590</option>
                                    <option value="+591" data-country="BO">+591</option>
                                    <option value="+592" data-country="GY">+592</option>
                                    <option value="+593" data-country="EC">+593</option>
                                    <option value="+594" data-country="GF">+594</option>
                                    <option value="+595" data-country="PY">+595</option>
                                    <option value="+596" data-country="MQ">+596</option>
                                    <option value="+597" data-country="SR">+597</option>
                                    <option value="+598" data-country="UY">+598</option>
                                    <option value="+599" data-country="AN">+599</option>
                                    <option value="+670" data-country="TL">+670</option>
                                    <option value="+672" data-country="AQ">+672</option>
                                    <option value="+672" data-country="NF">+672</option>
                                    <option value="+673" data-country="BN">+673</option>
                                    <option value="+674" data-country="NR">+674</option>
                                    <option value="+675" data-country="PG">+675</option>
                                    <option value="+676" data-country="TO">+676</option>
                                    <option value="+677" data-country="SB">+677</option>
                                    <option value="+678" data-country="VU">+678</option>
                                    <option value="+679" data-country="FJ">+679</option>
                                    <option value="+680" data-country="PW">+680</option>
                                    <option value="+681" data-country="WF">+681</option>
                                    <option value="+682" data-country="CK">+682</option>
                                    <option value="+683" data-country="NU">+683</option>
                                    <option value="+685" data-country="WS">+685</option>
                                    <option value="+686" data-country="KI">+686</option>
                                    <option value="+687" data-country="NC">+687</option>
                                    <option value="+688" data-country="TV">+688</option>
                                    <option value="+689" data-country="PF">+689</option>
                                    <option value="+690" data-country="TK">+690</option>
                                    <option value="+691" data-country="FM">+691</option>
                                    <option value="+692" data-country="MH">+692</option>
                                    <option value="+850" data-country="KP">+850</option>
                                    <option value="+852" data-country="HK">+852</option>
                                    <option value="+853" data-country="MO">+853</option>
                                    <option value="+855" data-country="KH">+855</option>
                                    <option value="+856" data-country="LA">+856</option>
                                    <option value="+880" data-country="BD">+880</option>
                                    <option value="+886" data-country="TW">+886</option>
                                    <option value="+960" data-country="MV">+960</option>
                                    <option value="+961" data-country="LB">+961</option>
                                    <option value="+962" data-country="JO">+962</option>
                                    <option value="+963" data-country="SY">+963</option>
                                    <option value="+964" data-country="IQ">+964</option>
                                    <option value="+965" data-country="KW">+965</option>
                                    <option value="+966" data-country="SA">+966</option>
                                    <option value="+967" data-country="YE">+967</option>
                                    <option value="+968" data-country="OM">+968</option>
                                    <option value="+970" data-country="PS">+970</option>
                                    <option value="+971" data-country="AE">+971</option>
                                    <option value="+972" data-country="IL">+972</option>
                                    <option value="+973" data-country="BH">+973</option>
                                    <option value="+974" data-country="QA">+974</option>
                                    <option value="+975" data-country="BT">+975</option>
                                    <option value="+976" data-country="MN">+976</option>
                                    <option value="+977" data-country="NP">+977</option>
                                    <option value="+992" data-country="TJ">+992</option>
                                    <option value="+993" data-country="TM">+993</option>
                                    <option value="+994" data-country="AZ">+994</option>
                                    <option value="+995" data-country="GE">+995</option>
                                    <option value="+996" data-country="KG">+996</option>
                                    <option value="+998" data-country="UZ">+998</option>
                                </select>
                                <input type="tel" class="mobile-form-field flex-1" name="phone" placeholder="Enter phone number" required>
                            </div>
                            
                            <label class="mobile-label">KingsChat Username (Optional)</label>
                            <input type="text" class="mobile-form-field" name="kingschat_username" placeholder="Enter your KingsChat username">
                        </div>
                    </div>

                    <!-- Step 2: Church Information -->
                    <div id="mobile-step-2" class="mobile-step">
                        <div class="mobile-form-section">
                            <h2 class="mobile-form-title">Church Information</h2>
                            
                            <label class="mobile-label">Zone (Optional)</label>
                            <select id="mobile-zone-select" class="mobile-form-field" name="zone">
                                <option value="">Select your zone</option>
                                <!-- Zone options will be populated from zones.json -->
                            </select>
                            
                            <label class="mobile-label">Group (Optional)</label>
                            <input type="text" class="mobile-form-field" name="group" placeholder="Enter your group name">

                            <label class="mobile-label">Church (Optional)</label>
                            <input type="text" class="mobile-form-field" name="church" placeholder="Enter your church name">
                        </div>
                    </div>

                    <!-- Step 3: Crusade Details -->
                    <div id="mobile-step-3" class="mobile-step">
                        <div class="mobile-form-section">
                            <h2 class="mobile-form-title">Crusade Details</h2>
                            
                            <label class="mobile-label">Crusade Title *</label>
                            <div class="relative">
                                <input type="text" id="mobile-crusade-title-input" class="mobile-form-field pr-28" name="crusade_title" placeholder="Enter crusade title" required>
                                <button type="button" id="mobile-unsure-btn" class="absolute right-2 top-0 bottom-4 my-1 bg-white bg-opacity-20 hover:bg-opacity-30 text-white text-xs px-2 py-1 transition-all duration-300 hover:scale-105 focus:outline-none whitespace-nowrap flex items-center justify-center">
                                    Unsure? Click here
                                </button>
                            </div>
                            
                            <label class="mobile-label">Country *</label>
                            <select id="mobile-country-select" class="mobile-form-field" name="country" required>
                                <option value="">Select country</option>
                                <!-- Countries will be loaded from countries.json -->
                            </select>
                            
                            <label class="mobile-label">City/Location *</label>
                            <div class="relative">
                                <input type="text" id="mobile-city-location-input" class="mobile-form-field pr-28" name="location" placeholder="Enter city or location" required>
                                <button type="button" id="mobile-city-unsure-btn" class="absolute right-2 top-0 bottom-4 my-1 bg-white bg-opacity-20 hover:bg-opacity-30 text-white text-xs px-2 py-1 transition-all duration-300 hover:scale-105 focus:outline-none whitespace-nowrap flex items-center justify-center">
                                    Unsure? Click here
                                </button>
                            </div>
                            
                            <label class="mobile-label">Crusade Type *</label>
                            <select class="mobile-form-field" name="crusade_type" required>
                                <option value="">Select crusade type</option>
                                <option value="mall">Mall Crusades</option>
                                <option value="school">School Crusades</option>
                                <option value="hospital">Hospital Crusades</option>
                                <option value="street">Street Crusades</option>
                                <option value="town">Town Crusades</option>
                                <option value="city">City Crusades</option>
                                <option value="country">Country Crusades</option>
                                <option value="mega">Mega Crusades (10,000+ people)</option>
                                <option value="youths-aglow">Youths Aglow Crusades</option>
                                <option value="teevolution">Teevolution Crusades (Teens-focused)</option>
                                <option value="say-yes-to-kids">Say Yes To Kids Crusades</option>
                                <option value="nolb">NOLB Crusades (No One Left Behind) for visually/hearing impaired</option>
                                <option value="professionals">Specialized Crusades to Professionals</option>
                                <option value="community">Community Crusades</option>
                                <option value="leading-ladies">Leading Ladies Crusades</option>
                                <option value="mighty-men">Mighty Men Crusades</option>
                                <option value="language">Language Crusades (TNI Crusades)</option>
                                <option value="transport-station">Bus Station / Train Station Crusades</option>
                                <option value="tap2read">TAP2READ Crusades – promoting TAP2READ app with evangelism</option>
                                <option value="online-community">Online Community Crusades – targeting digital audiences</option>
                            </select>
                            
                            <label class="mobile-label">Venue *</label>
                            <div class="relative">
                                <input type="text" id="mobile-venue-input" class="mobile-form-field pr-28" name="venue" placeholder="Enter venue" required>
                                <button type="button" id="mobile-venue-unsure-btn" class="absolute right-2 top-0 bottom-4 my-1 bg-white bg-opacity-20 hover:bg-opacity-30 text-white text-xs px-2 py-1 transition-all duration-300 hover:scale-105 focus:outline-none whitespace-nowrap flex items-center justify-center">
                                    Unsure? Click here
                                </button>
                            </div>
                            
                            <label class="mobile-label">Expected Attendance *</label>
                            <select class="mobile-form-field" name="attendance" required>
                                <option value="">Select expected attendance</option>
                                <option value="100-500">100 - 500</option>
                                <option value="500-1000">500 - 1,000</option>
                                <option value="1000-5000">1,000 - 5,000</option>
                                <option value="5000-10000">5,000 - 10,000</option>
                                <option value="10000+">10,000+</option>
                                <option value="unsure">Unsure</option>
                            </select>
                        </div>
                    </div>

                    <!-- Step 4: Additional Information -->
                    <div id="mobile-step-4" class="mobile-step">
                        <div class="mobile-form-section">
                            <h2 class="mobile-form-title">Additional Information</h2>
                            
                            <label class="mobile-label">Additional Comments (Optional)</label>
                            <textarea class="mobile-form-field" name="comments" placeholder="Any additional information..." rows="4"></textarea>
                            
                            <label class="mobile-label">Preferred Contact Method *</label>
                            <select class="mobile-form-field" name="contact_method" required>
                                <option value="">Select contact method</option>
                                <option value="email">Email</option>
                                <option value="phone">Phone</option>
                                <option value="kingschat">KingsChat</option>
                                <option value="whatsapp">WhatsApp</option>
                            </select>
                            
                            <div class="mobile-form-section mt-4">
                                <h3 class="text-white font-semibold mb-3">Important Information</h3>
                                <ul class="text-white text-sm space-y-2 opacity-90">
                                    <li>• All crusade requests are subject to approval</li>
                                    <li>• Thank you for your commitment to spreading the Gospel</li>
                                    <li>• Please ensure all information is accurate</li>
                                    <li>• Additional documentation may be required</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Mobile Form Navigation -->
                <div class="flex justify-between mt-6">
                    <button type="button" id="mobile-prev-btn" class="mobile-btn secondary" style="display: none;">Previous</button>
                    <button type="button" id="mobile-next-btn" class="mobile-btn">Next</button>
                    <button type="submit" id="mobile-submit-btn" class="mobile-btn" style="display: none;">Submit Church Request</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Language Overlay -->
    <div id="mobile-language-overlay" class="mobile-overlay">
        <div class="mobile-overlay-header">
            <h2 class="mobile-overlay-title">SELECT LANGUAGE</h2>
            <button id="mobile-close-language" class="mobile-close-btn">×</button>
        </div>
        <div class="mobile-language-list mobile-scroll">
            <div class="mobile-language-item" data-lang="en">ENGLISH</div>
            <div class="mobile-language-item" data-lang="es">ESPAÑOL</div>
            <div class="mobile-language-item" data-lang="fr">FRANÇAIS</div>

            <div class="mobile-language-item" data-lang="it">ITALIANO</div>
            <div class="mobile-language-item" data-lang="pt">PORTUGUÊS</div>
            <div class="mobile-language-item" data-lang="ja">JAPANESE</div>
            <div class="mobile-language-item" data-lang="ko">KOREAN</div>
            <div class="mobile-language-item" data-lang="zh">CHINESE</div>
            <div class="mobile-language-item" data-lang="ar">ARABIC</div>
            <div class="mobile-language-item" data-lang="hi">HINDI</div>
            <div class="mobile-language-item" data-lang="ru">RUSSIAN</div>
        </div>
    </div>

    <script>
        // Mobile form state
        let currentStep = 1;
        const totalSteps = 4;
        let formData = {};

        // Initialize mobile form
        document.addEventListener('DOMContentLoaded', function() {
            setupMobileForm();
            setupMobileLanguage();
            updateMobileProgress();
            updateMobileButtons();
            loadMobileZones();
            loadMobileCountries();
            setupMobileUnsureButton();
            setupMobileCityUnsureButton();
            setupMobileVenueUnsureButton();
        });

        // Setup mobile form functionality
        function setupMobileForm() {
            const nextBtn = document.getElementById('mobile-next-btn');
            const prevBtn = document.getElementById('mobile-prev-btn');
            const submitBtn = document.getElementById('mobile-submit-btn');
            const form = document.getElementById('mobile-church-form');
            
            nextBtn.addEventListener('click', () => {
                if (validateMobileStep(currentStep)) {
                    if (currentStep < totalSteps) {
                        currentStep++;
                        updateMobileStep();
                    }
                }
            });
            
            prevBtn.addEventListener('click', () => {
                if (currentStep > 1) {
                    currentStep--;
                    updateMobileStep();
                }
            });
            
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                if (validateMobileStep(currentStep)) {
                    submitMobileForm();
                }
            });
        }

        // Update mobile step display
        function updateMobileStep() {
            // Hide all steps
            document.querySelectorAll('.mobile-step').forEach(step => {
                step.classList.remove('active');
            });
            
            // Show current step
            document.getElementById(`mobile-step-${currentStep}`).classList.add('active');
            
            updateMobileProgress();
            updateMobileButtons();
        }

        // Update mobile progress indicator
        function updateMobileProgress() {
            const dots = document.querySelectorAll('.mobile-progress-dot');
            dots.forEach((dot, index) => {
                if (index < currentStep) {
                    dot.classList.add('active');
                } else {
                    dot.classList.remove('active');
                }
            });
        }

        // Update mobile navigation buttons
        function updateMobileButtons() {
            const nextBtn = document.getElementById('mobile-next-btn');
            const prevBtn = document.getElementById('mobile-prev-btn');
            const submitBtn = document.getElementById('mobile-submit-btn');
            
            // Previous button
            if (currentStep > 1) {
                prevBtn.style.display = 'block';
            } else {
                prevBtn.style.display = 'none';
            }
            
            // Next/Submit button
            if (currentStep < totalSteps) {
                nextBtn.style.display = 'block';
                submitBtn.style.display = 'none';
            } else {
                nextBtn.style.display = 'none';
                submitBtn.style.display = 'block';
            }
        }

        // Validate mobile step
        function validateMobileStep(step) {
            const stepElement = document.getElementById(`mobile-step-${step}`);
            const requiredFields = stepElement.querySelectorAll('[required]');
            
            for (let field of requiredFields) {
                if (!field.value.trim()) {
                    showMobileValidationError('Please fill in all required fields.');
                    field.focus();
                    return false;
                }
            }
            
            return true;
        }

        // Submit mobile form
        function submitMobileForm() {
            // Collect form data
            const formData = new FormData();
            const allFields = document.querySelectorAll('.mobile-form-field');
            const fieldData = {};
            
            // First collect all field data
            allFields.forEach(field => {
                if (field.name && field.value.trim() !== '') {
                    fieldData[field.name] = field.value.trim();
                }
            });
            
            // Combine country code and phone number into a single phone field
            if (fieldData.country_code && fieldData.phone) {
                fieldData.phone = fieldData.country_code + ' ' + fieldData.phone;
                delete fieldData.country_code; // Remove the separate country code field
            }
            
            // Add all field data to FormData
            Object.keys(fieldData).forEach(key => {
                formData.append(key, fieldData[key]);
            });
            
            showMobileValidationError('Submitting your church crusade request...', 'info');
            
            fetch('pastor-crusade-request-handler.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMobileValidationError('Church crusade request submitted successfully!', 'success');
                    setTimeout(() => {
                        document.getElementById('mobile-church-form').reset();
                        currentStep = 1;
                        updateMobileStep();
                    }, 3000);
                } else {
                    showMobileValidationError(data.message || 'An error occurred. Please try again.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showMobileValidationError('Network error. Please check your connection and try again.');
            });
        }

        // Show mobile validation error
        function showMobileValidationError(message, type = 'error') {
            const existingMessage = document.querySelector('.mobile-validation-message');
            if (existingMessage) {
                existingMessage.remove();
            }
            
            const messageDiv = document.createElement('div');
            messageDiv.className = `mobile-validation-message ${type === 'success' ? 'success' : ''}`;
            messageDiv.textContent = message;
            
            document.body.appendChild(messageDiv);
            
            setTimeout(() => {
                messageDiv.classList.add('show');
            }, 100);
            
            setTimeout(() => {
                messageDiv.classList.remove('show');
                setTimeout(() => {
                    messageDiv.remove();
                }, 300);
            }, 5000);
        }

        // Load mobile zones
        function loadMobileZones() {
            const zoneSelect = document.getElementById('mobile-zone-select');
            
            fetch('zones.json')
                .then(response => {
                    console.log('Mobile zone fetch response:', response.status, response.statusText);
                    if (!response.ok) {
                        throw new Error(`Network response was not ok: ${response.status} ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Mobile zones data loaded:', data);
                    const zones = data.zones;
                    if (!zones || !Array.isArray(zones)) {
                        throw new Error('Invalid zones data format');
                    }
                    zones.forEach(zone => {
                        const option = document.createElement('option');
                        option.value = zone;
                        option.textContent = zone;
                        zoneSelect.appendChild(option);
                    });
                    console.log(`Mobile loaded ${zones.length} zones successfully`);
                })
                .catch(error => {
                    console.error('Error loading mobile zones:', error);
                    // Fallback zones if JSON fails to load
                    const fallbackZones = [
                        'Not Applicable',
                        'GYLF',
                        'ISM',
                        'REON',
                        'RINT',
                        'TNI',
                        'Lagos Zone 1',
                        'Lagos Zone 2',
                        'Abuja Zone',
                        'BLW Nigeria Region'
                    ];
                    fallbackZones.forEach(zone => {
                        const option = document.createElement('option');
                        option.value = zone;
                        option.textContent = zone;
                        zoneSelect.appendChild(option);
                    });
                    console.log('Using mobile fallback zones due to error');
                });
        }

        // Load mobile countries
        function loadMobileCountries() {
            const countrySelect = document.getElementById('mobile-country-select');
            
            fetch('countries.json')
                .then(response => response.json())
                .then(countries => {
                    countries.forEach(country => {
                        const option = document.createElement('option');
                        option.value = country.toLowerCase().replace(/\s+/g, '-');
                        option.textContent = country;
                        countrySelect.appendChild(option);
                    });
                })
                .catch(error => {
                    console.error('Error loading countries:', error);
                });
        }

        // Setup mobile language functionality
        function setupMobileLanguage() {
            const languageBtn = document.getElementById('mobile-language-btn');
            const languageOverlay = document.getElementById('mobile-language-overlay');
            const closeLanguage = document.getElementById('mobile-close-language');
            const languageItems = document.querySelectorAll('.mobile-language-item');

            languageBtn.addEventListener('click', () => {
                languageOverlay.classList.add('active');
                document.body.style.overflow = 'hidden';
            });

            closeLanguage.addEventListener('click', () => {
                languageOverlay.classList.remove('active');
                document.body.style.overflow = 'auto';
            });

            languageItems.forEach(item => {
                item.addEventListener('click', () => {
                    const lang = item.dataset.lang;
                    const langText = item.textContent.substring(0, 2);
                    
                    languageBtn.querySelector('span').textContent = langText;
                    languageOverlay.classList.remove('active');
                    document.body.style.overflow = 'auto';
                    
                    gsap.to(item, {
                        scale: 0.95,
                        duration: 0.1,
                        yoyo: true,
                        repeat: 1
                    });
                });
            });

            languageOverlay.addEventListener('click', (e) => {
                if (e.target === languageOverlay) {
                    languageOverlay.classList.remove('active');
                    document.body.style.overflow = 'auto';
                }
            });
        }

        // Setup mobile unsure button functionality
        function setupMobileUnsureButton() {
            const crusadeTitleInput = document.getElementById('mobile-crusade-title-input');
            const unsureBtn = document.getElementById('mobile-unsure-btn');
            
            if (!crusadeTitleInput || !unsureBtn) return;
            
            function toggleMobileUnsureButton() {
                const hasUnsureText = crusadeTitleInput.value.toLowerCase().includes('unsure');
                
                if (hasUnsureText) {
                    gsap.to(unsureBtn, {
                        opacity: 0,
                        scale: 0.8,
                        duration: 0.3,
                        ease: "power2.out",
                        onComplete: () => {
                            unsureBtn.style.display = 'none';
                        }
                    });
                } else {
                    unsureBtn.style.display = 'block';
                    gsap.fromTo(unsureBtn, 
                        {
                            opacity: 0,
                            scale: 0.8,
                            y: 0
                        },
                        {
                            opacity: 1,
                            scale: 1,
                            y: 0,
                            duration: 0.3,
                            ease: "back.out(1.7)"
                        }
                    );
                }
            }
            
            unsureBtn.addEventListener('click', () => {
                crusadeTitleInput.value = 'Unsure';
                
                gsap.to(unsureBtn, {
                    scale: 0.9,
                    duration: 0.1,
                    yoyo: true,
                    repeat: 1,
                    ease: "power2.inOut",
                    onComplete: () => {
                        gsap.to(unsureBtn, {
                            opacity: 0,
                            scale: 0.8,
                            y: -10,
                            duration: 0.4,
                            ease: "power2.out",
                            onComplete: () => {
                                unsureBtn.style.display = 'none';
                                gsap.set(unsureBtn, { y: 0 });
                            }
                        });
                    }
                });
                
                gsap.to(crusadeTitleInput, {
                    boxShadow: '0 0 20px rgba(96, 165, 250, 0.5)',
                    duration: 0.3,
                    yoyo: true,
                    repeat: 1,
                    ease: "power2.inOut"
                });
                
                crusadeTitleInput.focus();
            });
            
            crusadeTitleInput.addEventListener('input', toggleMobileUnsureButton);
            crusadeTitleInput.addEventListener('blur', toggleMobileUnsureButton);
            crusadeTitleInput.addEventListener('focus', toggleMobileUnsureButton);
            
            toggleMobileUnsureButton();
        }

        // Setup mobile city unsure button functionality
        function setupMobileCityUnsureButton() {
            const cityInput = document.getElementById('mobile-city-location-input');
            const unsureBtn = document.getElementById('mobile-city-unsure-btn');
            
            if (!cityInput || !unsureBtn) return;
            
            function toggleMobileCityUnsureButton() {
                const hasUnsureText = cityInput.value.toLowerCase().includes('unsure');
                
                if (hasUnsureText) {
                    gsap.to(unsureBtn, {
                        opacity: 0,
                        scale: 0.8,
                        duration: 0.3,
                        ease: "power2.out",
                        onComplete: () => {
                            unsureBtn.style.display = 'none';
                        }
                    });
                } else {
                    unsureBtn.style.display = 'block';
                    gsap.fromTo(unsureBtn, 
                        {
                            opacity: 0,
                            scale: 0.8,
                            y: 0
                        },
                        {
                            opacity: 1,
                            scale: 1,
                            y: 0,
                            duration: 0.3,
                            ease: "back.out(1.7)"
                        }
                    );
                }
            }
            
            unsureBtn.addEventListener('click', () => {
                cityInput.value = 'Unsure';
                
                gsap.to(unsureBtn, {
                    scale: 0.9,
                    duration: 0.1,
                    yoyo: true,
                    repeat: 1,
                    ease: "power2.inOut",
                    onComplete: () => {
                        gsap.to(unsureBtn, {
                            opacity: 0,
                            scale: 0.8,
                            y: -10,
                            duration: 0.4,
                            ease: "power2.out",
                            onComplete: () => {
                                unsureBtn.style.display = 'none';
                                gsap.set(unsureBtn, { y: 0 });
                            }
                        });
                    }
                });
                
                gsap.to(cityInput, {
                    boxShadow: '0 0 20px rgba(96, 165, 250, 0.5)',
                    duration: 0.3,
                    yoyo: true,
                    repeat: 1,
                    ease: "power2.inOut"
                });
                
                cityInput.focus();
            });
            
            cityInput.addEventListener('input', toggleMobileCityUnsureButton);
            cityInput.addEventListener('blur', toggleMobileCityUnsureButton);
            cityInput.addEventListener('focus', toggleMobileCityUnsureButton);
            
            toggleMobileCityUnsureButton();
        }

        // Setup mobile venue unsure button functionality
        function setupMobileVenueUnsureButton() {
            const venueInput = document.getElementById('mobile-venue-input');
            const unsureBtn = document.getElementById('mobile-venue-unsure-btn');
            
            if (!venueInput || !unsureBtn) return;
            
            function toggleMobileVenueUnsureButton() {
                const hasUnsureText = venueInput.value.toLowerCase().includes('unsure');
                
                if (hasUnsureText) {
                    gsap.to(unsureBtn, {
                        opacity: 0,
                        scale: 0.8,
                        duration: 0.3,
                        ease: "power2.out",
                        onComplete: () => {
                            unsureBtn.style.display = 'none';
                        }
                    });
                } else {
                    unsureBtn.style.display = 'block';
                    gsap.fromTo(unsureBtn, 
                        {
                            opacity: 0,
                            scale: 0.8,
                            y: 0
                        },
                        {
                            opacity: 1,
                            scale: 1,
                            y: 0,
                            duration: 0.3,
                            ease: "back.out(1.7)"
                        }
                    );
                }
            }
            
            unsureBtn.addEventListener('click', () => {
                venueInput.value = 'Unsure';
                
                gsap.to(unsureBtn, {
                    scale: 0.9,
                    duration: 0.1,
                    yoyo: true,
                    repeat: 1,
                    ease: "power2.inOut",
                    onComplete: () => {
                        gsap.to(unsureBtn, {
                            opacity: 0,
                            scale: 0.8,
                            y: -10,
                            duration: 0.4,
                            ease: "power2.out",
                            onComplete: () => {
                                unsureBtn.style.display = 'none';
                                gsap.set(unsureBtn, { y: 0 });
                            }
                        });
                    }
                });
                
                gsap.to(venueInput, {
                    boxShadow: '0 0 20px rgba(96, 165, 250, 0.5)',
                    duration: 0.3,
                    yoyo: true,
                    repeat: 1,
                    ease: "power2.inOut"
                });
                
                venueInput.focus();
            });
            
            venueInput.addEventListener('input', toggleMobileVenueUnsureButton);
            venueInput.addEventListener('blur', toggleMobileVenueUnsureButton);
            venueInput.addEventListener('focus', toggleMobileVenueUnsureButton);
            
            toggleMobileVenueUnsureButton();
        }
    </script>
</body>
</html> 