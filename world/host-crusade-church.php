<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Host Your Crusade - Church Registration</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="mobile-redirect.js"></script>
    <style>
        /* Ensure all elements have no rounded corners */
        * {
            border-radius: 0 !important;
        }
        
        /* Custom button styling */
        button {
            border-radius: 0 !important;
        }
        
        /* Language overlay and buttons */
        #language-overlay,
        #language-btn,
        #sidebar-overlay {
            border-radius: 0 !important;
        }
        
        /* Progress indicators and form elements */
        .progress-box,
        .form-field,
        .form-section {
            border-radius: 0 !important;
        }
        
        .form-step {
            position: absolute;
            width: 100%;
            top: 0;
            left: 0;
        }
        
        .form-step.active {
            position: relative;
        }
        
        #form-container {
            position: relative;
            min-height: 500px;
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .form-section {
                padding: 1.5rem;
            }
            
            .form-step {
                min-height: auto;
            }
        }
        
        /* Background styling */
        body {
            overflow-x: hidden;
            overflow-y: auto;
            background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
            min-height: 100vh;
        }
        
        /* Form section styling */
        .form-section {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 2;
        }
        
        /* Progress box styling */
        .progress-box {
            width: 60px;
            height: 8px;
            background: rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            transform: scaleX(0.8);
        }
        
        .progress-box.active {
            background-color: #60a5fa !important;
            transform: scaleX(1.2);
        }
        
        /* Form field styling to match index.php glass morphism */
        .form-field {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 0;
            transition: all 0.3s ease;
            color: white;
        }
        
        .form-field:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #60a5fa;
            box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.2);
            outline: none;
        }
        
        .form-field::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        
        /* Specific dropdown styling */
        select.form-field {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 0;
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.5rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-right: 2.5rem;
        }
        
        select.form-field:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #60a5fa;
            box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.2);
        }
        
        /* Dropdown options styling */
        select.form-field option {
            background-color: #1e293b;
            color: white;
            padding: 0.5rem;
            border: none;
            border-radius: 0;
        }
        
        /* Sidebar overlay styling */
        #sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 300px;
            height: 100vh;
            background: rgba(0, 0, 0, 0.95);
            backdrop-filter: blur(20px);
            transform: translateX(-100%);
            transition: transform 0.5s ease;
            z-index: 60;
            padding: 2rem;
            overflow-y: auto;
        }
        
        /* Navigation link styling */
        .nav-link {
            display: block;
            color: white;
            text-decoration: none;
            padding: 1rem 0;
            font-size: 1.1rem;
            font-weight: 300;
            letter-spacing: 0.05em;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            opacity: 0;
            transform: translateX(-20px);
        }
        
        .nav-link:hover {
            color: #60a5fa;
            transform: translateX(0px);
        }
        
        .nav-link.active {
            color: #60a5fa;
            font-weight: 500;
        }
        
        /* Mobile overlay styles */
        .mobile-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.95);
            z-index: 90;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            padding: 1rem;
        }
        
        .mobile-overlay.active {
            opacity: 1;
            visibility: visible;
        }
        
        .mobile-overlay-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-top: 1rem;
        }
        
        .mobile-overlay-title {
            font-size: 1.5rem;
            font-weight: 900;
            color: white;
        }
        
        .mobile-close-btn {
            font-size: 2rem;
            color: white;
            background: none;
            border: none;
            padding: 0.5rem;
            transition: transform 0.3s ease;
        }
        
        .mobile-close-btn:hover {
            transform: rotate(90deg);
        }
        
        /* Mobile language list */
        .mobile-language-list {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
            max-height: 70vh;
            overflow-y: auto;
            padding-right: 0.5rem;
        }
        
        .mobile-language-item {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1.5rem;
            color: white;
            font-size: 1.1rem;
            font-weight: 700;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .mobile-language-item:hover {
            background: rgba(255, 255, 255, 0.2);
            color: #60a5fa;
            transform: translateY(-2px);
        }
        
        /* Mobile scroll styling */
        .mobile-scroll::-webkit-scrollbar {
            width: 4px;
        }
        
        .mobile-scroll::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }
        
        .mobile-scroll::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
        }
        
        /* Validation message styling */
        .validation-message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(239, 68, 68, 0.95);
            color: white;
            padding: 1rem 2rem;
            border-radius: 0;
            z-index: 1000;
            font-weight: 500;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        
        .validation-message.show {
            opacity: 1;
            visibility: visible;
        }
        
        .validation-message.success {
            background: rgba(34, 197, 94, 0.95);
        }
    </style>
</head>
<body>
    <!-- Floating Language Button -->
    <button id="language-btn" class="fixed top-8 right-8 z-40 bg-white bg-opacity-10 backdrop-blur-sm border border-white border-opacity-20 px-4 py-2 text-white hover:bg-opacity-20 hover:text-blue-400 transition-all duration-300">
        <div class="flex items-center space-x-2">
            <span class="text-sm font-light tracking-wide">EN</span>
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
        </div>
    </button>

    <!-- Floating Logo -->
    <div id="floating-logo" class="fixed top-8 left-8 z-40 opacity-0 transition-all duration-300">
        <img src="assets/images/logo.webp" alt="Logo" class="h-10 w-auto">
    </div>

    <!-- Hover Area for Sidebar -->
    <div id="hover-area" class="fixed left-0 top-0 w-12 h-full z-30 bg-transparent"></div>

    <!-- Arrow Indicator -->
    <div id="arrow-indicator" class="fixed left-4 top-1/2 transform -translate-y-1/2 z-40 opacity-30 hover:opacity-100 transition-opacity duration-300 pointer-events-none">
        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
        </svg>
    </div>

    <!-- Sidebar Overlay -->
    <div id="sidebar-overlay" class="fixed left-0 top-0 w-80 h-full bg-black bg-opacity-95 backdrop-blur-20 transform -translate-x-full transition-transform duration-500 z-60 p-8 overflow-y-auto">
        <div class="flex flex-col h-full">
            <!-- Logo -->
            <div class="mb-8">
                <img src="assets/images/logo.webp" alt="Logo" class="h-12 w-auto">
            </div>
            
            <!-- Navigation Links -->
            <nav class="flex-1">
                <a href="index.php" class="nav-link">Home</a>
                <a href="host-crusade.php" class="nav-link">Host Individual Crusade</a>
                <a href="host-crusade-church.php" class="nav-link active">Host Church Crusade</a>
            </nav>
            
            <!-- Language Button -->
            <button id="sidebar-language-btn" class="nav-link text-left">
                Language
            </button>
        </div>
    </div>

    <!-- Language Overlay -->
    <div id="language-overlay" class="fixed inset-0 bg-black bg-opacity-95 z-[100] opacity-0 invisible transition-all duration-500 flex items-center justify-center">
        <div class="w-full max-w-4xl px-8">
            <!-- Close Button -->
            <button id="close-overlay" class="absolute top-8 right-8 text-white text-4xl font-light hover:rotate-90 transition-transform duration-300">
                ×
            </button>
            
            <!-- Language List -->
            <div id="language-list" class="space-y-8">
                <div class="language-item opacity-0 transform translate-y-20" data-lang="en">
                    <h2 class="text-6xl md:text-8xl font-black text-white font-mono tracking-wider hover:text-blue-400 transition-colors duration-300 cursor-pointer">
                        ENGLISH
                    </h2>
                </div>
                <div class="language-item opacity-0 transform translate-y-20" data-lang="es">
                    <h2 class="text-6xl md:text-8xl font-black text-white font-mono tracking-wider hover:text-blue-400 transition-colors duration-300 cursor-pointer">
                        ESPAÑOL
                    </h2>
                </div>
                <div class="language-item opacity-0 transform translate-y-20" data-lang="fr">
                    <h2 class="text-6xl md:text-8xl font-black text-white font-mono tracking-wider hover:text-blue-400 transition-colors duration-300 cursor-pointer">
                        FRANÇAIS
                    </h2>
                </div>

                <div class="language-item opacity-0 transform translate-y-20" data-lang="it">
                    <h2 class="text-6xl md:text-8xl font-black text-white font-mono tracking-wider hover:text-blue-400 transition-colors duration-300 cursor-pointer">
                        ITALIANO
                    </h2>
                </div>
                <div class="language-item opacity-0 transform translate-y-20" data-lang="pt">
                    <h2 class="text-6xl md:text-8xl font-black text-white font-mono tracking-wider hover:text-blue-400 transition-colors duration-300 cursor-pointer">
                        PORTUGUÊS
                    </h2>
                </div>
                <div class="language-item opacity-0 transform translate-y-20" data-lang="ja">
                    <h2 class="text-6xl md:text-8xl font-black text-white font-mono tracking-wider hover:text-blue-400 transition-colors duration-300 cursor-pointer">
                        JAPANESE
                    </h2>
                </div>
                <div class="language-item opacity-0 transform translate-y-20" data-lang="ko">
                    <h2 class="text-6xl md:text-8xl font-black text-white font-mono tracking-wider hover:text-blue-400 transition-colors duration-300 cursor-pointer">
                        KOREAN
                    </h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div id="main-content" class="min-h-screen flex items-center justify-center transform translate-x-full p-4 sm:p-6 lg:p-8">
        <div class="w-full max-w-6xl">
            <!-- Form Header -->
            <div id="form-header" class="text-center mb-8 lg:mb-12">
                <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-black text-white leading-tight tracking-tight mb-4 lg:mb-6">
                    HOST YOUR CHURCH CRUSADE
                </h1>
                <p class="text-lg sm:text-xl text-white font-light leading-relaxed max-w-4xl mx-auto px-4">
                    Register your church or organization to host a crusade. Fill out this form and our team will get in touch with you.
                </p>
            </div>

            <!-- Progress Indicator -->
            <div id="progress-indicator" class="flex justify-center mb-8 lg:mb-12">
                <div class="flex space-x-3 sm:space-x-4">
                    <div class="progress-box active"></div>
                    <div class="progress-box"></div>
                    <div class="progress-box"></div>
                    <div class="progress-box"></div>
                </div>
            </div>

            <!-- Form Container -->
            <div id="form-container" class="relative overflow-hidden">
                <!-- Step 1: Personal Information -->
                <div id="step-1" class="form-step active">
                    <div class="form-section p-6 sm:p-8 lg:p-10">
                        <!-- Hidden field to identify this as church registration -->
                        <input type="hidden" name="registration_type" value="church">
                        
                        <h2 class="text-2xl sm:text-3xl lg:text-4xl font-black text-white mb-6 sm:mb-8 lg:mb-10 tracking-tight">Personal Information</h2>
                        
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
                            <div>
                                <label class="block text-white font-light mb-2 sm:mb-3 text-base lg:text-lg">Designation *</label>
                                <select name="designation" class="form-field w-full px-4 sm:px-6 py-3 sm:py-4 text-white placeholder-gray-300 focus:outline-none" required>
                                    <option value="">Select your designation</option>
                                    <option value="brother">Brother</option>
                                    <option value="sister">Sister</option>
                                    <option value="deacon">Deacon</option>
                                    <option value="deaconess">Deaconess</option>
                                    <option value="pastor">Pastor</option>
                                    <option value="evangelist">Evangelist</option>
                                    <option value="dr">Dr</option>
                                    <option value="apostle">Apostle</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-white font-light mb-2 sm:mb-3 text-base lg:text-lg">First Name *</label>
                                <input type="text" name="first_name" placeholder="Enter your first name" class="form-field w-full px-4 sm:px-6 py-3 sm:py-4 text-white placeholder-gray-300 focus:outline-none" required>
                            </div>
                            
                            <div>
                                <label class="block text-white font-light mb-2 sm:mb-3 text-base lg:text-lg">Last Name *</label>
                                <input type="text" name="last_name" placeholder="Enter your last name" class="form-field w-full px-4 sm:px-6 py-3 sm:py-4 text-white placeholder-gray-300 focus:outline-none" required>
                            </div>
                            
                            <div>
                                <label class="block text-white font-light mb-2 sm:mb-3 text-base lg:text-lg">Email Address *</label>
                                <input type="email" name="email" placeholder="Enter your email address" class="form-field w-full px-4 sm:px-6 py-3 sm:py-4 text-white placeholder-gray-300 focus:outline-none" required>
                            </div>
                            
                            <div>
                                <label class="block text-white font-light mb-2 sm:mb-3 text-base lg:text-lg">Phone Number *</label>
                                <div class="flex">
                                    <select id="country-code" name="country_code" class="form-field w-32 px-2 py-3 sm:py-4 text-white focus:outline-none border-r-0" style="border-top-right-radius: 0; border-bottom-right-radius: 0;" required>
                                        <option value="+1" data-country="US">+1</option>
                                        <option value="+1" data-country="CA">+1</option>
                                        <option value="******" data-country="BS">******</option>
                                        <option value="******" data-country="BB">******</option>
                                        <option value="******" data-country="AI">******</option>
                                        <option value="******" data-country="AG">******</option>
                                        <option value="******" data-country="VG">******</option>
                                        <option value="+1 340" data-country="VI">+1 340</option>
                                        <option value="+1 345" data-country="KY">+1 345</option>
                                        <option value="+1 441" data-country="BM">+1 441</option>
                                        <option value="+1 473" data-country="GD">+1 473</option>
                                        <option value="+1 649" data-country="TC">+1 649</option>
                                        <option value="+1 664" data-country="MS">+1 664</option>
                                        <option value="+1 670" data-country="MP">+1 670</option>
                                        <option value="+1 671" data-country="GU">+1 671</option>
                                        <option value="+1 684" data-country="AS">+1 684</option>
                                        <option value="+1 758" data-country="LC">+1 758</option>
                                        <option value="+1 767" data-country="DM">+1 767</option>
                                        <option value="+1 784" data-country="VC">+1 784</option>
                                        <option value="+1 787" data-country="PR">+1 787</option>
                                        <option value="+1 809" data-country="DO">+1 809</option>
                                        <option value="+1 868" data-country="TT">+1 868</option>
                                        <option value="+1 869" data-country="KN">+1 869</option>
                                        <option value="+1 876" data-country="JM">+1 876</option>
                                        <option value="+7" data-country="RU">+7</option>
                                        <option value="+7" data-country="KZ">+7</option>
                                        <option value="+7 840" data-country="AB">+7 840</option>
                                        <option value="+20" data-country="EG">+20</option>
                                        <option value="+27" data-country="ZA">+27</option>
                                        <option value="+30" data-country="GR">+30</option>
                                        <option value="+31" data-country="NL">+31</option>
                                        <option value="+32" data-country="BE">+32</option>
                                        <option value="+33" data-country="FR">+33</option>
                                        <option value="+34" data-country="ES">+34</option>
                                        <option value="+36" data-country="HU">+36</option>
                                        <option value="+39" data-country="IT">+39</option>
                                        <option value="+40" data-country="RO">+40</option>
                                        <option value="+41" data-country="CH">+41</option>
                                        <option value="+43" data-country="AT">+43</option>
                                        <option value="+44" data-country="GB">+44</option>
                                        <option value="+45" data-country="DK">+45</option>
                                        <option value="+46" data-country="SE">+46</option>
                                        <option value="+47" data-country="NO">+47</option>
                                        <option value="+48" data-country="PL">+48</option>
                                        <option value="+49" data-country="DE">+49</option>
                                        <option value="+51" data-country="PE">+51</option>
                                        <option value="+52" data-country="MX">+52</option>
                                        <option value="+53" data-country="CU">+53</option>
                                        <option value="+54" data-country="AR">+54</option>
                                        <option value="+55" data-country="BR">+55</option>
                                        <option value="+56" data-country="CL">+56</option>
                                        <option value="+57" data-country="CO">+57</option>
                                        <option value="+58" data-country="VE">+58</option>
                                        <option value="+60" data-country="MY">+60</option>
                                        <option value="+61" data-country="AU">+61</option>
                                        <option value="+61" data-country="CX">+61</option>
                                        <option value="+61" data-country="CC">+61</option>
                                        <option value="+62" data-country="ID">+62</option>
                                        <option value="+63" data-country="PH">+63</option>
                                        <option value="+64" data-country="NZ">+64</option>
                                        <option value="+65" data-country="SG">+65</option>
                                        <option value="+66" data-country="TH">+66</option>
                                        <option value="+81" data-country="JP">+81</option>
                                        <option value="+82" data-country="KR">+82</option>
                                        <option value="+84" data-country="VN">+84</option>
                                        <option value="+86" data-country="CN">+86</option>
                                        <option value="+90" data-country="TR">+90</option>
                                        <option value="+91" data-country="IN">+91</option>
                                        <option value="+92" data-country="PK">+92</option>
                                        <option value="+93" data-country="AF">+93</option>
                                        <option value="+94" data-country="LK">+94</option>
                                        <option value="+95" data-country="MM">+95</option>
                                        <option value="+98" data-country="IR">+98</option>
                                        <option value="+212" data-country="MA">+212</option>
                                        <option value="+212" data-country="EH">+212</option>
                                        <option value="+213" data-country="DZ">+213</option>
                                        <option value="+216" data-country="TN">+216</option>
                                        <option value="+218" data-country="LY">+218</option>
                                        <option value="+220" data-country="GM">+220</option>
                                        <option value="+221" data-country="SN">+221</option>
                                        <option value="+222" data-country="MR">+222</option>
                                        <option value="+223" data-country="ML">+223</option>
                                        <option value="+224" data-country="GN">+224</option>
                                        <option value="+225" data-country="CI">+225</option>
                                        <option value="+226" data-country="BF">+226</option>
                                        <option value="+227" data-country="NE">+227</option>
                                        <option value="+228" data-country="TG">+228</option>
                                        <option value="+229" data-country="BJ">+229</option>
                                        <option value="+230" data-country="MU">+230</option>
                                        <option value="+231" data-country="LR">+231</option>
                                        <option value="+232" data-country="SL">+232</option>
                                        <option value="+233" data-country="GH">+233</option>
                                        <option value="+234" data-country="NG">+234</option>
                                        <option value="+235" data-country="TD">+235</option>
                                        <option value="+236" data-country="CF">+236</option>
                                        <option value="+237" data-country="CM">+237</option>
                                        <option value="+238" data-country="CV">+238</option>
                                        <option value="+239" data-country="ST">+239</option>
                                        <option value="+240" data-country="GQ">+240</option>
                                        <option value="+241" data-country="GA">+241</option>
                                        <option value="+242" data-country="CG">+242</option>
                                        <option value="+243" data-country="CD">+243</option>
                                        <option value="+244" data-country="AO">+244</option>
                                        <option value="+245" data-country="GW">+245</option>
                                        <option value="+246" data-country="IO">+246</option>
                                        <option value="+248" data-country="SC">+248</option>
                                        <option value="+249" data-country="SD">+249</option>
                                        <option value="+250" data-country="RW">+250</option>
                                        <option value="+251" data-country="ET">+251</option>
                                        <option value="+252" data-country="SO">+252</option>
                                        <option value="+253" data-country="DJ">+253</option>
                                        <option value="+254" data-country="KE">+254</option>
                                        <option value="+255" data-country="TZ">+255</option>
                                        <option value="+256" data-country="UG">+256</option>
                                        <option value="+257" data-country="BI">+257</option>
                                        <option value="+258" data-country="MZ">+258</option>
                                        <option value="+260" data-country="ZM">+260</option>
                                        <option value="+261" data-country="MG">+261</option>
                                        <option value="+262" data-country="YT">+262</option>
                                        <option value="+262" data-country="RE">+262</option>
                                        <option value="+263" data-country="ZW">+263</option>
                                        <option value="+264" data-country="NA">+264</option>
                                        <option value="+265" data-country="MW">+265</option>
                                        <option value="+266" data-country="LS">+266</option>
                                        <option value="+267" data-country="BW">+267</option>
                                        <option value="+268" data-country="SZ">+268</option>
                                        <option value="+269" data-country="KM">+269</option>
                                        <option value="+290" data-country="SH">+290</option>
                                        <option value="+291" data-country="ER">+291</option>
                                        <option value="+297" data-country="AW">+297</option>
                                        <option value="+298" data-country="FO">+298</option>
                                        <option value="+299" data-country="GL">+299</option>
                                        <option value="+350" data-country="GI">+350</option>
                                        <option value="+351" data-country="PT">+351</option>
                                        <option value="+352" data-country="LU">+352</option>
                                        <option value="+353" data-country="IE">+353</option>
                                        <option value="+354" data-country="IS">+354</option>
                                        <option value="+355" data-country="AL">+355</option>
                                        <option value="+356" data-country="MT">+356</option>
                                        <option value="+357" data-country="CY">+357</option>
                                        <option value="+358" data-country="FI">+358</option>
                                        <option value="+359" data-country="BG">+359</option>
                                        <option value="+370" data-country="LT">+370</option>
                                        <option value="+371" data-country="LV">+371</option>
                                        <option value="+372" data-country="EE">+372</option>
                                        <option value="+373" data-country="MD">+373</option>
                                        <option value="+374" data-country="AM">+374</option>
                                        <option value="+375" data-country="BY">+375</option>
                                        <option value="+376" data-country="AD">+376</option>
                                        <option value="+377" data-country="MC">+377</option>
                                        <option value="+378" data-country="SM">+378</option>
                                        <option value="+380" data-country="UA">+380</option>
                                        <option value="+381" data-country="RS">+381</option>
                                        <option value="+382" data-country="ME">+382</option>
                                        <option value="+385" data-country="HR">+385</option>
                                        <option value="+386" data-country="SI">+386</option>
                                        <option value="+387" data-country="BA">+387</option>
                                        <option value="+389" data-country="MK">+389</option>
                                        <option value="+420" data-country="CZ">+420</option>
                                        <option value="+421" data-country="SK">+421</option>
                                        <option value="+423" data-country="LI">+423</option>
                                        <option value="+500" data-country="FK">+500</option>
                                        <option value="+501" data-country="BZ">+501</option>
                                        <option value="+502" data-country="GT">+502</option>
                                        <option value="+503" data-country="SV">+503</option>
                                        <option value="+504" data-country="HN">+504</option>
                                        <option value="+505" data-country="NI">+505</option>
                                        <option value="+506" data-country="CR">+506</option>
                                        <option value="+507" data-country="PA">+507</option>
                                        <option value="+508" data-country="PM">+508</option>
                                        <option value="+509" data-country="HT">+509</option>
                                        <option value="+590" data-country="GP">+590</option>
                                        <option value="+591" data-country="BO">+591</option>
                                        <option value="+592" data-country="GY">+592</option>
                                        <option value="+593" data-country="EC">+593</option>
                                        <option value="+594" data-country="GF">+594</option>
                                        <option value="+595" data-country="PY">+595</option>
                                        <option value="+596" data-country="MQ">+596</option>
                                        <option value="+597" data-country="SR">+597</option>
                                        <option value="+598" data-country="UY">+598</option>
                                        <option value="+599" data-country="AN">+599</option>
                                        <option value="+670" data-country="TL">+670</option>
                                        <option value="+672" data-country="AQ">+672</option>
                                        <option value="+672" data-country="NF">+672</option>
                                        <option value="+673" data-country="BN">+673</option>
                                        <option value="+674" data-country="NR">+674</option>
                                        <option value="+675" data-country="PG">+675</option>
                                        <option value="+676" data-country="TO">+676</option>
                                        <option value="+677" data-country="SB">+677</option>
                                        <option value="+678" data-country="VU">+678</option>
                                        <option value="+679" data-country="FJ">+679</option>
                                        <option value="+680" data-country="PW">+680</option>
                                        <option value="+681" data-country="WF">+681</option>
                                        <option value="+682" data-country="CK">+682</option>
                                        <option value="+683" data-country="NU">+683</option>
                                        <option value="+685" data-country="WS">+685</option>
                                        <option value="+686" data-country="KI">+686</option>
                                        <option value="+687" data-country="NC">+687</option>
                                        <option value="+688" data-country="TV">+688</option>
                                        <option value="+689" data-country="PF">+689</option>
                                        <option value="+690" data-country="TK">+690</option>
                                        <option value="+691" data-country="FM">+691</option>
                                        <option value="+692" data-country="MH">+692</option>
                                        <option value="+850" data-country="KP">+850</option>
                                        <option value="+852" data-country="HK">+852</option>
                                        <option value="+853" data-country="MO">+853</option>
                                        <option value="+855" data-country="KH">+855</option>
                                        <option value="+856" data-country="LA">+856</option>
                                        <option value="+880" data-country="BD">+880</option>
                                        <option value="+886" data-country="TW">+886</option>
                                        <option value="+960" data-country="MV">+960</option>
                                        <option value="+961" data-country="LB">+961</option>
                                        <option value="+962" data-country="JO">+962</option>
                                        <option value="+963" data-country="SY">+963</option>
                                        <option value="+964" data-country="IQ">+964</option>
                                        <option value="+965" data-country="KW">+965</option>
                                        <option value="+966" data-country="SA">+966</option>
                                        <option value="+967" data-country="YE">+967</option>
                                        <option value="+968" data-country="OM">+968</option>
                                        <option value="+970" data-country="PS">+970</option>
                                        <option value="+971" data-country="AE">+971</option>
                                        <option value="+972" data-country="IL">+972</option>
                                        <option value="+973" data-country="BH">+973</option>
                                        <option value="+974" data-country="QA">+974</option>
                                        <option value="+975" data-country="BT">+975</option>
                                        <option value="+976" data-country="MN">+976</option>
                                        <option value="+977" data-country="NP">+977</option>
                                        <option value="+992" data-country="TJ">+992</option>
                                        <option value="+993" data-country="TM">+993</option>
                                        <option value="+994" data-country="AZ">+994</option>
                                        <option value="+995" data-country="GE">+995</option>
                                        <option value="+996" data-country="KG">+996</option>
                                        <option value="+998" data-country="UZ">+998</option>
                                    </select>
                                    <input type="tel" id="phone-number" name="phone" placeholder="Enter your phone number" class="form-field flex-1 px-4 sm:px-6 py-3 sm:py-4 text-white placeholder-gray-300 focus:outline-none" style="border-top-left-radius: 0; border-bottom-left-radius: 0; border-left: 0;" required>
                                </div>
                            </div>
                            
                            <div>
                                <label class="block text-white font-light mb-2 sm:mb-3 text-base lg:text-lg">KingsChat Username (Optional)</label>
                                <input type="text" name="kingschat_username" placeholder="Enter your KingsChat username" class="form-field w-full px-4 sm:px-6 py-3 sm:py-4 text-white placeholder-gray-300 focus:outline-none">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Church Information (WITHOUT ZONE SELECTION) -->
                <div id="step-2" class="form-step">
                    <div class="form-section p-6 sm:p-8 lg:p-10">
                        <h2 class="text-2xl sm:text-3xl lg:text-4xl font-black text-white mb-6 sm:mb-8 lg:mb-10 tracking-tight">Church Information</h2>
                        
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
                            <div>
                                <label class="block text-white font-light mb-2 sm:mb-3 text-base lg:text-lg">Zone (Optional)</label>
                                <select id="zone-select" name="zone" class="form-field w-full px-4 sm:px-6 py-3 sm:py-4 text-white placeholder-gray-300 focus:outline-none">
                                    <option value="">Select your zone</option>
                                    <!-- Zone options will be populated from zones.json -->
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-white font-light mb-2 sm:mb-3 text-base lg:text-lg">Group (Optional)</label>
                                <input type="text" name="group" placeholder="Enter your group name" class="form-field w-full px-4 sm:px-6 py-3 sm:py-4 text-white placeholder-gray-300 focus:outline-none">
                            </div>
                            
                            <div class="lg:col-span-2">
                                <label class="block text-white font-light mb-2 sm:mb-3 text-base lg:text-lg">Church (Optional)</label>
                                <input type="text" name="church" placeholder="Enter your church name" class="form-field w-full px-4 sm:px-6 py-3 sm:py-4 text-white placeholder-gray-300 focus:outline-none">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Proposed Crusade Details -->
                <div id="step-3" class="form-step">
                    <div class="form-section p-6 sm:p-8 lg:p-10">
                        <h2 class="text-2xl sm:text-3xl lg:text-4xl font-black text-white mb-6 sm:mb-8 lg:mb-10 tracking-tight">Crusade Details</h2>
                        
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
                            <div class="lg:col-span-2">
                                <label class="block text-white font-light mb-2 sm:mb-3 text-base lg:text-lg">Crusade Title *</label>
                                <div class="relative">
                                    <input type="text" id="crusade-title-input" name="crusade_title" placeholder="Enter the proposed crusade title" class="form-field w-full px-4 sm:px-6 py-3 sm:py-4 text-white placeholder-gray-300 focus:outline-none pr-32" required>
                                    <button type="button" id="unsure-btn" class="absolute right-3 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-20 hover:bg-opacity-30 text-white text-xs px-3 py-2 transition-all duration-300 hover:scale-105 focus:outline-none whitespace-nowrap">
                                        Unsure? Click here
                                    </button>
                                </div>
                            </div>
                            
                            <div>
                                <label class="block text-white font-light mb-2 sm:mb-3 text-base lg:text-lg">Country *</label>
                                <select id="country-select" class="form-field w-full px-4 sm:px-6 py-3 sm:py-4 text-white placeholder-gray-300 focus:outline-none" name="country" required>
                                    <option value="">Select country</option>
                                    <!-- Countries will be loaded from countries.json -->
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-white font-light mb-2 sm:mb-3 text-base lg:text-lg">City/Location *</label>
                                <div class="relative">
                                    <input type="text" id="city-location-input" name="location" placeholder="Enter city or specific location" class="form-field w-full px-4 sm:px-6 py-3 sm:py-4 text-white placeholder-gray-300 focus:outline-none pr-32" required>
                                    <button type="button" id="city-unsure-btn" class="absolute right-3 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-20 hover:bg-opacity-30 text-white text-xs px-3 py-2 transition-all duration-300 hover:scale-105 focus:outline-none whitespace-nowrap">
                                        Unsure? Click here
                                    </button>
                            </div>
                            </div>
                            
                            <div>
                                <label class="block text-white font-light mb-2 sm:mb-3 text-base lg:text-lg">Crusade Type *</label>
                                <select name="crusade_type" class="form-field w-full px-4 sm:px-6 py-3 sm:py-4 text-white placeholder-gray-300 focus:outline-none" required>
                                    <option value="">Select crusade type</option>
                                    <option value="mall">Mall Crusades</option>
                                    <option value="school">School Crusades</option>
                                    <option value="hospital">Hospital Crusades</option>
                                    <option value="street">Street Crusades</option>
                                    <option value="town">Town Crusades</option>
                                    <option value="city">City Crusades</option>
                                    <option value="country">Country Crusades</option>
                                    <option value="mega">Mega Crusades (10,000+ people)</option>
                                    <option value="youths-aglow">Youths Aglow Crusades</option>
                                    <option value="teevolution">Teevolution Crusades (Teens-focused)</option>
                                    <option value="say-yes-to-kids">Say Yes To Kids Crusades</option>
                                    <option value="nolb">NOLB Crusades (No One Left Behind) for visually/hearing impaired</option>
                                    <option value="professionals">Specialized Crusades to Professionals</option>
                                    <option value="community">Community Crusades</option>
                                    <option value="leading-ladies">Leading Ladies Crusades</option>
                                    <option value="mighty-men">Mighty Men Crusades</option>
                                    <option value="language">Language Crusades (TNI Crusades)</option>
                                    <option value="transport-station">Bus Station / Train Station Crusades</option>
                                    <option value="tap2read">TAP2READ Crusades – promoting TAP2READ app with evangelism</option>
                                    <option value="online-community">Online Community Crusades – targeting digital audiences</option>
                                </select>
                            </div>
                            
                            <div class="lg:col-span-2">
                                <label class="block text-white font-light mb-2 sm:mb-3 text-base lg:text-lg">Venue *</label>
                                <div class="relative">
                                    <input type="text" id="venue-input" name="venue" placeholder="Enter venue (stadium, auditorium, etc.)" class="form-field w-full px-4 sm:px-6 py-3 sm:py-4 text-white placeholder-gray-300 focus:outline-none pr-32" required>
                                    <button type="button" id="venue-unsure-btn" class="absolute right-3 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-20 hover:bg-opacity-30 text-white text-xs px-3 py-2 transition-all duration-300 hover:scale-105 focus:outline-none whitespace-nowrap">
                                        Unsure? Click here
                                    </button>
                                </div>
                            </div>
                            
                            <div class="lg:col-span-2">
                                <label class="block text-white font-light mb-2 sm:mb-3 text-base lg:text-lg">Expected Attendance *</label>
                                <select name="attendance" class="form-field w-full px-4 sm:px-6 py-3 sm:py-4 text-white placeholder-gray-300 focus:outline-none" required>
                                    <option value="">Select expected attendance</option>
                                    <option value="100-500">100 - 500</option>
                                    <option value="500-1000">500 - 1,000</option>
                                    <option value="1000-5000">1,000 - 5,000</option>
                                    <option value="5000-10000">5,000 - 10,000</option>
                                    <option value="10000+">10,000+</option>
                                    <option value="unsure">Unsure</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 4: Additional Information -->
                <div id="step-4" class="form-step">
                    <div class="form-section p-6 sm:p-8 lg:p-10">
                        <h2 class="text-2xl sm:text-3xl lg:text-4xl font-black text-white mb-6 sm:mb-8 lg:mb-10 tracking-tight">Additional Information</h2>
                        
                        <div class="space-y-4 sm:space-y-6 lg:space-y-8">
                            <div>
                                <label class="block text-white font-light mb-2 sm:mb-3 text-base lg:text-lg">Additional Comments (Optional)</label>
                                <textarea name="comments" placeholder="Any additional information about your crusade request..." class="form-field w-full px-4 sm:px-6 py-3 sm:py-4 text-white placeholder-gray-300 focus:outline-none resize-none" rows="4"></textarea>
                            </div>
                            
                            <div>
                                <label class="block text-white font-light mb-2 sm:mb-3 text-base lg:text-lg">Preferred Contact Method *</label>
                                <select name="contact_method" class="form-field w-full px-4 sm:px-6 py-3 sm:py-4 text-white placeholder-gray-300 focus:outline-none" required>
                                    <option value="">Select preferred contact method</option>
                                    <option value="email">Email</option>
                                    <option value="phone">Phone</option>
                                    <option value="kingschat">KingsChat</option>
                                    <option value="whatsapp">WhatsApp</option>
                                </select>
                            </div>
                            
                            <div class="bg-white bg-opacity-5 p-4 sm:p-6 lg:p-8 border border-white border-opacity-10">
                                <h3 class="text-lg sm:text-xl font-semibold text-white mb-4">Important Information</h3>
                                <ul class="text-white text-sm sm:text-base space-y-2 opacity-90">
                                    <li>• All crusade requests are subject to approval and scheduling availability</li>
                                    <li>• Thank you for your commitment to spreading the Gospel</li>
                                    <li>• Please ensure all information provided is accurate and complete</li>
                                    <li>• Additional documentation may be required during the approval process</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Navigation -->
            <div id="form-navigation" class="flex flex-col sm:flex-row justify-between items-center mt-6 lg:mt-8 gap-4 sm:gap-0">
                <button id="prev-btn" class="w-full sm:w-auto px-6 lg:px-8 py-3 bg-transparent border-2 border-white text-white font-light tracking-wider hover:bg-white hover:text-slate-800 transition-all duration-300 opacity-0 invisible">
                    Previous
                </button>
                
                <button id="next-btn" class="w-full sm:w-auto px-6 lg:px-8 py-3 bg-blue-600 border-2 border-blue-600 text-white font-light tracking-wider hover:bg-blue-700 hover:border-blue-700 transition-all duration-300">
                    Next
                </button>
                
                <button id="submit-btn" class="w-full sm:w-auto px-6 lg:px-8 py-3 bg-green-600 border-2 border-green-600 text-white font-light tracking-wider hover:bg-green-700 hover:border-green-700 transition-all duration-300 opacity-0 invisible">
                    Submit Church Crusade Request
                </button>
                
                <button id="clear-btn" class="w-full sm:w-auto px-6 lg:px-8 py-3 bg-transparent border-2 border-red-400 text-red-400 font-light tracking-wider hover:bg-red-400 hover:text-white transition-all duration-300 opacity-0 invisible">
                    Clear Form
                </button>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        const totalSteps = 4;
        let sidebarOpen = false;

        // Initialize everything
        document.addEventListener('DOMContentLoaded', function() {
            // Set initial positions for form steps
            gsap.set(".form-step:not(.active)", { x: "100%", opacity: 0 });
            gsap.set(".form-step.active", { x: 0, opacity: 1 });
            
            // Slide in from right
            gsap.to("#main-content", {
                x: 0,
                duration: 1.2,
                ease: "power3.out",
                delay: 0.2
            });
            
            // Fade in language button
            gsap.to("#language-btn", {
                opacity: 1,
                duration: 0.8,
                delay: 0.8
            });
            
            // Fade in floating logo
            gsap.to("#floating-logo", {
                opacity: 1,
                duration: 0.8,
                delay: 0.8
            });
            
            // Setup form navigation
            setupFormNavigation();
            
            // Setup sidebar functionality
            setupSidebar();
            
            // Setup language functionality
            setupLanguage();
            
            // Load zones from JSON file
            loadZones();
            
            // Load countries from JSON file
            loadCountries();
            
            // Setup phone number functionality
            setupPhoneNumber();
            
            // Setup unsure button functionality
            setupUnsureButton();
            setupCityUnsureButton();
            setupVenueUnsureButton();
        });

        // Load countries from JSON file
        function loadCountries() {
            console.log('loadCountries function called');
            
            const countrySelect = document.getElementById('country-select');
            
            if (!countrySelect) {
                console.error('country-select element not found');
                return;
            }
            
            fetch('countries.json')
                .then(response => {
                    console.log('Countries fetch response:', response.status, response.statusText);
                    if (!response.ok) {
                        throw new Error(`Network response was not ok: ${response.status} ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(countries => {
                    console.log('Countries data loaded:', countries);
                    if (!countries || !Array.isArray(countries)) {
                        throw new Error('Invalid countries data format');
                    }
                    
                    // Clear existing options (except the first placeholder)
                    while (countrySelect.children.length > 1) {
                        countrySelect.removeChild(countrySelect.lastChild);
                    }
                    
                    countries.forEach(country => {
                        const option = document.createElement('option');
                        option.value = country.toLowerCase().replace(/\s+/g, '-');
                        option.textContent = country;
                        countrySelect.appendChild(option);
                    });
                    console.log(`Successfully loaded ${countries.length} countries. Total options now: ${countrySelect.options.length}`);
                })
                .catch(error => {
                    console.error('Error loading countries:', error);
                    // Fallback countries if JSON fails to load
                    const fallbackCountries = [
                        'United States', 'United Kingdom', 'Canada', 'Australia', 'Germany', 'France', 'Italy', 'Spain', 'Netherlands', 'Sweden', 'Norway', 'Denmark', 'Finland', 'Belgium', 'Switzerland', 'Austria', 'Portugal', 'Greece', 'Ireland', 'Poland', 'Czech Republic', 'Hungary', 'Romania', 'Bulgaria', 'Croatia', 'Slovenia', 'Slovakia', 'Estonia', 'Latvia', 'Lithuania', 'Nigeria', 'South Africa', 'Kenya', 'Ghana', 'Uganda', 'Tanzania', 'Zimbabwe', 'Zambia', 'Botswana', 'Namibia', 'Malawi', 'Rwanda', 'Cameroon', 'Ivory Coast', 'Senegal', 'Mali', 'Burkina Faso', 'Niger', 'Chad', 'Central African Republic', 'Democratic Republic of Congo', 'Republic of Congo', 'Gabon', 'Equatorial Guinea', 'Sao Tome and Principe', 'Cape Verde', 'Guinea-Bissau', 'Guinea', 'Sierra Leone', 'Liberia', 'Mauritania', 'Morocco', 'Algeria', 'Tunisia', 'Libya', 'Egypt', 'Sudan', 'South Sudan', 'Ethiopia', 'Eritrea', 'Djibouti', 'Somalia', 'Madagascar', 'Mauritius', 'Seychelles', 'Comoros', 'India', 'China', 'Japan', 'South Korea', 'Thailand', 'Vietnam', 'Philippines', 'Indonesia', 'Malaysia', 'Singapore', 'Myanmar', 'Cambodia', 'Laos', 'Bangladesh', 'Pakistan', 'Afghanistan', 'Iran', 'Iraq', 'Turkey', 'Syria', 'Lebanon', 'Jordan', 'Israel', 'Palestine', 'Saudi Arabia', 'UAE', 'Qatar', 'Kuwait', 'Bahrain', 'Oman', 'Yemen', 'Kazakhstan', 'Uzbekistan', 'Turkmenistan', 'Tajikistan', 'Kyrgyzstan', 'Mongolia', 'North Korea', 'Taiwan', 'Hong Kong', 'Macau', 'Sri Lanka', 'Nepal', 'Bhutan', 'Maldives', 'Brazil', 'Argentina', 'Chile', 'Peru', 'Colombia', 'Venezuela', 'Ecuador', 'Bolivia', 'Paraguay', 'Uruguay', 'Guyana', 'Suriname', 'French Guiana', 'Mexico', 'Guatemala', 'Belize', 'El Salvador', 'Honduras', 'Nicaragua', 'Costa Rica', 'Panama', 'Cuba', 'Jamaica', 'Haiti', 'Dominican Republic', 'Puerto Rico', 'Trinidad and Tobago', 'Barbados', 'Saint Lucia', 'Grenada', 'Saint Vincent and the Grenadines', 'Antigua and Barbuda', 'Dominica', 'Saint Kitts and Nevis', 'Bahamas', 'Russia', 'Ukraine', 'Belarus', 'Moldova', 'Georgia', 'Armenia', 'Azerbaijan', 'New Zealand', 'Fiji', 'Papua New Guinea', 'Solomon Islands', 'Vanuatu', 'Samoa', 'Tonga', 'Kiribati', 'Tuvalu', 'Nauru', 'Palau', 'Marshall Islands', 'Micronesia'
                    ];
                    
                    // Clear existing options (except the first placeholder)
                    while (countrySelect.children.length > 1) {
                        countrySelect.removeChild(countrySelect.lastChild);
                    }
                    
                    fallbackCountries.forEach(country => {
                        const option = document.createElement('option');
                        option.value = country.toLowerCase().replace(/\s+/g, '-');
                        option.textContent = country;
                        countrySelect.appendChild(option);
                    });
                    console.log(`Using fallback countries. Total options now: ${countrySelect.options.length}`);
                });
        }

        // Load zones from JSON file
        function loadZones() {
            console.log('loadZones function called');
            
            const zoneSelect = document.getElementById('zone-select');
            
            if (!zoneSelect) {
                console.error('zone-select element not found');
                return;
            }
            
            fetch('zones.json')
                .then(response => {
                    console.log('Zone fetch response:', response.status, response.statusText);
                    if (!response.ok) {
                        throw new Error(`Network response was not ok: ${response.status} ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Zones data loaded:', data);
                    const zones = data.zones;
                    if (!zones || !Array.isArray(zones)) {
                        throw new Error('Invalid zones data format');
                    }
                    
                    // Clear existing options (except the first placeholder)
                    while (zoneSelect.children.length > 1) {
                        zoneSelect.removeChild(zoneSelect.lastChild);
                    }
                    
                    zones.forEach(zone => {
                        const option = document.createElement('option');
                        option.value = zone;
                        option.textContent = zone;
                        zoneSelect.appendChild(option);
                    });
                    console.log(`Successfully loaded ${zones.length} zones. Total options now: ${zoneSelect.options.length}`);
                })
                .catch(error => {
                    console.error('Error loading zones:', error);
                    // Fallback zones if JSON fails to load
                    const fallbackZones = [
                        'Not Applicable',
                        'GYLF',
                        'ISM',
                        'REON',
                        'RINT',
                        'TNI',
                        'Lagos Zone 1',
                        'Lagos Zone 2',
                        'Abuja Zone',
                        'BLW Nigeria Region'
                    ];
                    
                    // Clear existing options (except the first placeholder)
                    while (zoneSelect.children.length > 1) {
                        zoneSelect.removeChild(zoneSelect.lastChild);
                    }
                    
                    fallbackZones.forEach(zone => {
                        const option = document.createElement('option');
                        option.value = zone;
                        option.textContent = zone;
                        zoneSelect.appendChild(option);
                    });
                    console.log(`Using fallback zones. Total options now: ${zoneSelect.options.length}`);
                });
        }

        // Setup form navigation
        function setupFormNavigation() {
            const nextBtn = document.getElementById('next-btn');
            const prevBtn = document.getElementById('prev-btn');
            const submitBtn = document.getElementById('submit-btn');
            const clearBtn = document.getElementById('clear-btn');
            
            nextBtn.addEventListener('click', () => {
                if (currentStep < totalSteps) {
                    goToStep(currentStep + 1);
                }
            });
            
            prevBtn.addEventListener('click', () => {
                if (currentStep > 1) {
                    goToStep(currentStep - 1);
                }
            });
            
            submitBtn.addEventListener('click', (e) => {
                e.preventDefault();
                submitForm();
            });
            
            clearBtn.addEventListener('click', () => {
                if (confirm('Are you sure you want to clear all form data?')) {
                    clearForm();
                }
            });
            
            // Initial setup
            updateProgressIndicator();
            updateNavigationButtons();
        }

        // Navigate to specific step
        function goToStep(step) {
            if (step < 1 || step > totalSteps) return;
            
            // Validate current step before proceeding (except when going backwards)
            if (step > currentStep && !validateCurrentStep()) {
                return; // Don't proceed if validation fails
            }
            
            const currentStepEl = document.getElementById(`step-${currentStep}`);
            const nextStepEl = document.getElementById(`step-${step}`);
            const direction = step > currentStep ? 1 : -1;
            
            // Create timeline for smoother transitions
            const tl = gsap.timeline();
            
            // Animate current step out with fade and slide
            tl.to(currentStepEl, {
                x: direction > 0 ? "-100%" : "100%",
                opacity: 0,
                duration: 0.8,
                ease: "power3.inOut",
                onComplete: () => {
                    currentStepEl.classList.remove('active');
                }
            })
            // Animate next step in with fade and slide
            .fromTo(nextStepEl, 
                {
                    x: direction > 0 ? "100%" : "-100%",
                    opacity: 0
                },
                {
                    x: 0,
                    opacity: 1,
                    duration: 0.8,
                    ease: "power3.out",
                    onStart: () => {
                        nextStepEl.classList.add('active');
                    }
                }, "-=0.2" // Overlap animations slightly
            );
            
            currentStep = step;
            updateProgressIndicator();
            updateNavigationButtons();
        }

        // Validate current step
        function validateCurrentStep() {
            const currentStepEl = document.getElementById(`step-${currentStep}`);
            const requiredFields = currentStepEl.querySelectorAll('[required]');
            
            for (let field of requiredFields) {
                if (!field.value.trim()) {
                    showValidationError(`Please fill in all required fields in this step.`);
                    field.focus();
                    return false;
                }
            }
            
            return true;
        }

        // Update progress indicator
        function updateProgressIndicator() {
            const progressBoxes = document.querySelectorAll('.progress-box');
            progressBoxes.forEach((box, index) => {
                if (index < currentStep) {
                    box.classList.add('active');
                } else {
                    box.classList.remove('active');
                }
            });
        }

        // Update navigation buttons
        function updateNavigationButtons() {
            const nextBtn = document.getElementById('next-btn');
            const prevBtn = document.getElementById('prev-btn');
            const submitBtn = document.getElementById('submit-btn');
            const clearBtn = document.getElementById('clear-btn');
            
            // Previous button
            if (currentStep > 1) {
                prevBtn.classList.remove('opacity-0', 'invisible');
                prevBtn.classList.add('opacity-100', 'visible');
            } else {
                prevBtn.classList.add('opacity-0', 'invisible');
                prevBtn.classList.remove('opacity-100', 'visible');
            }
            
            // Next/Submit button
            if (currentStep < totalSteps) {
                nextBtn.classList.remove('opacity-0', 'invisible');
                nextBtn.classList.add('opacity-100', 'visible');
                submitBtn.classList.add('opacity-0', 'invisible');
                submitBtn.classList.remove('opacity-100', 'visible');
            } else {
                nextBtn.classList.add('opacity-0', 'invisible');
                nextBtn.classList.remove('opacity-100', 'visible');
                submitBtn.classList.remove('opacity-0', 'invisible');
                submitBtn.classList.add('opacity-100', 'visible');
            }
            
            // Clear button (show on last step)
            if (currentStep === totalSteps) {
                clearBtn.classList.remove('opacity-0', 'invisible');
                clearBtn.classList.add('opacity-100', 'visible');
            } else {
                clearBtn.classList.add('opacity-0', 'invisible');
                clearBtn.classList.remove('opacity-100', 'visible');
            }
        }

        // Submit form
        function submitForm() {
            // Validate final step before submission
            if (!validateCurrentStep()) {
                return; // Don't submit if validation fails
            }
            
            // Collect form data
            const formData = collectFormData();
            
            // Show loading animation
            gsap.to("#main-content", {
                opacity: 0.5,
                duration: 0.5
            });
            
            // Show loading message
            showValidationError('Submitting your church crusade request...');
            
            // Create FormData object for submission
            const submitData = new FormData();
            Object.keys(formData).forEach(key => {
                submitData.append(key, formData[key]);
            });
            
            // Submit to our handler
            fetch('pastor-crusade-request-handler.php', {
                method: 'POST',
                body: submitData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showValidationError('Church crusade request submitted successfully! Our team will contact you soon.', 'success');
                    // Reset form after successful submission
                    setTimeout(() => {
                        clearForm();
                        goToStep(1);
                    }, 3000);
                } else {
                    showValidationError(data.message || 'An error occurred while submitting your request. Please try again.');
                }
            })
            .catch(error => {
                console.error('Error submitting form:', error);
                showValidationError('Network error. Please check your connection and try again.');
            })
            .finally(() => {
                // Restore form opacity
                gsap.to("#main-content", {
                    opacity: 1,
                    duration: 0.5
                });
            });
        }

        // Collect form data
        function collectFormData() {
            const formData = {};
            const inputs = document.querySelectorAll('input, select, textarea');
            
            inputs.forEach(input => {
                if (input.name) {
                    formData[input.name] = input.value;
                }
            });
            
            // Combine country code and phone number into a single phone field
            if (formData.country_code && formData.phone) {
                formData.phone = formData.country_code + ' ' + formData.phone;
                delete formData.country_code; // Remove the separate country code field
            }
            
            return formData;
        }

        // Clear form
        function clearForm() {
            const inputs = document.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                if (input.type !== 'submit' && input.type !== 'button') {
                    input.value = '';
                }
            });
            
            // Reset to first step
            goToStep(1);
        }

        // Show validation error
        function showValidationError(message, type = 'error') {
            // Remove existing message
            const existingMessage = document.querySelector('.validation-message');
            if (existingMessage) {
                existingMessage.remove();
            }
            
            // Create new message
            const messageDiv = document.createElement('div');
            messageDiv.className = `validation-message ${type === 'success' ? 'success' : ''}`;
            messageDiv.textContent = message;
            
            document.body.appendChild(messageDiv);
            
            // Show message
            setTimeout(() => {
                messageDiv.classList.add('show');
            }, 100);
            
            // Hide message after 5 seconds
            setTimeout(() => {
                messageDiv.classList.remove('show');
                setTimeout(() => {
                    messageDiv.remove();
                }, 300);
            }, 5000);
        }

        // Setup sidebar functionality
        function setupSidebar() {
            const hoverArea = document.getElementById('hover-area');
            const sidebarOverlay = document.getElementById('sidebar-overlay');
            const arrowIndicator = document.getElementById('arrow-indicator');
            
            // Show sidebar on hover
            hoverArea.addEventListener('mouseenter', openSidebar);
            
            // Keep sidebar open when hovering over it
            sidebarOverlay.addEventListener('mouseenter', () => {
                if (sidebarOpen) return;
                openSidebar();
            });
            
            // Close sidebar when leaving
            sidebarOverlay.addEventListener('mouseleave', closeSidebar);
            
            // Close sidebar when clicking outside
            document.addEventListener('click', (e) => {
                if (!sidebarOverlay.contains(e.target) && !hoverArea.contains(e.target)) {
                    closeSidebar();
                }
            });
        }

        // Open sidebar
        function openSidebar() {
            if (sidebarOpen) return;
            
            sidebarOpen = true;
            const sidebarOverlay = document.getElementById('sidebar-overlay');
            const languageBtn = document.getElementById('language-btn');
            const floatingLogo = document.getElementById('floating-logo');
            const arrowIndicator = document.getElementById('arrow-indicator');
            
            // Slide in sidebar
            gsap.to(sidebarOverlay, {
                x: 0,
                duration: 0.5,
                ease: "power3.out"
            });
            
            // Hide top language button
            gsap.to(languageBtn, {
                opacity: 0,
                duration: 0.3
            });
            
            // Hide floating logo
            gsap.to(floatingLogo, {
                opacity: 0,
                duration: 0.3
            });
            
            // Hide arrow indicator
            gsap.to(arrowIndicator, {
                opacity: 0,
                duration: 0.3
            });
            
            // Animate nav links
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach((link, index) => {
                gsap.fromTo(link, 
                    {
                        opacity: 0,
                        x: -50
                    },
                    {
                        opacity: 1,
                        x: 0,
                        duration: 0.5,
                        delay: 0.1 + (index * 0.1),
                        ease: "power2.out"
                    }
                );
            });
        }

        // Close sidebar
        function closeSidebar() {
            if (!sidebarOpen) return;
            
            sidebarOpen = false;
            const sidebarOverlay = document.getElementById('sidebar-overlay');
            const languageBtn = document.getElementById('language-btn');
            const floatingLogo = document.getElementById('floating-logo');
            const arrowIndicator = document.getElementById('arrow-indicator');
            
            // Slide out sidebar
            gsap.to(sidebarOverlay, {
                x: "-100%",
                duration: 0.5,
                ease: "power3.out"
            });
            
            // Show top language button
            gsap.to(languageBtn, {
                opacity: 1,
                duration: 0.5,
                delay: 0.2
            });
            
            // Show floating logo
            gsap.to(floatingLogo, {
                opacity: 1,
                duration: 0.5,
                delay: 0.2
            });
            
            // Show arrow indicator
            gsap.to(arrowIndicator, {
                opacity: 0.3,
                duration: 0.5,
                delay: 0.2
            });
            
            // Reset nav links
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                gsap.set(link, { opacity: 0, x: -50 });
            });
        }

        // Setup language functionality
        function setupLanguage() {
            const languageBtn = document.getElementById('language-btn');
            const sidebarLanguageBtn = document.getElementById('sidebar-language-btn');
            const closeOverlay = document.getElementById('close-overlay');
            const languageItems = document.querySelectorAll('.language-item');

            // Open overlay from top language button
            languageBtn.addEventListener('click', openLanguageOverlay);
            
            // Open overlay from sidebar language button
            sidebarLanguageBtn.addEventListener('click', openLanguageOverlay);

            // Close overlay
            closeOverlay.addEventListener('click', closeLanguageOverlay);

            // Close on escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    closeLanguageOverlay();
                }
            });

            // Language selection with animations
            languageItems.forEach((item) => {
                const text = item.querySelector('h2');
                
                // Hover effects
                item.addEventListener('mouseenter', () => {
                    gsap.to(text, {
                        scale: 1.05,
                        x: 20,
                        duration: 0.3,
                        ease: "power2.out"
                    });
                    
                    // Add glitch effect
                    gsap.to(text, {
                        textShadow: "2px 0 #ff0000, -2px 0 #00ffff",
                        duration: 0.1,
                        repeat: 3,
                        yoyo: true
                    });
                });
                
                item.addEventListener('mouseleave', () => {
                    gsap.to(text, {
                        scale: 1,
                        x: 0,
                        textShadow: "none",
                        duration: 0.3,
                        ease: "power2.out"
                    });
                });
                
                // Click to select language
                item.addEventListener('click', () => {
                    const lang = item.dataset.lang;
                    console.log('Language selected:', lang);
                    closeLanguageOverlay();
                    
                    // Update language button text
                    const langText = text.textContent.substring(0, 2);
                    languageBtn.querySelector('span').textContent = langText;
                });
            });
        }

        // Open language overlay
        function openLanguageOverlay() {
            const languageOverlay = document.getElementById('language-overlay');
            const languageItems = document.querySelectorAll('.language-item');
            
            languageOverlay.classList.remove('invisible');
            languageOverlay.classList.remove('opacity-0');
            
            // Animate language items
            languageItems.forEach((item, index) => {
                gsap.to(item, {
                    opacity: 1,
                    y: 0,
                    duration: 0.6,
                    delay: index * 0.1,
                    ease: "power3.out"
                });
            });
        }

        // Close language overlay
        function closeLanguageOverlay() {
            const languageOverlay = document.getElementById('language-overlay');
            const languageItems = document.querySelectorAll('.language-item');
            
            languageOverlay.classList.add('opacity-0');
            
            // Reset language items
            languageItems.forEach((item) => {
                gsap.set(item, {
                    opacity: 0,
                    y: 20
                });
            });
            
            setTimeout(() => {
                languageOverlay.classList.add('invisible');
            }, 500);
        }

        // Setup phone number functionality
        function setupPhoneNumber() {
            const countryCode = document.getElementById('country-code');
            const phoneNumber = document.getElementById('phone-number');
            
            // Format phone number as user types
            phoneNumber.addEventListener('input', (e) => {
                let value = e.target.value.replace(/\D/g, '');
                let formattedValue = '';
                
                if (value.length > 0) {
                    if (value.length <= 3) {
                        formattedValue = value;
                    } else if (value.length <= 6) {
                        formattedValue = value.slice(0, 3) + '-' + value.slice(3);
                    } else {
                        formattedValue = value.slice(0, 3) + '-' + value.slice(3, 6) + '-' + value.slice(6, 10);
                    }
                }
                
                e.target.value = formattedValue;
            });
        }

        // Setup unsure button functionality
        function setupUnsureButton() {
            const crusadeTitleInput = document.getElementById('crusade-title-input');
            const unsureBtn = document.getElementById('unsure-btn');
            
            if (!crusadeTitleInput || !unsureBtn) {
                console.error('Crusade title input or unsure button not found');
                return;
            }
            
            // Function to show/hide unsure button based on input value
            function toggleUnsureButton() {
                const hasUnsureText = crusadeTitleInput.value.toLowerCase().includes('unsure');
                
                if (hasUnsureText) {
                    // Hide button with animation
                    gsap.to(unsureBtn, {
                        opacity: 0,
                        scale: 0.8,
                        duration: 0.3,
                        ease: "power2.out",
                        onComplete: () => {
                            unsureBtn.style.display = 'none';
                        }
                    });
                } else {
                    // Show button with animation
                    unsureBtn.style.display = 'block';
                    gsap.fromTo(unsureBtn, 
                        {
                            opacity: 0,
                            scale: 0.8,
                            y: 0
                        },
                        {
                            opacity: 1,
                            scale: 1,
                            y: 0,
                            duration: 0.3,
                            ease: "back.out(1.7)"
                        }
                    );
                }
            }
            
            // Click handler for unsure button
            unsureBtn.addEventListener('click', () => {
                // Fill input with "Unsure" text
                crusadeTitleInput.value = 'Unsure';
                
                // Animate button click and disappear
                gsap.to(unsureBtn, {
                    scale: 0.9,
                    duration: 0.1,
                    yoyo: true,
                    repeat: 1,
                    ease: "power2.inOut",
                    onComplete: () => {
                        // Hide button after animation
                        gsap.to(unsureBtn, {
                            opacity: 0,
                            scale: 0.8,
                            y: -10,
                            duration: 0.4,
                            ease: "power2.out",
                            onComplete: () => {
                                unsureBtn.style.display = 'none';
                                // Reset y position for next appearance
                                gsap.set(unsureBtn, { y: 0 });
                            }
                        });
                    }
                });
                
                // Add a subtle glow effect to the input
                gsap.to(crusadeTitleInput, {
                    boxShadow: '0 0 20px rgba(96, 165, 250, 0.5)',
                    duration: 0.3,
                    yoyo: true,
                    repeat: 1,
                    ease: "power2.inOut"
                });
                
                // Focus the input
                crusadeTitleInput.focus();
            });
            
            // Input event listener to show/hide button
            crusadeTitleInput.addEventListener('input', toggleUnsureButton);
            
            // Also check on blur and focus events
            crusadeTitleInput.addEventListener('blur', toggleUnsureButton);
            crusadeTitleInput.addEventListener('focus', toggleUnsureButton);
            
            // Initial check
            toggleUnsureButton();
        }

        // Setup city unsure button functionality
        function setupCityUnsureButton() {
            const cityInput = document.getElementById('city-location-input');
            const unsureBtn = document.getElementById('city-unsure-btn');
            
            if (!cityInput || !unsureBtn) {
                console.error('City input or unsure button not found');
                return;
            }
            
            // Function to show/hide unsure button based on input value
            function toggleCityUnsureButton() {
                const hasUnsureText = cityInput.value.toLowerCase().includes('unsure');
                
                if (hasUnsureText) {
                    // Hide button with animation
                    gsap.to(unsureBtn, {
                        opacity: 0,
                        scale: 0.8,
                        duration: 0.3,
                        ease: "power2.out",
                        onComplete: () => {
                            unsureBtn.style.display = 'none';
                        }
                    });
                } else {
                    // Show button with animation
                    unsureBtn.style.display = 'block';
                    gsap.fromTo(unsureBtn, 
                        {
                            opacity: 0,
                            scale: 0.8,
                            y: 0
                        },
                        {
                            opacity: 1,
                            scale: 1,
                            y: 0,
                            duration: 0.3,
                            ease: "back.out(1.7)"
                        }
                    );
                }
            }
            
            // Click handler for unsure button
            unsureBtn.addEventListener('click', () => {
                // Fill input with "Unsure" text
                cityInput.value = 'Unsure';
                
                // Animate button click and disappear
                gsap.to(unsureBtn, {
                    scale: 0.9,
                    duration: 0.1,
                    yoyo: true,
                    repeat: 1,
                    ease: "power2.inOut",
                    onComplete: () => {
                        // Hide button after animation
                        gsap.to(unsureBtn, {
                            opacity: 0,
                            scale: 0.8,
                            y: -10,
                            duration: 0.4,
                            ease: "power2.out",
                            onComplete: () => {
                                unsureBtn.style.display = 'none';
                                // Reset y position for next appearance
                                gsap.set(unsureBtn, { y: 0 });
                            }
                        });
                    }
                });
                
                // Add a subtle glow effect to the input
                gsap.to(cityInput, {
                    boxShadow: '0 0 20px rgba(96, 165, 250, 0.5)',
                    duration: 0.3,
                    yoyo: true,
                    repeat: 1,
                    ease: "power2.inOut"
                });
                
                // Focus the input
                cityInput.focus();
            });
            
            // Input event listener to show/hide button
            cityInput.addEventListener('input', toggleCityUnsureButton);
            
            // Also check on blur and focus events
            cityInput.addEventListener('blur', toggleCityUnsureButton);
            cityInput.addEventListener('focus', toggleCityUnsureButton);
            
            // Initial check
            toggleCityUnsureButton();
        }

        // Setup venue unsure button functionality
        function setupVenueUnsureButton() {
            const venueInput = document.getElementById('venue-input');
            const unsureBtn = document.getElementById('venue-unsure-btn');
            
            if (!venueInput || !unsureBtn) {
                console.error('Venue input or unsure button not found');
                return;
            }
            
            // Function to show/hide unsure button based on input value
            function toggleVenueUnsureButton() {
                const hasUnsureText = venueInput.value.toLowerCase().includes('unsure');
                
                if (hasUnsureText) {
                    // Hide button with animation
                    gsap.to(unsureBtn, {
                        opacity: 0,
                        scale: 0.8,
                        duration: 0.3,
                        ease: "power2.out",
                        onComplete: () => {
                            unsureBtn.style.display = 'none';
                        }
                    });
                } else {
                    // Show button with animation
                    unsureBtn.style.display = 'block';
                    gsap.fromTo(unsureBtn, 
                        {
                            opacity: 0,
                            scale: 0.8,
                            y: 0
                        },
                        {
                            opacity: 1,
                            scale: 1,
                            y: 0,
                            duration: 0.3,
                            ease: "back.out(1.7)"
                        }
                    );
                }
            }
            
            // Click handler for unsure button
            unsureBtn.addEventListener('click', () => {
                // Fill input with "Unsure" text
                venueInput.value = 'Unsure';
                
                // Animate button click and disappear
                gsap.to(unsureBtn, {
                    scale: 0.9,
                    duration: 0.1,
                    yoyo: true,
                    repeat: 1,
                    ease: "power2.inOut",
                    onComplete: () => {
                        // Hide button after animation
                        gsap.to(unsureBtn, {
                            opacity: 0,
                            scale: 0.8,
                            y: -10,
                            duration: 0.4,
                            ease: "power2.out",
                            onComplete: () => {
                                unsureBtn.style.display = 'none';
                                // Reset y position for next appearance
                                gsap.set(unsureBtn, { y: 0 });
                            }
                        });
                    }
                });
                
                // Add a subtle glow effect to the input
                gsap.to(venueInput, {
                    boxShadow: '0 0 20px rgba(96, 165, 250, 0.5)',
                    duration: 0.3,
                    yoyo: true,
                    repeat: 1,
                    ease: "power2.inOut"
                });
                
                // Focus the input
                venueInput.focus();
            });
            
            // Input event listener to show/hide button
            venueInput.addEventListener('input', toggleVenueUnsureButton);
            
            // Also check on blur and focus events
            venueInput.addEventListener('blur', toggleVenueUnsureButton);
            venueInput.addEventListener('focus', toggleVenueUnsureButton);
            
            // Initial check
            toggleVenueUnsureButton();
        }
    </script>
</body>
</html> 