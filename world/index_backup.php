<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Night of a Thousand Crusades</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
    <script src="mobile-redirect.js"></script>
    <style>
        /* Ensure all elements have no rounded corners */
        * {
            border-radius: 0 !important;
        }
        
        /* Custom button styling */
        button {
            border-radius: 0 !important;
        }
        
        /* Navbar styling */
        nav {
            border-radius: 0 !important;
        }
        
        /* Language overlay and buttons */
        #language-overlay,
        #language-btn,
        #floating-language-btn {
            border-radius: 0 !important;
        }
        
        /* Action buttons */
        .action-btn {
            border-radius: 0 !important;
        }
        
        /* Progress indicators and form elements */
        .progress-box,
        .form-field,
        .form-section {
            border-radius: 0 !important;
        }
        
        /* Fixed background for seamless scrolling */
        #hero,
        #second-section {
            background-image: url('assets/images/hero-bg.png');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            background-attachment: fixed;
        }
        
        /* Fixed overlay for consistent depth */
        #hero::before,
        #second-section::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, rgba(0,0,0,0.6), rgba(0,0,0,0.5), rgba(0,0,0,0.7));
            pointer-events: none;
            z-index: 1;
        }
        
        /* Smooth scrolling and scroll snap */
        html {
            scroll-behavior: smooth;
            scroll-snap-type: y mandatory;
        }
        
        body {
            overflow-x: hidden;
            overflow-y: auto;
        }
        
        /* Scroll snap for sections */
        #hero,
        #second-section {
            scroll-snap-align: start;
            scroll-snap-stop: always;
        }
        
        /* Ensure sections take full viewport */
        #hero,
        #second-section {
            height: 100vh;
            min-height: 100vh;
        }
        
        /* Custom scrollbar styling */
        .custom-scrollbar::-webkit-scrollbar {
            width: 8px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 0;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 0;
            transition: background 0.3s ease;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }
        
        /* Firefox scrollbar styling */
        .custom-scrollbar {
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1);
        }
    </style>
</head>
<body>
    <nav id="navbar" class="fixed top-8 left-1/2 transform -translate-x-1/2 bg-white px-12 py-6 shadow-sm z-50 border border-gray-100 opacity-0">
        <div class="flex items-center justify-between min-w-[500px] space-x-24">
            <a href="index.php" class="flex items-center">
                <img src="assets/images/logo.webp" alt="Logo" class="h-8 w-auto">
            </a>
            
            <div class="relative">
                <button id="language-btn" class="bg-gray-900 text-white px-8 py-3 flex items-center space-x-4 font-light tracking-wide hover:bg-gray-800 transition-colors duration-200">
                    <span class="text-sm">Language</span>
                    <svg id="dropdown-arrow" class="w-4 h-4 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
                </div>
            </div>
    </nav>

    <!-- Floating Language Button (appears when navbar is hidden) -->
    <button id="floating-language-btn" class="fixed top-8 right-8 z-40 opacity-0 invisible transition-all duration-300 bg-white bg-opacity-10 backdrop-blur-sm border border-white border-opacity-20 px-4 py-2 text-white hover:bg-opacity-20 hover:text-blue-400">
        <div class="flex items-center space-x-2">
            <span class="text-sm font-light tracking-wide">EN</span>
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
        </div>
    </button>

    <!-- Floating Logo (appears when navbar is hidden) -->
    <div id="floating-logo" class="fixed top-8 left-8 z-40 opacity-0 invisible transition-all duration-300">
        <img src="assets/images/logo.webp" alt="Logo" class="h-10 w-auto">
    </div>

    <!-- Language Overlay -->
    <div id="language-overlay" class="fixed inset-0 bg-black bg-opacity-95 z-[100] opacity-0 invisible transition-all duration-500 flex items-center justify-center">
        <div class="w-full max-w-4xl px-8">
            <!-- Close Button -->
            <button id="close-overlay" class="absolute top-8 right-8 text-white text-4xl font-light hover:rotate-90 transition-transform duration-300">
                ×
            </button>
            
            <!-- Language List -->
            <div id="language-list" class="space-y-8">
                <div class="language-item opacity-0 transform translate-y-20" data-lang="en">
                    <h2 class="text-6xl md:text-8xl font-black text-white font-mono tracking-wider hover:text-blue-400 transition-colors duration-300 cursor-pointer">
                        ENGLISH
                    </h2>
                </div>
                <div class="language-item opacity-0 transform translate-y-20" data-lang="es">
                    <h2 class="text-6xl md:text-8xl font-black text-white font-mono tracking-wider hover:text-blue-400 transition-colors duration-300 cursor-pointer">
                        ESPAÑOL
                    </h2>
                </div>
                <div class="language-item opacity-0 transform translate-y-20" data-lang="fr">
                    <h2 class="text-6xl md:text-8xl font-black text-white font-mono tracking-wider hover:text-blue-400 transition-colors duration-300 cursor-pointer">
                        FRANÇAIS
                    </h2>
                </div>
                <div class="language-item opacity-0 transform translate-y-20" data-lang="de">
                    <h2 class="text-6xl md:text-8xl font-black text-white font-mono tracking-wider hover:text-blue-400 transition-colors duration-300 cursor-pointer">
                        DEUTSCH
                    </h2>
                </div>
                <div class="language-item opacity-0 transform translate-y-20" data-lang="it">
                    <h2 class="text-6xl md:text-8xl font-black text-white font-mono tracking-wider hover:text-blue-400 transition-colors duration-300 cursor-pointer">
                        ITALIANO
                    </h2>
                </div>
                <div class="language-item opacity-0 transform translate-y-20" data-lang="pt">
                    <h2 class="text-6xl md:text-8xl font-black text-white font-mono tracking-wider hover:text-blue-400 transition-colors duration-300 cursor-pointer">
                        PORTUGUÊS
                    </h2>
                </div>
                <div class="language-item opacity-0 transform translate-y-20" data-lang="ja">
                    <h2 class="text-6xl md:text-8xl font-black text-white font-mono tracking-wider hover:text-blue-400 transition-colors duration-300 cursor-pointer">
                        JAPANESE
                    </h2>
                </div>
                <div class="language-item opacity-0 transform translate-y-20" data-lang="ko">
                    <h2 class="text-6xl md:text-8xl font-black text-white font-mono tracking-wider hover:text-blue-400 transition-colors duration-300 cursor-pointer">
                        KOREAN
                    </h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Crusades Overlay -->
    <div id="crusades-overlay" class="fixed inset-0 bg-black bg-opacity-95 z-[100] opacity-0 invisible transition-all duration-500 flex items-center justify-center overflow-y-auto">
        <div class="w-full max-w-5xl px-8 py-16 min-h-full flex flex-col justify-center">
            <!-- Close Button -->
            <button id="close-crusades-overlay" class="fixed top-8 right-8 text-white text-4xl font-light hover:rotate-90 transition-transform duration-300 z-10">
                ×
            </button>
            
            <!-- Title -->
            <div class="text-center mb-12 flex-shrink-0">
                <h1 class="text-4xl md:text-6xl font-black text-white font-mono tracking-wider mb-4">
                    CONFIRMED CRUSADES
                </h1>
                <p class="text-xl text-gray-300 font-light mb-6">
                    Join ongoing crusades around the world
                </p>
                
                <!-- Divider Line -->
                <div class="w-32 h-px bg-white opacity-30 mx-auto mb-6"></div>
                
                <p class="text-lg text-gray-400 font-light max-w-3xl mx-auto leading-relaxed">
                    These are the officially confirmed crusade events currently scheduled across multiple nations. We are still updating.
                </p>
    </div>
    
            <!-- Countries List with Scrollable Container -->
            <div class="flex-1 overflow-y-auto max-h-[70vh] pr-4 custom-scrollbar">
                <div id="crusades-list" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 pb-8">
                    <!-- Countries will be loaded dynamically from API -->
                    <div id="loading-countries" class="col-span-full text-center py-8">
                        <div class="text-white text-lg">Loading countries...</div>
                        <div class="animate-spin w-8 h-8 border-2 border-white border-t-transparent mx-auto mt-4"></div>
                    </div>
                </div>
            </div>
        </div>
                        </div>
                        
    <!-- Loading Screen -->
    <div id="loading-screen" class="fixed inset-0 bg-black z-[200] opacity-0 invisible flex items-center justify-center">
        <div class="text-center">
            <h1 id="loading-text" class="text-8xl md:text-9xl font-black text-white font-mono tracking-wider">
                <span class="glitch-char">A</span>
                <span class="glitch-char"> </span>
                <span class="glitch-char">T</span>
                <span class="glitch-char">H</span>
                <span class="glitch-char">O</span>
                <span class="glitch-char">U</span>
                <span class="glitch-char">S</span>
                <span class="glitch-char">A</span>
                <span class="glitch-char">N</span>
                <span class="glitch-char">D</span>
                <span class="glitch-char"> </span>
                <span class="glitch-char">C</span>
                <span class="glitch-char">R</span>
                <span class="glitch-char">U</span>
                <span class="glitch-char">S</span>
                <span class="glitch-char">A</span>
                <span class="glitch-char">D</span>
                <span class="glitch-char">E</span>
                <span class="glitch-char">S</span>
            </h1>
            <div class="mt-8">
                <div class="w-64 h-1 bg-gray-800 mx-auto rounded-full overflow-hidden">
                    <div id="loading-bar" class="h-full bg-blue-400 w-0 transition-all duration-3000 ease-out"></div>
                </div>
            </div>
        </div>
                        </div>
                        
    <!-- Hero Section -->
    <section id="hero" class="min-h-screen bg-slate-900 relative flex items-end justify-start">
        <!-- Scroll Down Message - Top Right -->
        <div id="scroll-message" class="absolute top-32 right-12 text-white opacity-0 z-10">
            <div class="flex flex-col items-center">
                <p class="text-xs font-light tracking-[0.3em] transform rotate-90 whitespace-nowrap">SCROLL DOWN</p>
                        </div>
                    </div>

        <!-- Main Hero Text - Bottom Left -->
        <div id="hero-text" class="absolute bottom-16 left-12 max-w-4xl opacity-0 z-10">
            <h1 class="text-6xl md:text-7xl lg:text-8xl font-black text-white leading-tight tracking-tight">
                <span class="block">
                    <span class="word opacity-0">WELCOME</span>
                    <span class="word opacity-0">TO</span>
                    <span class="word opacity-0">A</span>
                </span>
                <span class="block">
                    <span class="word opacity-0">NIGHT</span>
                    <span class="word opacity-0">OF</span>
                    <span class="word opacity-0">A</span>
                </span>
                <span class="block">
                    <span class="word opacity-0">THOUSAND</span>
                </span>
                <span class="block">
                    <span class="word opacity-0 text-blue-400">CRUSADES</span>
                </span>
            </h1>
                </div>

        <!-- Large Scroll Arrow - Bottom Center -->
        <div id="scroll-arrow" class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white opacity-0 z-10">
            <svg class="w-8 h-8 animate-bounce" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
        </div>
    </section>

    <!-- Second Section -->
    <section id="second-section" class="min-h-screen bg-slate-900 relative flex items-center justify-center">
        <!-- Main Content Container -->
        <div class="text-center z-10 relative">
            <!-- Date Element -->
            <div id="event-date" class="mb-4 opacity-0 transform translate-y-8" style="transform: translateY(100px) scale(0.8);">
                <h3 class="text-2xl md:text-3xl lg:text-4xl font-light text-white tracking-widest">
                    22ND OF AUGUST 2025
                </h3>
            </div>
            
            <!-- Centered Title -->
            <div id="centered-title" class="mb-16 opacity-0">
                <h2 class="text-6xl md:text-7xl lg:text-8xl font-black text-white leading-tight tracking-tight">
                    <span class="block">
                        <span class="second-word opacity-0">NIGHT</span>
                        <span class="second-word opacity-0">OF</span>
                        <span class="second-word opacity-0">A</span>
                    </span>
                    <span class="block">
                        <span class="second-word opacity-0">THOUSAND</span>
                    </span>
                    <span class="block text-blue-400">
                        <span class="second-word opacity-0">CRUSADES</span>
                    </span>
                </h2>
                </div>

            <!-- Action Buttons with Divider -->
            <div id="action-buttons" class="flex items-center justify-center space-x-12 opacity-0">
                <button id="view-crusades-btn" class="action-btn w-80 bg-transparent border-2 border-white text-white px-12 py-6 text-xl font-light tracking-wider hover:bg-white hover:text-slate-900 transition-all duration-300 transform hover:scale-105">
                    VIEW CRUSADES
                        </button>
                        
                <!-- Vertical Divider -->
                <div class="w-px h-16 bg-white opacity-50"></div>
                
                <button id="host-crusade-btn" class="action-btn w-80 bg-transparent border-2 border-white text-white px-12 py-6 text-xl font-light tracking-wider hover:bg-white hover:text-slate-900 transition-all duration-300 transform hover:scale-105">
                    HOST YOUR CRUSADE
                        </button>
                    </div>
            
            <!-- Descriptive Text -->
            <div id="description-text" class="mt-6 opacity-0">
                <p class="text-2xl text-white font-serif leading-relaxed max-w-4xl mx-auto text-center">
                    Join us for powerful crusades around the world as we spread the gospel and reach souls in every nation.
                </p>
        </div>
    </div>
</section>

    <script>
        // Register ScrollTrigger plugin
        gsap.registerPlugin(ScrollTrigger);

        // Function to play main screen animation
        function playMainAnimation() {
            // Reset all elements to initial state
            gsap.set("#navbar", { opacity: 0, y: -50, scale: 0.9 });
            gsap.set("#navbar a", { opacity: 0, x: -30 });
            gsap.set("#navbar button", { opacity: 0, x: 30 });
            gsap.set("#hero-text", { opacity: 0 });
            gsap.set("#scroll-message", { opacity: 0, x: 50 });
            gsap.set("#scroll-arrow", { opacity: 0, y: 20 });
            gsap.set(".word", { opacity: 0, rotationX: 90, transformOrigin: "50% 50% -50px", z: -50 });

            // Create new timeline
            const tl = gsap.timeline();

            // Navbar entrance animation
            tl.fromTo("#navbar", 
                {
                    opacity: 0,
                    y: -50,
                    scale: 0.9
                },
                {
                    opacity: 1,
                    y: 0,
                    scale: 1,
                    duration: 1.2,
                    ease: "power3.out",
                    delay: 0.3
                }
            )
            // Animate navbar elements individually
            .fromTo("#navbar a", 
                {
                    opacity: 0,
                    x: -30
                },
                {
                    opacity: 1,
                    x: 0,
                    duration: 0.8,
                    ease: "power2.out"
                }, "-=0.4"
            )
            .fromTo("#navbar button", 
                {
                    opacity: 0,
                    x: 30
                },
                {
                    opacity: 1,
                    x: 0,
                    duration: 0.8,
                    ease: "power2.out"
                }, "-=0.6"
            )
            // Hero container animation
            .fromTo("#hero-text", 
                {
                    opacity: 0
                },
                {
                    opacity: 1,
                    duration: 0.5,
                    ease: "power2.out"
                }, "-=0.2");

            // Word-by-word animation for hero text
            const words = document.querySelectorAll('.word');
            
            words.forEach((word, index) => {
                gsap.fromTo(word, 
                    {
                        opacity: 0,
                        rotationX: 90,
                        transformOrigin: "50% 50% -50px",
                        z: -50
                    },
                    {
                        opacity: 1,
                        rotationX: 0,
                        z: 0,
                        duration: 0.8,
                        ease: "back.out(1.7)",
                        delay: 1.5 + (index * 0.15),
                        onComplete: function() {
                            // Show scroll message after the last word
                            if (index === words.length - 1) {
                                gsap.fromTo("#scroll-message", 
                                    {
                                        opacity: 0,
                                        x: 50
                                    },
                                    {
                                        opacity: 1,
                                        x: 0,
                                        duration: 1,
                                        ease: "power2.out",
                                        delay: 0.5
                                    }
                                );
                                
                                // Show scroll arrow
                                gsap.fromTo("#scroll-arrow", 
                                    {
                                        opacity: 0,
                                        y: 20
                                    },
                                    {
                                        opacity: 1,
                                        y: 0,
                                        duration: 1,
                                        ease: "power2.out",
                                        delay: 0.7
                                    }
                                );
                            }
                        }
                    }
                );
            });
        }

        // Function to play reverse hero animation
        function playReverseHeroAnimation() {
            // Create reverse timeline
            const reverseTl = gsap.timeline();
            
            // Reverse word animation (last word first)
            const words = document.querySelectorAll('.word');
            const reversedWords = Array.from(words).reverse();
            
            reversedWords.forEach((word, index) => {
                gsap.fromTo(word, 
                    {
                        opacity: 0,
                        rotationX: -90,
                        transformOrigin: "50% 50% -50px",
                        z: -50
                    },
                    {
                        opacity: 1,
                        rotationX: 0,
                        z: 0,
                        duration: 0.6,
                        ease: "back.out(1.7)",
                        delay: index * 0.1
                    }
                );
            });
            
            // Show hero text container
            gsap.to("#hero-text", {
                opacity: 1,
                duration: 0.3,
                ease: "power2.out"
            });
            
            // Animate scroll indicators back in
            gsap.fromTo("#scroll-message", 
                {
                    opacity: 0,
                    x: 50
                },
                {
                    opacity: 1,
                    x: 0,
                    duration: 0.8,
                    ease: "power2.out",
                    delay: 0.5
                }
            );
            
            gsap.fromTo("#scroll-arrow", 
                {
                    opacity: 0,
                    y: 20
                },
                {
                    opacity: 1,
                    y: 0,
                    duration: 0.8,
                    ease: "power2.out",
                    delay: 0.7
                }
            );
        }

        // Scroll animations
        function setupScrollAnimations() {
            // Navbar fade out on scroll and floating button fade in
            ScrollTrigger.create({
                trigger: "#second-section",
                start: "top 90%",
                end: "bottom 10%",
                scrub: true,
                onUpdate: (self) => {
                    const progress = self.progress;
                    
                    // Fade out navbar
                    gsap.to("#navbar", {
                        opacity: 1 - progress,
                        y: -50 * progress,
                        duration: 0.3
                    });
                    
                    // Fade in floating language button
                    gsap.to("#floating-language-btn", {
                        opacity: progress,
                        duration: 0.3
                    });
                    
                    // Fade in floating logo
                    gsap.to("#floating-logo", {
                        opacity: progress,
                        duration: 0.3
                    });
                    
                    // Toggle visibility
                    if (progress > 0.3) {
                        document.getElementById('floating-language-btn').classList.remove('invisible');
                        document.getElementById('floating-logo').classList.remove('invisible');
    } else {
                        document.getElementById('floating-language-btn').classList.add('invisible');
                        document.getElementById('floating-logo').classList.add('invisible');
                    }
                },
                onEnter: () => {
                    // Ensure navbar is hidden when entering second section
                    gsap.to("#navbar", { opacity: 0, y: -50, duration: 0.5 });
                    gsap.to("#floating-language-btn", { opacity: 1, duration: 0.5 });
                    gsap.to("#floating-logo", { opacity: 1, duration: 0.5 });
                    document.getElementById('floating-language-btn').classList.remove('invisible');
                    document.getElementById('floating-logo').classList.remove('invisible');
                },
                onLeave: () => {
                    // Show navbar when leaving second section
                    gsap.to("#navbar", { opacity: 1, y: 0, duration: 0.5 });
                    gsap.to("#floating-language-btn", { opacity: 0, duration: 0.5 });
                    gsap.to("#floating-logo", { opacity: 0, duration: 0.5 });
                    setTimeout(() => {
                        document.getElementById('floating-language-btn').classList.add('invisible');
                        document.getElementById('floating-logo').classList.add('invisible');
                    }, 500);
                },
                onEnterBack: () => {
                    // Hide navbar when scrolling back into second section
                    gsap.to("#navbar", { opacity: 0, y: -50, duration: 0.5 });
                    gsap.to("#floating-language-btn", { opacity: 1, duration: 0.5 });
                    gsap.to("#floating-logo", { opacity: 1, duration: 0.5 });
                    document.getElementById('floating-language-btn').classList.remove('invisible');
                    document.getElementById('floating-logo').classList.remove('invisible');
                },
                onLeaveBack: () => {
                    // Show navbar when scrolling back to first section
                    gsap.to("#navbar", { opacity: 1, y: 0, duration: 0.5 });
                    gsap.to("#floating-language-btn", { opacity: 0, duration: 0.5 });
                    gsap.to("#floating-logo", { opacity: 0, duration: 0.5 });
                    setTimeout(() => {
                        document.getElementById('floating-language-btn').classList.add('invisible');
                        document.getElementById('floating-logo').classList.add('invisible');
                    }, 500);
                }
            });

            // Additional aggressive navbar hiding for second section
            ScrollTrigger.create({
                trigger: "#second-section",
                start: "top 95%",
                end: "bottom 5%",
                onEnter: () => {
                    // Force hide navbar completely
                    document.getElementById('navbar').style.display = 'none';
                },
                onLeave: () => {
                    // Show navbar when leaving second section
                    document.getElementById('navbar').style.display = 'block';
                },
                onEnterBack: () => {
                    // Force hide navbar when scrolling back into second section
                    document.getElementById('navbar').style.display = 'none';
                },
                onLeaveBack: () => {
                    // Show navbar when scrolling back to first section
                    document.getElementById('navbar').style.display = 'block';
                }
            });

            // Hero text fade out as we scroll to second section
            ScrollTrigger.create({
                trigger: "#second-section",
                start: "top 80%",
                end: "top 20%",
                scrub: true,
                onUpdate: (self) => {
                    const progress = self.progress;
                    
                    // Fade out hero text
                    gsap.to("#hero-text", {
                        opacity: 1 - progress,
                        y: -50 * progress,
                        duration: 0.3
                    });
                    
                    // Fade out scroll indicators
                    gsap.to("#scroll-message", {
                        opacity: 1 - progress,
                        x: 50 * progress,
                        duration: 0.3
                    });
                    
                    gsap.to("#scroll-arrow", {
                        opacity: 1 - progress,
                        y: 20 * progress,
                        duration: 0.3
                    });
                }
            });

            // Hero section reverse animation when scrolling back
            ScrollTrigger.create({
                trigger: "#hero",
                start: "top 80%",
                end: "bottom 20%",
                onEnterBack: () => {
                    // Play reverse hero animation
                    playReverseHeroAnimation();
                },
                onLeaveBack: () => {
                    // Reset elements when leaving hero section backwards
                    gsap.set(".word", { opacity: 0, rotationX: 90, transformOrigin: "50% 50% -50px", z: -50 });
                    gsap.set("#hero-text", { opacity: 0 });
                    gsap.set("#scroll-message", { opacity: 0, x: 50 });
                    gsap.set("#scroll-arrow", { opacity: 0, y: 20 });
                }
            });

            // Animate centered title and buttons when second section comes into view
            ScrollTrigger.create({
                trigger: "#second-section",
                start: "top 60%",
                end: "bottom 40%",
                onEnter: () => {
                    // Show centered title container
                    gsap.fromTo("#centered-title", 
                        {
                            opacity: 0
                        },
                        {
                            opacity: 1,
                            duration: 0.5,
                            ease: "power2.out"
                        }
                    );
                    
                    // Word-by-word animation for second section title
                    const secondWords = document.querySelectorAll('.second-word');
                    secondWords.forEach((word, index) => {
                        gsap.fromTo(word, 
                            {
                                opacity: 0,
                                rotationX: 90,
                                transformOrigin: "50% 50% -50px",
                                z: -50
                            },
                            {
                                opacity: 1,
                                rotationX: 0,
                                z: 0,
                                duration: 0.8,
                                ease: "back.out(1.7)",
                                delay: 0.3 + (index * 0.15),
                                onComplete: function() {
                                    // Show date after all words are complete
                                    if (index === secondWords.length - 1) {
                                        gsap.fromTo("#event-date", 
                                            {
                                                opacity: 0,
                                                scale: 0.8,
                                                y: 20
                                            },
                                            {
                                                opacity: 1,
                                                scale: 1,
                                                y: 0,
                                                duration: 0.8,
                                                delay: 0.3,
                                                ease: "back.out(1.7)"
                                            }
                                        );
                                    }
                                }
                            }
                        );
                    });
                    
                    // Animate action buttons with stagger (after words complete)
                    gsap.fromTo("#action-buttons", 
                        {
                            opacity: 0,
                            y: 30
                        },
                        {
                            opacity: 1,
                            y: 0,
                            duration: 1,
                            delay: 1.5, // Wait for words to complete
                            ease: "power3.out"
                        }
                    );
                    
                    // Individual button animations
                    gsap.fromTo(".action-btn", 
                        {
                            opacity: 0,
                            scale: 0.8,
                            y: 20
                        },
                        {
                            opacity: 1,
                            scale: 1,
                            y: 0,
                            duration: 0.8,
                            stagger: 0.2,
                            delay: 1.7, // Wait for words to complete
                            ease: "back.out(1.7)",
                            onComplete: function() {
                                // Trigger description text animation after last button completes
                                gsap.fromTo("#description-text", 
                                    {
                                        opacity: 0,
                                        y: 20
                                    },
                                    {
                                        opacity: 1,
                                        y: 0,
                                        duration: 1,
                                        delay: 0.3, // Small delay after buttons complete
                                        ease: "power3.out"
                                    }
                                );
                            }
                        }
                    );
                },
                onLeave: () => {
                    // Fade out all elements when leaving second section
                    gsap.to("#centered-title", { opacity: 0, duration: 0.5 });
                    gsap.to("#action-buttons", { opacity: 0, duration: 0.5 });
                    gsap.to("#description-text", { opacity: 0, duration: 0.5 });
                    gsap.to("#event-date", { opacity: 0, y: -20, duration: 0.5 });
                    gsap.to(".second-word", { opacity: 0, duration: 0.3 });
                },
                onEnterBack: () => {
                    // Fade in all elements when returning to second section
                    gsap.to("#centered-title", { opacity: 1, duration: 0.5 });
                    gsap.to("#action-buttons", { opacity: 1, duration: 0.5 });
                    gsap.to("#description-text", { opacity: 1, duration: 0.5 });
                    gsap.to("#event-date", { opacity: 1, y: 0, scale: 1, duration: 0.5 });
                    gsap.to(".second-word", { opacity: 1, duration: 0.3, stagger: 0.1 });
                },
                onLeaveBack: () => {
                    // Fade out all elements when scrolling back to first section
                    gsap.to("#centered-title", { opacity: 0, duration: 0.5 });
                    gsap.to("#action-buttons", { opacity: 0, duration: 0.5 });
                    gsap.to("#description-text", { opacity: 0, duration: 0.5 });
                    gsap.to("#event-date", { opacity: 0, y: 20, scale: 0.8, duration: 0.5 });
                    gsap.to(".second-word", { opacity: 0, duration: 0.3 });
                }
            });
        }

        // Glitch animation for loading screen
        function startGlitchAnimation() {
            const glitchChars = document.querySelectorAll('.glitch-char');
            const originalChars = Array.from(glitchChars).map(char => char.textContent);
            const glitchSymbols = ['█', '▓', '▒', '░', '▄', '▀', '■', '□', '▪', '▫', '◆', '◇', '●', '○', '◉', '◎', '⬛', '⬜', '▲', '△', '▼', '▽', '◀', '▶', '◄', '►'];

            function glitchChar(char, index) {
                const originalChar = originalChars[index];
                
                // Random glitch effect
                if (Math.random() < 0.3) {
                    const randomSymbol = glitchSymbols[Math.floor(Math.random() * glitchSymbols.length)];
                    char.textContent = randomSymbol;
                    char.style.color = Math.random() < 0.5 ? '#ff0000' : '#00ffff';
                    char.style.textShadow = '2px 0 #ff0000, -2px 0 #00ffff';
                    
                    // Reset after short delay
                    setTimeout(() => {
                        char.textContent = originalChar;
                        char.style.color = 'white';
                        char.style.textShadow = 'none';
                    }, Math.random() * 200 + 50);
                }
            }

            // Start continuous glitch effect
            const glitchInterval = setInterval(() => {
                glitchChars.forEach((char, index) => {
                    if (Math.random() < 0.1) { // 10% chance per character
                        glitchChar(char, index);
                    }
                });
            }, 100);

            return glitchInterval;
        }

        // Show loading screen with glitch animation
        function showLoadingScreen(callback) {
            const loadingScreen = document.getElementById('loading-screen');
            const loadingBar = document.getElementById('loading-bar');
            
            // Show loading screen
            loadingScreen.classList.remove('invisible');
            loadingScreen.classList.remove('opacity-0');
            
            // Start glitch animation
            const glitchInterval = startGlitchAnimation();
            
            // Animate loading bar
            setTimeout(() => {
                loadingBar.style.width = '100%';
            }, 100);
            
            // Complete loading after 3 seconds
            setTimeout(() => {
                clearInterval(glitchInterval);
                
                // Fade out loading screen
                loadingScreen.classList.add('opacity-0');
                
                setTimeout(() => {
                    loadingScreen.classList.add('invisible');
                    loadingBar.style.width = '0%';
                    if (callback) callback();
                }, 500);
            }, 3000);
        }

        // Initialize everything
document.addEventListener('DOMContentLoaded', function() {
            // Check initial scroll position and adjust navbar/floating button visibility
            checkInitialScrollPosition();
            
            // Play initial animation on page load
            playMainAnimation();
            
            // Setup scroll animations
            setupScrollAnimations();
            
            // Setup button click handlers
            setupButtonHandlers();
        });

        // Function to check scroll position on page load
        function checkInitialScrollPosition() {
            const secondSection = document.getElementById('second-section');
            const rect = secondSection.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            
            // If second section is visible at all (more aggressive check)
            if (rect.top <= viewportHeight * 0.95) {
                // Force hide navbar and show floating button
                gsap.set("#navbar", { opacity: 0, y: -50, display: 'none' });
                gsap.set("#floating-language-btn", { opacity: 1 });
                document.getElementById('floating-language-btn').classList.remove('invisible');
            } else {
                // Show navbar and hide floating button
                gsap.set("#navbar", { opacity: 1, y: 0, display: 'block' });
                gsap.set("#floating-language-btn", { opacity: 0 });
                document.getElementById('floating-language-btn').classList.add('invisible');
            }
        }

        // Language overlay functionality
        const languageBtn = document.getElementById('language-btn');
        const floatingLanguageBtn = document.getElementById('floating-language-btn');
        const languageOverlay = document.getElementById('language-overlay');
        const closeOverlay = document.getElementById('close-overlay');
        const dropdownArrow = document.getElementById('dropdown-arrow');
        const languageItems = document.querySelectorAll('.language-item');

        // Function to open language overlay
        function openLanguageOverlay() {
            languageOverlay.classList.remove('invisible');
            languageOverlay.classList.remove('opacity-0');
            if (dropdownArrow) dropdownArrow.style.transform = 'rotate(180deg)';
            
            // Animate language items
            languageItems.forEach((item, index) => {
                gsap.to(item, {
                    opacity: 1,
                    y: 0,
                    duration: 0.6,
                    delay: index * 0.1,
                    ease: "power3.out"
                });
            });
        }

        // Open overlay from navbar button
        languageBtn.addEventListener('click', openLanguageOverlay);
        
        // Open overlay from floating button
        floatingLanguageBtn.addEventListener('click', openLanguageOverlay);

        // Close overlay
        function closeLanguageOverlay() {
            languageOverlay.classList.add('opacity-0');
            dropdownArrow.style.transform = 'rotate(0deg)';
            
            // Reset language items
            languageItems.forEach((item) => {
                gsap.set(item, {
                    opacity: 0,
                    y: 20
                });
            });
            
        setTimeout(() => {
                languageOverlay.classList.add('invisible');
            }, 500);
        }

        closeOverlay.addEventListener('click', closeLanguageOverlay);

        // Close on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeLanguageOverlay();
            }
        });

        // Language selection with exciting animations
        languageItems.forEach((item) => {
            const text = item.querySelector('h2');
            
            // Hover effects
            item.addEventListener('mouseenter', () => {
                gsap.to(text, {
                    scale: 1.05,
                    x: 20,
                    duration: 0.3,
                    ease: "power2.out"
                });
                
                // Add glitch effect
                gsap.to(text, {
                    textShadow: "2px 0 #ff0000, -2px 0 #00ffff",
                    duration: 0.1,
                    repeat: 3,
                    yoyo: true
                });
            });
            
            item.addEventListener('mouseleave', () => {
                gsap.to(text, {
                    scale: 1,
                    x: 0,
                    textShadow: "none",
                    duration: 0.3,
                    ease: "power2.out"
                });
            });
            
            // Click to select language - shows loading screen then reloads
            item.addEventListener('click', () => {
                const lang = item.dataset.lang;
                
                // Update floating button language prefix
                updateFloatingButtonLanguage(lang);
                
                // Close overlay first
                closeLanguageOverlay();
                
                // Wait for overlay to close, then show loading screen
                setTimeout(() => {
                    showLoadingScreen(() => {
                        // After loading animation completes, reload the page
                        // You can modify this to redirect to a language-specific URL
                        window.location.reload();
                        // Or redirect to: window.location.href = `?lang=${lang}`;
                    });
                }, 600);
            });
        });

        function changeLanguage(lang) {
            console.log('Language changed to:', lang);
            // Add your language switching logic here
        }

        // Function to update floating button language prefix
        function updateFloatingButtonLanguage(lang) {
            const floatingBtn = document.getElementById('floating-language-btn');
            const floatingLangSpan = floatingBtn.querySelector('span');
            
            const langPrefixes = {
                'en': 'EN',
                'es': 'ES',
                'fr': 'FR',
                'de': 'DE',
                'it': 'IT',
                'pt': 'PT',
                'ja': 'JA',
                'ko': 'KO'
            };
            
            if (floatingLangSpan && langPrefixes[lang]) {
                floatingLangSpan.textContent = langPrefixes[lang];
            }
        }

        // Function to setup button click handlers
        function setupButtonHandlers() {
            const hostCrusadeBtn = document.getElementById('host-crusade-btn');
            const viewCrusadesBtn = document.getElementById('view-crusades-btn');
            const scrollArrow = document.getElementById('scroll-arrow');
            
            // Scroll arrow click handler - smooth scroll to second section
            scrollArrow.addEventListener('click', () => {
                document.getElementById('second-section').scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            });
            
            // Host Your Crusade button click handler
            hostCrusadeBtn.addEventListener('click', () => {
                // Show loading screen then navigate to new page
                showLoadingScreen(() => {
                    // After loading animation completes, redirect to the new page
                    window.location.href = 'host-crusade.php';
                });
            });
            
            // View Crusades button click handler
            viewCrusadesBtn.addEventListener('click', () => {
                openCrusadesOverlay();
            });
        }

        // Crusades overlay functionality
        const crusadesOverlay = document.getElementById('crusades-overlay');
        const closeCrusadesOverlay = document.getElementById('close-crusades-overlay');
        const crusadeItems = document.querySelectorAll('.crusade-item');

        // Function to load countries from API
        async function loadCrusadeCountries() {
            try {
                const response = await fetch('api.php?endpoint=countries');
                const data = await response.json();
                
                if (data.success && data.countries) {
                    renderCountries(data.countries);
                } else {
                    showErrorMessage('Failed to load countries data');
                }
            } catch (error) {
                console.error('Error loading countries:', error);
                showErrorMessage('Network error while loading countries');
            }
        }
        
        // Function to render countries in the modal
        function renderCountries(countries) {
            const crusadesList = document.getElementById('crusades-list');
            const loadingDiv = document.getElementById('loading-countries');
            
            // Remove loading indicator
            if (loadingDiv) {
                loadingDiv.remove();
            }
            
            // Clear existing content
            crusadesList.innerHTML = '';
            
            // Add countries
            countries.forEach((country, index) => {
                const countryDiv = document.createElement('div');
                countryDiv.className = 'crusade-item opacity-0 transform translate-y-20';
                countryDiv.setAttribute('data-country', country.id);
                
                countryDiv.innerHTML = `
                    <div class="bg-white bg-opacity-10 backdrop-blur-sm border border-white border-opacity-20 p-6 hover:bg-opacity-20 transition-all duration-300 cursor-pointer group">
                        <h3 class="text-2xl font-bold text-white mb-2 group-hover:text-blue-400 transition-colors duration-300">
                            ${country.name}
                        </h3>
                        <p class="text-gray-300 text-sm mb-3">
                            ${country.cities}
                        </p>
                        <div class="flex justify-between items-center">
                            <span class="text-blue-400 text-sm font-semibold">
                                ${country.crusade_count} Confirmed Crusades
                            </span>
                            <svg class="w-5 h-5 text-white group-hover:text-blue-400 transition-colors duration-300" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                    </div>
                `;
                
                crusadesList.appendChild(countryDiv);
                
                // Add click handler
                countryDiv.addEventListener('click', () => {
                    handleCountryClick(country);
                });
                
                // Add hover effects
                addCountryHoverEffects(countryDiv);
            });
            
            // Update global crusadeItems for animation
            window.crusadeItems = document.querySelectorAll('.crusade-item');
        }
        
        // Function to handle country click
        function handleCountryClick(country) {
            console.log('View crusades for:', country.name);
            // Add visual feedback
            const title = event.currentTarget.querySelector('h3');
            gsap.to(title, {
                color: '#60a5fa',
                duration: 0.2,
                yoyo: true,
                repeat: 1
            });
        }
        
        // Function to add hover effects to country items
        function addCountryHoverEffects(item) {
            const container = item.querySelector('div');
            const title = item.querySelector('h3');
            const arrow = item.querySelector('svg');
            
            item.addEventListener('mouseenter', () => {
                gsap.to(container, {
                    scale: 1.02,
                    duration: 0.3,
                    ease: "power2.out"
                });
                
                gsap.to(arrow, {
                    x: 5,
                    duration: 0.3,
                    ease: "power2.out"
                });
            });
            
            item.addEventListener('mouseleave', () => {
                gsap.to(container, {
                    scale: 1,
                    duration: 0.3,
                    ease: "power2.out"
                });
                
                gsap.to(arrow, {
                    x: 0,
                    duration: 0.3,
                    ease: "power2.out"
                });
            });
        }
        
        // Function to show error message
        function showErrorMessage(message) {
            const crusadesList = document.getElementById('crusades-list');
            const loadingDiv = document.getElementById('loading-countries');
            
            if (loadingDiv) {
                loadingDiv.innerHTML = `
                    <div class="text-center py-8">
                        <div class="text-red-400 text-lg mb-4">
                            <i class="fas fa-exclamation-triangle mb-2"></i><br>
                            ${message}
                        </div>
                        <button onclick="loadCrusadeCountries()" class="bg-blue-600 text-white px-4 py-2 hover:bg-blue-700 transition-colors">
                            Try Again
                        </button>
                    </div>
                `;
            }
        }

        // Function to open crusades overlay
        function openCrusadesOverlay() {
            crusadesOverlay.classList.remove('invisible');
            crusadesOverlay.classList.remove('opacity-0');
            
            // Load countries if not already loaded
            if (!document.querySelector('.crusade-item:not(#loading-countries)')) {
                loadCrusadeCountries();
            }
            
            // Animate crusade items (if they exist)
            setTimeout(() => {
                const items = document.querySelectorAll('.crusade-item:not(#loading-countries)');
                items.forEach((item, index) => {
                    gsap.to(item, {
                        opacity: 1,
                        y: 0,
                        duration: 0.6,
                        delay: index * 0.1,
                        ease: "power3.out"
                    });
                });
            }, 100);
        }

        // Close crusades overlay
        function closeCrusadesOverlayFunc() {
            crusadesOverlay.classList.add('opacity-0');
            
            // Reset crusade items
            crusadeItems.forEach((item) => {
                gsap.set(item, {
                    opacity: 0,
                    y: 20
                });
            });
            
            setTimeout(() => {
                crusadesOverlay.classList.add('invisible');
            }, 500);
        }

        closeCrusadesOverlay.addEventListener('click', closeCrusadesOverlayFunc);

        // Close crusades overlay on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeCrusadesOverlayFunc();
            }
        });

        // Crusade item interactions
        crusadeItems.forEach((item) => {
            const container = item.querySelector('div');
            const title = item.querySelector('h3');
            const arrow = item.querySelector('svg');
            
            // Hover effects
            item.addEventListener('mouseenter', () => {
                gsap.to(container, {
                    scale: 1.02,
                    duration: 0.3,
                    ease: "power2.out"
                });
                
                gsap.to(arrow, {
                    x: 5,
                    duration: 0.3,
                    ease: "power2.out"
                });
            });
            
            item.addEventListener('mouseleave', () => {
                gsap.to(container, {
                    scale: 1,
                    duration: 0.3,
                    ease: "power2.out"
                });
                
                gsap.to(arrow, {
                    x: 0,
                    duration: 0.3,
                    ease: "power2.out"
                });
            });
            
            // Click to view country details
            item.addEventListener('click', () => {
                const country = item.dataset.country;
                console.log('View crusades for:', country);
                // Add your country-specific crusades logic here
                // For now, we'll just add a visual feedback
                gsap.to(title, {
                    color: '#60a5fa',
                    duration: 0.2,
                    yoyo: true,
                    repeat: 1
                });
            });
        });
    </script>
</body>
</html>









