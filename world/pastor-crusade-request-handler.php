<?php
/**
 * World Crusade Request Handler
 * Processes form submissions from host-crusade.php and host-crusade-mobile.php
 */

// Set response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Start session
session_start();

// Define response function
function sendResponse($success, $message, $data = null) {
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit();
}

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, 'Invalid request method. Only POST requests are allowed.');
}

// Define paths
$dataDir = __DIR__ . '/../data';
$requestsFile = $dataDir . '/world_crusade_requests.json';

// Ensure data directory exists
if (!is_dir($dataDir)) {
    if (!mkdir($dataDir, 0755, true)) {
        sendResponse(false, 'Unable to create data directory. Please check permissions.');
    }
}

// Function to read existing requests
function getExistingRequests($file) {
    if (!file_exists($file)) {
        return ['requests' => []];
    }
    $json = file_get_contents($file);
    $data = json_decode($json, true);
    return $data ?: ['requests' => []];
}

// Function to save requests
function saveRequests($file, $data) {
    $json = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
    return file_put_contents($file, $json) !== false;
}

// Function to sanitize input
function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

// Function to validate email
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// Function to validate phone
function validatePhone($phone) {
    // Basic phone validation - allows numbers, spaces, hyphens, parentheses, plus sign
    return preg_match('/^[\+]?[0-9\s\-\(\)]{7,20}$/', $phone);
}

try {
    // Get existing requests
    $existingData = getExistingRequests($requestsFile);
    
    // Get registration type first to determine which fields are required
    $registrationType = isset($_POST['registration_type']) ? sanitizeInput($_POST['registration_type']) : '';
    
    // Define base required fields (common to all registration types)
    $baseRequiredFields = [
        'registration_type' => 'Registration Type',
        'designation' => 'Designation',
        'first_name' => 'First Name',
        'last_name' => 'Last Name',
        'email' => 'Email Address',
        'phone' => 'Phone Number',
        'kingschat_username' => 'KingsChat Username',
        'church' => 'Church',
        'crusade_title' => 'Crusade Title',
        'country' => 'Country',
        'location' => 'City/Location',
        'crusade_type' => 'Crusade Type',
        'venue' => 'Venue',
        'attendance' => 'Expected Attendance',
        'contact_method' => 'Preferred Contact Method'
    ];
    
    // Add additional required fields based on registration type
    $requiredFields = $baseRequiredFields;
    if ($registrationType === 'individual') {
        $requiredFields['zone'] = 'Zone';
        $requiredFields['group'] = 'Group';
    }
    // For church registrations, zone and group are not required
    
    $errors = [];
    $data = [];
    
    // Validate required fields
    foreach ($requiredFields as $field => $label) {
        if (!isset($_POST[$field]) || empty(trim($_POST[$field]))) {
            $errors[] = "$label is required";
        } else {
            $data[$field] = sanitizeInput($_POST[$field]);
        }
    }
    
    // Validate email format
    if (isset($data['email']) && !validateEmail($data['email'])) {
        $errors[] = 'Please enter a valid email address';
    }
    
    // Validate phone format
    if (isset($data['phone']) && !validatePhone($data['phone'])) {
        $errors[] = 'Please enter a valid phone number';
    }
    
    // If there are validation errors, return them
    if (!empty($errors)) {
        sendResponse(false, 'Validation failed: ' . implode(', ', $errors));
    }
    
    // Add optional fields
    $data['comments'] = isset($_POST['comments']) ? sanitizeInput($_POST['comments']) : '';
    $data['additional_comments'] = isset($_POST['additional_comments']) ? sanitizeInput($_POST['additional_comments']) : '';
    
    // Add zone and group fields if they exist (for individual registrations)
    if (isset($_POST['zone'])) {
        $data['zone'] = sanitizeInput($_POST['zone']);
    } else {
        $data['zone'] = ''; // Empty for church registrations
    }
    
    if (isset($_POST['group'])) {
        $data['group'] = sanitizeInput($_POST['group']);
    } else {
        $data['group'] = ''; // Empty for church registrations
    }
    
    // Generate unique ID
    $newId = 1;
    if (!empty($existingData['requests'])) {
        $ids = array_column($existingData['requests'], 'id');
        $newId = max($ids) + 1;
    }
    
    // Create new request record
    $newRequest = [
        'id' => $newId,
        'registration_type' => $data['registration_type'],
        'designation' => $data['designation'],
        'first_name' => $data['first_name'],
        'last_name' => $data['last_name'],
        'email' => $data['email'],
        'phone' => $data['phone'],
        'kingschat_username' => $data['kingschat_username'],
        'zone' => $data['zone'],
        'group' => $data['group'],
        'church' => $data['church'],
        'crusade_title' => $data['crusade_title'],
        'country' => $data['country'],
        'location' => $data['location'],
        'crusade_type' => $data['crusade_type'],
        'venue' => $data['venue'],
        'attendance' => $data['attendance'],
        'contact_method' => $data['contact_method'],
        'comments' => $data['comments'],
        'additional_comments' => $data['additional_comments'],
        'status' => 'pending',
        'created_at' => date('Y-m-d H:i:s'),
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ];
    
    // Add to existing requests
    array_unshift($existingData['requests'], $newRequest);
    
    // Save to file
    if (saveRequests($requestsFile, $existingData)) {
        // Log successful submission
        $logMessage = "New world crusade request submitted: ID {$newId}, Type: {$data['registration_type']}, Email: {$data['email']}, Country: {$data['country']}";
        error_log($logMessage);
        
        $successMessage = $registrationType === 'church' 
            ? 'Church crusade request submitted successfully! Thank you for your heart for ministry and spreading the Gospel.'
            : 'Crusade request submitted successfully! Thank you for your heart for ministry and spreading the Gospel.';
        
        sendResponse(true, $successMessage, [
            'request_id' => $newId
        ]);
    } else {
        sendResponse(false, 'Unable to save your request. Please try again or contact support.');
    }
    
} catch (Exception $e) {
    error_log("Error processing world crusade request: " . $e->getMessage());
    sendResponse(false, 'An unexpected error occurred. Please try again later.');
}
?> 