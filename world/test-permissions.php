<?php
// Test file writing permissions
$testFile = 'crusade-data.json';

echo "Testing file permissions...\n";
echo "Current directory: " . getcwd() . "\n";
echo "File exists: " . (file_exists($testFile) ? 'YES' : 'NO') . "\n";
echo "Directory writable: " . (is_writable('.') ? 'YES' : 'NO') . "\n";

if (file_exists($testFile)) {
    echo "File writable: " . (is_writable($testFile) ? 'YES' : 'NO') . "\n";
    echo "File readable: " . (is_readable($testFile) ? 'YES' : 'NO') . "\n";
}

// Test data to write
$testData = [
    'countries' => [
        [
            'id' => 'test-1',
            'name' => 'Test Country',
            'cities' => 'Test City',
            'crusade_count' => 1,
            'enabled' => true,
            'created_at' => date('Y-m-d H:i:s')
        ]
    ],
    'settings' => [
        'created_at' => date('Y-m-d H:i:s'),
        'last_updated' => date('Y-m-d H:i:s')
    ]
];

echo "\nAttempting to write test data...\n";
$result = file_put_contents($testFile, json_encode($testData, JSON_PRETTY_PRINT));

if ($result !== false) {
    echo "SUCCESS: Wrote " . $result . " bytes\n";
    echo "File contents:\n";
    echo file_get_contents($testFile);
} else {
    echo "FAILED: Could not write to file\n";
    echo "Error: " . error_get_last()['message'] ?? 'Unknown error';
}
?> 